import { <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import Header from "./common/sections/Header";
import Footer from "./common/sections/Footer";
import CookieConsent from "./common/CookieConsent/CookieConsent";
import CookieInitializer from "./common/CookieConsent/CookieInitializer";
import AnnouncementBanner from "./Home/components/AnnouncementBanner";
import Script from "next/script";
import { Inter } from 'next/font/google'

const montserrat = Montserrat({ 
  subsets: ["latin"],
  variable: '--font-montserrat',
});

const lato = Lato({ 
  subsets: ["latin"],
  weight: ['300', '400', '700', '900'],
  variable: '--font-lato',
});

const inter = Inter({ subsets: ['latin'] })

 
export default function RootLayout({ children }) {
  // We'll use this to skip analytics in development
  const isDevelopment = process.env.NODE_ENV === 'development';

  return (
    <>
      <html lang="en">
        <head>
          {/* Script to block Microsoft Clarity in development environment */}
          {isDevelopment && (
            <Script id="block-clarity" strategy="beforeInteractive">
              {`
                // Disable Microsoft Clarity in development
                window.clarity = window.clarity || function() {
                  console.log('Microsoft Clarity tracking disabled in development');
                };
                
                // Block loading of Clarity script
                const originalCreateElement = document.createElement;
                document.createElement = function(tagName) {
                  const element = originalCreateElement.call(document, tagName);
                  if (tagName.toLowerCase() === 'script') {
                    const originalSetAttribute = element.setAttribute;
                    element.setAttribute = function(name, value) {
                      if (name === 'src' && (
                        value.includes('clarity.ms') || 
                        value.includes('clarity-js') || 
                        value.includes('c.clarity')
                      )) {
                        console.log('Blocked Microsoft Clarity script from loading');
                        return element;
                      }
                      return originalSetAttribute.call(this, name, value);
                    };
                  }
                  return element;
                };
              `}
            </Script>
          )}
          
          {/* Only load analytics scripts in production environment */}
          {!isDevelopment && (
            <>
              <Script 
                id="gtm-head" 
                strategy="afterInteractive" 
                data-consent-required="analytics"
              >
                {`
                  // This will now be controlled by the CookieInitializer component
                  // which will only execute this script if consent has been given
                  (function(w,d,s,l,i){
                    w[l]=w[l]||[];
                    w[l].push({'gtm.start': new Date().getTime(),event:'gtm.js'});
                    var f=d.getElementsByTagName(s)[0],
                    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;
                    j.src='https://www.googletagmanager.com/gtm.js?id='+i+dl;
                    f.parentNode.insertBefore(j,f);
                  })(window,document,'script','dataLayer','GTM-MMMXC8Z');
                `}
              </Script>

              <Script
                id="gozen-personalizer"
                src="https://assets.personalizer.gozen.io/script/embed.js"
                data-gz-d-id="bA3Rto819xFazGgpvDxU"
                strategy="afterInteractive"
                data-consent-required="analytics"
              />
            </>
          )}
        </head>
        <body className={`${montserrat.variable} ${lato.variable} ${inter.className}`}>
          {/* Google Tag Manager (noscript) will be handled via DOM in CookieInitializer */}
          <AnnouncementBanner />
          <Header />

          <div
            className="mt-10 transition-all duration-300 ease-in-out"
            style={{
              paddingTop: 'var(--announcement-banner-height, 0px)'
            }}
          >
            {children}
            <CookieConsent />
            <CookieInitializer />
          </div>
          <Footer />
        </body>
      </html>
    </>
  );
}