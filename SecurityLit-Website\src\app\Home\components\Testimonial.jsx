"use client";
import React from "react";
import Image from "next/image";
import { Star } from "lucide-react";

const testimonials = [
  {
    quote:
      "SecurityLit helped us identify and fix critical vulnerabilities before they became a problem. Their team is professional, responsive, and truly cares about our security.",
    name: "<PERSON><PERSON>",
    title: "CTO, FinTech Corp",
    avatar: "/seclit-logo-1.png",
    initials: "PS",
  },
  {
    quote:
      "The vCISO service from SecurityLit gave us the strategic guidance we needed to scale securely. Highly recommended for any growing business!",
    name: "<PERSON>",
    title: "Founder, HealthSync",
    avatar: "/seclit-logo-1.png",
    initials: "<PERSON><PERSON>",
  },
  {
    quote:
      "Their red teaming and compliance assessment were top-notch. We now feel confident about our security posture and compliance readiness.",
    name: "<PERSON><PERSON><PERSON>",
    title: "COO, EduTech Solutions",
    avatar: "/seclit-logo-1.png",
    initials: "AP",
  },
  {
    quote:
      "SecurityLit transformed our entire security posture. Their 24/7 monitoring and incident response capabilities give us complete peace of mind.",
    name: "<PERSON>",
    title: "<PERSON><PERSON><PERSON>, CloudFirst",
    avatar: "/seclit-logo-1.png",
    initials: "SR",
  },
  {
    quote:
      "The compliance pre-assessment saved us months of preparation time for our audit. Their expertise in regulatory frameworks is unmatched.",
    name: "David Chen",
    title: "VP Security, DataSecure",
    avatar: "/seclit-logo-1.png",
    initials: "DC",
  },
  {
    quote:
      "Working with SecurityLit has been transformational. Their proactive approach and advanced technology stack prevented multiple security incidents.",
    name: "Emily Parker",
    title: "IT Director, RetailMax",
    avatar: "/seclit-logo-1.png",
    initials: "EP",
  },
];

const Testimonial = () => (
  <section className="py-24 bg-gradient-to-br from-slate-50 via-white to-blue-50/30 relative overflow-hidden">
    {/* Background Elements */}
    <div className="absolute top-0 left-0 w-full h-full bg-grid-slate-100/30 [mask-image:linear-gradient(0deg,white,rgba(255,255,255,0.6))]"></div>
    <div className="absolute top-0 right-0 w-72 h-72 bg-gradient-to-bl from-[var(--color-brand-blue)]/20 to-transparent rounded-full blur-3xl"></div>
    <div className="absolute bottom-0 left-0 w-72 h-72 bg-gradient-to-tr from-[var(--color-brand-light)]/20 to-transparent rounded-full blur-3xl"></div>

    <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
      <div className="text-center space-y-6 mb-20 fade-in-up">
        
        <h2 className="text-4xl font-bold text-[var(--color-brand-navy)] sm:text-5xl lg:text-6xl">
          What Our Clients
          <span className="block bg-gradient-to-r from-[var(--color--blue)] via-[var(--color-blue)] to-[var(--color-brand-blue-alt)] bg-clip-text text-transparent">
            Say About Us
          </span>
        </h2>
        <p className="text-xl text-[var(--foreground-secondary)] max-w-4xl mx-auto leading-relaxed">
          Don't just take our word for it. Here's what industry leaders and security professionals say about working
          with SecurityLit
        </p>
      </div>

      {/* Moving Testimonials Container */}
      <div className="relative">
        {/* First Row - Moving Right */}
        <div className="flex space-x-6 animate-scroll-right mb-8">
          {/* Map through testimonials */}
          {testimonials.map((testimonial, idx) => (
            <div key={`first-${idx}`} className="flex-shrink-0 w-96 bg-white/80 backdrop-blur-sm border border-[var(--color-brand-blue)]/20 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300">
              <div className="flex items-center space-x-1 mb-6">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="h-4 w-4 text-yellow-400 fill-current" />
                ))}
              </div>
              <blockquote className="text-[var(--color-brand-navy)] leading-relaxed mb-6">
                "{testimonial.quote}"
              </blockquote>
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-gradient-to-br from-[var(--color-brand-blue)] to-[var(--color-brand-navy)] rounded-full flex items-center justify-center">
                  <span className="text-white font-bold text-sm">{testimonial.initials}</span>
                </div>
                <div>
                  <div className="font-bold text-[var(--color-brand-navy)]">{testimonial.name}</div>
                  <div className="text-[var(--foreground-secondary)] text-sm">{testimonial.title}</div>
                </div>
              </div>
            </div>
          ))}

          {/* Duplicate testimonials for seamless loop */}
          {testimonials.map((testimonial, idx) => (
            <div key={`duplicate-${idx}`} className="flex-shrink-0 w-96 bg-white/80 backdrop-blur-sm border border-[var(--color-brand-blue)]/20 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300">
              <div className="flex items-center space-x-1 mb-6">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="h-4 w-4 text-yellow-400 fill-current" />
                ))}
              </div>
              <blockquote className="text-[var(--color-brand-navy)] leading-relaxed mb-6">
                "{testimonial.quote}"
              </blockquote>
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-gradient-to-br from-[var(--color-brand-blue)] to-[var(--color-brand-navy)] rounded-full flex items-center justify-center">
                  <span className="text-white font-bold text-sm">{testimonial.initials}</span>
                </div>
                <div>
                  <div className="font-bold text-[var(--color-brand-navy)]">{testimonial.name}</div>
                  <div className="text-[var(--foreground-secondary)] text-sm">{testimonial.title}</div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Trust Indicators */}
     
    </div>

    {/* Custom CSS for animations */}
    <style jsx>{`
      @keyframes scroll-right {
        0% {
          transform: translateX(-100%);
        }
        100% {
          transform: translateX(0%);
        }
      }

      .animate-scroll-right {
        animation: scroll-right 40s linear infinite;
      }

      .animate-scroll-right:hover {
        animation-play-state: paused;
      }

      @media (max-width: 768px) {
        .animate-scroll-right {
          animation: scroll-right 30s linear infinite;
        }
      }

      @media (prefers-reduced-motion: reduce) {
        .animate-scroll-right {
          animation: none;
        }
      }
    `}</style>
  </section>
);

export default Testimonial;
