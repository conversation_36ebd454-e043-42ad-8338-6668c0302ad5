{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/common/components/BreadcrumbNavigation.jsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState } from 'react';\r\nimport Link from 'next/link';\r\nimport { ChevronRight, Home, FileText, Users, Settings, MapPin, Shield, Code, Smartphone, Globe, DollarSign, Building, Phone, Download } from 'lucide-react';\r\nimport { motion } from 'framer-motion';\r\n\r\n/**\r\n * Truncated Text Component for Mobile Breadcrumbs\r\n * Shows truncated text on mobile with click to expand functionality\r\n */\r\nconst TruncatedText = ({ text, maxLength = 25, onExpansionChange }) => {\r\n  const [isExpanded, setIsExpanded] = useState(false);\r\n\r\n  const handleToggle = () => {\r\n    const newExpanded = !isExpanded;\r\n    setIsExpanded(newExpanded);\r\n    if (onExpansionChange) {\r\n      onExpansionChange(newExpanded);\r\n    }\r\n  };\r\n\r\n  if (text.length <= maxLength) {\r\n    return <span>{text}</span>;\r\n  }\r\n\r\n  return (\r\n    <span\r\n      className=\"cursor-pointer\"\r\n      onClick={handleToggle}\r\n      title={isExpanded ? \"Click to collapse\" : \"Click to expand\"}\r\n    >\r\n      <span className=\"sm:hidden\">\r\n        {isExpanded ? text : `${text.substring(0, maxLength)}...`}\r\n      </span>\r\n      <span className=\"hidden sm:inline\">\r\n        {text}\r\n      </span>\r\n    </span>\r\n  );\r\n};\r\n\r\n/**\r\n * Structured Data Component for SEO\r\n */\r\nconst BreadcrumbStructuredData = ({ items }) => {\r\n  const structuredData = {\r\n    \"@context\": \"https://schema.org\",\r\n    \"@type\": \"BreadcrumbList\",\r\n    \"itemListElement\": items.map((item, index) => ({\r\n      \"@type\": \"ListItem\",\r\n      \"position\": index + 1,\r\n      \"name\": item.name,\r\n      \"item\": item.current ? undefined : `https://securitylit.com${item.url}`\r\n    }))\r\n  };\r\n\r\n  return (\r\n    <script\r\n      type=\"application/ld+json\"\r\n      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}\r\n    />\r\n  );\r\n};\r\n\r\n/**\r\n * Universal Breadcrumb Navigation Component with SEO structured data\r\n * Provides clear navigation hierarchy and improves SEO across all pages\r\n */\r\nconst BreadcrumbNavigation = ({ items, className = \"\" }) => {\r\n  const [hasExpandedText, setHasExpandedText] = useState(false);\r\n\r\n  // Check if this is a blog page for conditional padding\r\n  const isBlogPage = items?.some(item =>\r\n    item.url?.includes('/Blogs') ||\r\n    item.iconKey === 'blogs' ||\r\n    className?.includes('blog')\r\n  );\r\n\r\n  return (\r\n    <>\r\n      {/* Structured Data for SEO */}\r\n      <BreadcrumbStructuredData items={items} />\r\n      \r\n      {/* Simple Breadcrumb Navigation */}\r\n      <nav\r\n        className={`bg-transparent py-2 sm:py-3 ${\r\n          isBlogPage\r\n            ? hasExpandedText\r\n              ? 'pb-6 sm:pb-4'\r\n              : 'pb-3 sm:pb-4'\r\n            : 'pb-2 sm:pb-3'\r\n        } ${className}`}\r\n        aria-label=\"Breadcrumb navigation\"\r\n      >\r\n        <div className=\"max-w-7xl mx-auto px-4\">\r\n          <motion.ol\r\n            className={`flex items-center space-x-1 text-xs sm:text-sm ${\r\n              className?.includes('text-white')\r\n                ? 'text-white'\r\n                : className?.includes('text-blue-600')\r\n                ? 'text-blue-600'\r\n                : 'text-gray-600'\r\n            }`}\r\n            initial={{ opacity: 0 }}\r\n            animate={{ opacity: 1 }}\r\n            transition={{ duration: 0.4 }}\r\n          >\r\n            {items.map((item, index) => (\r\n              <motion.li\r\n                key={index}\r\n                className=\"flex items-center\"\r\n                initial={{ opacity: 0 }}\r\n                animate={{ opacity: 1 }}\r\n                transition={{ duration: 0.3, delay: index * 0.1 }}\r\n              >\r\n                {index > 0 && (\r\n                  <ChevronRight\r\n                    className={`w-3 h-3 mx-1 ${\r\n                      className?.includes('text-white')\r\n                        ? 'text-white/70'\r\n                        : className?.includes('text-blue-600')\r\n                        ? 'text-blue-400'\r\n                        : 'text-gray-400'\r\n                    }`}\r\n                    aria-hidden=\"true\"\r\n                  />\r\n                )}\r\n\r\n                {item.current ? (\r\n                  <span\r\n                    className={`font-medium flex items-center gap-1 ${\r\n                      className?.includes('text-white')\r\n                        ? 'text-white'\r\n                        : className?.includes('text-blue-600')\r\n                        ? 'text-blue-600'\r\n                        : 'text-gray-900'\r\n                    }`}\r\n                    aria-current=\"page\"\r\n                    title={item.description}\r\n                  >\r\n                    {item.iconKey === 'home' && (\r\n                      <Home className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-blue-600'\r\n                      }`} />\r\n                    )}\r\n                    {item.iconKey === 'blogs' && (\r\n                      <FileText className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-blue-600'\r\n                      }`} />\r\n                    )}\r\n                    {item.iconKey === 'contact-us' && (\r\n                      <Phone className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-blue-600'\r\n                      }`} />\r\n                    )}\r\n                    <TruncatedText\r\n                      text={item.name}\r\n                      onExpansionChange={setHasExpandedText}\r\n                    />\r\n                  </span>\r\n                ) : (\r\n                  <Link\r\n                    href={item.url}\r\n                    className={`transition-colors duration-200 flex items-center gap-1 hover:underline ${\r\n                      className?.includes('text-white')\r\n                        ? 'text-white hover:text-white/80'\r\n                        : className?.includes('text-blue-600')\r\n                        ? 'text-blue-600 hover:text-blue-800'\r\n                        : 'text-blue-600 hover:text-blue-800'\r\n                    }`}\r\n                    title={item.description}\r\n                  >\r\n                    {item.iconKey === 'home' && (\r\n                      <Home className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-current'\r\n                      }`} />\r\n                    )}\r\n                    {item.iconKey === 'blogs' && (\r\n                      <FileText className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-current'\r\n                      }`} />\r\n                    )}\r\n                    {item.iconKey === 'contact-us' && (\r\n                      <Phone className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-current'\r\n                      }`} />\r\n                    )}\r\n                    <TruncatedText\r\n                      text={item.name}\r\n                      onExpansionChange={setHasExpandedText}\r\n                    />\r\n                  </Link>\r\n                )}\r\n              </motion.li>\r\n            ))}\r\n          </motion.ol>\r\n        </div>\r\n      </nav>\r\n    </>\r\n  );\r\n};\r\n\r\n// Client-side helper function for generating breadcrumb items\r\nexport const generateBreadcrumbs = (pageType, params = {}) => {\r\n  const baseBreadcrumb = {\r\n    name: \"Home\",\r\n    url: \"/\",\r\n    iconKey: \"home\",\r\n    description: \"Return to homepage\"\r\n  };\r\n\r\n  const breadcrumbConfigs = {\r\n    blog: (slug) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Blog\",\r\n        url: \"/Blogs\",\r\n        iconKey: \"blogs\",\r\n        description: \"View all blog posts\"\r\n      },\r\n      {\r\n        name: params.title || \"Blog Post\",\r\n        url: `/Blogs/${slug}`,\r\n        current: true,\r\n        description: `${params.title || 'Blog post'} - Cybersecurity insights and penetration testing guidance`\r\n      }\r\n    ],\r\n    \r\n    service: (service) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Services\",\r\n        url: \"/Services\",\r\n        iconKey: \"services\",\r\n        description: \"View all cybersecurity services\"\r\n      },\r\n      {\r\n        name: params.title || service,\r\n        url: `/Services/${service}`,\r\n        current: true,\r\n        iconKey: service,\r\n        description: `${params.title || service} penetration testing services`\r\n      }\r\n    ],\r\n\r\n    location: (country) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Locations\",\r\n        url: \"/Locations\",\r\n        iconKey: \"locations\",\r\n        description: \"View all locations\"\r\n      },\r\n      {\r\n        name: params.countryName || country,\r\n        url: `/Locations/${country}`,\r\n        current: true,\r\n        description: `${params.countryName || country} cybersecurity services`\r\n      }\r\n    ],\r\n\r\n    company: (page) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Company\",\r\n        url: \"/Company\",\r\n        iconKey: \"company\",\r\n        description: \"Learn about SecurityLit\"\r\n      },\r\n      {\r\n        name: params.title || page,\r\n        url: `/Company/${page}`,\r\n        current: true,\r\n        description: params.description || `${page} information`\r\n      }\r\n    ],\r\n\r\n    'company-size': (size) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Company Size\",\r\n        url: \"/Company-size\",\r\n        iconKey: \"company\",\r\n        description: \"Solutions for different company sizes\"\r\n      },\r\n      {\r\n        name: params.title || size,\r\n        url: `/Company-size/${size}`,\r\n        current: true,\r\n        description: params.description || `${size} cybersecurity solutions`\r\n      }\r\n    ],\r\n\r\n    industry: (industry) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Industries\",\r\n        url: \"/Industries\",\r\n        iconKey: \"company\",\r\n        description: \"Industry-specific cybersecurity solutions\"\r\n      },\r\n      {\r\n        name: params.title || industry,\r\n        url: `/Industries/${industry}`,\r\n        current: true,\r\n        description: params.description || `${industry} cybersecurity solutions`\r\n      }\r\n    ],\r\n\r\n    product: (product) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Product\",\r\n        url: \"/Product\",\r\n        iconKey: \"product\",\r\n        description: \"Explore our cybersecurity products\"\r\n      },\r\n      {\r\n        name: params.title || product,\r\n        url: `/Product/${product}`,\r\n        current: true,\r\n        description: params.description || `${product} product information`\r\n      }\r\n    ],\r\n\r\n    simple: (pageName, url) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: pageName,\r\n        url: url,\r\n        current: true,\r\n        iconKey: params.iconKey,\r\n        description: params.description || `${pageName} page`\r\n      }\r\n    ]\r\n  };\r\n\r\n  return breadcrumbConfigs[pageType] || (() => [baseBreadcrumb]);\r\n};\r\n\r\nexport default BreadcrumbNavigation; "], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;;AAMA;;;CAGC,GACD,MAAM,gBAAgB,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,EAAE,iBAAiB,EAAE;IAChE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,eAAe;QACnB,MAAM,cAAc,CAAC;QACrB,cAAc;QACd,IAAI,mBAAmB;YACrB,kBAAkB;QACpB;IACF;IAEA,IAAI,KAAK,MAAM,IAAI,WAAW;QAC5B,qBAAO,8OAAC;sBAAM;;;;;;IAChB;IAEA,qBACE,8OAAC;QACC,WAAU;QACV,SAAS;QACT,OAAO,aAAa,sBAAsB;;0BAE1C,8OAAC;gBAAK,WAAU;0BACb,aAAa,OAAO,GAAG,KAAK,SAAS,CAAC,GAAG,WAAW,GAAG,CAAC;;;;;;0BAE3D,8OAAC;gBAAK,WAAU;0BACb;;;;;;;;;;;;AAIT;AAEA;;CAEC,GACD,MAAM,2BAA2B,CAAC,EAAE,KAAK,EAAE;IACzC,MAAM,iBAAiB;QACrB,YAAY;QACZ,SAAS;QACT,mBAAmB,MAAM,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;gBAC7C,SAAS;gBACT,YAAY,QAAQ;gBACpB,QAAQ,KAAK,IAAI;gBACjB,QAAQ,KAAK,OAAO,GAAG,YAAY,CAAC,uBAAuB,EAAE,KAAK,GAAG,EAAE;YACzE,CAAC;IACH;IAEA,qBACE,8OAAC;QACC,MAAK;QACL,yBAAyB;YAAE,QAAQ,KAAK,SAAS,CAAC;QAAgB;;;;;;AAGxE;AAEA;;;CAGC,GACD,MAAM,uBAAuB,CAAC,EAAE,KAAK,EAAE,YAAY,EAAE,EAAE;IACrD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,uDAAuD;IACvD,MAAM,aAAa,OAAO,KAAK,CAAA,OAC7B,KAAK,GAAG,EAAE,SAAS,aACnB,KAAK,OAAO,KAAK,WACjB,WAAW,SAAS;IAGtB,qBACE;;0BAEE,8OAAC;gBAAyB,OAAO;;;;;;0BAGjC,8OAAC;gBACC,WAAW,CAAC,4BAA4B,EACtC,aACI,kBACE,iBACA,iBACF,eACL,CAAC,EAAE,WAAW;gBACf,cAAW;0BAEX,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;wBACR,WAAW,CAAC,+CAA+C,EACzD,WAAW,SAAS,gBAChB,eACA,WAAW,SAAS,mBACpB,kBACA,iBACJ;wBACF,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,YAAY;4BAAE,UAAU;wBAAI;kCAE3B,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;gCAER,WAAU;gCACV,SAAS;oCAAE,SAAS;gCAAE;gCACtB,SAAS;oCAAE,SAAS;gCAAE;gCACtB,YAAY;oCAAE,UAAU;oCAAK,OAAO,QAAQ;gCAAI;;oCAE/C,QAAQ,mBACP,8OAAC,sNAAA,CAAA,eAAY;wCACX,WAAW,CAAC,aAAa,EACvB,WAAW,SAAS,gBAChB,kBACA,WAAW,SAAS,mBACpB,kBACA,iBACJ;wCACF,eAAY;;;;;;oCAIf,KAAK,OAAO,iBACX,8OAAC;wCACC,WAAW,CAAC,oCAAoC,EAC9C,WAAW,SAAS,gBAChB,eACA,WAAW,SAAS,mBACpB,kBACA,iBACJ;wCACF,gBAAa;wCACb,OAAO,KAAK,WAAW;;4CAEtB,KAAK,OAAO,KAAK,wBAChB,8OAAC,mMAAA,CAAA,OAAI;gDAAC,WAAW,CAAC,QAAQ,EACxB,WAAW,SAAS,gBAChB,eACA,WAAW,SAAS,mBACpB,kBACA,iBACJ;;;;;;4CAEH,KAAK,OAAO,KAAK,yBAChB,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAW,CAAC,QAAQ,EAC5B,WAAW,SAAS,gBAChB,eACA,WAAW,SAAS,mBACpB,kBACA,iBACJ;;;;;;4CAEH,KAAK,OAAO,KAAK,8BAChB,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAW,CAAC,QAAQ,EACzB,WAAW,SAAS,gBAChB,eACA,WAAW,SAAS,mBACpB,kBACA,iBACJ;;;;;;0DAEJ,8OAAC;gDACC,MAAM,KAAK,IAAI;gDACf,mBAAmB;;;;;;;;;;;iGAIvB,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAM,KAAK,GAAG;wCACd,WAAW,CAAC,uEAAuE,EACjF,WAAW,SAAS,gBAChB,mCACA,WAAW,SAAS,mBACpB,sCACA,qCACJ;wCACF,OAAO,KAAK,WAAW;;4CAEtB,KAAK,OAAO,KAAK,wBAChB,8OAAC,mMAAA,CAAA,OAAI;gDAAC,WAAW,CAAC,QAAQ,EACxB,WAAW,SAAS,gBAChB,eACA,WAAW,SAAS,mBACpB,kBACA,gBACJ;;;;;;4CAEH,KAAK,OAAO,KAAK,yBAChB,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAW,CAAC,QAAQ,EAC5B,WAAW,SAAS,gBAChB,eACA,WAAW,SAAS,mBACpB,kBACA,gBACJ;;;;;;4CAEH,KAAK,OAAO,KAAK,8BAChB,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAW,CAAC,QAAQ,EACzB,WAAW,SAAS,gBAChB,eACA,WAAW,SAAS,mBACpB,kBACA,gBACJ;;;;;;0DAEJ,8OAAC;gDACC,MAAM,KAAK,IAAI;gDACf,mBAAmB;;;;;;;;;;;;;+BAxGpB;;;;;;;;;;;;;;;;;;;;;;AAmHrB;AAGO,MAAM,sBAAsB,CAAC,UAAU,SAAS,CAAC,CAAC;IACvD,MAAM,iBAAiB;QACrB,MAAM;QACN,KAAK;QACL,SAAS;QACT,aAAa;IACf;IAEA,MAAM,oBAAoB;QACxB,MAAM,CAAC,OAAS;gBACd;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,CAAC,OAAO,EAAE,MAAM;oBACrB,SAAS;oBACT,aAAa,GAAG,OAAO,KAAK,IAAI,YAAY,0DAA0D,CAAC;gBACzG;aACD;QAED,SAAS,CAAC,UAAY;gBACpB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,CAAC,UAAU,EAAE,SAAS;oBAC3B,SAAS;oBACT,SAAS;oBACT,aAAa,GAAG,OAAO,KAAK,IAAI,QAAQ,6BAA6B,CAAC;gBACxE;aACD;QAED,UAAU,CAAC,UAAY;gBACrB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,WAAW,IAAI;oBAC5B,KAAK,CAAC,WAAW,EAAE,SAAS;oBAC5B,SAAS;oBACT,aAAa,GAAG,OAAO,WAAW,IAAI,QAAQ,uBAAuB,CAAC;gBACxE;aACD;QAED,SAAS,CAAC,OAAS;gBACjB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,CAAC,SAAS,EAAE,MAAM;oBACvB,SAAS;oBACT,aAAa,OAAO,WAAW,IAAI,GAAG,KAAK,YAAY,CAAC;gBAC1D;aACD;QAED,gBAAgB,CAAC,OAAS;gBACxB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,CAAC,cAAc,EAAE,MAAM;oBAC5B,SAAS;oBACT,aAAa,OAAO,WAAW,IAAI,GAAG,KAAK,wBAAwB,CAAC;gBACtE;aACD;QAED,UAAU,CAAC,WAAa;gBACtB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,CAAC,YAAY,EAAE,UAAU;oBAC9B,SAAS;oBACT,aAAa,OAAO,WAAW,IAAI,GAAG,SAAS,wBAAwB,CAAC;gBAC1E;aACD;QAED,SAAS,CAAC,UAAY;gBACpB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,CAAC,SAAS,EAAE,SAAS;oBAC1B,SAAS;oBACT,aAAa,OAAO,WAAW,IAAI,GAAG,QAAQ,oBAAoB,CAAC;gBACrE;aACD;QAED,QAAQ,CAAC,UAAU,MAAQ;gBACzB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,SAAS,OAAO,OAAO;oBACvB,aAAa,OAAO,WAAW,IAAI,GAAG,SAAS,KAAK,CAAC;gBACvD;aACD;IACH;IAEA,OAAO,iBAAiB,CAAC,SAAS,IAAI,CAAC,IAAM;YAAC;SAAe;AAC/D;uCAEe", "debugId": null}}, {"offset": {"line": 386, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/training/components/HeroBento.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport { Users, Star, Clock, Award } from 'lucide-react';\r\nimport BreadcrumbNavigation from '../../common/components/BreadcrumbNavigation';\r\n\r\nexport default function HeroBento({ content }) {\r\n  // Default content for backward compatibility\r\n  const defaultContent = {\r\n    breadcrumbItems: [\r\n      {\r\n        name: \"Home\",\r\n        url: \"/\",\r\n        iconKey: \"home\",\r\n        description: \"Return to homepage\"\r\n      },\r\n      {\r\n        name: \"Training\",\r\n        url: \"/training\",\r\n        current: true,\r\n        iconKey: \"graduation-cap\",\r\n        description: \"Explore SecurityLit's cybersecurity training programs\"\r\n      }\r\n    ],\r\n    title: \"Elite Security Training\",\r\n    subtitle: \"Launch Your Cyber Security Career\",\r\n    tagline: \"Free and Premium Pathways\",\r\n    description: \"Dive into the world of penetration testing with our refined program. Designed for aspiring security professionals with complete pentesting skills and real-time live projects.\",\r\n    keyBenefits: [\r\n      \"Complete pentesting skills with real-time live projects\",\r\n      \"Latest tools and topics in cybersecurity\",\r\n      \"Free and premium pathways available\"\r\n    ],\r\n    buttons: [\r\n      {\r\n        text: \"Start Free Training\",\r\n        href: \"#form\",\r\n        primary: true\r\n      },\r\n      {\r\n        text: \"Upgrade to Premium\",\r\n        href: \"#premium\",\r\n        primary: false\r\n      }\r\n    ],\r\n    heroImage: \"/images/p1s1.png\"\r\n  };\r\n\r\n  const heroContent = content || defaultContent;\r\n  const { breadcrumbItems, title, subtitle, secondaryImage } = heroContent;\r\n\r\n  const trustStats = [\r\n    { value: \"500+\", label: \"Students Trained\", icon: Users },\r\n    { value: \"98%\", label: \"Success Rate\", icon: Star },\r\n    { value: \"83\", label: \"Total Lessons\", icon: Clock },\r\n    { value: \"11\", label: \"Course Sections\", icon: Award }\r\n  ];\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-white\">\r\n      <div className=\"flex flex-col lg:flex-row min-h-screen relative\">\r\n\r\n        {/* Left Section - Optimized Content with Rounded Corner */}\r\n        <div className=\"w-full lg:w-1/2 bg-gradient-to-br from-[var(--color-dark-blue)] to-[var(--color-dark-blue-hover)] relative overflow-hidden lg:rounded-br-[100px]\">\r\n          {/* Background Pattern */}\r\n          <div className=\"absolute inset-0 bg-cover bg-center bg-no-repeat opacity-10\"\r\n               style={{ backgroundImage: 'url(/images/hexagon.svg)' }}>\r\n          </div>\r\n          <div className=\"absolute inset-0 bg-[var(--color-dark-blue)]/90 lg:rounded-br-[100px]\"></div>\r\n          \r\n          <div className=\"relative z-10 flex justify-center px-4 sm:px-6 lg:px-12 py-8 sm:py-12 lg:pt-20 lg:pb-8 min-h-screen lg:h-full\">\r\n            <div className=\"max-w-lg w-full flex flex-col justify-center\">\r\n              {/* Breadcrumb */}\r\n              <div className=\"mb-4 mt-2 lg:mt-0\">\r\n                <BreadcrumbNavigation items={breadcrumbItems} className=\"text-white\" />\r\n              </div>\r\n\r\n              {/* Main Heading */}\r\n              <h1 className=\"text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-4 leading-tight font-poppins\">\r\n                {title.includes('Security') ? (\r\n                  <>\r\n                    Elite Security\r\n                    <span className=\"block bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] bg-clip-text text-transparent\">\r\n                      Training\r\n                    </span>\r\n                  </>\r\n                ) : (\r\n                  title\r\n                )}\r\n              </h1>\r\n\r\n              {/* Subtitle */}\r\n              {subtitle && (\r\n                <h2 className=\"text-lg sm:text-xl lg:text-2xl font-bold text-white mb-6 leading-tight font-poppins\">\r\n                  {subtitle}\r\n                </h2>\r\n              )}\r\n\r\n              {/* Description */}\r\n              <p className=\"text-sm sm:text-base text-white/90 mb-8 leading-relaxed font-roboto\">\r\n                Dive Into The World Of Penetration Testing With Our Refined Program\r\n              </p>\r\n\r\n              {/* CTA Buttons */}\r\n              <div className=\"flex flex-col gap-3\">\r\n                <a\r\n                  href=\"#form\"\r\n                  className=\"bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] text-white px-6 py-3 rounded-lg font-semibold text-base transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center gap-2 font-poppins\"\r\n                >\r\n                  Enroll Now\r\n                </a>\r\n\r\n                <a\r\n                  href=\"/CyberSecTraining\"\r\n                  className=\"bg-white/10 backdrop-blur-sm text-white border-2 border-white/20 hover:bg-white/20 px-6 py-3 rounded-lg font-semibold text-base transition-all duration-300 flex items-center justify-center gap-2 font-poppins\"\r\n                >\r\n                  Program Details\r\n                </a>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n\r\n\r\n        {/* Right Section - Enhanced Visual Focus */}\r\n        <div className=\"w-full lg:w-1/2 bg-white\">\r\n          <div className=\"max-w-2xl mx-auto px-4 sm:px-6 lg:px-12 py-8 sm:py-12 lg:pt-20 lg:pb-8 min-h-screen lg:h-full flex flex-col justify-center\">\r\n            \r\n            {/* Enhanced Hero Visual */}\r\n            <div className=\"relative mb-6\">\r\n              <div className=\"relative bg-gradient-to-br from-[var(--color-blue)]/10 to-[var(--color-blue-secondary)]/10 rounded-2xl p-4 sm:p-6 lg:p-8 border-2 border-[var(--color-blue)]/20\">\r\n                <div className=\"absolute inset-0 bg-gradient-to-br from-[var(--color-blue)]/5 to-[var(--color-blue-secondary)]/5 rounded-2xl\"></div>\r\n                \r\n                <div className=\"relative z-10 text-center\">\r\n                  <div className=\"w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-xl flex items-center justify-center mx-auto mb-4 shadow-lg\">\r\n                    <img\r\n                      src=\"/SecurityLit_Icon_White.png\"\r\n                      alt=\"SecurityLit Logo\"\r\n                      className=\"w-8 h-8 sm:w-12 sm:h-12 object-contain\"\r\n                    />\r\n                  </div>\r\n\r\n                  <h3 className=\"text-lg sm:text-xl font-bold text-[var(--color-dark-blue)] mb-2\">\r\n                    SecurityLit Presents\r\n                  </h3>\r\n                  <p className=\"text-[var(--foreground-secondary)] text-sm sm:text-base\">\r\n                    Professional cybersecurity training designed by industry experts\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Trust Indicators */}\r\n            <div className=\"grid grid-cols-2 gap-3 sm:gap-4 mb-6\">\r\n              {trustStats.map((stat, index) => (\r\n                <div key={index} className=\"text-center\">\r\n                  <div className=\"w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-br from-[var(--color-blue)]/10 to-[var(--color-blue-secondary)]/10 rounded-lg flex items-center justify-center mx-auto mb-2\">\r\n                    <stat.icon className=\"w-4 h-4 sm:w-5 sm:h-5 text-[var(--color-blue)]\" />\r\n                  </div>\r\n                  <div className=\"text-xl sm:text-2xl lg:text-3xl font-bold text-[var(--color-dark-blue)] mb-1 font-poppins\">\r\n                    {stat.value}\r\n                  </div>\r\n                  <div className=\"text-[var(--foreground-secondary)] text-xs font-medium font-roboto\">\r\n                    {stat.label}\r\n                  </div>\r\n                </div>\r\n              ))}\r\n            </div>\r\n\r\n\r\n\r\n            {secondaryImage && (\r\n              <div className=\"text-center mt-6\">\r\n                <img\r\n                  src={secondaryImage}\r\n                  alt=\"Cybersecurity Expert\"\r\n                  className=\"w-full max-w-md mx-auto rounded-lg\"\r\n                />\r\n              </div>\r\n            )}\r\n\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAMe,SAAS,UAAU,EAAE,OAAO,EAAE;IAC3C,6CAA6C;IAC7C,MAAM,iBAAiB;QACrB,iBAAiB;YACf;gBACE,MAAM;gBACN,KAAK;gBACL,SAAS;gBACT,aAAa;YACf;YACA;gBACE,MAAM;gBACN,KAAK;gBACL,SAAS;gBACT,SAAS;gBACT,aAAa;YACf;SACD;QACD,OAAO;QACP,UAAU;QACV,SAAS;QACT,aAAa;QACb,aAAa;YACX;YACA;YACA;SACD;QACD,SAAS;YACP;gBACE,MAAM;gBACN,MAAM;gBACN,SAAS;YACX;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,SAAS;YACX;SACD;QACD,WAAW;IACb;IAEA,MAAM,cAAc,WAAW;IAC/B,MAAM,EAAE,eAAe,EAAE,KAAK,EAAE,QAAQ,EAAE,cAAc,EAAE,GAAG;IAE7D,MAAM,aAAa;QACjB;YAAE,OAAO;YAAQ,OAAO;YAAoB,MAAM,oMAAA,CAAA,QAAK;QAAC;QACxD;YAAE,OAAO;YAAO,OAAO;YAAgB,MAAM,kMAAA,CAAA,OAAI;QAAC;QAClD;YAAE,OAAO;YAAM,OAAO;YAAiB,MAAM,oMAAA,CAAA,QAAK;QAAC;QACnD;YAAE,OAAO;YAAM,OAAO;YAAmB,MAAM,oMAAA,CAAA,QAAK;QAAC;KACtD;IAED,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAGb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;4BACV,OAAO;gCAAE,iBAAiB;4BAA2B;;;;;;sCAE1D,8OAAC;4BAAI,WAAU;;;;;;sCAEf,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,2JAAA,CAAA,UAAoB;4CAAC,OAAO;4CAAiB,WAAU;;;;;;;;;;;kDAI1D,8OAAC;wCAAG,WAAU;kDACX,MAAM,QAAQ,CAAC,4BACd;;gDAAE;8DAEA,8OAAC;oDAAK,WAAU;8DAAiH;;;;;;;2DAKnI;;;;;;oCAKH,0BACC,8OAAC;wCAAG,WAAU;kDACX;;;;;;kDAKL,8OAAC;wCAAE,WAAU;kDAAsE;;;;;;kDAKnF,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,MAAK;gDACL,WAAU;0DACX;;;;;;0DAID,8OAAC;gDACC,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAWT,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAGb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDAEf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDACC,KAAI;wDACJ,KAAI;wDACJ,WAAU;;;;;;;;;;;8DAId,8OAAC;oDAAG,WAAU;8DAAkE;;;;;;8DAGhF,8OAAC;oDAAE,WAAU;8DAA0D;;;;;;;;;;;;;;;;;;;;;;;0CAQ7E,8OAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,MAAM,sBACrB,8OAAC;wCAAgB,WAAU;;0DACzB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;;;;;;0DAEvB,8OAAC;gDAAI,WAAU;0DACZ,KAAK,KAAK;;;;;;0DAEb,8OAAC;gDAAI,WAAU;0DACZ,KAAK,KAAK;;;;;;;uCARL;;;;;;;;;;4BAgBb,gCACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,KAAK;oCACL,KAAI;oCACJ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU5B", "debugId": null}}, {"offset": {"line": 749, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/training/components/CurriculumHighlights.jsx"], "sourcesContent": ["\"use client\";\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { BookOpen, Brain, Code, Compass, Target, Lock, Zap, Users, Shield, ArrowRight, Star, Clock, Award } from 'lucide-react';\nimport { PrimaryButton } from '../../common/buttons/BrandButtons';\n\nconst curriculumItems = [\n  {\n    icon: BookOpen,\n    title: \"Advanced Assessment\",\n    description: \"Master penetration testing methodologies and tools\",\n    color: \"from-[var(--color-blue)] to-[var(--color-blue-secondary)]\",\n    delay: 0.1,\n    size: \"large\",\n    position: \"top-left\",\n    features: [\"OWASP Top 10\", \"Vulnerability Assessment\", \"Report Writing\"]\n  },\n  {\n    icon: Brain,\n    title: \"API Security\",\n    description: \"Learn to secure REST APIs and GraphQL endpoints\",\n    color: \"from-[var(--color-dark-blue)] to-[var(--color-dark-blue-hover)]\",\n    delay: 0.2,\n    size: \"medium\",\n    position: \"top-right\",\n    features: [\"Authentication\", \"Authorization\", \"Rate Limiting\"]\n  },\n  {\n    icon: Code,\n    title: \"Hands-on Labs\",\n    description: \"Real-world experience with live environments\",\n    color: \"from-[var(--color-blue)] to-[var(--color-blue-secondary)]\",\n    delay: 0.3,\n    size: \"medium\",\n    position: \"middle-left\",\n    features: [\"Live Environments\", \"Real Scenarios\", \"Practice Labs\"]\n  },\n  {\n    icon: Compass,\n    title: \"Navigation & Recon\",\n    description: \"Advanced reconnaissance and information gathering\",\n    color: \"from-[var(--color-blue)] to-[var(--color-blue-secondary)]\",\n    delay: 0.4,\n    size: \"small\",\n    position: \"middle-center\",\n    features: [\"OSINT\", \"Network Mapping\", \"Footprinting\"]\n  },\n  {\n    icon: Target,\n    title: \"Role-based Training\",\n    description: \"Specialized paths for different career goals\",\n    color: \"from-[var(--color-dark-blue)] to-[var(--color-dark-blue-subtle)]\",\n    delay: 0.5,\n    size: \"large\",\n    position: \"middle-right\",\n    features: [\"Penetration Tester\", \"Security Analyst\", \"Security Engineer\"]\n  },\n  {\n    icon: Lock,\n    title: \"Authentication & Auth\",\n    description: \"Deep dive into modern authentication systems\",\n    color: \"from-[var(--color-blue)] to-[var(--color-blue-secondary)]\",\n    delay: 0.6,\n    size: \"medium\",\n    position: \"bottom-left\",\n    features: [\"OAuth 2.0\", \"JWT\", \"Multi-Factor Auth\"]\n  }\n];\n\nexport default function CurriculumHighlights() {\n  return (\n    <section className=\"py-12 sm:py-16 lg:py-24 bg-white relative overflow-hidden\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0\">\n        <div className=\"absolute top-20 left-10 w-72 h-72 bg-[var(--color-blue)]/5 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob\"></div>\n        <div className=\"absolute top-40 right-10 w-72 h-72 bg-[var(--color-yellow)]/5 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-2000\"></div>\n        <div className=\"absolute -bottom-8 left-20 w-72 h-72 bg-[var(--color-blue-secondary)]/5 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-4000\"></div>\n      </div>\n\n      <div className=\"relative z-10 container mx-auto px-4 max-w-7xl\">\n        {/* Header */}\n        <motion.div \n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-20\"\n        >\n          <motion.div \n            initial={{ opacity: 0, scale: 0.8 }}\n            whileInView={{ opacity: 1, scale: 1 }}\n            transition={{ delay: 0.2, duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"inline-flex items-center bg-gradient-to-r from-[var(--color-blue)]/10 to-[var(--color-blue-secondary)]/10 px-6 py-3 rounded-full mb-8 border border-[var(--color-blue)]/20\"\n          >\n            <Zap className=\"w-5 h-5 text-[var(--color-blue)] mr-3\" />\n            <span className=\"text-sm font-semibold text-[var(--color-blue)]\">Cybersecurity Training</span>\n          </motion.div>\n          \n          <motion.h2 \n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.4, duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"text-2xl sm:text-3xl lg:text-4xl xl:text-5xl 2xl:text-6xl font-bold text-[var(--color-dark-blue)] mb-8 leading-tight\"\n          >\n            Explore Our\n            <span className=\"block bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] bg-clip-text text-transparent\">\n              Comprehensive Curriculum\n            </span>\n          </motion.h2>\n          \n          <motion.p \n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.6, duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"text-base sm:text-lg lg:text-xl text-[var(--foreground-secondary)] max-w-4xl mx-auto leading-relaxed px-4\"\n          >\n            Dive into essential areas such as web application security, network penetration testing, cloud security, API security,\n            and ethical hacking fundamentals.\n          </motion.p>\n        </motion.div>\n\n        {/* Professional Bento Grid - Fixed Layout */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-12 gap-4 sm:gap-6 mb-12 sm:mb-16 lg:mb-20\" style={{ gridTemplateRows: 'repeat(3, minmax(240px, auto))' }}>\n          {/* Large Card - Advanced Assessment */}\n            <motion.div\n            initial={{ opacity: 0, scale: 0.9 }}\n            whileInView={{ opacity: 1, scale: 1 }}\n            transition={{ delay: 0.1, duration: 0.8 }}\n              viewport={{ once: true }}\n              whileHover={{ \n                y: -8,\n                scale: 1.02,\n              transition: { duration: 0.3 }\n            }}\n            className=\"col-span-1 lg:col-span-8 lg:row-span-2 group relative\"\n          >\n            <div className=\"relative h-full bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-3xl p-6 lg:p-8 overflow-hidden\">\n              {/* Background Pattern */}\n              <div className=\"absolute inset-0 bg-[url('/images/hexagon.svg')] bg-cover opacity-10\"></div>\n              <div className=\"absolute inset-0 bg-gradient-to-br from-[var(--color-blue)]/90 to-[var(--color-blue-secondary)]/90\"></div>\n              \n              <div className=\"relative z-10 h-full flex flex-col justify-between\">\n                <div className=\"flex-1\">\n                  <div className=\"flex items-start gap-4 mb-6\">\n                    <div className=\"w-14 h-14 lg:w-16 lg:h-16 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center flex-shrink-0 shadow-lg\">\n                      <BookOpen className=\"w-7 h-7 lg:w-8 lg:h-8 text-white\" />\n                    </div>\n                    <div className=\"flex-1 min-w-0\">\n                      <h3 className=\"text-2xl lg:text-3xl font-bold text-white drop-shadow mb-3 leading-tight\">Advanced Assessment</h3>\n                      <p className=\"text-white/90 text-base lg:text-lg leading-relaxed\">Master penetration testing methodologies and tools</p>\n                    </div>\n                  </div>\n                  \n                  {/* Image and Features Grid */}\n                  <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6\">\n                    {/* Features */}\n                    <div className=\"space-y-4\">\n                      {curriculumItems[0].features.map((feature, index) => (\n                        <div key={index} className=\"bg-white/15 backdrop-blur-sm rounded-2xl p-4 lg:p-5 border border-white/25 shadow-lg\">\n                          <div className=\"text-white font-semibold text-sm lg:text-base mb-2 leading-tight\">{feature}</div>\n                          <div className=\"text-white/70 text-sm leading-relaxed\">\n                            {feature === \"OWASP Top 10\" && \"Industry-standard web application security risks\"}\n                            {feature === \"Vulnerability Assessment\" && \"Systematic identification and analysis\"}\n                            {feature === \"Report Writing\" && \"Professional documentation and recommendations\"}\n                          </div>\n                        </div>\n                      ))}\n                    </div>\n                    \n                    {/* Image */}\n                    <div className=\"relative\">\n                      <div className=\"relative h-full min-h-[140px] lg:min-h-[180px] bg-white/15 backdrop-blur-sm rounded-2xl overflow-hidden border border-white/25 shadow-lg\">\n                        <div className=\"absolute inset-0 bg-gradient-to-br from-white/5 to-white/10\"></div>\n                        <div className=\"relative z-10 h-full flex items-center justify-center p-4\">\n                          <img \n                            src=\"/images/about-us-image-3.jpg\" \n                            alt=\"Cybersecurity Assessment\" \n                            className=\"w-full h-full object-cover rounded-xl\"\n                          />\n                          <div className=\"absolute inset-0 bg-gradient-to-br from-[var(--color-blue)]/20 to-[var(--color-blue-secondary)]/20 rounded-xl\"></div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n                \n                <div className=\"flex items-center justify-between pt-6 border-t border-white/20\">\n                  <div className=\"flex items-center gap-4 text-white/90\">\n                    <div className=\"flex items-center gap-2 bg-white/10 px-3 py-2 rounded-xl\">\n                      <Clock className=\"w-4 h-4\" />\n                      <span className=\"text-sm font-medium\">40+ hours</span>\n                    </div>\n                    <div className=\"flex items-center gap-2 bg-white/10 px-3 py-2 rounded-xl\">\n                      <Star className=\"w-4 h-4\" />\n                      <span className=\"text-sm font-medium\">Advanced</span>\n                    </div>\n                  </div>\n                  <ArrowRight className=\"w-6 h-6 text-white group-hover:translate-x-2 transition-transform\" />\n                </div>\n              </div>\n            </div>\n          </motion.div>\n\n          {/* Medium Card - API Security */}\n                <motion.div \n            initial={{ opacity: 0, scale: 0.9 }}\n            whileInView={{ opacity: 1, scale: 1 }}\n            transition={{ delay: 0.2, duration: 0.8 }}\n            viewport={{ once: true }}\n            whileHover={{ \n              y: -8,\n              scale: 1.02,\n              transition: { duration: 0.3 }\n            }}\n            className=\"col-span-12 lg:col-span-4 row-span-1 group relative\"\n          >\n            <div className=\"relative h-full bg-gradient-to-br from-[var(--color-dark-blue)] to-[var(--color-dark-blue-hover)] rounded-3xl p-6 overflow-hidden\">\n              <div className=\"absolute inset-0 bg-[url('/images/hexagon.svg')] bg-cover opacity-5\"></div>\n              <div className=\"absolute inset-0 bg-gradient-to-br from-[var(--color-dark-blue)]/95 to-[var(--color-dark-blue-hover)]/95\"></div>\n              \n              <div className=\"relative z-10 h-full flex flex-col justify-between\">\n                <div className=\"flex-1\">\n                  <div className=\"w-12 h-12 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center mb-4 shadow-lg\">\n                    <Brain className=\"w-6 h-6 text-white\" />\n                  </div>\n                  <h3 className=\"text-xl font-bold text-white mb-3 leading-tight\">API Security</h3>\n                  <p className=\"text-white/80 text-sm mb-4 leading-relaxed\">Learn to secure REST APIs and GraphQL endpoints</p>\n                  \n                  <div className=\"space-y-3\">\n                    {curriculumItems[1].features.map((feature, index) => (\n                      <div key={index} className=\"flex items-center gap-3 bg-white/10 rounded-xl p-3\">\n                        <div className=\"w-2 h-2 bg-[var(--color-yellow)] rounded-full flex-shrink-0\"></div>\n                        <span className=\"text-white/90 text-sm font-medium leading-tight\">{feature}</span>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n                \n                <ArrowRight className=\"w-5 h-5 text-white/60 group-hover:translate-x-1 transition-transform mt-4\" />\n              </div>\n            </div>\n                </motion.div>\n\n          {/* Medium Card - Hands-on Labs */}\n          <motion.div\n            initial={{ opacity: 0, scale: 0.9 }}\n            whileInView={{ opacity: 1, scale: 1 }}\n            transition={{ delay: 0.3, duration: 0.8 }}\n            viewport={{ once: true }}\n            whileHover={{ \n              y: -8,\n              scale: 1.02,\n              transition: { duration: 0.3 }\n            }}\n            className=\"col-span-12 lg:col-span-4 row-span-1 group relative\"\n          >\n            <div className=\"relative h-full bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-3xl p-6 overflow-hidden\">\n              <div className=\"absolute inset-0 bg-[url('/images/hexagon.svg')] bg-cover opacity-5\"></div>\n              <div className=\"absolute inset-0 bg-gradient-to-br from-[var(--color-blue)]/95 to-[var(--color-blue-secondary)]/95\"></div>\n              \n              <div className=\"relative z-10 h-full flex flex-col justify-between\">\n                <div className=\"flex-1\">\n                  <div className=\"w-12 h-12 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center mb-4 shadow-lg\">\n                    <Code className=\"w-6 h-6 text-white\" />\n                  </div>\n                  <h3 className=\"text-xl font-bold text-white mb-3 leading-tight\">Hands-on Labs</h3>\n                  <p className=\"text-white/80 text-sm mb-4 leading-relaxed\">Real-world experience with live environments</p>\n                  \n                  <div className=\"space-y-3\">\n                    {curriculumItems[2].features.map((feature, index) => (\n                      <div key={index} className=\"flex items-center gap-3 bg-white/10 rounded-xl p-3\">\n                        <div className=\"w-2 h-2 bg-white rounded-full flex-shrink-0\"></div>\n                        <span className=\"text-white/90 text-sm font-medium leading-tight\">{feature}</span>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n                \n                <ArrowRight className=\"w-5 h-5 text-white/60 group-hover:translate-x-1 transition-transform mt-4\" />\n              </div>\n            </div>\n          </motion.div>\n\n          {/* Small Card - Navigation & Recon */}\n          <motion.div\n            initial={{ opacity: 0, scale: 0.9 }}\n            whileInView={{ opacity: 1, scale: 1 }}\n            transition={{ delay: 0.4, duration: 0.8 }}\n            viewport={{ once: true }}\n            whileHover={{ \n              y: -8,\n              scale: 1.02,\n              transition: { duration: 0.3 }\n            }}\n            className=\"col-span-12 lg:col-span-4 row-span-1 group relative\"\n          >\n            <div className=\"relative h-full bg-white rounded-3xl p-6 shadow-[0_8px_30px_rgb(0,0,0,0.12)] hover:shadow-[0_20px_40px_rgb(0,0,0,0.15)] transition-all duration-300 border border-gray-100\">\n              <div className=\"h-full flex flex-col justify-between\">\n                <div className=\"flex-1\">\n                  <div className=\"w-12 h-12 bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-2xl flex items-center justify-center mb-4 shadow-lg\">\n                    <Compass className=\"w-6 h-6 text-white\" />\n                  </div>\n                  <h3 className=\"text-xl font-bold text-[var(--color-dark-blue)] mb-3 leading-tight\">Navigation & Recon</h3>\n                  <p className=\"text-[var(--foreground-secondary)] text-sm leading-relaxed\">Advanced reconnaissance and information gathering</p>\n                </div>\n                \n                <ArrowRight className=\"w-5 h-5 text-[var(--color-blue)] group-hover:translate-x-1 transition-transform mt-4\" />\n              </div>\n              </div>\n            </motion.div>\n\n          {/* Large Card - Role-based Training */}\n          <motion.div\n            initial={{ opacity: 0, scale: 0.9 }}\n            whileInView={{ opacity: 1, scale: 1 }}\n            transition={{ delay: 0.5, duration: 0.8 }}\n            viewport={{ once: true }}\n            whileHover={{ \n              y: -8,\n              scale: 1.02,\n              transition: { duration: 0.3 }\n            }}\n            className=\"col-span-12 lg:col-span-8 row-span-2 group relative\"\n          >\n            <div className=\"relative h-full bg-gradient-to-br from-[var(--color-dark-blue)] to-[var(--color-dark-blue-subtle)] rounded-3xl p-6 lg:p-8 overflow-hidden\">\n              <div className=\"absolute inset-0 bg-[url('/images/hexagon.svg')] bg-cover opacity-10\"></div>\n              <div className=\"absolute inset-0 bg-gradient-to-br from-[var(--color-dark-blue)]/90 to-[var(--color-dark-blue-subtle)]/90\"></div>\n              \n              <div className=\"relative z-10 h-full flex flex-col justify-between\">\n                <div className=\"flex-1\">\n                  <div className=\"flex items-start gap-4 mb-6\">\n                    <div className=\"w-14 h-14 lg:w-16 lg:h-16 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center flex-shrink-0 shadow-lg\">\n                      <Target className=\"w-7 h-7 lg:w-8 lg:h-8 text-white\" />\n                    </div>\n                    <div className=\"flex-1 min-w-0\">\n                      <h3 className=\"text-2xl lg:text-3xl font-bold text-white drop-shadow mb-3 leading-tight\">Role-based Training</h3>\n                      <p className=\"text-white/90 text-base lg:text-lg leading-relaxed\">Specialized paths for different career goals</p>\n                    </div>\n                  </div>\n                  \n                  {/* Image and Features Grid */}\n                  <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6\">\n                    {/* Features */}\n                    <div className=\"space-y-4\">\n                      {curriculumItems[4].features.map((feature, index) => (\n                        <div key={index} className=\"bg-white/15 backdrop-blur-sm rounded-2xl p-4 lg:p-5 border border-white/25 shadow-lg\">\n                          <div className=\"text-white font-semibold text-sm lg:text-base mb-2 leading-tight\">{feature}</div>\n                          <div className=\"text-white/70 text-sm leading-relaxed\">\n                            {feature === \"Penetration Tester\" && \"Ethical hacking and security testing\"}\n                            {feature === \"Security Analyst\" && \"Threat detection and incident response\"}\n                            {feature === \"Security Engineer\" && \"Security architecture and implementation\"}\n                          </div>\n                        </div>\n                      ))}\n                    </div>\n                    \n                    {/* Image */}\n                    <div className=\"relative\">\n                      <div className=\"relative h-full min-h-[140px] lg:min-h-[180px] bg-white/15 backdrop-blur-sm rounded-2xl overflow-hidden border border-white/25 shadow-lg\">\n                        <div className=\"absolute inset-0 bg-gradient-to-br from-white/5 to-white/10\"></div>\n                        <div className=\"relative z-10 h-full flex items-center justify-center p-4\">\n                          <img \n                            src=\"/images/about-us-image-3.jpg\" \n                            alt=\"Cybersecurity Assessment\" \n                            className=\"w-full h-full object-cover rounded-xl\"\n                          />\n                          <div className=\"absolute inset-0 bg-gradient-to-br from-[var(--color-blue)]/20 to-[var(--color-blue-secondary)]/20 rounded-xl\"></div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n                \n                <div className=\"flex items-center justify-between pt-6 border-t border-white/20\">\n                  <div className=\"flex items-center gap-4 text-white/90\">\n                    <div className=\"flex items-center gap-2 bg-white/10 px-3 py-2 rounded-xl\">\n                      <Award className=\"w-4 h-4\" />\n                      <span className=\"text-sm font-medium\">Certified</span>\n                    </div>\n                    <div className=\"flex items-center gap-2 bg-white/10 px-3 py-2 rounded-xl\">\n                      <Users className=\"w-4 h-4\" />\n                      <span className=\"text-sm font-medium\">Career-focused</span>\n                    </div>\n                  </div>\n                  <ArrowRight className=\"w-6 h-6 text-white group-hover:translate-x-2 transition-transform\" />\n                </div>\n              </div>\n            </div>\n          </motion.div>\n\n          {/* Medium Card - Authentication & Auth */}\n          <motion.div\n            initial={{ opacity: 0, scale: 0.9 }}\n            whileInView={{ opacity: 1, scale: 1 }}\n            transition={{ delay: 0.6, duration: 0.8 }}\n            viewport={{ once: true }}\n            whileHover={{ \n              y: -8,\n              scale: 1.02,\n              transition: { duration: 0.3 }\n            }}\n            className=\"col-span-12 lg:col-span-4 row-span-1 group relative\"\n          >\n            <div className=\"relative h-full bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-3xl p-6 overflow-hidden\">\n              <div className=\"absolute inset-0 bg-[url('/images/hexagon.svg')] bg-cover opacity-5\"></div>\n              <div className=\"absolute inset-0 bg-gradient-to-br from-[var(--color-blue)]/95 to-[var(--color-blue-secondary)]/95\"></div>\n              \n              <div className=\"relative z-10 h-full flex flex-col justify-between\">\n                <div className=\"flex-1\">\n                  <div className=\"w-12 h-12 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center mb-4 shadow-lg\">\n                    <Lock className=\"w-6 h-6 text-white\" />\n                  </div>\n                  <h3 className=\"text-xl font-bold text-white mb-3 leading-tight\">Authentication & Auth</h3>\n                  <p className=\"text-white/80 text-sm mb-4 leading-relaxed\">Deep dive into modern authentication systems</p>\n                  \n                  <div className=\"space-y-3\">\n                    {curriculumItems[5].features.map((feature, index) => (\n                      <div key={index} className=\"flex items-center gap-3 bg-white/10 rounded-xl p-3\">\n                        <div className=\"w-2 h-2 bg-[var(--color-yellow)] rounded-full flex-shrink-0\"></div>\n                        <span className=\"text-white/90 text-sm font-medium leading-tight\">{feature}</span>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n                \n                <ArrowRight className=\"w-5 h-5 text-white/60 group-hover:translate-x-1 transition-transform mt-4\" />\n              </div>\n            </div>\n          </motion.div>\n        </div>\n\n        {/* Bottom CTA */}\n        <motion.div \n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.8, duration: 0.6 }}\n          viewport={{ once: true }}\n          className=\"text-center pb-8\"\n        >\n          <PrimaryButton className=\"inline-flex items-center gap-3 sm:gap-6 px-6 sm:px-10 py-4 sm:py-5 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer group\">\n            <Shield className=\"w-5 h-5 sm:w-6 sm:h-6\" />\n            <span className=\"font-semibold text-base sm:text-lg lg:text-xl\">Join 500+ Security Professionals</span>\n            <ArrowRight className=\"w-5 h-5 sm:w-6 sm:h-6 group-hover:translate-x-2 transition-transform\" />\n          </PrimaryButton>\n        </motion.div>\n      </div>\n    </section>\n  );\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AALA;;;;;;AAOA,MAAM,kBAAkB;IACtB;QACE,MAAM,8MAAA,CAAA,WAAQ;QACd,OAAO;QACP,aAAa;QACb,OAAO;QACP,OAAO;QACP,MAAM;QACN,UAAU;QACV,UAAU;YAAC;YAAgB;YAA4B;SAAiB;IAC1E;IACA;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;QACb,OAAO;QACP,OAAO;QACP,MAAM;QACN,UAAU;QACV,UAAU;YAAC;YAAkB;YAAiB;SAAgB;IAChE;IACA;QACE,MAAM,kMAAA,CAAA,OAAI;QACV,OAAO;QACP,aAAa;QACb,OAAO;QACP,OAAO;QACP,MAAM;QACN,UAAU;QACV,UAAU;YAAC;YAAqB;YAAkB;SAAgB;IACpE;IACA;QACE,MAAM,wMAAA,CAAA,UAAO;QACb,OAAO;QACP,aAAa;QACb,OAAO;QACP,OAAO;QACP,MAAM;QACN,UAAU;QACV,UAAU;YAAC;YAAS;YAAmB;SAAe;IACxD;IACA;QACE,MAAM,sMAAA,CAAA,SAAM;QACZ,OAAO;QACP,aAAa;QACb,OAAO;QACP,OAAO;QACP,MAAM;QACN,UAAU;QACV,UAAU;YAAC;YAAsB;YAAoB;SAAoB;IAC3E;IACA;QACE,MAAM,kMAAA,CAAA,OAAI;QACV,OAAO;QACP,aAAa;QACb,OAAO;QACP,OAAO;QACP,MAAM;QACN,UAAU;QACV,UAAU;YAAC;YAAa;YAAO;SAAoB;IACrD;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,aAAa;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCACpC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAEV,8OAAC,gMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;kDACf,8OAAC;wCAAK,WAAU;kDAAiD;;;;;;;;;;;;0CAGnE,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;gCACR,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;oCACX;kDAEC,8OAAC;wCAAK,WAAU;kDAAiH;;;;;;;;;;;;0CAKnI,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gCACP,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;0CACX;;;;;;;;;;;;kCAOH,8OAAC;wBAAI,WAAU;wBAA0E,OAAO;4BAAE,kBAAkB;wBAAiC;;0CAEjJ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACX,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,aAAa;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCACpC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACtC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,YAAY;oCACV,GAAG,CAAC;oCACJ,OAAO;oCACT,YAAY;wCAAE,UAAU;oCAAI;gCAC9B;gCACA,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;sDAEf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;;;;;;8EAEtB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAG,WAAU;sFAA2E;;;;;;sFACzF,8OAAC;4EAAE,WAAU;sFAAqD;;;;;;;;;;;;;;;;;;sEAKtE,8OAAC;4DAAI,WAAU;;8EAEb,8OAAC;oEAAI,WAAU;8EACZ,eAAe,CAAC,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBACzC,8OAAC;4EAAgB,WAAU;;8FACzB,8OAAC;oFAAI,WAAU;8FAAoE;;;;;;8FACnF,8OAAC;oFAAI,WAAU;;wFACZ,YAAY,kBAAkB;wFAC9B,YAAY,8BAA8B;wFAC1C,YAAY,oBAAoB;;;;;;;;2EAL3B;;;;;;;;;;8EAYd,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;;;;;;0FACf,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;wFACC,KAAI;wFACJ,KAAI;wFACJ,WAAU;;;;;;kGAEZ,8OAAC;wFAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8DAOzB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,oMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;sFACjB,8OAAC;4EAAK,WAAU;sFAAsB;;;;;;;;;;;;8EAExC,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,kMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;sFAChB,8OAAC;4EAAK,WAAU;sFAAsB;;;;;;;;;;;;;;;;;;sEAG1C,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACf,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,aAAa;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCACpC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,YAAY;oCACV,GAAG,CAAC;oCACJ,OAAO;oCACP,YAAY;wCAAE,UAAU;oCAAI;gCAC9B;gCACA,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;sDAEf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;;;;;;sEAEnB,8OAAC;4DAAG,WAAU;sEAAkD;;;;;;sEAChE,8OAAC;4DAAE,WAAU;sEAA6C;;;;;;sEAE1D,8OAAC;4DAAI,WAAU;sEACZ,eAAe,CAAC,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBACzC,8OAAC;oEAAgB,WAAU;;sFACzB,8OAAC;4EAAI,WAAU;;;;;;sFACf,8OAAC;4EAAK,WAAU;sFAAmD;;;;;;;mEAF3D;;;;;;;;;;;;;;;;8DAQhB,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0CAM5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,aAAa;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCACpC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,YAAY;oCACV,GAAG,CAAC;oCACJ,OAAO;oCACP,YAAY;wCAAE,UAAU;oCAAI;gCAC9B;gCACA,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;sDAEf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;sEAElB,8OAAC;4DAAG,WAAU;sEAAkD;;;;;;sEAChE,8OAAC;4DAAE,WAAU;sEAA6C;;;;;;sEAE1D,8OAAC;4DAAI,WAAU;sEACZ,eAAe,CAAC,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBACzC,8OAAC;oEAAgB,WAAU;;sFACzB,8OAAC;4EAAI,WAAU;;;;;;sFACf,8OAAC;4EAAK,WAAU;sFAAmD;;;;;;;mEAF3D;;;;;;;;;;;;;;;;8DAQhB,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0CAM5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,aAAa;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCACpC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,YAAY;oCACV,GAAG,CAAC;oCACJ,OAAO;oCACP,YAAY;wCAAE,UAAU;oCAAI;gCAC9B;gCACA,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,wMAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;;;;;;kEAErB,8OAAC;wDAAG,WAAU;kEAAqE;;;;;;kEACnF,8OAAC;wDAAE,WAAU;kEAA6D;;;;;;;;;;;;0DAG5E,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0CAM5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,aAAa;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCACpC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,YAAY;oCACV,GAAG,CAAC;oCACJ,OAAO;oCACP,YAAY;wCAAE,UAAU;oCAAI;gCAC9B;gCACA,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;sDAEf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;;;;;;8EAEpB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAG,WAAU;sFAA2E;;;;;;sFACzF,8OAAC;4EAAE,WAAU;sFAAqD;;;;;;;;;;;;;;;;;;sEAKtE,8OAAC;4DAAI,WAAU;;8EAEb,8OAAC;oEAAI,WAAU;8EACZ,eAAe,CAAC,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBACzC,8OAAC;4EAAgB,WAAU;;8FACzB,8OAAC;oFAAI,WAAU;8FAAoE;;;;;;8FACnF,8OAAC;oFAAI,WAAU;;wFACZ,YAAY,wBAAwB;wFACpC,YAAY,sBAAsB;wFAClC,YAAY,uBAAuB;;;;;;;;2EAL9B;;;;;;;;;;8EAYd,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;;;;;;0FACf,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;wFACC,KAAI;wFACJ,KAAI;wFACJ,WAAU;;;;;;kGAEZ,8OAAC;wFAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8DAOzB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,oMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;sFACjB,8OAAC;4EAAK,WAAU;sFAAsB;;;;;;;;;;;;8EAExC,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,oMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;sFACjB,8OAAC;4EAAK,WAAU;sFAAsB;;;;;;;;;;;;;;;;;;sEAG1C,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAO9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,aAAa;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCACpC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,YAAY;oCACV,GAAG,CAAC;oCACJ,OAAO;oCACP,YAAY;wCAAE,UAAU;oCAAI;gCAC9B;gCACA,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;sDAEf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;sEAElB,8OAAC;4DAAG,WAAU;sEAAkD;;;;;;sEAChE,8OAAC;4DAAE,WAAU;sEAA6C;;;;;;sEAE1D,8OAAC;4DAAI,WAAU;sEACZ,eAAe,CAAC,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBACzC,8OAAC;oEAAgB,WAAU;;sFACzB,8OAAC;4EAAI,WAAU;;;;;;sFACf,8OAAC;4EAAK,WAAU;sFAAmD;;;;;;;mEAF3D;;;;;;;;;;;;;;;;8DAQhB,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,OAAO;4BAAK,UAAU;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;kCAEV,cAAA,8OAAC,gJAAA,CAAA,gBAAa;4BAAC,WAAU;;8CACvB,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,8OAAC;oCAAK,WAAU;8CAAgD;;;;;;8CAChE,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlC", "debugId": null}}, {"offset": {"line": 2171, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/training/components/WhoCanJoin.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\n\r\nconst audienceTypes = [\r\n  {\r\n    icon: User,\r\n    title: \"Cybersecurity Enthusiasts\",\r\n    subtitle: \"All Experience Levels\",\r\n    description: \"Whether you're new to the field or have some experience, our program caters to learners of all levels. Start with the free tier to build a strong foundation, then upgrade to the premium tier for a more immersive, mentor-guided experience.\",\r\n    features: [\"Free tier available\", \"Progressive learning path\", \"Mentor-guided experience\"],\r\n    color: \"from-[var(--color-blue)] to-[var(--color-blue-secondary)]\",\r\n    badge: \"All Levels\",\r\n    delay: 0.1\r\n  },\r\n  {\r\n    icon: GraduationCap,\r\n    title: \"Information Security Professionals\",\r\n    subtitle: \"Experienced Practitioners\",\r\n    description: \"If you have been practicing cybersecurity topics, you can join the premium tier to take your skills to the next level through hands-on projects and industry-relevant training.\",\r\n    features: [\"Advanced skill development\", \"Industry-relevant training\", \"Hands-on projects\"],\r\n    color: \"from-[var(--color-dark-blue)] to-[var(--color-dark-blue-hover)]\",\r\n    badge: \"Advanced\",\r\n    delay: 0.3\r\n  },\r\n  {\r\n    icon: Briefcase,\r\n    title: \"Career Switchers\",\r\n    subtitle: \"Transitioning to Security\",\r\n    description: \"Looking to transition into the exciting world of cybersecurity? Our program provides the knowledge and practical experience you need to kickstart your career in this high-demand field.\",\r\n    features: [\"Career transition support\", \"Practical experience\", \"High-demand field\"],\r\n    color: \"from-[var(--color-yellow)] to-[var(--color-yellow-hover)]\",\r\n    badge: \"Career Change\",\r\n    delay: 0.5\r\n  }\r\n];\r\n\r\nexport default function WhoCanJoin({ content }) {\r\n  // Use content if provided, otherwise use default\r\n  const sectionContent = content || {\r\n    title: \"So Who Can Take Up This Training?\",\r\n    description: \"Our program is designed to be accessible and beneficial for a wide range of learners, from beginners to experienced professionals. Whether you're looking to start your cybersecurity journey or take it to new heights, we've got you covered.\",\r\n    targetAudience: audienceTypes.map(type => ({\r\n      title: type.title,\r\n      description: type.description\r\n    })),\r\n    bottomImage: \"/images/TrainingHacker.png\"\r\n  };\r\n  return (\r\n    <section className=\"py-12 sm:py-16 lg:py-24 bg-white relative overflow-hidden\">\r\n      {/* Background Elements */}\r\n      <div className=\"absolute inset-0\">\r\n        <div className=\"absolute top-0 left-1/4 w-96 h-96 bg-gradient-to-br from-[var(--color-blue)]/10 to-[var(--color-blue-secondary)]/10 rounded-full blur-3xl\"></div>\r\n        <div className=\"absolute bottom-0 right-1/4 w-96 h-96 bg-gradient-to-br from-[var(--color-yellow)]/10 to-[var(--color-yellow-hover)]/10 rounded-full blur-3xl\"></div>\r\n      </div>\r\n\r\n      <div className=\"relative z-10 container mx-auto px-4 max-w-7xl\">\r\n        {/* Header */}\r\n        <div className=\"text-center mb-16\">\r\n          <h2 className=\"text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-bold text-[var(--color-dark-blue)] mb-6 font-poppins\">\r\n            {sectionContent.title}\r\n          </h2>\r\n\r\n          <p className=\"text-base sm:text-lg lg:text-xl text-[var(--foreground-secondary)] max-w-4xl mx-auto leading-relaxed px-4 font-roboto\">\r\n            {sectionContent.description}\r\n          </p>\r\n        </div>\r\n\r\n        {/* Cards with Equal Heights */}\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 lg:gap-12\">\r\n          {(sectionContent.targetAudience || audienceTypes).map((audience, index) => {\r\n            const audienceType = audienceTypes[index] || audienceTypes[0]; // Fallback for styling\r\n            return (\r\n            <div key={index} className=\"h-full\">\r\n              {/* Simple Professional Card */}\r\n              <div className=\"bg-white rounded-lg p-6 shadow-md border border-gray-200 h-full flex flex-col\">\r\n\r\n                {/* Icon Container */}\r\n                <div className=\"w-12 h-12 bg-[var(--color-blue)] rounded-lg flex items-center justify-center mb-4\">\r\n                  <img\r\n                    src=\"/images/block.png\"\r\n                    alt=\"Target Audience Icon\"\r\n                    className=\"w-6 h-6\"\r\n                  />\r\n                </div>\r\n\r\n                {/* Content */}\r\n                <div className=\"flex-1\">\r\n                  <h4 className=\"text-xl font-bold text-[var(--color-dark-blue)] mb-3 font-poppins\">\r\n                    {audience.title}\r\n                  </h4>\r\n\r\n                  <p className=\"text-[var(--foreground-secondary)] leading-relaxed font-roboto\">\r\n                    {audience.description}\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            );\r\n          })}\r\n        </div>\r\n\r\n\r\n      </div>\r\n    </section>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIA,MAAM,gBAAgB;IACpB;QACE,MAAM;QACN,OAAO;QACP,UAAU;QACV,aAAa;QACb,UAAU;YAAC;YAAuB;YAA6B;SAA2B;QAC1F,OAAO;QACP,OAAO;QACP,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;QACP,UAAU;QACV,aAAa;QACb,UAAU;YAAC;YAA8B;YAA8B;SAAoB;QAC3F,OAAO;QACP,OAAO;QACP,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;QACP,UAAU;QACV,aAAa;QACb,UAAU;YAAC;YAA6B;YAAwB;SAAoB;QACpF,OAAO;QACP,OAAO;QACP,OAAO;IACT;CACD;AAEc,SAAS,WAAW,EAAE,OAAO,EAAE;IAC5C,iDAAiD;IACjD,MAAM,iBAAiB,WAAW;QAChC,OAAO;QACP,aAAa;QACb,gBAAgB,cAAc,GAAG,CAAC,CAAA,OAAQ,CAAC;gBACzC,OAAO,KAAK,KAAK;gBACjB,aAAa,KAAK,WAAW;YAC/B,CAAC;QACD,aAAa;IACf;IACA,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CACX,eAAe,KAAK;;;;;;0CAGvB,8OAAC;gCAAE,WAAU;0CACV,eAAe,WAAW;;;;;;;;;;;;kCAK/B,8OAAC;wBAAI,WAAU;kCACZ,CAAC,eAAe,cAAc,IAAI,aAAa,EAAE,GAAG,CAAC,CAAC,UAAU;4BAC/D,MAAM,eAAe,aAAa,CAAC,MAAM,IAAI,aAAa,CAAC,EAAE,EAAE,uBAAuB;4BACtF,qBACA,8OAAC;gCAAgB,WAAU;0CAEzB,cAAA,8OAAC;oCAAI,WAAU;;sDAGb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,KAAI;gDACJ,KAAI;gDACJ,WAAU;;;;;;;;;;;sDAKd,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DACX,SAAS,KAAK;;;;;;8DAGjB,8OAAC;oDAAE,WAAU;8DACV,SAAS,WAAW;;;;;;;;;;;;;;;;;;+BApBnB;;;;;wBA0BZ;;;;;;;;;;;;;;;;;;AAOV", "debugId": null}}, {"offset": {"line": 2372, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/training/components/LearningModules.jsx"], "sourcesContent": ["\"use client\";\n\nimport React from 'react';\n\nexport default function LearningModules({ content }) {\n  // Use content if provided, otherwise use default modules\n  const moduleContent = content || {\n    title: \"Here's A Short Teaser Of What You Will Learn\",\n    modules: [\n      {\n        title: \"Web Application Security\",\n        description: \"Fortifying the digital frontline against cyber threats.\"\n      },\n      {\n        title: \"API & Network Security\", \n        description: \"Safeguarding the backbone of modern interconnected systems.\"\n      },\n      {\n        title: \"Practical Skills Development\",\n        description: \"Honing real-world cybersecurity expertise through hands-on learning.\"\n      },\n      {\n        title: \"Soft Skill & Professional Growth\",\n        description: \"Cultivating the human element in technical cybersecurity roles.\"\n      },\n      {\n        title: \"Real World Environment Navigation\",\n        description: \"Mastering the art of securing complex, live digital ecosystems.\"\n      },\n      {\n        title: \"Active Directory & Cloud Security\",\n        description: \"Protecting the nerve centers of enterprise and cloud infrastructures.\"\n      },\n      {\n        title: \"Continuous Learning & Adaption\",\n        description: \"Staying ahead in the ever-evolving cybersecurity landscape.\"\n      },\n      {\n        title: \"Report Writing Skills\",\n        description: \"Crafting clear, concise, and impactful cybersecurity documentation for stakeholders at all levels.\"\n      }\n    ]\n  };\n\n  return (\n    <section className=\"py-16 lg:py-24 bg-gradient-to-br from-gray-50 to-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        \n        {/* Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-3xl sm:text-4xl lg:text-5xl font-bold text-[var(--color-dark-blue)] mb-6 font-poppins\">\n            {moduleContent.title}\n          </h2>\n        </div>\n\n        {/* Learning Areas Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 lg:gap-8\">\n          {moduleContent.modules.map((module, index) => (\n            <div key={index} className=\"group\">\n              <div className=\"bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 group-hover:border-[var(--color-blue)]/20 h-full flex flex-col\">\n                \n                {/* Icon */}\n                <div className=\"w-12 h-12 bg-gradient-to-br from-[var(--color-blue)]/10 to-[var(--color-blue-secondary)]/10 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform\">\n                  <img \n                    src=\"/images/web.png\" \n                    alt=\"Web Security Icon\" \n                    className=\"w-6 h-6\"\n                  />\n                </div>\n\n                {/* Content */}\n                <h5 className=\"text-lg font-bold text-[var(--color-dark-blue)] mb-3 font-poppins\">\n                  {module.title}\n                </h5>\n                \n                <p className=\"text-[var(--foreground-secondary)] text-sm leading-relaxed flex-grow font-roboto\">\n                  {module.description}\n                </p>\n              </div>\n            </div>\n          ))}\n        </div>\n\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS,gBAAgB,EAAE,OAAO,EAAE;IACjD,yDAAyD;IACzD,MAAM,gBAAgB,WAAW;QAC/B,OAAO;QACP,SAAS;YACP;gBACE,OAAO;gBACP,aAAa;YACf;YACA;gBACE,OAAO;gBACP,aAAa;YACf;YACA;gBACE,OAAO;gBACP,aAAa;YACf;YACA;gBACE,OAAO;gBACP,aAAa;YACf;YACA;gBACE,OAAO;gBACP,aAAa;YACf;YACA;gBACE,OAAO;gBACP,aAAa;YACf;YACA;gBACE,OAAO;gBACP,aAAa;YACf;YACA;gBACE,OAAO;gBACP,aAAa;YACf;SACD;IACH;IAEA,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAGb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAG,WAAU;kCACX,cAAc,KAAK;;;;;;;;;;;8BAKxB,8OAAC;oBAAI,WAAU;8BACZ,cAAc,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAClC,8OAAC;4BAAgB,WAAU;sCACzB,cAAA,8OAAC;gCAAI,WAAU;;kDAGb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,KAAI;4CACJ,KAAI;4CACJ,WAAU;;;;;;;;;;;kDAKd,8OAAC;wCAAG,WAAU;kDACX,OAAO,KAAK;;;;;;kDAGf,8OAAC;wCAAE,WAAU;kDACV,OAAO,WAAW;;;;;;;;;;;;2BAlBf;;;;;;;;;;;;;;;;;;;;;AA4BtB", "debugId": null}}, {"offset": {"line": 2510, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/training/components/StudentLogos.jsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useEffect, useRef } from 'react';\nimport { motion } from 'framer-motion';\nimport { Building2, GraduationCap, Users, Star } from 'lucide-react';\n\nconst studentLogos = [\n  {\n    id: 1,\n    name: \"AIA NZ\",\n    logo: \"/images/company1.png\",\n    type: \"Insurance\",\n    students: 45,\n    rating: 4.9\n  },\n  {\n    id: 2,\n    name: \"Data Torque\",\n    logo: \"/images/company2.png\",\n    type: \"Data Analytics\",\n    students: 32,\n    rating: 4.8\n  },\n  {\n    id: 3,\n    name: \"CyberDefense Inc\",\n    logo: \"/images/company3.png\",\n    type: \"Security Firm\",\n    students: 28,\n    rating: 4.9\n  },\n  {\n    id: 4,\n    name: \"CloudTech Systems\",\n    logo: \"/images/company4.png\",\n    type: \"Cloud Provider\",\n    students: 38,\n    rating: 4.7\n  },\n  {\n    id: 5,\n    name: \"SecureNet\",\n    logo: \"/images/company5.png\",\n    type: \"Network Security\",\n    students: 25,\n    rating: 4.8\n  },\n  {\n    id: 6,\n    name: \"DataVault\",\n    logo: \"/images/company6.png\",\n    type: \"Data Protection\",\n    students: 30,\n    rating: 4.9\n  },\n  {\n    id: 7,\n    name: \"AppShield\",\n    logo: \"/images/company7.png\",\n    type: \"Application Security\",\n    students: 22,\n    rating: 4.7\n  },\n  {\n    id: 8,\n    name: \"InfraGuard\",\n    logo: \"/images/company8.png\",\n    type: \"Infrastructure\",\n    students: 35,\n    rating: 4.8\n  }\n];\n\nexport default function StudentLogos() {\n  const containerRef = useRef(null);\n\n  useEffect(() => {\n    const container = containerRef.current;\n    if (!container) return;\n\n    let animationId;\n    let scrollPosition = 0;\n    const scrollSpeed = 0.5;\n\n    const animate = () => {\n      scrollPosition += scrollSpeed;\n      if (container) {\n        container.style.transform = `translateX(-${scrollPosition}px)`;\n        \n        // Reset position when scrolled too far\n        if (scrollPosition >= container.scrollWidth / 2) {\n          scrollPosition = 0;\n        }\n      }\n      animationId = requestAnimationFrame(animate);\n    };\n\n    animate();\n\n    return () => {\n      if (animationId) {\n        cancelAnimationFrame(animationId);\n      }\n    };\n  }, []);\n\n  return (\n    <section className=\"py-24 bg-gradient-to-br from-[var(--color-dark-blue)] via-[var(--color-dark-blue-hover)] to-[var(--color-dark-blue-bg)] relative overflow-hidden\">\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0\">\n        <div className=\"absolute top-0 left-0 w-full h-full bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.03%22%3E%3Ccircle%20cx%3D%2230%22%20cy%3D%2230%22%20r%3D%221%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-50\"></div>\n      </div>\n\n      <div className=\"relative z-10 container mx-auto px-4 max-w-7xl\">\n        {/* Header */}\n        <motion.div \n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <motion.div \n            initial={{ opacity: 0, scale: 0.8 }}\n            whileInView={{ opacity: 1, scale: 1 }}\n            transition={{ delay: 0.2, duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"inline-flex items-center bg-[var(--color-blue)]/20 px-4 py-2 rounded-full mb-6\"\n          >\n            <Building2 className=\"w-4 h-4 text-[var(--color-blue)] mr-2\" />\n            <span className=\"text-sm font-medium text-[var(--color-blue)]\">Our Trained Students works at</span>\n          </motion.div>\n          \n          <motion.h2 \n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.4, duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"text-4xl lg:text-5xl font-bold text-white mb-6\"\n          >\n            Companies That\n            <span className=\"block bg-gradient-to-r from-[var(--color-blue)] via-[var(--color-blue-secondary)] to-[var(--color-yellow)] bg-clip-text text-transparent\">\n              Trust Our Training\n            </span>\n          </motion.h2>\n          \n          <motion.p \n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.6, duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"text-xl text-white/80 max-w-4xl mx-auto leading-relaxed\"\n          >\n            Join hundreds of organizations that have upskilled their teams with our \n            comprehensive cybersecurity training programs.\n          </motion.p>\n        </motion.div>\n\n        {/* Floating Logos Carousel */}\n        <div className=\"relative overflow-hidden py-8\">\n          <div \n            ref={containerRef}\n            className=\"flex gap-8 items-center\"\n            style={{ width: 'max-content' }}\n          >\n            {/* Duplicate logos for seamless loop */}\n            {[...studentLogos, ...studentLogos].map((company, index) => (\n              <motion.div\n                key={`${company.id}-${index}`}\n                initial={{ opacity: 0, y: 30 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ delay: index * 0.1, duration: 0.6 }}\n                viewport={{ once: true }}\n                whileHover={{ \n                  y: -5,\n                  scale: 1.02,\n                  transition: { duration: 0.3 }\n                }}\n                className=\"group relative flex-shrink-0\"\n              >\n                {/* 3D Card */}\n                <div className=\"relative bg-white/10 backdrop-blur-xl rounded-3xl p-6 border border-white/20 w-64 h-32 flex items-center justify-center shadow-lg hover:shadow-2xl transition-all duration-300\">\n                  \n                  {/* Company Logo Placeholder */}\n                  <div className=\"text-center space-y-3\">\n                    <div className=\"w-16 h-16 bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-2xl flex items-center justify-center mx-auto shadow-lg group-hover:shadow-xl transition-all duration-300\">\n                      <Building2 className=\"w-8 h-8 text-white\" />\n                    </div>\n                    <div>\n                      <h3 className=\"font-semibold text-white text-lg group-hover:text-[var(--color-blue)] transition-colors\">\n                        {company.name}\n                      </h3>\n                      <p className=\"text-[var(--color-blue)] text-sm\">{company.type}</p>\n                    </div>\n                  </div>\n\n                  {/* Stats Overlay */}\n                  <div className=\"absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300\">\n                    <div className=\"bg-black/50 backdrop-blur-sm rounded-lg p-2 text-white text-xs\">\n                      <div className=\"flex items-center gap-1\">\n                        <Users className=\"w-3 h-3\" />\n                        <span>{company.students}</span>\n                      </div>\n                      <div className=\"flex items-center gap-1\">\n                        <Star className=\"w-3 h-3 text-[var(--color-yellow)]\" />\n                        <span>{company.rating}</span>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Hover Effects */}\n                  <div className=\"absolute inset-0 bg-gradient-to-br from-[var(--color-blue)]/10 to-[var(--color-blue-secondary)]/10 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300\" />\n                  <div className=\"absolute inset-0 bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-3xl opacity-0 group-hover:opacity-5 transition-opacity duration-300 blur-xl\" />\n                </div>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n\n        {/* Stats Section */}\n        <motion.div \n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.8, duration: 0.6 }}\n          viewport={{ once: true }}\n          className=\"mt-20 grid grid-cols-1 md:grid-cols-4 gap-8\"\n        >\n          <div className=\"text-center\">\n            <div className=\"text-4xl font-bold text-[var(--color-blue)] mb-2\">500+</div>\n            <div className=\"text-white/70\">Students Trained</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-4xl font-bold text-[var(--color-blue-secondary)] mb-2\">50+</div>\n            <div className=\"text-white/70\">Companies</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-4xl font-bold text-[var(--color-yellow)] mb-2\">98%</div>\n            <div className=\"text-white/70\">Success Rate</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-4xl font-bold text-[var(--color-blue)] mb-2\">4.9</div>\n            <div className=\"text-white/70\">Average Rating</div>\n          </div>\n        </motion.div>\n\n        {/* CTA Section */}\n        <motion.div \n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ delay: 1, duration: 0.6 }}\n          viewport={{ once: true }}\n          className=\"text-center mt-16\"\n        >\n          <div className=\"inline-flex items-center gap-4 bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] text-white px-8 py-4 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer group\">\n            <GraduationCap className=\"w-5 h-5\" />\n            <span className=\"font-semibold text-lg\">Join These Companies</span>\n            <Star className=\"w-5 h-5 group-hover:scale-110 transition-transform\" />\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAJA;;;;;AAMA,MAAM,eAAe;IACnB;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,MAAM;QACN,UAAU;QACV,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,MAAM;QACN,UAAU;QACV,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,MAAM;QACN,UAAU;QACV,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,MAAM;QACN,UAAU;QACV,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,MAAM;QACN,UAAU;QACV,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,MAAM;QACN,UAAU;QACV,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,MAAM;QACN,UAAU;QACV,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,MAAM;QACN,UAAU;QACV,QAAQ;IACV;CACD;AAEc,SAAS;IACtB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAE5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY,aAAa,OAAO;QACtC,IAAI,CAAC,WAAW;QAEhB,IAAI;QACJ,IAAI,iBAAiB;QACrB,MAAM,cAAc;QAEpB,MAAM,UAAU;YACd,kBAAkB;YAClB,IAAI,WAAW;gBACb,UAAU,KAAK,CAAC,SAAS,GAAG,CAAC,YAAY,EAAE,eAAe,GAAG,CAAC;gBAE9D,uCAAuC;gBACvC,IAAI,kBAAkB,UAAU,WAAW,GAAG,GAAG;oBAC/C,iBAAiB;gBACnB;YACF;YACA,cAAc,sBAAsB;QACtC;QAEA;QAEA,OAAO;YACL,IAAI,aAAa;gBACf,qBAAqB;YACvB;QACF;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,aAAa;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCACpC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAEV,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,8OAAC;wCAAK,WAAU;kDAA+C;;;;;;;;;;;;0CAGjE,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;gCACR,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;oCACX;kDAEC,8OAAC;wCAAK,WAAU;kDAA2I;;;;;;;;;;;;0CAK7J,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gCACP,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;0CACX;;;;;;;;;;;;kCAOH,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,KAAK;4BACL,WAAU;4BACV,OAAO;gCAAE,OAAO;4BAAc;sCAG7B;mCAAI;mCAAiB;6BAAa,CAAC,GAAG,CAAC,CAAC,SAAS,sBAChD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,OAAO,QAAQ;wCAAK,UAAU;oCAAI;oCAChD,UAAU;wCAAE,MAAM;oCAAK;oCACvB,YAAY;wCACV,GAAG,CAAC;wCACJ,OAAO;wCACP,YAAY;4CAAE,UAAU;wCAAI;oCAC9B;oCACA,WAAU;8CAGV,cAAA,8OAAC;wCAAI,WAAU;;0DAGb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,gNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;;;;;;kEAEvB,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EACX,QAAQ,IAAI;;;;;;0EAEf,8OAAC;gEAAE,WAAU;0EAAoC,QAAQ,IAAI;;;;;;;;;;;;;;;;;;0DAKjE,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;8EACjB,8OAAC;8EAAM,QAAQ,QAAQ;;;;;;;;;;;;sEAEzB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,8OAAC;8EAAM,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;;;;0DAM3B,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;;;;;;;mCA5CZ,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,OAAO;;;;;;;;;;;;;;;kCAoDrC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,OAAO;4BAAK,UAAU;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAmD;;;;;;kDAClE,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;0CAEjC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAA6D;;;;;;kDAC5E,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;0CAEjC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAqD;;;;;;kDACpE,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;0CAEjC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAmD;;;;;;kDAClE,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;kCAKnC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,OAAO;4BAAG,UAAU;wBAAI;wBACtC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;8CACzB,8OAAC;oCAAK,WAAU;8CAAwB;;;;;;8CACxC,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM5B", "debugId": null}}, {"offset": {"line": 3131, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/training/components/PremiumCTA.jsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { Crown, ArrowRight, CheckCircle, Star, Zap, Shield } from 'lucide-react';\n\nconst premiumFeatures = [\n  \"Access to advance Lab Subscription (TryHackMe, HackTheBox)\",\n  \"Personalized mentorship and guided learning\",\n  \"3 months of hands-on experience with SecurityLit\",\n  \"Professional report writing training\",\n  \"Experience letter upon completion\"\n];\n\nexport default function PremiumCTA() {\n  const [showForm, setShowForm] = useState(false);\n\n  return (\n    <>\n      {/* Main Premium Section */}\n      <section className=\"py-24 bg-gradient-to-br from-[var(--color-dark-blue)] via-[var(--color-dark-blue-hover)] to-[var(--color-dark-blue-bg)] relative overflow-hidden\">\n        {/* Background Elements */}\n        <div className=\"absolute inset-0\">\n          <div className=\"absolute top-0 left-0 w-full h-full bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.03%22%3E%3Ccircle%20cx%3D%2230%22%20cy%3D%2230%22%20r%3D%221%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-50\"></div>\n          <div className=\"absolute top-20 left-10 w-72 h-72 bg-[var(--color-blue)]/20 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob\"></div>\n          <div className=\"absolute bottom-20 right-10 w-72 h-72 bg-[var(--color-blue-secondary)]/20 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000\"></div>\n        </div>\n\n        <div className=\"relative z-10 container mx-auto px-4 max-w-7xl\">\n          {/* Header */}\n          <motion.div \n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n            className=\"text-center mb-16\"\n          >\n            <motion.div \n              initial={{ opacity: 0, scale: 0.8 }}\n              whileInView={{ opacity: 1, scale: 1 }}\n              transition={{ delay: 0.2, duration: 0.6 }}\n              viewport={{ once: true }}\n              className=\"inline-flex items-center bg-[var(--color-yellow)]/20 px-4 py-2 rounded-full mb-6\"\n            >\n              <Crown className=\"w-4 h-4 text-[var(--color-yellow)] mr-2\" />\n              <span className=\"text-sm font-medium text-[var(--color-yellow)]\">Premium Tier Benefits</span>\n            </motion.div>\n            \n            <motion.h2 \n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.4, duration: 0.6 }}\n              viewport={{ once: true }}\n              className=\"text-4xl lg:text-5xl font-bold text-white mb-6\"\n            >\n              As a Premium member, you'll work directly with\n              <span className=\"block bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] bg-clip-text text-transparent\">\n                SecurityLit\n              </span>\n            </motion.h2>\n            \n            <motion.p \n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.6, duration: 0.6 }}\n              viewport={{ once: true }}\n              className=\"text-xl text-white/80 max-w-4xl mx-auto leading-relaxed\"\n            >\n              on live penetration testing projects. This hands-on experience will immerse you in real-time cybersecurity challenges, teaching you how professional pentests are conducted. You'll learn the intricacies of report writing, client communication, and industry best practices. Upon completion, you'll receive an experience letter, validating your practical skills and boosting your career prospects in the cybersecurity field.\n            </motion.p>\n          </motion.div>\n\n          {/* Premium Features Grid */}\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-16\">\n            {/* Features List */}\n            <motion.div \n              initial={{ opacity: 0, x: -50 }}\n              whileInView={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.8 }}\n              viewport={{ once: true }}\n              className=\"space-y-6\"\n            >\n              {premiumFeatures.map((feature, index) => (\n                <motion.div\n                  key={index}\n                  initial={{ opacity: 0, x: -30 }}\n                  whileInView={{ opacity: 1, x: 0 }}\n                  transition={{ delay: index * 0.1, duration: 0.6 }}\n                  viewport={{ once: true }}\n                  className=\"flex items-center gap-4\"\n                >\n                  <div className=\"w-12 h-12 bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-2xl flex items-center justify-center shadow-lg\">\n                    <CheckCircle className=\"w-6 h-6 text-[var(--color-yellow)]\" />\n                  </div>\n                  <div>\n                    <h3 className=\"text-lg font-semibold text-white\">{feature}</h3>\n                  </div>\n                </motion.div>\n              ))}\n            </motion.div>\n\n            {/* Premium Card */}\n            <motion.div \n              initial={{ opacity: 0, x: 50 }}\n              whileInView={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.8 }}\n              viewport={{ once: true }}\n              className=\"relative\"\n            >\n              <div className=\"bg-white/10 backdrop-blur-xl rounded-3xl p-8 border border-white/20 shadow-2xl\">\n                <div className=\"text-center space-y-6\">\n                  {/* Premium Badge */}\n                  <div className=\"inline-flex items-center gap-2 bg-gradient-to-r from-[var(--color-yellow)] to-[var(--color-yellow-hover)] text-white px-6 py-3 rounded-full\">\n                    <Crown className=\"w-5 h-5\" />\n                    <span className=\"font-semibold\">Premium Plan</span>\n                  </div>\n\n                  {/* Pricing */}\n                  <div>\n                    <div className=\"text-4xl font-bold text-white mb-2\">\n                      $299\n                      <span className=\"text-lg text-white/70 font-normal\">/month</span>\n                    </div>\n                    <p className=\"text-white/70\">or $2,999/year (save 17%)</p>\n                  </div>\n\n                  {/* Features */}\n                  <div className=\"space-y-3\">\n                    <div className=\"flex items-center justify-between text-sm\">\n                      <span className=\"text-white/70\">All Basic Features</span>\n                      <CheckCircle className=\"w-4 h-4 text-green-400\" />\n                    </div>\n                    <div className=\"flex items-center justify-between text-sm\">\n                      <span className=\"text-white/70\">Advanced Modules</span>\n                      <CheckCircle className=\"w-4 h-4 text-green-400\" />\n                    </div>\n                    <div className=\"flex items-center justify-between text-sm\">\n                      <span className=\"text-white/70\">1-on-1 Mentoring</span>\n                      <CheckCircle className=\"w-4 h-4 text-green-400\" />\n                    </div>\n                    <div className=\"flex items-center justify-between text-sm\">\n                      <span className=\"text-white/70\">Certification</span>\n                      <CheckCircle className=\"w-4 h-4 text-green-400\" />\n                    </div>\n                    <div className=\"flex items-center justify-between text-sm\">\n                      <span className=\"text-white/70\">Priority Support</span>\n                      <CheckCircle className=\"w-4 h-4 text-green-400\" />\n                    </div>\n                  </div>\n\n                  {/* CTA Button */}\n                  <motion.button\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    onClick={() => setShowForm(true)}\n                    className=\"w-full bg-gradient-to-r from-[var(--color-yellow)] to-[var(--color-yellow-hover)] hover:from-[var(--color-yellow-hover)] hover:to-[var(--color-yellow)] text-white py-4 px-8 rounded-2xl font-bold text-lg shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center gap-2\"\n                  >\n                    <Zap className=\"w-5 h-5\" />\n                    Start Premium Trial\n                    <ArrowRight className=\"w-5 h-5\" />\n                  </motion.button>\n\n                  <p className=\"text-xs text-white/50\">\n                    30-day money-back guarantee • Cancel anytime\n                  </p>\n                </div>\n              </div>\n\n              {/* Floating Elements */}\n              <motion.div\n                animate={{ y: [-10, 10, -10] }}\n                transition={{ duration: 3, repeat: Infinity, ease: \"easeInOut\" }}\n                className=\"absolute -top-4 -right-4 w-8 h-8 bg-[var(--color-blue)] rounded-full opacity-80\"\n              />\n              <motion.div\n                animate={{ y: [10, -10, 10] }}\n                transition={{ duration: 4, repeat: Infinity, ease: \"easeInOut\" }}\n                className=\"absolute -bottom-4 -left-4 w-6 h-6 bg-[var(--color-blue-secondary)] rounded-full opacity-80\"\n              />\n            </motion.div>\n          </div>\n\n          {/* Trust Indicators */}\n          <motion.div \n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.8, duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"text-center\"\n          >\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-[var(--color-yellow)] mb-2\">10,000+</div>\n                <div className=\"text-white/70\">Premium Students</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-green-400 mb-2\">99%</div>\n                <div className=\"text-white/70\">Satisfaction Rate</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-[var(--color-blue)] mb-2\">24/7</div>\n                <div className=\"text-white/70\">Expert Support</div>\n              </div>\n            </div>\n          </motion.div>\n\n          {/* Bottom CTA */}\n          <motion.div \n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ delay: 1, duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"text-center mt-16\"\n          >\n            <motion.button\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              onClick={() => setShowForm(true)}\n              className=\"bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] text-[var(--color-dark-blue)] px-12 py-4 rounded-2xl font-bold text-xl shadow-2xl hover:shadow-3xl transition-all duration-300 flex items-center gap-3 mx-auto\"\n            >\n              <Crown className=\"w-6 h-6\" />\n              Upgrade to the Premium Tier\n              <ArrowRight className=\"w-6 h-6\" />\n            </motion.button>\n            <p className=\"text-white/70 mt-4 text-lg\">\n              Take your cybersecurity training to the next level with our premium tier. Unlock exclusive access to advanced labs, personalized mentorship, and hands-on projects that will transform your skills and career.\n            </p>\n          </motion.div>\n        </div>\n      </section>\n\n      {/* Premium Form Modal */}\n      {showForm && (\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          exit={{ opacity: 0 }}\n          className=\"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm\"\n          onClick={() => setShowForm(false)}\n        >\n          <motion.div\n            initial={{ opacity: 0, scale: 0.9, y: 20 }}\n            animate={{ opacity: 1, scale: 1, y: 0 }}\n            exit={{ opacity: 0, scale: 0.9, y: 20 }}\n            transition={{ type: \"spring\", damping: 25, stiffness: 300 }}\n            className=\"relative bg-white rounded-3xl shadow-2xl max-w-md w-full p-8\"\n            onClick={(e) => e.stopPropagation()}\n          >\n            <div className=\"text-center space-y-6\">\n              <div className=\"w-16 h-16 bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-3xl flex items-center justify-center mx-auto\">\n                <Crown className=\"w-8 h-8 text-[var(--color-yellow)]\" />\n              </div>\n              \n              <div>\n                <h3 className=\"text-2xl font-bold text-[var(--color-dark-blue)] mb-2\">Upgrade to Premium</h3>\n                <p className=\"text-[var(--foreground-secondary)]\">Get started with your premium learning journey</p>\n              </div>\n\n              <form className=\"space-y-4\">\n                <input\n                  type=\"email\"\n                  placeholder=\"Enter your email\"\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:border-[var(--color-blue)]\"\n                />\n                <input\n                  type=\"text\"\n                  placeholder=\"Full name\"\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:border-[var(--color-blue)]\"\n                />\n                <motion.button\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                  className=\"w-full bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] text-white py-3 px-6 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300\"\n                >\n                  Start Premium Trial\n                </motion.button>\n              </form>\n\n              <button\n                onClick={() => setShowForm(false)}\n                className=\"text-[var(--foreground-secondary)] hover:text-[var(--color-dark-blue)] transition-colors\"\n              >\n                Maybe later\n              </button>\n            </div>\n          </motion.div>\n        </motion.div>\n      )}\n    </>\n  );\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAJA;;;;;AAMA,MAAM,kBAAkB;IACtB;IACA;IACA;IACA;IACA;CACD;AAEc,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,qBACE;;0BAEE,8OAAC;gBAAQ,WAAU;;kCAEjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;kCAGjB,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAI;wCAClC,aAAa;4CAAE,SAAS;4CAAG,OAAO;wCAAE;wCACpC,YAAY;4CAAE,OAAO;4CAAK,UAAU;wCAAI;wCACxC,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;;0DAEV,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;gDAAK,WAAU;0DAAiD;;;;;;;;;;;;kDAGnE,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;wCACR,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,OAAO;4CAAK,UAAU;wCAAI;wCACxC,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;;4CACX;0DAEC,8OAAC;gDAAK,WAAU;0DAAiH;;;;;;;;;;;;kDAKnI,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;wCACP,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,OAAO;4CAAK,UAAU;wCAAI;wCACxC,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;kDACX;;;;;;;;;;;;0CAMH,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,UAAU;wCAAI;wCAC5B,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;kDAET,gBAAgB,GAAG,CAAC,CAAC,SAAS,sBAC7B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,SAAS;oDAAG,GAAG,CAAC;gDAAG;gDAC9B,aAAa;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAChC,YAAY;oDAAE,OAAO,QAAQ;oDAAK,UAAU;gDAAI;gDAChD,UAAU;oDAAE,MAAM;gDAAK;gDACvB,WAAU;;kEAEV,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,2NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;;;;;;kEAEzB,8OAAC;kEACC,cAAA,8OAAC;4DAAG,WAAU;sEAAoC;;;;;;;;;;;;+CAX/C;;;;;;;;;;kDAkBX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,UAAU;wCAAI;wCAC5B,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEAEb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;8EACjB,8OAAC;oEAAK,WAAU;8EAAgB;;;;;;;;;;;;sEAIlC,8OAAC;;8EACC,8OAAC;oEAAI,WAAU;;wEAAqC;sFAElD,8OAAC;4EAAK,WAAU;sFAAoC;;;;;;;;;;;;8EAEtD,8OAAC;oEAAE,WAAU;8EAAgB;;;;;;;;;;;;sEAI/B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAAgB;;;;;;sFAChC,8OAAC,2NAAA,CAAA,cAAW;4EAAC,WAAU;;;;;;;;;;;;8EAEzB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAAgB;;;;;;sFAChC,8OAAC,2NAAA,CAAA,cAAW;4EAAC,WAAU;;;;;;;;;;;;8EAEzB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAAgB;;;;;;sFAChC,8OAAC,2NAAA,CAAA,cAAW;4EAAC,WAAU;;;;;;;;;;;;8EAEzB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAAgB;;;;;;sFAChC,8OAAC,2NAAA,CAAA,cAAW;4EAAC,WAAU;;;;;;;;;;;;8EAEzB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAAgB;;;;;;sFAChC,8OAAC,2NAAA,CAAA,cAAW;4EAAC,WAAU;;;;;;;;;;;;;;;;;;sEAK3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4DACZ,YAAY;gEAAE,OAAO;4DAAK;4DAC1B,UAAU;gEAAE,OAAO;4DAAK;4DACxB,SAAS,IAAM,YAAY;4DAC3B,WAAU;;8EAEV,8OAAC,gMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;gEAAY;8EAE3B,8OAAC,kNAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;;;;;;;sEAGxB,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;0DAOzC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,GAAG;wDAAC,CAAC;wDAAI;wDAAI,CAAC;qDAAG;gDAAC;gDAC7B,YAAY;oDAAE,UAAU;oDAAG,QAAQ;oDAAU,MAAM;gDAAY;gDAC/D,WAAU;;;;;;0DAEZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,GAAG;wDAAC;wDAAI,CAAC;wDAAI;qDAAG;gDAAC;gDAC5B,YAAY;oDAAE,UAAU;oDAAG,QAAQ;oDAAU,MAAM;gDAAY;gDAC/D,WAAU;;;;;;;;;;;;;;;;;;0CAMhB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAqD;;;;;;8DACpE,8OAAC;oDAAI,WAAU;8DAAgB;;;;;;;;;;;;sDAEjC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAyC;;;;;;8DACxD,8OAAC;oDAAI,WAAU;8DAAgB;;;;;;;;;;;;sDAEjC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAmD;;;;;;8DAClE,8OAAC;oDAAI,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;;;;;;0CAMrC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,OAAO;oCAAG,UAAU;gCAAI;gCACtC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;wCACxB,SAAS,IAAM,YAAY;wCAC3B,WAAU;;0DAEV,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAY;0DAE7B,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;kDAExB,8OAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;;;;;;;;;;;;;YAQ/C,0BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,MAAM;oBAAE,SAAS;gBAAE;gBACnB,WAAU;gBACV,SAAS,IAAM,YAAY;0BAE3B,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,OAAO;wBAAK,GAAG;oBAAG;oBACzC,SAAS;wBAAE,SAAS;wBAAG,OAAO;wBAAG,GAAG;oBAAE;oBACtC,MAAM;wBAAE,SAAS;wBAAG,OAAO;wBAAK,GAAG;oBAAG;oBACtC,YAAY;wBAAE,MAAM;wBAAU,SAAS;wBAAI,WAAW;oBAAI;oBAC1D,WAAU;oBACV,SAAS,CAAC,IAAM,EAAE,eAAe;8BAEjC,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;0CAGnB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAwD;;;;;;kDACtE,8OAAC;wCAAE,WAAU;kDAAqC;;;;;;;;;;;;0CAGpD,8OAAC;gCAAK,WAAU;;kDACd,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;kDAEZ,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;kDAEZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;wCACxB,WAAU;kDACX;;;;;;;;;;;;0CAKH,8OAAC;gCACC,SAAS,IAAM,YAAY;gCAC3B,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}, {"offset": {"line": 4026, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/training/components/TrainingAccessForm.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState } from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport { Shield, User, Mail, Phone, Building, GraduationCap, ArrowRight, CheckCircle } from 'lucide-react';\r\n\r\nexport default function TrainingAccessForm() {\r\n  const [formData, setFormData] = useState({\r\n    name: '',\r\n    organization: '',\r\n    phone: '',\r\n    email: '',\r\n    disclaimer: false,\r\n    terms: false\r\n  });\r\n\r\n  const handleInputChange = (e) => {\r\n    const { name, value, type, checked } = e.target;\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      [name]: type === 'checkbox' ? checked : value\r\n    }));\r\n  };\r\n\r\n  const handleSubmit = (e) => {\r\n    e.preventDefault();\r\n    // Handle form submission\r\n    console.log('Form submitted:', formData);\r\n  };\r\n\r\n  return (\r\n    <section className=\"py-24 bg-gradient-to-br from-[var(--color-dark-blue)] via-[var(--color-dark-blue-hover)] to-[var(--color-dark-blue-bg)] relative overflow-hidden\">\r\n      {/* Background Pattern */}\r\n      <div className=\"absolute inset-0\">\r\n        <div className=\"absolute top-0 left-0 w-full h-full bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.03%22%3E%3Ccircle%20cx%3D%2230%22%20cy%3D%2230%22%20r%3D%221%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-50\"></div>\r\n      </div>\r\n\r\n      <div className=\"relative z-10 container mx-auto px-4 max-w-7xl\">\r\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center\">\r\n          {/* Content */}\r\n          <motion.div \r\n            initial={{ opacity: 0, x: -50 }}\r\n            whileInView={{ opacity: 1, x: 0 }}\r\n            transition={{ duration: 0.8 }}\r\n            viewport={{ once: true }}\r\n            className=\"space-y-8\"\r\n          >\r\n            <div>\r\n              <motion.div \r\n                initial={{ opacity: 0, scale: 0.8 }}\r\n                whileInView={{ opacity: 1, scale: 1 }}\r\n                transition={{ delay: 0.2, duration: 0.6 }}\r\n                viewport={{ once: true }}\r\n                className=\"inline-flex items-center bg-[var(--color-blue)]/20 px-4 py-2 rounded-full mb-6\"\r\n              >\r\n                <Shield className=\"w-4 h-4 text-[var(--color-blue)] mr-2\" />\r\n                <span className=\"text-sm font-medium text-[var(--color-blue)]\">Free Access</span>\r\n              </motion.div>\r\n              \r\n              <motion.h2 \r\n                initial={{ opacity: 0, y: 20 }}\r\n                whileInView={{ opacity: 1, y: 0 }}\r\n                transition={{ delay: 0.4, duration: 0.6 }}\r\n                viewport={{ once: true }}\r\n                className=\"text-4xl lg:text-5xl font-bold text-white mb-6\"\r\n              >\r\n                Start Your\r\n                <span className=\"block bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] bg-clip-text text-transparent\">\r\n                  Training Now!\r\n                </span>\r\n              </motion.h2>\r\n              \r\n              <motion.p \r\n                initial={{ opacity: 0, y: 20 }}\r\n                whileInView={{ opacity: 1, y: 0 }}\r\n                transition={{ delay: 0.6, duration: 0.6 }}\r\n                viewport={{ once: true }}\r\n                className=\"text-xl text-white/80 leading-relaxed mb-8\"\r\n              >\r\n                Become part of the next generation of cybersecurity professionals with SecurityLit. Our industry-aligned training program is designed to equip you with the skills needed to succeed in today's cybersecurity landscape.\r\n              </motion.p>\r\n            </div>\r\n\r\n            {/* Benefits */}\r\n            <div className=\"space-y-4\">\r\n              <div className=\"flex items-center gap-4\">\r\n                <div className=\"w-12 h-12 bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-xl flex items-center justify-center shadow-lg\">\r\n                  <CheckCircle className=\"w-6 h-6 text-white\" />\r\n                </div>\r\n                <div>\r\n                  <h3 className=\"text-white font-semibold text-lg\">Free Access</h3>\r\n                  <p className=\"text-white/70\">Get started with our comprehensive cybersecurity training program at no cost.</p>\r\n                </div>\r\n              </div>\r\n              \r\n              <div className=\"flex items-center gap-4\">\r\n                <div className=\"w-12 h-12 bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-xl flex items-center justify-center shadow-lg\">\r\n                  <GraduationCap className=\"w-6 h-6 text-white\" />\r\n                </div>\r\n                <div>\r\n                  <h3 className=\"text-white font-semibold text-lg\">Industry-Relevant Skills</h3>\r\n                  <p className=\"text-white/70\">Learn the latest tools and techniques used by cybersecurity professionals.</p>\r\n                </div>\r\n              </div>\r\n              \r\n              <div className=\"flex items-center gap-4\">\r\n                <div className=\"w-12 h-12 bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-xl flex items-center justify-center shadow-lg\">\r\n                  <Shield className=\"w-6 h-6 text-white\" />\r\n                </div>\r\n                <div>\r\n                  <h3 className=\"text-white font-semibold text-lg\">Career Ready</h3>\r\n                  <p className=\"text-white/70\">Develop practical skills that prepare you for real-world cybersecurity challenges.</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </motion.div>\r\n\r\n          {/* Form */}\r\n          <motion.div \r\n            initial={{ opacity: 0, x: 50 }}\r\n            whileInView={{ opacity: 1, x: 0 }}\r\n            transition={{ duration: 0.8 }}\r\n            viewport={{ once: true }}\r\n            className=\"relative\"\r\n          >\r\n            <div className=\"bg-white/10 backdrop-blur-xl rounded-3xl p-8 border border-white/20 shadow-2xl\">\r\n              <div className=\"text-center mb-8\">\r\n                <div className=\"w-16 h-16 bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-3xl flex items-center justify-center mx-auto mb-4\">\r\n                  <Shield className=\"w-8 h-8 text-white\" />\r\n                </div>\r\n                <h3 className=\"text-2xl font-bold text-white mb-2\">Sign Up for Free Access</h3>\r\n                <p className=\"text-white/70\">Join our Cybersecurity Training Program</p>\r\n              </div>\r\n\r\n              <form onSubmit={handleSubmit} className=\"space-y-6\">\r\n                <div>\r\n                  <label className=\"block text-white font-medium mb-2\">Name *</label>\r\n                  <div className=\"relative\">\r\n                    <User className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-white/50\" />\r\n                    <input\r\n                      type=\"text\"\r\n                      name=\"name\"\r\n                      value={formData.name}\r\n                      onChange={handleInputChange}\r\n                      required\r\n                      className=\"w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/50 focus:outline-none focus:border-[var(--color-blue)] transition-colors\"\r\n                      placeholder=\"Enter your full name\"\r\n                    />\r\n                  </div>\r\n                </div>\r\n\r\n                <div>\r\n                  <label className=\"block text-white font-medium mb-2\">Name of Organization *</label>\r\n                  <div className=\"relative\">\r\n                    <Building className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-white/50\" />\r\n                    <input\r\n                      type=\"text\"\r\n                      name=\"organization\"\r\n                      value={formData.organization}\r\n                      onChange={handleInputChange}\r\n                      required\r\n                      className=\"w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/50 focus:outline-none focus:border-[var(--color-blue)] transition-colors\"\r\n                      placeholder=\"Enter your organization name\"\r\n                    />\r\n                  </div>\r\n                </div>\r\n\r\n                <div>\r\n                  <label className=\"block text-white font-medium mb-2\">Contact Number</label>\r\n                  <div className=\"relative\">\r\n                    <Phone className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-white/50\" />\r\n                    <input\r\n                      type=\"tel\"\r\n                      name=\"phone\"\r\n                      value={formData.phone}\r\n                      onChange={handleInputChange}\r\n                      className=\"w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/50 focus:outline-none focus:border-[var(--color-blue)] transition-colors\"\r\n                      placeholder=\"Enter your phone number\"\r\n                    />\r\n                  </div>\r\n                </div>\r\n\r\n                <div>\r\n                  <label className=\"block text-white font-medium mb-2\">Work Email *</label>\r\n                  <div className=\"relative\">\r\n                    <Mail className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-white/50\" />\r\n                    <input\r\n                      type=\"email\"\r\n                      name=\"email\"\r\n                      value={formData.email}\r\n                      onChange={handleInputChange}\r\n                      required\r\n                      className=\"w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/50 focus:outline-none focus:border-[var(--color-blue)] transition-colors\"\r\n                      placeholder=\"Enter your work email\"\r\n                    />\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"space-y-4\">\r\n                  <label className=\"flex items-center gap-3 cursor-pointer\">\r\n                    <input\r\n                      type=\"checkbox\"\r\n                      name=\"disclaimer\"\r\n                      checked={formData.disclaimer}\r\n                      onChange={handleInputChange}\r\n                      required\r\n                      className=\"w-4 h-4 text-[var(--color-blue)] bg-white/10 border-white/20 rounded focus:ring-[var(--color-blue)]\"\r\n                    />\r\n                    <span className=\"text-white/80 text-sm\">\r\n                      I consent to receive communications about the security training program\r\n                    </span>\r\n                  </label>\r\n\r\n                  <label className=\"flex items-center gap-3 cursor-pointer\">\r\n                    <input\r\n                      type=\"checkbox\"\r\n                      name=\"terms\"\r\n                      checked={formData.terms}\r\n                      onChange={handleInputChange}\r\n                      required\r\n                      className=\"w-4 h-4 text-[var(--color-blue)] bg-white/10 border-white/20 rounded focus:ring-[var(--color-blue)]\"\r\n                    />\r\n                    <span className=\"text-white/80 text-sm\">\r\n                      I agree to the terms and conditions\r\n                    </span>\r\n                  </label>\r\n                </div>\r\n\r\n                <motion.button\r\n                  whileHover={{ scale: 1.02 }}\r\n                  whileTap={{ scale: 0.98 }}\r\n                  type=\"submit\"\r\n                  className=\"w-full bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] text-white py-4 px-8 rounded-xl font-bold text-lg shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center gap-2\"\r\n                >\r\n                  <Shield className=\"w-5 h-5\" />\r\n                  Submit Now\r\n                  <ArrowRight className=\"w-5 h-5\" />\r\n                </motion.button>\r\n              </form>\r\n            </div>\r\n          </motion.div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,cAAc;QACd,OAAO;QACP,OAAO;QACP,YAAY;QACZ,OAAO;IACT;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM;QAC/C,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE,SAAS,aAAa,UAAU;YAC1C,CAAC;IACH;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,yBAAyB;QACzB,QAAQ,GAAG,CAAC,mBAAmB;IACjC;IAEA,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,8OAAC;;sDACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,SAAS;gDAAG,OAAO;4CAAI;4CAClC,aAAa;gDAAE,SAAS;gDAAG,OAAO;4CAAE;4CACpC,YAAY;gDAAE,OAAO;gDAAK,UAAU;4CAAI;4CACxC,UAAU;gDAAE,MAAM;4CAAK;4CACvB,WAAU;;8DAEV,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;oDAAK,WAAU;8DAA+C;;;;;;;;;;;;sDAGjE,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;4CACR,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,aAAa;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAChC,YAAY;gDAAE,OAAO;gDAAK,UAAU;4CAAI;4CACxC,UAAU;gDAAE,MAAM;4CAAK;4CACvB,WAAU;;gDACX;8DAEC,8OAAC;oDAAK,WAAU;8DAAiH;;;;;;;;;;;;sDAKnI,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;4CACP,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,aAAa;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAChC,YAAY;gDAAE,OAAO;gDAAK,UAAU;4CAAI;4CACxC,UAAU;gDAAE,MAAM;4CAAK;4CACvB,WAAU;sDACX;;;;;;;;;;;;8CAMH,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,2NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;;;;;;8DAEzB,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAmC;;;;;;sEACjD,8OAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;sDAIjC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;;;;;;8DAE3B,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAmC;;;;;;sEACjD,8OAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;sDAIjC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;8DAEpB,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAmC;;;;;;sEACjD,8OAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOrC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;0DAEpB,8OAAC;gDAAG,WAAU;0DAAqC;;;;;;0DACnD,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;kDAG/B,8OAAC;wCAAK,UAAU;wCAAc,WAAU;;0DACtC,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAoC;;;;;;kEACrD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,8OAAC;gEACC,MAAK;gEACL,MAAK;gEACL,OAAO,SAAS,IAAI;gEACpB,UAAU;gEACV,QAAQ;gEACR,WAAU;gEACV,aAAY;;;;;;;;;;;;;;;;;;0DAKlB,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAoC;;;;;;kEACrD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,8OAAC;gEACC,MAAK;gEACL,MAAK;gEACL,OAAO,SAAS,YAAY;gEAC5B,UAAU;gEACV,QAAQ;gEACR,WAAU;gEACV,aAAY;;;;;;;;;;;;;;;;;;0DAKlB,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAoC;;;;;;kEACrD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,8OAAC;gEACC,MAAK;gEACL,MAAK;gEACL,OAAO,SAAS,KAAK;gEACrB,UAAU;gEACV,WAAU;gEACV,aAAY;;;;;;;;;;;;;;;;;;0DAKlB,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAoC;;;;;;kEACrD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,8OAAC;gEACC,MAAK;gEACL,MAAK;gEACL,OAAO,SAAS,KAAK;gEACrB,UAAU;gEACV,QAAQ;gEACR,WAAU;gEACV,aAAY;;;;;;;;;;;;;;;;;;0DAKlB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAM,WAAU;;0EACf,8OAAC;gEACC,MAAK;gEACL,MAAK;gEACL,SAAS,SAAS,UAAU;gEAC5B,UAAU;gEACV,QAAQ;gEACR,WAAU;;;;;;0EAEZ,8OAAC;gEAAK,WAAU;0EAAwB;;;;;;;;;;;;kEAK1C,8OAAC;wDAAM,WAAU;;0EACf,8OAAC;gEACC,MAAK;gEACL,MAAK;gEACL,SAAS,SAAS,KAAK;gEACvB,UAAU;gEACV,QAAQ;gEACR,WAAU;;;;;;0EAEZ,8OAAC;gEAAK,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;0DAM5C,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gDACZ,YAAY;oDAAE,OAAO;gDAAK;gDAC1B,UAAU;oDAAE,OAAO;gDAAK;gDACxB,MAAK;gDACL,WAAU;;kEAEV,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAY;kEAE9B,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxC", "debugId": null}}]}