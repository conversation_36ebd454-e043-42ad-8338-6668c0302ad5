"use client";
import React from "react";
import Image from "next/image";
// import Button from "@/app/common/buttons/Button";

const AboutUsHero = () => {
  // Arrow icon for buttons (not used in this JSX but kept for future use)
  const arrowIcon = (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
      <path fillRule="evenodd" d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
    </svg>
  );

  return (
    // Removed `min-h-screen` and `flex items-center` to reduce excessive vertical space.
    // Spacing is now controlled by responsive padding on the child container below.
    <div className="bg-slate-50">
      {/* Replaced fixed `py-12` with responsive padding for better control across screen sizes. */}
      <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 sm:py-20 lg:py-24">
        <div className="w-full grid grid-cols-1 lg:grid-cols-2 gap-y-12 lg:gap-y-0 lg:gap-x-8 items-center">
          {/* Left Hero Section */}
          <div className="space-y-4 z-10 w-full max-w-lg">
            <div className="space-y-3">
              {/* COMPANY label */}
              <div className="uppercase text-ctb-blue-350 font-semibold tracking-widest text-xs sm:text-sm text-left">Company</div>
              <h1 className="font-[400] leading-tight text-ctb-light-blue text-left tracking-tight">
                <span className="block text-2xl xs:text-3xl sm:text-4xl md:text-5xl lg:text-5xl mb-8">
                  About Capture the Bug
                </span>
              </h1>
              {/* Subheading */}
              <h2 className="font-semibold text-ctb-blue-350 text-left text-lg sm:text-xl md:text-2xl lg:text-3xl leading-snug mt-4">
                Cyber threats don&apos;t wait - and neither should your security.
              </h2>
              <p className="text-base sm:text-lg md:text-xl text-gray-600 leading-relaxed tracking-normal max-w-full md:max-w-[600px] text-left mt-4">
                As cyberattacks are growing everyday, companies need to protect their systems and customers&apos; data more than ever. We believe there is a better way to address cybersecurity. That&apos;s why Capture the Bug was born.
              </p>
            </div>
          </div>

          {/* Right Dashboard Image Section - Responsive */}
          <div className="flex justify-center lg:justify-end items-center w-full h-full">
            <div className="relative w-full max-w-[360px] sm:max-w-[440px] md:max-w-[520px] lg:max-w-[580px] xl:max-w-[640px] h-[240px] sm:h-[320px] md:h-[400px] lg:h-[460px] xl:h-[520px] rounded-xl overflow-hidden">
              <Image
                src="/images/global-cybersecurity-network-map.png"
                alt="Global cybersecurity network visualization showing Capture The Bug's worldwide penetration testing and security assessment capabilities"
                fill
                priority
                className="object-cover object-top rounded-xl shadow-2xl"
                sizes="(max-width: 640px) 360px, (max-width: 768px) 440px, (max-width: 1024px) 520px, (max-width: 1280px) 580px, 640px"
                quality={90}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AboutUsHero;
