import Enterprise from './enterprise';

export const metadata = {
  title: "Capture The Bug | Security Testing for Enterprise",
  description: "Scalable, expert-led PTaaS for enterprises. Capture The Bug delivers precise, controlled penetration testing built for complex infrastructures.",
  keywords: "enterprise penetration testing, scalable PTaaS, enterprise cybersecurity, penetration testing for large organizations, enterprise-grade security testing, Capture The Bug, compliance-driven pentesting",
  robots: "index, follow",
  icons: {
    icon: '/favicon.ico',
  },
  openGraph: {
    title: "Capture The Bug | Enterprise ",
    type: "website",
    url: "https://capturethebug.xyz/Company-size/Enterprise",
    description: "Scalable, expert-led PTaaS for enterprises. Capture The Bug delivers precise, controlled penetration testing built for complex infrastructures.",
    images: "https://i.ibb.co/27Y2JrzC/Screenshot-2025-06-18-220819.png",
  },
  twitter: {
    card: 'summary_large_image',
    title: "Capture The Bug | Enterprise",
    description: "Scalable, expert-led PTaaS for enterprises. Capture The Bug delivers precise, controlled penetration testing built for complex infrastructures.",
    images: "https://i.ibb.co/27Y2JrzC/Screenshot-2025-06-18-220819.png",
  }
};

export default function Page() {
  return <Enterprise />;
}
