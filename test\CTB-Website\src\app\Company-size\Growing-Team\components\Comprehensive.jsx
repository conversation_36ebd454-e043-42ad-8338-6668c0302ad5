"use client";
import React from "react";
import { ArrowRight, Monitor, RotateCcw, Zap, FileCheck } from "lucide-react";

export default function ComprehensiveSection() {
  const features = [
    {
      icon: Monitor,
      emoji: "📊",
      title: "Real-Time Pentest Dashboard",
      subtitle: "for Startups",
      description: "Get instant visibility into vulnerabilities-no waiting for static PDFs. View CVSS scores, affected endpoints, triage status, and fix guidance in one always-on dashboard.",
      linkText: "Explore how real-time visibility works",
      linkUrl: "/dashboard-demo",
      gradient: "from-blue-50 to-indigo-50",
      iconColor: "text-blue-600"
    },
    {
      icon: RotateCcw,
      emoji: "♻️", 
      title: "Unlimited Retesting",
      subtitle: "for Faster Fix Cycles",
      description: "Fix and validate on your timeline. Capture The Bug offers unlimited retesting and follow-ups at no extra cost-built for agile teams and CI/CD workflows.",
      linkText: "Learn about our retest process",
      linkUrl: "/retest-process",
      gradient: "from-purple-50 to-pink-50",
      iconColor: "text-purple-600"
    },
    {
      icon: Zap,
      emoji: "🔌",
      title: "Jira, Slack & GitHub",
      subtitle: "Integrations Built-In",
      description: "Send findings to engineers where they already work. Automate workflows with Jira tickets, Slack alerts, and GitHub pull request annotations-zero context switching.",
      linkText: "See supported integrations",
      linkUrl: "/integrations",
      gradient: "from-green-50 to-emerald-50",
      iconColor: "text-green-600"
    },
    {
      icon: FileCheck,
      emoji: "📄",
      title: "SOC 2 & ISO 27001-Ready",
      subtitle: "Reports",
      description: "Generate exportable reports mapped to security frameworks like SOC 2, ISO 27001, GDPR, CIS, and HIPAA. Perfect for vendor due diligence, audits, or customer reviews.",
      linkText: "View sample compliance report",
      linkUrl: "/Download-Sample-Report",
      gradient: "from-orange-50 to-red-50",
      iconColor: "text-orange-600"
    }
  ];

  return (
    <div className="bg-gradient-to-br from-gray-50 to-white py-12 sm:py-16 md:py-24 md:pt-80 pt-[500px] -mt-[480px] md:-mt-64 ">
      <div className="container mx-auto px-4 sm:px-6 md:px-8">
        
        {/* Section Header */}
        <div className="text-center mb-12 sm:mb-16">
          <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-3 sm:mb-4 px-2">
            Everything you need to scale securely
          </h2>
          <p className="text-base sm:text-lg text-gray-600 max-w-2xl mx-auto px-4">
            From real-time dashboards to compliance reports, our PTaaS platform gives startups the tools to move fast without breaking security.
          </p>
        </div>

        {/* Cards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6 lg:gap-8 max-w-7xl mx-auto px-2 sm:px-0">
          {features.map((feature, index) => {
            const IconComponent = feature.icon;
            return (
              <div 
                key={index}
                className={`group relative bg-gradient-to-br ${feature.gradient} border border-gray-100 rounded-xl sm:rounded-2xl p-6 sm:p-8 hover:shadow-xl transition-all duration-300 hover:-translate-y-1 h-full flex flex-col`}
              >
                {/* Icon and Emoji */}
                <div className="flex items-center justify-between mb-4 sm:mb-6">
                  <div className={`p-2 sm:p-3 rounded-lg sm:rounded-xl bg-white shadow-sm ${feature.iconColor}`}>
                    <IconComponent className="h-6 w-6 sm:h-8 sm:w-8" />
                  </div>
                  <div className="text-2xl sm:text-3xl opacity-70">
                    {feature.emoji}
                  </div>
                </div>

                {/* Content */}
                <div className="flex-grow">
                  <h3 className="text-lg sm:text-xl md:text-2xl font-bold text-gray-900 mb-1 sm:mb-2">
                    {feature.title}
                  </h3>
                  <div className="text-base sm:text-lg font-medium text-gray-700 mb-3 sm:mb-4">
                    {feature.subtitle}
                  </div>
                  <p className="text-gray-600 text-sm sm:text-base leading-relaxed mb-4 sm:mb-6 flex-grow">
                    {feature.description}
                  </p>
                </div>

                {/* CTA Button */}
                <a
                  href={feature.linkUrl}
                  className="group-hover:text-purple-700 text-purple-600 font-semibold flex items-center justify-start transition-colors duration-200 mt-auto hover:underline text-sm sm:text-base group relative overflow-hidden"
                >
                  <span className="absolute inset-0 w-full h-full bg-purple-100/20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out"></span>
                  <span className="relative z-10 flex items-center">
                    {feature.linkText}
                    <span className="ml-2 group-hover:translate-x-1 transition-transform duration-200">
                      <ArrowRight className="h-3 w-3 sm:h-4 sm:w-4" />
                    </span>
                  </span>
                </a>

                {/* Subtle hover effect overlay */}
                <div className="absolute inset-0 rounded-2xl bg-white opacity-0 group-hover:opacity-5 transition-opacity duration-300 pointer-events-none"></div>
              </div>
            );
          })}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-12 sm:mt-16 px-4">
          <a
            href="/Request-Demo"
            className="inline-flex items-center px-4 sm:px-6 py-2 sm:py-3 bg-purple-600 text-white font-semibold rounded-xl shadow-lg hover:bg-purple-700 hover:shadow-xl transition-all duration-200 text-sm sm:text-base group relative overflow-hidden"
          >
            <span className="absolute inset-0 w-full h-full bg-white/10 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out"></span>
            <span className="relative z-10 flex items-center">
              Start your pentest today
              <span className="ml-2 group-hover:translate-x-1 transition-transform duration-200">
                <ArrowRight className="h-4 w-4 sm:h-5 sm:w-5" />
              </span>
            </span>
          </a>
        </div>
      </div>
    </div>
  );
}