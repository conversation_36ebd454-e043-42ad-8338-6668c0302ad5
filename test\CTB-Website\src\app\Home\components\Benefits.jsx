"use client";
import Button from "../../common/buttons/Button";
import ModalComponent from '../components/Modal';
import Image from "next/image";
import React,{ useState }  from "react";
import { sendEmail } from "../../utils/send-email";

import { isBusinessEmail } from "@/app/utils/emailValidator";

const Benefits = React.memo(function Benefits() {
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    companyEmail: "",
  });
  
  const [isModalOpen, setIsModalOpen] = useState(false);

  const openModal = () => setIsModalOpen(true);
  const closeModal = () => setIsModalOpen(false);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    console.log("Form submitted:", formData);

    const emailContent = `
      Name: ${formData.firstName} ${formData.lastName}\n
      Company Email: ${formData.companyEmail}\n
    `;

    closeModal(); // Close modal after submission

    // Start the download immediately
    const link = document.createElement("a");
    link.href = "/PtaaS for Saas- A deep dive.pdf";
    link.download = "PtaaS for Saas- A deep dive.pdf";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Send email in background
    sendEmail("PTaaS Guide Download", emailContent)
      .then(() => {
        console.log("Email sent successfully");
      })
      .catch((error) => {
        console.error("Error sending email:", error);
        alert("Failed to send email.");
      });
  };

  // Arrow icon for button
  const arrowIcon = (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
      <path fillRule="evenodd" d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
    </svg>
  );

  return (
    <div className="px-4 md:px-16 lg:px-20 py-16 md:py-20 bg-gradient-to-b from-white to-[#f8faff]">
      <div className="max-w-7xl mx-auto">
        <div className="flex flex-col md:flex-row items-start justify-between gap-8 md:gap-12">
          <div className="w-full md:w-[45%] text-left relative">
            {/* Decorative element */}
            <div className="absolute -left-4 md:-left-8 top-0 h-full w-1 bg-gradient-to-b from-secondary-blue via-primary-blue to-transparent opacity-70 rounded-full"></div>
            
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 md:mb-8 text-tertiary-blue">
              Continuous <span className="text-primary-blue">Penetration Testing</span>
            </h2>
            
            <p className="mb-6 text-base md:text-lg font-medium text-slate-700 leading-relaxed">
              Explore how Capture The Bug&apos;s PTaaS model delivers continuous
              security testing, seamless integration with software development
              cycles, and enhances security posture while maximizing ROI and
              reducing costs for SaaS.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 md:gap-6 pt-4 md:pt-6">
              <Button 
                href="/Pricing" 
                variant="primary"
                size="md"
              >
                Calculate Price
              </Button>
              
              <Button 
                href="/Request-Demo" 
                variant="secondary"
                size="md"
                rightIcon={arrowIcon}
              >
                Request Demo
              </Button>
            </div>
          </div>

          <div className="w-full md:w-1/2 md:pl-6 lg:pl-10 mt-8 md:mt-0">
            <div 
              onClick={openModal} 
              className="cursor-pointer relative group rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
            >
              <div className="absolute inset-0 bg-gradient-to-tr from-primary-blue/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <Image
                src="/images/home-2.png"
                alt="Dashboard"
                width={1000}
                height={600}
                className="w-full object-cover"
              />
              <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-tertiary-blue/80 to-transparent p-4 md:p-6 transform translate-y-full group-hover:translate-y-0 transition-transform duration-300">
                <p className="text-white text-sm md:text-base font-medium">Click to download our comprehensive PTaaS guide</p>
              </div>
            </div>
            <ModalComponent
              isOpen={isModalOpen}
              onRequestClose={closeModal}
              formData={formData}
              handleChange={handleChange}
              handleSubmit={handleSubmit}
            />
          </div>
        </div>
      </div>
    </div>
  );
});

export default Benefits;
