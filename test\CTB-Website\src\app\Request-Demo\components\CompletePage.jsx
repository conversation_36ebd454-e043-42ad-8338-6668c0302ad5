"use client";
import React, { useState } from "react";
import Button from "@/app/common/buttons/Button";
import { sendEmail } from "../../utils/send-email";
import { useRouter } from "next/navigation";
import Image from "next/image";
import Link from "next/link";

const VantaStyleDemo = () => {
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    companyEmail: "",
    companyName: "",
    phoneNumber: "",
    industry: "",
    message: "",
    stayInformed: false,
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const router = useRouter();
  const [apiError, setApiError] = useState("");
  const [success, setSuccess] = useState("");
  const [canDownload, setCanDownload] = useState(false);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (isSubmitting) return;
    setIsSubmitting(true);
    setApiError("");
    setSuccess("");
    setCanDownload(false);
    try {
      // Make the API request to store submission and send email
      const response = await fetch("/api/submissions", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          firstName: formData.firstName,
          lastName: formData.lastName,
          businessEmail: formData.companyEmail,
          company: formData.companyName,
          phoneNumber: formData.phoneNumber,
          industry: formData.industry,
          message: formData.message,
          stayInformed: formData.stayInformed,
          formType: 'request-demo',
          subject: 'New Demo Request'
        }),
      });
     
      const result = await response.json();
      if (response.ok && result.success) {
        // Clear form fields on success
        setFormData({
          firstName: "",
          lastName: "",
          companyEmail: "",
          companyName: "",
          phoneNumber: "",
          industry: "",
          message: "",
          stayInformed: false,
        });
       
        setSuccess("Thank you! Your request has been submitted.");
        setCanDownload(true);
      } else {
        setApiError(result.error || "Something went wrong. Please try again.");
      }
    } catch (err) {
      console.error("Submission error:", err);
      setApiError("Network error. Please try again later.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className=" min-h-[calc(100vh-64px)] bg-gradient-to-br from-slate-50 to-blue-50">
      
      <div className="container mx-auto px-4 sm:px-6 md:px-8 lg:px-20 py-16 sm:py-24 lg:py-26">
        <div className="grid lg:grid-cols-2 gap-8 md:gap-12 lg:gap-16 xl:gap-24 items-start max-w-[2000px] mx-auto">
          
          {/* Left Column - Content */}
          <div className="space-y-6 lg:space-y-8 lg:px-4 lg:sticky lg:top-24 max-w-[600px] mx-auto lg:mx-0">
            <div className="space-y-4 md:space-y-6">
              <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-[500] text-slate-900 leading-tight">
                The most efficient way to  
                <span className="text-[#027bfc] font-[600]"> launch a pentest</span>
              </h1>

              <p>
                  <span className="font-semibold">Book your pentest in minutes - go live in under 2 weeks. </span> 
                  </p>
              
              <div className="space-y-3 md:space-y-4 text-sm sm:text-base md:text-lg text-slate-700">
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-[#062575] rounded-full mt-2 flex-shrink-0"></div>
                  <p>
                    <span className="font-semibold">On-demand pentesting</span> with full visibility via our PTaaS platform
                  </p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-[#062575] rounded-full mt-3 flex-shrink-0"></div>
                  <p>
                    <span className="font-semibold">Real humans for edge cases</span> – not just automation
                  </p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-[#062575] rounded-full mt-3 flex-shrink-0"></div>
                  <p>
                    <span className="font-semibold">Built-in compliance mapping </span> for ISO 27001, SOC 2, PCI-DSS, and more
                  </p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-[#062575] rounded-full mt-2 flex-shrink-0"></div>
                  <p>
                    <span className="font-semibold">Direct access to security engineers  </span> for remediation support
                  </p>
                </div>
              </div>
              
              <div className="pt-2 md:pt-4">
                <p className="text-sm sm:text-base md:text-lg text-slate-700">
                  The leading security compliance solution trusted by over{" "}
                  <span className="font-bold text-slate-900">10,000 customers.</span>
                </p>
              </div>

              <p>
                  <span className="font-semibold">Trusted by high-growth teams and enterprises across the globe. </span>
                  </p>
            </div>
            
            {/* Company Logos */}
            <div className="grid grid-cols-2 sm:grid-cols-3 gap-3 sm:gap-4 md:gap-6 items-center  ">
              <div className="relative h-10 scale-[100%] w-full">
                <Image
                  src="/images/paysauce_logo.png"
                  alt="Company 1"
                  fill
                  className="object-contain max-w-full max-h-full scale-100"
                />
              </div>
              <div className="relative h-8 w-full">
                <Image
                src= '/images/lawvu.jpg' 
                  alt="Company 2"
                  fill
                  className="object-contain"
                />
              </div>
              <div className="relative h-8 w-full">
            <Image
  src="/images/EROAD_Logo.webp"
  alt="Company 3"
  className="object-contain max-w-full max-h-full scale-100"
  fill
/>

              </div>
              <div className="relative h-8 w-full">
                <Image
                  src='/images/Cotiss_Logo.svg'
                  alt="Company 4"
                  fill
                  className="object-contain"
                />
              </div>
              <div className="relative h-8 w-full">
                <Image
                  src='/images/blackpearl_logo.png'
                  alt="Company 5"
                  fill
                  className="object-contain"
                />
              </div>
              <div className="relative h-8 w-full">
                <Image
                  src='/images/whiparound_logo.png'
                  alt="Company 6"
                  fill
                  className="object-contain"
                />
              </div>
            </div>
            
            
          </div>

          {/* Right Column - Form */}
          <div className="w-full max-w-[500px] sm:max-w-[600px] md:max-w-[700px] lg:max-w-none mx-auto lg:mx-0">
            <div className="bg-white rounded-xl sm:rounded-2xl shadow-lg sm:shadow-xl p-4 sm:p-6 lg:p-8">
              <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-5 md:space-y-6">
                
                {/* Name Fields */}
                <div className="grid sm:grid-cols-2 gap-3 sm:gap-4">
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-2">
                      First Name *
                    </label>
                    <input
                      type="text"
                      name="firstName"
                      value={formData.firstName}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-[#062575] focus:border-transparent transition-all"
                      placeholder="First Name"
                      required
                      disabled={isSubmitting}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-2">
                      Last Name *
                    </label>
                    <input
                      type="text"
                      name="lastName"
                      value={formData.lastName}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-[#062575] focus:border-transparent transition-all"
                      placeholder="Last Name"
                      required
                      disabled={isSubmitting}
                    />
                  </div>
                </div>

                {/* Work Email */}
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1.5 sm:mb-2">
                    Work email *
                  </label>
                  <input
                    type="email"
                    name="companyEmail"
                    value={formData.companyEmail}
                    onChange={handleChange}
                    className="w-full px-3 sm:px-4 py-2.5 sm:py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-[#062575] focus:border-transparent transition-all text-sm sm:text-base"
                    placeholder="<EMAIL>"
                    required
                    disabled={isSubmitting}
                  />
                </div>

                {/* Company Name */}
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1.5 sm:mb-2">
                    Company Name *
                  </label>
                  <input
                    type="text"
                    name="companyName"
                    value={formData.companyName}
                    onChange={handleChange}
                    className="w-full px-3 sm:px-4 py-2.5 sm:py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-[#062575] focus:border-transparent transition-all text-sm sm:text-base"
                    placeholder="Company Name"
                    required
                    disabled={isSubmitting}
                  />
                </div>

                {/* Phone Number */}
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1.5 sm:mb-2">
                    Phone Number *
                  </label>
                  <input
                    type="tel"
                    name="phoneNumber"
                    value={formData.phoneNumber}
                    onChange={handleChange}
                    className="w-full px-3 sm:px-4 py-2.5 sm:py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-[#062575] focus:border-transparent transition-all text-sm sm:text-base"
                    placeholder="Enter number"
                    required
                    disabled={isSubmitting}
                  />
                </div>

                {/* Industry Dropdown */}
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1.5 sm:mb-2">
                    Industry *
                  </label>
                  <select
                    name="industry"
                    value={formData.industry}
                    onChange={handleChange}
                    className="w-full px-3 sm:px-4 py-2.5 sm:py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-[#062575] focus:border-transparent transition-all bg-white text-sm sm:text-base"
                    required
                    disabled={isSubmitting}
                  >
                    <option value="">Please select</option>
                    <option value="Advertising & Media, Publishing">Advertising & Media, Publishing</option>
                    <option value="Auto">Auto</option>
                    <option value="Aviation, Railways">Aviation, Railways</option>
                    <option value="Finance / Fintech / Payments">Finance / Fintech / Payments</option>
                    <option value="Software Dev (B2B)">Software Dev (B2B)</option>
                    <option value="IT Services">IT Services</option>
                    <option value="Healthcare">Healthcare</option>
                    <option value="Education / Edtech">Education / Edtech</option>
                    <option value="Government">Government</option>
                    <option value="Other">Other</option>
                  </select>
                </div>

                {/* Message */}
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1.5 sm:mb-2">
                    Message *
                  </label>
                  <textarea
                    name="message"
                    value={formData.message}
                    onChange={handleChange}
                    rows="4"
                    className="w-full px-3 sm:px-4 py-2.5 sm:py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-[#062575] focus:border-transparent transition-all resize-none text-sm sm:text-base"
                    placeholder="Tell us about your compliance and security needs..."
                    required
                    disabled={isSubmitting}
                  ></textarea>
                </div>

                {/* Checkbox */}
                <div className="flex items-center space-x-2 sm:space-x-3">
                  <input
                    type="checkbox"
                    name="stayInformed"
                    checked={formData.stayInformed}
                    onChange={handleChange}
                    className="h-4 w-4 text-[#062575] focus:ring-[#062575] border-slate-300 rounded"
                    disabled={isSubmitting}
                  />
                  <label className="text-xs sm:text-sm text-slate-600">
                    I want to stay informed about CTB updates
                  </label>
                </div>

                {/* Privacy Policy */}
                <p className="text-xs text-slate-500">
                  By submitting, I agree to CTB&apos;s <Link href="/Useful-Links/Privacy-Policy" className="text-[#062575] hover:text-[#027bfc] underline">Privacy Policy</Link>.
                </p>

                {/* Submit Button */}
                <Button
                  type="submit"
                  variant="primary"
                  fullWidth={true}
                  disabled={isSubmitting}
                  className="font-semibold py-3 sm:py-4 px-6 disabled:opacity-50 disabled:cursor-not-allowed text-sm sm:text-base"
                >
                  {isSubmitting ? 'Submitting...' : 'Request a demo'}
                </Button>

                {success && <div className="text-green-600 text-sm font-semibold">{success}</div>}
                {apiError && <div className="text-red-600 text-sm font-semibold">{apiError}</div>}
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VantaStyleDemo;
