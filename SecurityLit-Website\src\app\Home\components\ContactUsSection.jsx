"use client";

import React, { useState } from "react";
import { ArrowRight, Mail, Phone, MapPin } from "lucide-react";

export function ContactUsSection() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    company: '',
    message: ''
  });

  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setTimeout(() => {
      setIsSubmitting(false);
      alert('Thank you! We\'ll get back to you within 24 hours.');
      setFormData({ name: '', email: '', company: '', message: '' });
    }, 2000);
  };

  return (
    <section className="py-16 bg-gray-50 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0">
        <div className="absolute top-0 right-1/4 w-72 h-72 bg-[var(--color-blue)]/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 left-1/4 w-96 h-96 bg-[var(--color-yellow)]/5 rounded-full blur-3xl"></div>
      </div>

      <div className="w-full max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        {/* Header Section */}
        <div className="text-center mb-16 fade-in-up">
          <div className="inline-flex items-center bg-[var(--color-blue)]/10 px-4 py-2 rounded-full mb-6">
            <div className="w-2 h-2 bg-[var(--color-blue)] rounded-full mr-2"></div>
            <span className="text-sm font-medium text-[var(--color-blue)]">Get In Touch</span>
          </div>
          
          <h2 className="text-3xl md:text-4xl font-bold font-bold text-[var(--color-dark-blue)] mb-6 leading-tight">
            Ready to
            <span className="block text-[var(--color-blue)]">
              Secure Your Business?
            </span>
          </h2>
          
          <p className="text-xl text-[var(--color-dark-blue)]/70 max-w-3xl mx-auto leading-relaxed">
            Connect with our cybersecurity experts for a free consultation and discover how we can protect your organization.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12 items-start">
          {/* Left: Content */}
          <div className="space-y-8">
            <div className="bg-white rounded-2xl p-8 shadow-md border border-gray-100 fade-in-up">
              <div className="flex items-center gap-4 mb-6">
                <div className="w-14 h-14 bg-[var(--color-blue)] rounded-xl flex items-center justify-center shadow-lg">
                  <Mail className="w-7 h-7 text-white" />
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-[var(--color-dark-blue)]">Contact Information</h3>
                  <p className="text-[var(--color-dark-blue)]/70">Get in touch with our team</p>
                </div>
              </div>
              
              <div className="space-y-6">
                <div className="flex items-center gap-4">
                  <div className="w-10 h-10 bg-[var(--color-blue)]/10 rounded-lg flex items-center justify-center">
                    <Mail className="w-5 h-5 text-[var(--color-blue)]" />
                  </div>
                  <div>
                    <p className="font-semibold text-[var(--color-dark-blue)]">Email</p>
                    <p className="text-[var(--color-blue)]"><EMAIL></p>
                  </div>
                </div>
                
                <div className="flex items-center gap-4">
                  <div className="w-10 h-10 bg-[var(--color-blue)]/10 rounded-lg flex items-center justify-center">
                    <Phone className="w-5 h-5 text-[var(--color-blue)]" />
                  </div>
                  <div>
                    <p className="font-semibold text-[var(--color-dark-blue)]">Phone</p>
                    <p className="text-[var(--color-blue)]">+91 8527 800769</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-4">
                  <div className="w-10 h-10 bg-[var(--color-blue)]/10 rounded-lg flex items-center justify-center">
                    <MapPin className="w-5 h-5 text-[var(--color-blue)]" />
                  </div>
                  <div>
                    <p className="font-semibold text-[var(--color-dark-blue)]">Office</p>
                    <p className="text-[var(--color-dark-blue)]/70">Gurugram, Haryana, India</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-[var(--color-dark-blue)] rounded-2xl p-8 text-white fade-in-up">
              <h4 className="text-xl font-bold mb-4">Why Choose SecurityLit?</h4>
              <ul className="space-y-3 text-white">
                <li className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-white rounded-full"></div>
                  <span>Free initial consultation</span>
                </li>
                <li className="flex items-center gap-3">
                  <div className="w-2 h-2  bg-white rounded-full"></div>
                  <span>24/7 expert support</span>
                </li>
                <li className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-white rounded-full"></div>
                  <span>Customized security solutions</span>
                </li>
                <li className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-white rounded-full"></div>
                  <span>Proven track record</span>
                </li>
              </ul>
            </div>
          </div>

          {/* Right: Form */}
          <div className="bg-white rounded-2xl shadow-xl p-8 border border-gray-100 fade-in-up">
            <div className="mb-8">
              <h3 className="text-2xl font-bold text-[var(--color-dark-blue)] mb-2">Send us a message</h3>
              <p className="text-[var(--color-dark-blue)]/70">Fill out the form below and we'll get back to you within 24 hours.</p>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-semibold text-[var(--color-dark-blue)] mb-2">Name *</label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg text-[var(--color-dark-blue)] placeholder-gray-400 focus:outline-none focus:border-[var(--color-blue)] focus:bg-white transition-all"
                    placeholder="Your name"
                  />
                </div>
                <div>
                  <label className="block text-sm font-semibold text-[var(--color-dark-blue)] mb-2">Email *</label>
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg text-[var(--color-dark-blue)] placeholder-gray-400 focus:outline-none focus:border-[var(--color-blue)] focus:bg-white transition-all"
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-semibold text-[var(--color-dark-blue)] mb-2">Company</label>
                <input
                  type="text"
                  name="company"
                  value={formData.company}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg text-[var(--color-dark-blue)] placeholder-gray-400 focus:outline-none focus:border-[var(--color-blue)] focus:bg-white transition-all"
                  placeholder="Your company"
                />
              </div>
              
              <div>
                <label className="block text-sm font-semibold text-[var(--color-dark-blue)] mb-2">Message</label>
                <textarea
                  name="message"
                  value={formData.message}
                  onChange={handleInputChange}
                  rows="4"
                  className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg text-[var(--color-dark-blue)] placeholder-gray-400 focus:outline-none focus:border-[var(--color-blue)] focus:bg-white transition-all resize-none"
                  placeholder="Tell us about your security needs..."
                ></textarea>
              </div>
              
              <button
                type="submit"
                disabled={isSubmitting}
                className="w-full bg-[var(--color-blue)] hover:bg-[var(--color-blue-secondary)] disabled:bg-gray-400 text-white px-8 py-4 rounded-lg font-bold text-lg transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center gap-2"
              >
                {isSubmitting ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                    Sending Message...
                  </>
                ) : (
                  <>
                    Send Message
                    <ArrowRight className="w-5 h-5" />
                  </>
                )}
              </button>
            </form>
          </div>
        </div>
      </div>
    </section>
  );
}

export default ContactUsSection;
