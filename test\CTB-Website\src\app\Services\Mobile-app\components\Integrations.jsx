import React from 'react';
import Image from 'next/image';

const IntegrationCard = ({ logo, text }) => (
  <div className="flex items-center space-x-4 bg-white rounded-full shadow-md p-2 md:p-4">
    <Image src={logo} alt={text} className="h-6" width={24} height={24} />
    <span className="text-sm md:text-base">{text}</span>
  </div>
);

const Integration = () => {
  return (
    <div className="container mx-auto px-6 py-8 md:py-16 flex flex-col justify-center items-center">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between">
        <div className="mb-8 md:mb-0 md:w-1/2">
          <h1 className="text-2xl md:text-4xl font-bold mb-4">
            Connect Capture The Bug With Your Existing Tech Stack And Collaborate Seamlessly
          </h1>
        </div>
        <div className="md:w-1/2">
          <p className="text-sm md:text-xl text-gray-600">
            Let Capture The Bug Find Security Issues And Push Them To Your Project Management Tool For Easier Collaboration And Tracking.
          </p>
        </div>
      </div>
      
      <div className="mt-12 flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4">
        <IntegrationCard 
          logo="/images/slack_logo.png"
          text="Get Notified About New Vulnerabilities In Slack"
        />
        <IntegrationCard 
          logo="/images/jira_logo.png"
          text="Sync Tasks To Your Jira Board"
        />
      </div>
    </div>
  );
};

export default Integration;