{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/common/components/BreadcrumbNavigation.jsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState } from 'react';\r\nimport Link from 'next/link';\r\nimport { ChevronRight, Home, FileText, Users, Settings, MapPin, Shield, Code, Smartphone, Globe, DollarSign, Building, Phone, Download } from 'lucide-react';\r\nimport { motion } from 'framer-motion';\r\n\r\n/**\r\n * Truncated Text Component for Mobile Breadcrumbs\r\n * Shows truncated text on mobile with click to expand functionality\r\n */\r\nconst TruncatedText = ({ text, maxLength = 25, onExpansionChange }) => {\r\n  const [isExpanded, setIsExpanded] = useState(false);\r\n\r\n  const handleToggle = () => {\r\n    const newExpanded = !isExpanded;\r\n    setIsExpanded(newExpanded);\r\n    if (onExpansionChange) {\r\n      onExpansionChange(newExpanded);\r\n    }\r\n  };\r\n\r\n  if (text.length <= maxLength) {\r\n    return <span>{text}</span>;\r\n  }\r\n\r\n  return (\r\n    <span\r\n      className=\"cursor-pointer\"\r\n      onClick={handleToggle}\r\n      title={isExpanded ? \"Click to collapse\" : \"Click to expand\"}\r\n    >\r\n      <span className=\"sm:hidden\">\r\n        {isExpanded ? text : `${text.substring(0, maxLength)}...`}\r\n      </span>\r\n      <span className=\"hidden sm:inline\">\r\n        {text}\r\n      </span>\r\n    </span>\r\n  );\r\n};\r\n\r\n/**\r\n * Structured Data Component for SEO\r\n */\r\nconst BreadcrumbStructuredData = ({ items }) => {\r\n  const structuredData = {\r\n    \"@context\": \"https://schema.org\",\r\n    \"@type\": \"BreadcrumbList\",\r\n    \"itemListElement\": items.map((item, index) => ({\r\n      \"@type\": \"ListItem\",\r\n      \"position\": index + 1,\r\n      \"name\": item.name,\r\n      \"item\": item.current ? undefined : `https://securitylit.com${item.url}`\r\n    }))\r\n  };\r\n\r\n  return (\r\n    <script\r\n      type=\"application/ld+json\"\r\n      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}\r\n    />\r\n  );\r\n};\r\n\r\n/**\r\n * Universal Breadcrumb Navigation Component with SEO structured data\r\n * Provides clear navigation hierarchy and improves SEO across all pages\r\n */\r\nconst BreadcrumbNavigation = ({ items, className = \"\" }) => {\r\n  const [hasExpandedText, setHasExpandedText] = useState(false);\r\n\r\n  // Check if this is a blog page for conditional padding\r\n  const isBlogPage = items?.some(item =>\r\n    item.url?.includes('/Blogs') ||\r\n    item.iconKey === 'blogs' ||\r\n    className?.includes('blog')\r\n  );\r\n\r\n  return (\r\n    <>\r\n      {/* Structured Data for SEO */}\r\n      <BreadcrumbStructuredData items={items} />\r\n      \r\n      {/* Simple Breadcrumb Navigation */}\r\n      <nav\r\n        className={`bg-transparent py-2 sm:py-3 ${\r\n          isBlogPage\r\n            ? hasExpandedText\r\n              ? 'pb-6 sm:pb-4'\r\n              : 'pb-3 sm:pb-4'\r\n            : 'pb-2 sm:pb-3'\r\n        } ${className}`}\r\n        aria-label=\"Breadcrumb navigation\"\r\n      >\r\n        <div className=\"max-w-7xl mx-auto px-4\">\r\n          <motion.ol\r\n            className={`flex items-center space-x-1 text-xs sm:text-sm ${\r\n              className?.includes('text-white')\r\n                ? 'text-white'\r\n                : className?.includes('text-blue-600')\r\n                ? 'text-blue-600'\r\n                : 'text-gray-600'\r\n            }`}\r\n            initial={{ opacity: 0 }}\r\n            animate={{ opacity: 1 }}\r\n            transition={{ duration: 0.4 }}\r\n          >\r\n            {items.map((item, index) => (\r\n              <motion.li\r\n                key={index}\r\n                className=\"flex items-center\"\r\n                initial={{ opacity: 0 }}\r\n                animate={{ opacity: 1 }}\r\n                transition={{ duration: 0.3, delay: index * 0.1 }}\r\n              >\r\n                {index > 0 && (\r\n                  <ChevronRight\r\n                    className={`w-3 h-3 mx-1 ${\r\n                      className?.includes('text-white')\r\n                        ? 'text-white/70'\r\n                        : className?.includes('text-blue-600')\r\n                        ? 'text-blue-400'\r\n                        : 'text-gray-400'\r\n                    }`}\r\n                    aria-hidden=\"true\"\r\n                  />\r\n                )}\r\n\r\n                {item.current ? (\r\n                  <span\r\n                    className={`font-medium flex items-center gap-1 ${\r\n                      className?.includes('text-white')\r\n                        ? 'text-white'\r\n                        : className?.includes('text-blue-600')\r\n                        ? 'text-blue-600'\r\n                        : 'text-gray-900'\r\n                    }`}\r\n                    aria-current=\"page\"\r\n                    title={item.description}\r\n                  >\r\n                    {item.iconKey === 'home' && (\r\n                      <Home className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-blue-600'\r\n                      }`} />\r\n                    )}\r\n                    {item.iconKey === 'blogs' && (\r\n                      <FileText className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-blue-600'\r\n                      }`} />\r\n                    )}\r\n                    {item.iconKey === 'contact-us' && (\r\n                      <Phone className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-blue-600'\r\n                      }`} />\r\n                    )}\r\n                    <TruncatedText\r\n                      text={item.name}\r\n                      onExpansionChange={setHasExpandedText}\r\n                    />\r\n                  </span>\r\n                ) : (\r\n                  <Link\r\n                    href={item.url}\r\n                    className={`transition-colors duration-200 flex items-center gap-1 hover:underline ${\r\n                      className?.includes('text-white')\r\n                        ? 'text-white hover:text-white/80'\r\n                        : className?.includes('text-blue-600')\r\n                        ? 'text-blue-600 hover:text-blue-800'\r\n                        : 'text-blue-600 hover:text-blue-800'\r\n                    }`}\r\n                    title={item.description}\r\n                  >\r\n                    {item.iconKey === 'home' && (\r\n                      <Home className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-current'\r\n                      }`} />\r\n                    )}\r\n                    {item.iconKey === 'blogs' && (\r\n                      <FileText className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-current'\r\n                      }`} />\r\n                    )}\r\n                    {item.iconKey === 'contact-us' && (\r\n                      <Phone className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-current'\r\n                      }`} />\r\n                    )}\r\n                    <TruncatedText\r\n                      text={item.name}\r\n                      onExpansionChange={setHasExpandedText}\r\n                    />\r\n                  </Link>\r\n                )}\r\n              </motion.li>\r\n            ))}\r\n          </motion.ol>\r\n        </div>\r\n      </nav>\r\n    </>\r\n  );\r\n};\r\n\r\n// Client-side helper function for generating breadcrumb items\r\nexport const generateBreadcrumbs = (pageType, params = {}) => {\r\n  const baseBreadcrumb = {\r\n    name: \"Home\",\r\n    url: \"/\",\r\n    iconKey: \"home\",\r\n    description: \"Return to homepage\"\r\n  };\r\n\r\n  const breadcrumbConfigs = {\r\n    blog: (slug) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Blog\",\r\n        url: \"/Blogs\",\r\n        iconKey: \"blogs\",\r\n        description: \"View all blog posts\"\r\n      },\r\n      {\r\n        name: params.title || \"Blog Post\",\r\n        url: `/Blogs/${slug}`,\r\n        current: true,\r\n        description: `${params.title || 'Blog post'} - Cybersecurity insights and penetration testing guidance`\r\n      }\r\n    ],\r\n    \r\n    service: (service) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Services\",\r\n        url: \"/Services\",\r\n        iconKey: \"services\",\r\n        description: \"View all cybersecurity services\"\r\n      },\r\n      {\r\n        name: params.title || service,\r\n        url: `/Services/${service}`,\r\n        current: true,\r\n        iconKey: service,\r\n        description: `${params.title || service} penetration testing services`\r\n      }\r\n    ],\r\n\r\n    location: (country) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Locations\",\r\n        url: \"/Locations\",\r\n        iconKey: \"locations\",\r\n        description: \"View all locations\"\r\n      },\r\n      {\r\n        name: params.countryName || country,\r\n        url: `/Locations/${country}`,\r\n        current: true,\r\n        description: `${params.countryName || country} cybersecurity services`\r\n      }\r\n    ],\r\n\r\n    company: (page) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Company\",\r\n        url: \"/Company\",\r\n        iconKey: \"company\",\r\n        description: \"Learn about SecurityLit\"\r\n      },\r\n      {\r\n        name: params.title || page,\r\n        url: `/Company/${page}`,\r\n        current: true,\r\n        description: params.description || `${page} information`\r\n      }\r\n    ],\r\n\r\n    'company-size': (size) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Company Size\",\r\n        url: \"/Company-size\",\r\n        iconKey: \"company\",\r\n        description: \"Solutions for different company sizes\"\r\n      },\r\n      {\r\n        name: params.title || size,\r\n        url: `/Company-size/${size}`,\r\n        current: true,\r\n        description: params.description || `${size} cybersecurity solutions`\r\n      }\r\n    ],\r\n\r\n    industry: (industry) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Industries\",\r\n        url: \"/Industries\",\r\n        iconKey: \"company\",\r\n        description: \"Industry-specific cybersecurity solutions\"\r\n      },\r\n      {\r\n        name: params.title || industry,\r\n        url: `/Industries/${industry}`,\r\n        current: true,\r\n        description: params.description || `${industry} cybersecurity solutions`\r\n      }\r\n    ],\r\n\r\n    product: (product) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Product\",\r\n        url: \"/Product\",\r\n        iconKey: \"product\",\r\n        description: \"Explore our cybersecurity products\"\r\n      },\r\n      {\r\n        name: params.title || product,\r\n        url: `/Product/${product}`,\r\n        current: true,\r\n        description: params.description || `${product} product information`\r\n      }\r\n    ],\r\n\r\n    simple: (pageName, url) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: pageName,\r\n        url: url,\r\n        current: true,\r\n        iconKey: params.iconKey,\r\n        description: params.description || `${pageName} page`\r\n      }\r\n    ]\r\n  };\r\n\r\n  return breadcrumbConfigs[pageType] || (() => [baseBreadcrumb]);\r\n};\r\n\r\nexport default BreadcrumbNavigation; "], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;;;AAJA;;;;;AAMA;;;CAGC,GACD,MAAM,gBAAgB;QAAC,EAAE,IAAI,EAAE,YAAY,EAAE,EAAE,iBAAiB,EAAE;;IAChE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,eAAe;QACnB,MAAM,cAAc,CAAC;QACrB,cAAc;QACd,IAAI,mBAAmB;YACrB,kBAAkB;QACpB;IACF;IAEA,IAAI,KAAK,MAAM,IAAI,WAAW;QAC5B,qBAAO,6LAAC;sBAAM;;;;;;IAChB;IAEA,qBACE,6LAAC;QACC,WAAU;QACV,SAAS;QACT,OAAO,aAAa,sBAAsB;;0BAE1C,6LAAC;gBAAK,WAAU;0BACb,aAAa,OAAO,AAAC,GAA+B,OAA7B,KAAK,SAAS,CAAC,GAAG,YAAW;;;;;;0BAEvD,6LAAC;gBAAK,WAAU;0BACb;;;;;;;;;;;;AAIT;GA7BM;KAAA;AA+BN;;CAEC,GACD,MAAM,2BAA2B;QAAC,EAAE,KAAK,EAAE;IACzC,MAAM,iBAAiB;QACrB,YAAY;QACZ,SAAS;QACT,mBAAmB,MAAM,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;gBAC7C,SAAS;gBACT,YAAY,QAAQ;gBACpB,QAAQ,KAAK,IAAI;gBACjB,QAAQ,KAAK,OAAO,GAAG,YAAY,AAAC,0BAAkC,OAAT,KAAK,GAAG;YACvE,CAAC;IACH;IAEA,qBACE,6LAAC;QACC,MAAK;QACL,yBAAyB;YAAE,QAAQ,KAAK,SAAS,CAAC;QAAgB;;;;;;AAGxE;MAlBM;AAoBN;;;CAGC,GACD,MAAM,uBAAuB;QAAC,EAAE,KAAK,EAAE,YAAY,EAAE,EAAE;;IACrD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,uDAAuD;IACvD,MAAM,aAAa,kBAAA,4BAAA,MAAO,IAAI,CAAC,CAAA;YAC7B;eAAA,EAAA,YAAA,KAAK,GAAG,cAAR,gCAAA,UAAU,QAAQ,CAAC,cACnB,KAAK,OAAO,KAAK,YACjB,sBAAA,gCAAA,UAAW,QAAQ,CAAC;;IAGtB,qBACE;;0BAEE,6LAAC;gBAAyB,OAAO;;;;;;0BAGjC,6LAAC;gBACC,WAAW,AAAC,+BAMR,OALF,aACI,kBACE,iBACA,iBACF,gBACL,KAAa,OAAV;gBACJ,cAAW;0BAEX,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;wBACR,WAAW,AAAC,kDAMX,OALC,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,iBAChB,eACA,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,oBACpB,kBACA;wBAEN,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,YAAY;4BAAE,UAAU;wBAAI;kCAE3B,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;gCAER,WAAU;gCACV,SAAS;oCAAE,SAAS;gCAAE;gCACtB,SAAS;oCAAE,SAAS;gCAAE;gCACtB,YAAY;oCAAE,UAAU;oCAAK,OAAO,QAAQ;gCAAI;;oCAE/C,QAAQ,mBACP,6LAAC,yNAAA,CAAA,eAAY;wCACX,WAAW,AAAC,gBAMX,OALC,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,iBAChB,kBACA,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,oBACpB,kBACA;wCAEN,eAAY;;;;;;oCAIf,KAAK,OAAO,iBACX,6LAAC;wCACC,WAAW,AAAC,uCAMX,OALC,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,iBAChB,eACA,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,oBACpB,kBACA;wCAEN,gBAAa;wCACb,OAAO,KAAK,WAAW;;4CAEtB,KAAK,OAAO,KAAK,wBAChB,6LAAC,sMAAA,CAAA,OAAI;gDAAC,WAAW,AAAC,WAMjB,OALC,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,iBAChB,eACA,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,oBACpB,kBACA;;;;;;4CAGP,KAAK,OAAO,KAAK,yBAChB,6LAAC,iNAAA,CAAA,WAAQ;gDAAC,WAAW,AAAC,WAMrB,OALC,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,iBAChB,eACA,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,oBACpB,kBACA;;;;;;4CAGP,KAAK,OAAO,KAAK,8BAChB,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAW,AAAC,WAMlB,OALC,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,iBAChB,eACA,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,oBACpB,kBACA;;;;;;0DAGR,6LAAC;gDACC,MAAM,KAAK,IAAI;gDACf,mBAAmB;;;;;;;;;;;iGAIvB,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAM,KAAK,GAAG;wCACd,WAAW,AAAC,0EAMX,OALC,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,iBAChB,mCACA,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,oBACpB,sCACA;wCAEN,OAAO,KAAK,WAAW;;4CAEtB,KAAK,OAAO,KAAK,wBAChB,6LAAC,sMAAA,CAAA,OAAI;gDAAC,WAAW,AAAC,WAMjB,OALC,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,iBAChB,eACA,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,oBACpB,kBACA;;;;;;4CAGP,KAAK,OAAO,KAAK,yBAChB,6LAAC,iNAAA,CAAA,WAAQ;gDAAC,WAAW,AAAC,WAMrB,OALC,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,iBAChB,eACA,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,oBACpB,kBACA;;;;;;4CAGP,KAAK,OAAO,KAAK,8BAChB,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAW,AAAC,WAMlB,OALC,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,iBAChB,eACA,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,oBACpB,kBACA;;;;;;0DAGR,6LAAC;gDACC,MAAM,KAAK,IAAI;gDACf,mBAAmB;;;;;;;;;;;;;+BAxGpB;;;;;;;;;;;;;;;;;;;;;;AAmHrB;IA5JM;MAAA;AA+JC,MAAM,sBAAsB,SAAC;QAAU,0EAAS,CAAC;IACtD,MAAM,iBAAiB;QACrB,MAAM;QACN,KAAK;QACL,SAAS;QACT,aAAa;IACf;IAEA,MAAM,oBAAoB;QACxB,MAAM,CAAC,OAAS;gBACd;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,AAAC,UAAc,OAAL;oBACf,SAAS;oBACT,aAAa,AAAC,GAA8B,OAA5B,OAAO,KAAK,IAAI,aAAY;gBAC9C;aACD;QAED,SAAS,CAAC,UAAY;gBACpB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,AAAC,aAAoB,OAAR;oBAClB,SAAS;oBACT,SAAS;oBACT,aAAa,AAAC,GAA0B,OAAxB,OAAO,KAAK,IAAI,SAAQ;gBAC1C;aACD;QAED,UAAU,CAAC,UAAY;gBACrB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,WAAW,IAAI;oBAC5B,KAAK,AAAC,cAAqB,OAAR;oBACnB,SAAS;oBACT,aAAa,AAAC,GAAgC,OAA9B,OAAO,WAAW,IAAI,SAAQ;gBAChD;aACD;QAED,SAAS,CAAC,OAAS;gBACjB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,AAAC,YAAgB,OAAL;oBACjB,SAAS;oBACT,aAAa,OAAO,WAAW,IAAI,AAAC,GAAO,OAAL,MAAK;gBAC7C;aACD;QAED,gBAAgB,CAAC,OAAS;gBACxB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,AAAC,iBAAqB,OAAL;oBACtB,SAAS;oBACT,aAAa,OAAO,WAAW,IAAI,AAAC,GAAO,OAAL,MAAK;gBAC7C;aACD;QAED,UAAU,CAAC,WAAa;gBACtB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,AAAC,eAAuB,OAAT;oBACpB,SAAS;oBACT,aAAa,OAAO,WAAW,IAAI,AAAC,GAAW,OAAT,UAAS;gBACjD;aACD;QAED,SAAS,CAAC,UAAY;gBACpB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,AAAC,YAAmB,OAAR;oBACjB,SAAS;oBACT,aAAa,OAAO,WAAW,IAAI,AAAC,GAAU,OAAR,SAAQ;gBAChD;aACD;QAED,QAAQ,CAAC,UAAU,MAAQ;gBACzB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,SAAS,OAAO,OAAO;oBACvB,aAAa,OAAO,WAAW,IAAI,AAAC,GAAW,OAAT,UAAS;gBACjD;aACD;IACH;IAEA,OAAO,iBAAiB,CAAC,SAAS,IAAI,CAAC,IAAM;YAAC;SAAe;AAC/D;uCAEe", "debugId": null}}, {"offset": {"line": 412, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/training/components/HeroBento.jsx"], "sourcesContent": ["\"use client\";\n\nimport React from 'react';\nimport { Users, Star, Clock, Award } from 'lucide-react';\nimport BreadcrumbNavigation from '../../common/components/BreadcrumbNavigation';\n\nexport default function HeroBento({ content }) {\n  // Default content for backward compatibility\n  const defaultContent = {\n    breadcrumbItems: [\n      {\n        name: \"Home\",\n        url: \"/\",\n        iconKey: \"home\",\n        description: \"Return to homepage\"\n      },\n      {\n        name: \"Training\",\n        url: \"/training\",\n        current: true,\n        iconKey: \"graduation-cap\",\n        description: \"Explore SecurityLit's cybersecurity training programs\"\n      }\n    ],\n    title: \"Elite Security Training\",\n    subtitle: \"Launch Your Cyber Security Career\",\n    tagline: \"Free and Premium Pathways\",\n    description: \"Dive into the world of penetration testing with our refined program. Designed for aspiring security professionals with complete pentesting skills and real-time live projects.\",\n    keyBenefits: [\n      \"Complete pentesting skills with real-time live projects\",\n      \"Latest tools and topics in cybersecurity\",\n      \"Free and premium pathways available\"\n    ],\n    buttons: [\n      {\n        text: \"Start Free Training\",\n        href: \"#form\",\n        primary: true\n      },\n      {\n        text: \"Upgrade to Premium\",\n        href: \"#premium\",\n        primary: false\n      }\n    ],\n    heroImage: \"/images/p1s1.png\"\n  };\n\n  const heroContent = content || defaultContent;\n  const { breadcrumbItems, title, subtitle, secondaryImage } = heroContent;\n\n  const trustStats = [\n    { value: \"500+\", label: \"Students Trained\", icon: Users },\n    { value: \"98%\", label: \"Success Rate\", icon: Star },\n    { value: \"83\", label: \"Total Lessons\", icon: Clock },\n    { value: \"11\", label: \"Course Sections\", icon: Award }\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-white\">\n      <div className=\"flex flex-col lg:flex-row min-h-screen relative\">\n\n        {/* Left Section - Optimized Content with Rounded Corner */}\n        <div className=\"w-full lg:w-1/2 bg-gradient-to-br from-[var(--color-dark-blue)] to-[var(--color-dark-blue-hover)] relative overflow-hidden lg:rounded-br-[100px]\">\n          {/* Background Pattern */}\n          <div className=\"absolute inset-0 bg-cover bg-center bg-no-repeat opacity-10\"\n               style={{ backgroundImage: 'url(/images/hexagon.svg)' }}>\n          </div>\n          <div className=\"absolute inset-0 bg-[var(--color-dark-blue)]/90 lg:rounded-br-[100px]\"></div>\n          \n          <div className=\"relative z-10 flex justify-center px-4 sm:px-6 lg:px-12 py-8 sm:py-12 lg:pt-20 lg:pb-8 min-h-screen lg:h-full\">\n            <div className=\"max-w-lg w-full flex flex-col justify-center\">\n              {/* Breadcrumb */}\n              <div className=\"mb-4 mt-2 lg:mt-0\">\n                <BreadcrumbNavigation items={breadcrumbItems} className=\"text-white\" />\n              </div>\n\n              {/* Main Heading */}\n              <h1 className=\"text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-4 leading-tight font-poppins\">\n                {title.includes('Security') ? (\n                  <>\n                    Elite Security\n                    <span className=\"block bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] bg-clip-text text-transparent\">\n                      Training\n                    </span>\n                  </>\n                ) : (\n                  title\n                )}\n              </h1>\n\n              {/* Subtitle */}\n              {subtitle && (\n                <h2 className=\"text-lg sm:text-xl lg:text-2xl font-bold text-white mb-6 leading-tight font-poppins\">\n                  {subtitle}\n                </h2>\n              )}\n\n              {/* Description */}\n              <p className=\"text-sm sm:text-base text-white/90 mb-8 leading-relaxed font-roboto\">\n                Dive Into The World Of Penetration Testing With Our Refined Program\n              </p>\n\n              {/* CTA Buttons */}\n              <div className=\"flex flex-col gap-3\">\n                <a\n                  href=\"#form\"\n                  className=\"bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] text-white px-6 py-3 rounded-lg font-semibold text-base transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center gap-2 font-poppins\"\n                >\n                  Enroll Now\n                </a>\n\n                <a\n                  href=\"/CyberSecTraining\"\n                  className=\"bg-white/10 backdrop-blur-sm text-white border-2 border-white/20 hover:bg-white/20 px-6 py-3 rounded-lg font-semibold text-base transition-all duration-300 flex items-center justify-center gap-2 font-poppins\"\n                >\n                  Program Details\n                </a>\n              </div>\n            </div>\n          </div>\n        </div>\n\n\n\n        {/* Right Section - Enhanced Visual Focus */}\n        <div className=\"w-full lg:w-1/2 bg-white\">\n          <div className=\"max-w-2xl mx-auto px-4 sm:px-6 lg:px-12 py-8 sm:py-12 lg:pt-20 lg:pb-8 min-h-screen lg:h-full flex flex-col justify-center\">\n            \n            {/* Enhanced Hero Visual */}\n            <div className=\"relative mb-6\">\n              <div className=\"relative bg-gradient-to-br from-[var(--color-blue)]/10 to-[var(--color-blue-secondary)]/10 rounded-2xl p-4 sm:p-6 lg:p-8 border-2 border-[var(--color-blue)]/20\">\n                <div className=\"absolute inset-0 bg-gradient-to-br from-[var(--color-blue)]/5 to-[var(--color-blue-secondary)]/5 rounded-2xl\"></div>\n                \n                <div className=\"relative z-10 text-center\">\n                  <div className=\"w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-xl flex items-center justify-center mx-auto mb-4 shadow-lg\">\n                    <img\n                      src=\"/SecurityLit_Icon_White.png\"\n                      alt=\"SecurityLit Logo\"\n                      className=\"w-8 h-8 sm:w-12 sm:h-12 object-contain\"\n                    />\n                  </div>\n\n                  <h3 className=\"text-lg sm:text-xl font-bold text-[var(--color-dark-blue)] mb-2\">\n                    SecurityLit Presents\n                  </h3>\n                  <p className=\"text-[var(--foreground-secondary)] text-sm sm:text-base\">\n                    Professional cybersecurity training designed by industry experts\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            {/* Trust Indicators */}\n            <div className=\"grid grid-cols-2 gap-3 sm:gap-4 mb-6\">\n              {trustStats.map((stat, index) => (\n                <div key={index} className=\"text-center\">\n                  <div className=\"w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-br from-[var(--color-blue)]/10 to-[var(--color-blue-secondary)]/10 rounded-lg flex items-center justify-center mx-auto mb-2\">\n                    <stat.icon className=\"w-4 h-4 sm:w-5 sm:h-5 text-[var(--color-blue)]\" />\n                  </div>\n                  <div className=\"text-xl sm:text-2xl lg:text-3xl font-bold text-[var(--color-dark-blue)] mb-1 font-poppins\">\n                    {stat.value}\n                  </div>\n                  <div className=\"text-[var(--foreground-secondary)] text-xs font-medium font-roboto\">\n                    {stat.label}\n                  </div>\n                </div>\n              ))}\n            </div>\n\n\n\n            {secondaryImage && (\n              <div className=\"text-center mt-6\">\n                <img\n                  src={secondaryImage}\n                  alt=\"Cybersecurity Expert\"\n                  className=\"w-full max-w-md mx-auto rounded-lg\"\n                />\n              </div>\n            )}\n\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAMe,SAAS,UAAU,KAAW;QAAX,EAAE,OAAO,EAAE,GAAX;IAChC,6CAA6C;IAC7C,MAAM,iBAAiB;QACrB,iBAAiB;YACf;gBACE,MAAM;gBACN,KAAK;gBACL,SAAS;gBACT,aAAa;YACf;YACA;gBACE,MAAM;gBACN,KAAK;gBACL,SAAS;gBACT,SAAS;gBACT,aAAa;YACf;SACD;QACD,OAAO;QACP,UAAU;QACV,SAAS;QACT,aAAa;QACb,aAAa;YACX;YACA;YACA;SACD;QACD,SAAS;YACP;gBACE,MAAM;gBACN,MAAM;gBACN,SAAS;YACX;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,SAAS;YACX;SACD;QACD,WAAW;IACb;IAEA,MAAM,cAAc,WAAW;IAC/B,MAAM,EAAE,eAAe,EAAE,KAAK,EAAE,QAAQ,EAAE,cAAc,EAAE,GAAG;IAE7D,MAAM,aAAa;QACjB;YAAE,OAAO;YAAQ,OAAO;YAAoB,MAAM,uMAAA,CAAA,QAAK;QAAC;QACxD;YAAE,OAAO;YAAO,OAAO;YAAgB,MAAM,qMAAA,CAAA,OAAI;QAAC;QAClD;YAAE,OAAO;YAAM,OAAO;YAAiB,MAAM,uMAAA,CAAA,QAAK;QAAC;QACnD;YAAE,OAAO;YAAM,OAAO;YAAmB,MAAM,uMAAA,CAAA,QAAK;QAAC;KACtD;IAED,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAGb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;4BACV,OAAO;gCAAE,iBAAiB;4BAA2B;;;;;;sCAE1D,6LAAC;4BAAI,WAAU;;;;;;sCAEf,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,8JAAA,CAAA,UAAoB;4CAAC,OAAO;4CAAiB,WAAU;;;;;;;;;;;kDAI1D,6LAAC;wCAAG,WAAU;kDACX,MAAM,QAAQ,CAAC,4BACd;;gDAAE;8DAEA,6LAAC;oDAAK,WAAU;8DAAiH;;;;;;;2DAKnI;;;;;;oCAKH,0BACC,6LAAC;wCAAG,WAAU;kDACX;;;;;;kDAKL,6LAAC;wCAAE,WAAU;kDAAsE;;;;;;kDAKnF,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,WAAU;0DACX;;;;;;0DAID,6LAAC;gDACC,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAWT,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CAGb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDAEf,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDACC,KAAI;wDACJ,KAAI;wDACJ,WAAU;;;;;;;;;;;8DAId,6LAAC;oDAAG,WAAU;8DAAkE;;;;;;8DAGhF,6LAAC;oDAAE,WAAU;8DAA0D;;;;;;;;;;;;;;;;;;;;;;;0CAQ7E,6LAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,MAAM,sBACrB,6LAAC;wCAAgB,WAAU;;0DACzB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;;;;;;0DAEvB,6LAAC;gDAAI,WAAU;0DACZ,KAAK,KAAK;;;;;;0DAEb,6LAAC;gDAAI,WAAU;0DACZ,KAAK,KAAK;;;;;;;uCARL;;;;;;;;;;4BAgBb,gCACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,KAAK;oCACL,KAAI;oCACJ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU5B;KArLwB", "debugId": null}}, {"offset": {"line": 784, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/training/components/ProgramStructure.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport { CheckCircle, Code, Network, Cloud, Clock, Users, Award } from 'lucide-react';\r\n\r\nconst phases = [\r\n  {\r\n    id: 1,\r\n    title: \"Web And API Penetration Testing\",\r\n    description: \"Our program introduces participants to VAPT (Vulnerability Assessment and Penetration Testing) for mobile and web applications, focusing on identifying and mitigating security flaws. With the growing reliance on mobile apps and APIs, security is critical for organizations.\",\r\n    icon: Code,\r\n    color: \"from-[var(--color-blue)] to-[var(--color-blue-secondary)]\",\r\n    features: [\"Assignments\", \"Labs\", \"Mentoring (Premium Only)\", \"Hands-On Real Project (Premium Only)\"],\r\n    image: \"/images/p2s3.png\"\r\n  },\r\n  {\r\n    id: 2,\r\n    title: \"Network and Cloud Penetration Testing\",\r\n    description: \"This phase focuses on network security, equipping participants with the skills to assess and secure organizational networks and endpoints. The emphasis is on identifying vulnerabilities in network infrastructure and developing robust defenses.\",\r\n    icon: Network,\r\n    color: \"from-[var(--color-dark-blue)] to-[var(--color-dark-blue-hover)]\",\r\n    features: [\"Assignments\", \"Labs\", \"Mentoring (Premium Only)\", \"Hands-On Real Project (Premium Only)\"],\r\n    image: \"/images/p2s4.png\"\r\n  },\r\n  {\r\n    id: 3,\r\n    title: \"Cloud Security\",\r\n    description: \"This phase introduces participants to essential cloud security practices, focusing on securing AWS environments. With more organizations moving to the cloud, understanding foundational cloud security principles is crucial for safeguarding cloud-based infrastructure.\",\r\n    icon: Cloud,\r\n    color: \"from-[var(--color-blue)] to-[var(--color-blue-secondary)]\",\r\n    features: [\"Assignments\", \"Labs\", \"Mentoring (Premium Only)\", \"Hands-On Real Project (Premium Only)\"],\r\n    image: \"/images/p2s5.png\"\r\n  }\r\n];\r\n\r\nexport default function ProgramStructure() {\r\n  return (\r\n    <section className=\"py-24 bg-gradient-to-br from-gray-50 to-white relative overflow-hidden\">\r\n      {/* Background Elements */}\r\n      <div className=\"absolute inset-0\">\r\n        <div className=\"absolute top-20 left-10 w-72 h-72 bg-[var(--color-blue)]/5 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob\"></div>\r\n        <div className=\"absolute bottom-20 right-10 w-72 h-72 bg-[var(--color-blue-secondary)]/5 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-2000\"></div>\r\n      </div>\r\n\r\n      <div className=\"relative z-10 container mx-auto px-4 max-w-7xl\">\r\n        {/* Header */}\r\n        <motion.div \r\n          initial={{ opacity: 0, y: 30 }}\r\n          whileInView={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.8 }}\r\n          viewport={{ once: true }}\r\n          className=\"text-center mb-20\"\r\n        >\r\n          <motion.div \r\n            initial={{ opacity: 0, scale: 0.8 }}\r\n            whileInView={{ opacity: 1, scale: 1 }}\r\n            transition={{ delay: 0.2, duration: 0.6 }}\r\n            viewport={{ once: true }}\r\n            className=\"inline-flex items-center bg-gradient-to-r from-[var(--color-blue)]/10 to-[var(--color-blue-secondary)]/10 px-6 py-3 rounded-full mb-8 border border-[var(--color-blue)]/20\"\r\n          >\r\n            <Award className=\"w-5 h-5 text-[var(--color-blue)] mr-3\" />\r\n            <span className=\"text-sm font-semibold text-[var(--color-blue)]\">Job-Focused Curriculum</span>\r\n          </motion.div>\r\n          \r\n          <motion.h2 \r\n            initial={{ opacity: 0, y: 20 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ delay: 0.4, duration: 0.6 }}\r\n            viewport={{ once: true }}\r\n            className=\"text-5xl lg:text-6xl font-bold text-[var(--color-dark-blue)] mb-8 leading-tight\"\r\n          >\r\n            Program\r\n            <span className=\"block bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] bg-clip-text text-transparent\">\r\n              Structure\r\n            </span>\r\n          </motion.h2>\r\n          \r\n          <motion.p \r\n            initial={{ opacity: 0, y: 20 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ delay: 0.6, duration: 0.6 }}\r\n            viewport={{ once: true }}\r\n            className=\"text-xl text-[var(--foreground-secondary)] max-w-4xl mx-auto leading-relaxed\"\r\n          >\r\n            We provide a job-focused, hands-on curriculum designed to take participants from foundational to advanced cybersecurity skills across three comprehensive phases.\r\n          </motion.p>\r\n        </motion.div>\r\n\r\n        {/* Phases Grid */}\r\n        <div className=\"space-y-16\">\r\n          {phases.map((phase, index) => (\r\n            <motion.div\r\n              key={phase.id}\r\n              initial={{ opacity: 0, y: 50 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ delay: index * 0.2, duration: 0.8 }}\r\n              viewport={{ once: true }}\r\n              className={`flex flex-col lg:flex-row items-center gap-12 ${\r\n                index % 2 === 1 ? 'lg:flex-row-reverse' : ''\r\n              }`}\r\n            >\r\n              {/* Content */}\r\n              <div className=\"lg:w-1/2 space-y-6\">\r\n                <div className=\"flex items-center gap-4\">\r\n                  <div className={`w-16 h-16 bg-gradient-to-br ${phase.color} rounded-2xl flex items-center justify-center shadow-lg`}>\r\n                    <phase.icon className=\"w-8 h-8 text-white\" />\r\n                  </div>\r\n                  <div>\r\n                    <div className=\"text-sm font-semibold text-[var(--color-blue)] uppercase tracking-wide\">Phase {phase.id}</div>\r\n                    <h3 className=\"text-3xl lg:text-4xl font-bold text-[var(--color-dark-blue)] leading-tight\">{phase.title}</h3>\r\n                  </div>\r\n                </div>\r\n                \r\n                <p className=\"text-lg text-[var(--foreground-secondary)] leading-relaxed\">\r\n                  {phase.description}\r\n                </p>\r\n\r\n                {/* Features */}\r\n                <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4\">\r\n                  {phase.features.map((feature, featureIndex) => (\r\n                    <div key={featureIndex} className=\"flex items-center gap-3\">\r\n                      <CheckCircle className=\"w-5 h-5 text-[var(--color-blue)] flex-shrink-0\" />\r\n                      <span className=\"text-[var(--color-dark-blue)] font-medium\">{feature}</span>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              </div>\r\n\r\n              {/* Image */}\r\n              <div className=\"lg:w-1/2\">\r\n                <div className=\"relative\">\r\n                  <div className={`absolute inset-0 bg-gradient-to-br ${phase.color} rounded-3xl opacity-10 blur-xl`}></div>\r\n                  <div className=\"relative bg-white rounded-3xl p-8 shadow-[0_20px_40px_rgb(0,0,0,0.08)] border border-gray-100\">\r\n                    <img \r\n                      src={phase.image} \r\n                      alt={phase.title}\r\n                      className=\"w-full h-auto rounded-2xl\"\r\n                    />\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </motion.div>\r\n          ))}\r\n        </div>\r\n\r\n        {/* Bottom Stats */}\r\n        <motion.div \r\n          initial={{ opacity: 0, y: 30 }}\r\n          whileInView={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.8, duration: 0.6 }}\r\n          viewport={{ once: true }}\r\n          className=\"mt-20 grid grid-cols-1 md:grid-cols-3 gap-8\"\r\n        >\r\n          <div className=\"text-center\">\r\n            <div className=\"text-4xl font-bold text-[var(--color-blue)] mb-2\">3</div>\r\n            <div className=\"text-[var(--foreground-secondary)]\">Comprehensive Phases</div>\r\n          </div>\r\n          <div className=\"text-center\">\r\n            <div className=\"text-4xl font-bold text-[var(--color-dark-blue)] mb-2\">6</div>\r\n            <div className=\"text-[var(--foreground-secondary)]\">Months Duration</div>\r\n          </div>\r\n          <div className=\"text-center\">\r\n            <div className=\"text-4xl font-bold text-[var(--color-blue-secondary)] mb-2\">100%</div>\r\n            <div className=\"text-[var(--foreground-secondary)]\">Hands-On Learning</div>\r\n          </div>\r\n        </motion.div>\r\n      </div>\r\n    </section>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAMA,MAAM,SAAS;IACb;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM,qMAAA,CAAA,OAAI;QACV,OAAO;QACP,UAAU;YAAC;YAAe;YAAQ;YAA4B;SAAuC;QACrG,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM,2MAAA,CAAA,UAAO;QACb,OAAO;QACP,UAAU;YAAC;YAAe;YAAQ;YAA4B;SAAuC;QACrG,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM,uMAAA,CAAA,QAAK;QACX,OAAO;QACP,UAAU;YAAC;YAAe;YAAQ;YAA4B;SAAuC;QACrG,OAAO;IACT;CACD;AAEc,SAAS;IACtB,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,aAAa;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCACpC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAEV,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,6LAAC;wCAAK,WAAU;kDAAiD;;;;;;;;;;;;0CAGnE,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;gCACR,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;oCACX;kDAEC,6LAAC;wCAAK,WAAU;kDAAiH;;;;;;;;;;;;0CAKnI,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;gCACP,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;0CACX;;;;;;;;;;;;kCAMH,6LAAC;wBAAI,WAAU;kCACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,OAAO,QAAQ;oCAAK,UAAU;gCAAI;gCAChD,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAW,AAAC,iDAEX,OADC,QAAQ,MAAM,IAAI,wBAAwB;;kDAI5C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAW,AAAC,+BAA0C,OAAZ,MAAM,KAAK,EAAC;kEACzD,cAAA,6LAAC,MAAM,IAAI;4DAAC,WAAU;;;;;;;;;;;kEAExB,6LAAC;;0EACC,6LAAC;gEAAI,WAAU;;oEAAyE;oEAAO,MAAM,EAAE;;;;;;;0EACvG,6LAAC;gEAAG,WAAU;0EAA8E,MAAM,KAAK;;;;;;;;;;;;;;;;;;0DAI3G,6LAAC;gDAAE,WAAU;0DACV,MAAM,WAAW;;;;;;0DAIpB,6LAAC;gDAAI,WAAU;0DACZ,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC5B,6LAAC;wDAAuB,WAAU;;0EAChC,6LAAC,8NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;0EACvB,6LAAC;gEAAK,WAAU;0EAA6C;;;;;;;uDAFrD;;;;;;;;;;;;;;;;kDAShB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAW,AAAC,sCAAiD,OAAZ,MAAM,KAAK,EAAC;;;;;;8DAClE,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDACC,KAAK,MAAM,KAAK;wDAChB,KAAK,MAAM,KAAK;wDAChB,WAAU;;;;;;;;;;;;;;;;;;;;;;;+BA5Cb,MAAM,EAAE;;;;;;;;;;kCAsDnB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,OAAO;4BAAK,UAAU;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAmD;;;;;;kDAClE,6LAAC;wCAAI,WAAU;kDAAqC;;;;;;;;;;;;0CAEtD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAwD;;;;;;kDACvE,6LAAC;wCAAI,WAAU;kDAAqC;;;;;;;;;;;;0CAEtD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAA6D;;;;;;kDAC5E,6LAAC;wCAAI,WAAU;kDAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMhE;KAtIwB", "debugId": null}}, {"offset": {"line": 1284, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/training/components/Testimonials.jsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Quote, Star, Building2, User, ChevronLeft, ChevronRight } from 'lucide-react';\n\nconst testimonials = [\n  {\n    id: 1,\n    quote: \"SecurityLit transformed my perspective on IT security. Through identifying risks for organizations, I've grown in my role and feel more capable now. The supportive work environment and knowledgeable colleagues at SecurityLit have been invaluable.\",\n    author: \"<PERSON><PERSON><PERSON>\",\n    position: \"Information Security Analyst at DataTorque Ltd\",\n    company: \"DataTorque Ltd\",\n    rating: 5\n  },\n  {\n    id: 2,\n    quote: \"The training boosted my expertise and confidence, helping me excel as a pentester. Hands-on projects deepened my skills in web security and advanced exploitation, which I now apply regularly, along with improved communication skills.\",\n    author: \"<PERSON><PERSON><PERSON>\",\n    position: \"Associate Penetration Tester at SecurityLit\",\n    company: \"SecurityLit\",\n    rating: 5\n  },\n  {\n    id: 3,\n    quote: \"This training improved my approach to pentesting, enhancing my process, documentation, and teamwork. Collaborating on tests and following a structured process was far more effective than my previous ad-hoc methods in bug hunting.\",\n    author: \"<PERSON>\",\n    position: \"Associate Penetration Tester at SecurityLit\",\n    company: \"SecurityLit\",\n    rating: 5\n  },\n  {\n    id: 4,\n    quote: \"I believe I got the job thanks to the training, support, and feedback from Ankita and the SecurityLit team, which helped me excel in the interviews. Thank you for all the guidance that led me to this role.\",\n    author: \"Rini Sebastian\",\n    position: \"Information Security Analyst at AIA NZ\",\n    company: \"AIA NZ\",\n    rating: 5\n  }\n];\n\nexport default function Testimonials() {\n  const [currentIndex, setCurrentIndex] = useState(0);\n  const [direction, setDirection] = useState(0);\n\n  // Auto-play functionality\n  useEffect(() => {\n    const timer = setInterval(() => {\n      setDirection(1);\n      setCurrentIndex((prevIndex) => (prevIndex + 1) % testimonials.length);\n    }, 5000); // Change slide every 5 seconds\n\n    return () => clearInterval(timer);\n  }, [testimonials.length]);\n\n  const slideVariants = {\n    enter: (direction) => ({\n      x: direction > 0 ? 1000 : -1000,\n      opacity: 0\n    }),\n    center: {\n      zIndex: 1,\n      x: 0,\n      opacity: 1\n    },\n    exit: (direction) => ({\n      zIndex: 0,\n      x: direction < 0 ? 1000 : -1000,\n      opacity: 0\n    })\n  };\n\n  const swipeConfidenceThreshold = 10000;\n  const swipePower = (offset, velocity) => {\n    return Math.abs(offset) * velocity;\n  };\n\n  const paginate = (newDirection) => {\n    setDirection(newDirection);\n    setCurrentIndex((prevIndex) => {\n      if (newDirection === 1) {\n        return (prevIndex + 1) % testimonials.length;\n      } else {\n        return prevIndex === 0 ? testimonials.length - 1 : prevIndex - 1;\n      }\n    });\n  };\n\n  const goToSlide = (index) => {\n    setDirection(index > currentIndex ? 1 : -1);\n    setCurrentIndex(index);\n  };\n\n  return (\n    <section className=\"py-24 bg-gradient-to-br from-[var(--color-dark-blue)] via-[var(--color-dark-blue-hover)] to-[var(--color-dark-blue-bg)] relative overflow-hidden\">\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0\">\n        <div className=\"absolute top-0 left-0 w-full h-full bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.03%22%3E%3Ccircle%20cx%3D%2230%22%20cy%3D%2230%22%20r%3D%221%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-50\"></div>\n      </div>\n\n      <div className=\"relative z-10 container mx-auto px-4 max-w-7xl\">\n        {/* Header */}\n        <motion.div \n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-20\"\n        >\n          <motion.div \n            initial={{ opacity: 0, scale: 0.8 }}\n            whileInView={{ opacity: 1, scale: 1 }}\n            transition={{ delay: 0.2, duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"inline-flex items-center bg-[var(--color-blue)]/20 px-4 py-2 rounded-full mb-6\"\n          >\n            <Quote className=\"w-4 h-4 text-[var(--color-blue)] mr-2\" />\n            <span className=\"text-sm font-medium text-[var(--color-blue)]\">Real Success Stories</span>\n          </motion.div>\n          \n          <motion.h2 \n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.4, duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"text-4xl lg:text-5xl font-bold text-white mb-6\"\n          >\n            Real Feedback from\n            <span className=\"block bg-gradient-to-r from-[var(--color-blue)] via-[var(--color-blue-secondary)] to-[var(--color-blue)] bg-clip-text text-transparent\">\n              Professionals\n            </span>\n          </motion.h2>\n          \n          <motion.p \n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.6, duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"text-xl text-white/80 max-w-4xl mx-auto leading-relaxed\"\n          >\n            Hear from professionals who completed our Security Training and transformed their careers in cybersecurity.\n          </motion.p>\n        </motion.div>\n\n        {/* Testimonials Slider */}\n        <div className=\"relative max-w-4xl mx-auto\">\n          {/* Navigation Arrows */}\n          <motion.button\n            whileHover={{ scale: 1.1 }}\n            whileTap={{ scale: 0.9 }}\n            onClick={() => paginate(-1)}\n            className=\"absolute left-4 top-1/2 transform -translate-y-1/2 z-20 w-12 h-12 bg-white/10 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-white/20 transition-all duration-300 border border-white/20\"\n          >\n            <ChevronLeft className=\"w-6 h-6\" />\n          </motion.button>\n          \n          <motion.button\n            whileHover={{ scale: 1.1 }}\n            whileTap={{ scale: 0.9 }}\n            onClick={() => paginate(1)}\n            className=\"absolute right-4 top-1/2 transform -translate-y-1/2 z-20 w-12 h-12 bg-white/10 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-white/20 transition-all duration-300 border border-white/20\"\n          >\n            <ChevronRight className=\"w-6 h-6\" />\n          </motion.button>\n\n          {/* Slider Container */}\n          <div className=\"relative h-[400px] overflow-hidden\">\n            <AnimatePresence initial={false} custom={direction}>\n              <motion.div\n                key={currentIndex}\n                custom={direction}\n                variants={slideVariants}\n                initial=\"enter\"\n                animate=\"center\"\n                exit=\"exit\"\n                transition={{\n                  x: { type: \"spring\", stiffness: 300, damping: 30 },\n                  opacity: { duration: 0.2 }\n                }}\n                drag=\"x\"\n                dragConstraints={{ left: 0, right: 0 }}\n                dragElastic={1}\n                onDragEnd={(e, { offset, velocity }) => {\n                  const swipe = swipePower(offset.x, velocity.x);\n\n                  if (swipe < -swipeConfidenceThreshold) {\n                    paginate(1);\n                  } else if (swipe > swipeConfidenceThreshold) {\n                    paginate(-1);\n                  }\n                }}\n                className=\"absolute inset-0\"\n              >\n                <div className=\"relative bg-white/10 backdrop-blur-xl rounded-3xl p-8 border border-white/20 shadow-2xl h-full flex flex-col justify-center\">\n                  {/* Quote Icon */}\n                  <div className=\"absolute top-6 right-6 opacity-20\">\n                    <Quote className=\"w-8 h-8 text-white\" />\n                  </div>\n\n                  {/* Rating */}\n                  <div className=\"flex items-center gap-1 mb-6\">\n                    {[...Array(testimonials[currentIndex].rating)].map((_, i) => (\n                      <Star key={i} className=\"w-5 h-5 text-[var(--color-yellow)] fill-current\" />\n                    ))}\n                  </div>\n\n                  {/* Quote */}\n                  <blockquote className=\"text-white/90 text-xl leading-relaxed mb-8 italic flex-1\">\n                    \"{testimonials[currentIndex].quote}\"\n                  </blockquote>\n\n                  {/* Author */}\n                  <div className=\"flex items-center gap-4\">\n                    <div className=\"w-12 h-12 bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-full flex items-center justify-center shadow-lg\">\n                      <User className=\"w-6 h-6 text-white\" />\n                    </div>\n                    <div>\n                      <div className=\"text-white font-semibold text-lg\">{testimonials[currentIndex].author}</div>\n                      <div className=\"text-white/70 text-sm\">{testimonials[currentIndex].position}</div>\n                      <div className=\"flex items-center gap-2 text-white/60 text-sm\">\n                        <Building2 className=\"w-4 h-4\" />\n                        <span>{testimonials[currentIndex].company}</span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </motion.div>\n            </AnimatePresence>\n          </div>\n\n          {/* Dots Navigation */}\n          <div className=\"flex justify-center mt-8 space-x-2\">\n            {testimonials.map((_, index) => (\n              <motion.button\n                key={index}\n                whileHover={{ scale: 1.2 }}\n                whileTap={{ scale: 0.8 }}\n                onClick={() => goToSlide(index)}\n                className={`w-3 h-3 rounded-full transition-all duration-300 ${\n                  index === currentIndex \n                    ? 'bg-[var(--color-blue)] scale-125' \n                    : 'bg-white/30 hover:bg-white/50'\n                }`}\n              />\n            ))}\n          </div>\n\n          {/* Slide Counter */}\n          <div className=\"text-center mt-4\">\n            <span className=\"text-white/60 text-sm\">\n              {currentIndex + 1} of {testimonials.length}\n            </span>\n          </div>\n        </div>\n\n        {/* Bottom CTA */}\n        <motion.div \n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.8, duration: 0.6 }}\n          viewport={{ once: true }}\n          className=\"text-center mt-16\"\n        >\n          <div className=\"inline-flex items-center gap-4 bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] text-white px-8 py-4 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer group\">\n            <Star className=\"w-5 h-5\" />\n            <span className=\"font-semibold text-lg\">Join These Success Stories</span>\n            <Quote className=\"w-5 h-5 group-hover:scale-110 transition-transform\" />\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;;AAMA,MAAM,eAAe;IACnB;QACE,IAAI;QACJ,OAAO;QACP,QAAQ;QACR,UAAU;QACV,SAAS;QACT,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,QAAQ;QACR,UAAU;QACV,SAAS;QACT,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,QAAQ;QACR,UAAU;QACV,SAAS;QACT,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,QAAQ;QACR,UAAU;QACV,SAAS;QACT,QAAQ;IACV;CACD;AAEc,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,0BAA0B;IAC1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM,QAAQ;gDAAY;oBACxB,aAAa;oBACb;wDAAgB,CAAC,YAAc,CAAC,YAAY,CAAC,IAAI,aAAa,MAAM;;gBACtE;+CAAG,OAAO,+BAA+B;YAEzC;0CAAO,IAAM,cAAc;;QAC7B;iCAAG;QAAC,aAAa,MAAM;KAAC;IAExB,MAAM,gBAAgB;QACpB,OAAO,CAAC,YAAc,CAAC;gBACrB,GAAG,YAAY,IAAI,OAAO,CAAC;gBAC3B,SAAS;YACX,CAAC;QACD,QAAQ;YACN,QAAQ;YACR,GAAG;YACH,SAAS;QACX;QACA,MAAM,CAAC,YAAc,CAAC;gBACpB,QAAQ;gBACR,GAAG,YAAY,IAAI,OAAO,CAAC;gBAC3B,SAAS;YACX,CAAC;IACH;IAEA,MAAM,2BAA2B;IACjC,MAAM,aAAa,CAAC,QAAQ;QAC1B,OAAO,KAAK,GAAG,CAAC,UAAU;IAC5B;IAEA,MAAM,WAAW,CAAC;QAChB,aAAa;QACb,gBAAgB,CAAC;YACf,IAAI,iBAAiB,GAAG;gBACtB,OAAO,CAAC,YAAY,CAAC,IAAI,aAAa,MAAM;YAC9C,OAAO;gBACL,OAAO,cAAc,IAAI,aAAa,MAAM,GAAG,IAAI,YAAY;YACjE;QACF;IACF;IAEA,MAAM,YAAY,CAAC;QACjB,aAAa,QAAQ,eAAe,IAAI,CAAC;QACzC,gBAAgB;IAClB;IAEA,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;;;;;;;;;;0BAGjB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,aAAa;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCACpC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAEV,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,6LAAC;wCAAK,WAAU;kDAA+C;;;;;;;;;;;;0CAGjE,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;gCACR,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;oCACX;kDAEC,6LAAC;wCAAK,WAAU;kDAAyI;;;;;;;;;;;;0CAK3J,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;gCACP,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;0CACX;;;;;;;;;;;;kCAMH,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,YAAY;oCAAE,OAAO;gCAAI;gCACzB,UAAU;oCAAE,OAAO;gCAAI;gCACvB,SAAS,IAAM,SAAS,CAAC;gCACzB,WAAU;0CAEV,cAAA,6LAAC,uNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;0CAGzB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,YAAY;oCAAE,OAAO;gCAAI;gCACzB,UAAU;oCAAE,OAAO;gCAAI;gCACvB,SAAS,IAAM,SAAS;gCACxB,WAAU;0CAEV,cAAA,6LAAC,yNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;;;;;;0CAI1B,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,4LAAA,CAAA,kBAAe;oCAAC,SAAS;oCAAO,QAAQ;8CACvC,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,QAAQ;wCACR,UAAU;wCACV,SAAQ;wCACR,SAAQ;wCACR,MAAK;wCACL,YAAY;4CACV,GAAG;gDAAE,MAAM;gDAAU,WAAW;gDAAK,SAAS;4CAAG;4CACjD,SAAS;gDAAE,UAAU;4CAAI;wCAC3B;wCACA,MAAK;wCACL,iBAAiB;4CAAE,MAAM;4CAAG,OAAO;wCAAE;wCACrC,aAAa;wCACb,WAAW,CAAC;gDAAG,EAAE,MAAM,EAAE,QAAQ,EAAE;4CACjC,MAAM,QAAQ,WAAW,OAAO,CAAC,EAAE,SAAS,CAAC;4CAE7C,IAAI,QAAQ,CAAC,0BAA0B;gDACrC,SAAS;4CACX,OAAO,IAAI,QAAQ,0BAA0B;gDAC3C,SAAS,CAAC;4CACZ;wCACF;wCACA,WAAU;kDAEV,cAAA,6LAAC;4CAAI,WAAU;;8DAEb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;8DAInB,6LAAC;oDAAI,WAAU;8DACZ;2DAAI,MAAM,YAAY,CAAC,aAAa,CAAC,MAAM;qDAAE,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrD,6LAAC,qMAAA,CAAA,OAAI;4DAAS,WAAU;2DAAb;;;;;;;;;;8DAKf,6LAAC;oDAAW,WAAU;;wDAA2D;wDAC7E,YAAY,CAAC,aAAa,CAAC,KAAK;wDAAC;;;;;;;8DAIrC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;sEAElB,6LAAC;;8EACC,6LAAC;oEAAI,WAAU;8EAAoC,YAAY,CAAC,aAAa,CAAC,MAAM;;;;;;8EACpF,6LAAC;oEAAI,WAAU;8EAAyB,YAAY,CAAC,aAAa,CAAC,QAAQ;;;;;;8EAC3E,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,mNAAA,CAAA,YAAS;4EAAC,WAAU;;;;;;sFACrB,6LAAC;sFAAM,YAAY,CAAC,aAAa,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uCApD5C;;;;;;;;;;;;;;;0CA8DX,6LAAC;gCAAI,WAAU;0CACZ,aAAa,GAAG,CAAC,CAAC,GAAG,sBACpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wCAEZ,YAAY;4CAAE,OAAO;wCAAI;wCACzB,UAAU;4CAAE,OAAO;wCAAI;wCACvB,SAAS,IAAM,UAAU;wCACzB,WAAW,AAAC,oDAIX,OAHC,UAAU,eACN,qCACA;uCAPD;;;;;;;;;;0CAcX,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;;wCACb,eAAe;wCAAE;wCAAK,aAAa,MAAM;;;;;;;;;;;;;;;;;;kCAMhD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,OAAO;4BAAK,UAAU;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;kCAEV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,6LAAC;oCAAK,WAAU;8CAAwB;;;;;;8CACxC,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM7B;GAvOwB;KAAA", "debugId": null}}, {"offset": {"line": 1874, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/training/components/TrainingAccessForm.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState } from 'react';\r\nimport { motion, AnimatePresence } from 'framer-motion';\r\nimport { Shield, User, Mail, Phone, Building, GraduationCap, ArrowRight, CheckCircle, ChevronLeft, ChevronRight, Linkedin, Briefcase, BookOpen, MessageSquare, ExternalLink } from 'lucide-react';\r\n\r\nexport default function TrainingAccessForm() {\r\n  const [currentStep, setCurrentStep] = useState(1);\r\n  const [formData, setFormData] = useState({\r\n    disclaimer: false,\r\n    fullName: '',\r\n    email: '',\r\n    phone: '',\r\n    linkedin: '',\r\n    occupation: '',\r\n    experience: '',\r\n    education: '',\r\n    interest: '',\r\n    source: '',\r\n    securitySkills: 'no',\r\n    certification: false,\r\n    terms: false\r\n  });\r\n\r\n  const totalSteps = 3;\r\n\r\n  const handleInputChange = (e) => {\r\n    const { name, value, type, checked } = e.target;\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      [name]: type === 'checkbox' ? checked : value\r\n    }));\r\n  };\r\n\r\n  const nextStep = () => {\r\n    if (currentStep < totalSteps) {\r\n      setCurrentStep(currentStep + 1);\r\n    }\r\n  };\r\n\r\n  const prevStep = () => {\r\n    if (currentStep > 1) {\r\n      setCurrentStep(currentStep - 1);\r\n    }\r\n  };\r\n\r\n  const handleSubmit = (e) => {\r\n    e.preventDefault();\r\n    console.log('Form submitted:', formData);\r\n  };\r\n\r\n  const renderStep = () => {\r\n    switch (currentStep) {\r\n      case 1:\r\n        return (\r\n          <div className=\"space-y-6\">\r\n            <div className=\"text-center mb-8\">\r\n              <div className=\"w-16 h-16 bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-3xl flex items-center justify-center mx-auto mb-4\">\r\n                <Shield className=\"w-8 h-8 text-white\" />\r\n              </div>\r\n              <h3 className=\"text-2xl font-bold text-[var(--color-dark-blue)] mb-2\">Step 1 of {totalSteps}</h3>\r\n              <p className=\"text-[var(--foreground-secondary)]\">Personal Information & Disclaimer</p>\r\n            </div>\r\n            \r\n            {/* Disclaimer */}\r\n            <div className=\"bg-gradient-to-br from-[var(--color-blue)]/10 to-[var(--color-blue-secondary)]/10 rounded-2xl p-6 border border-[var(--color-blue)]/20\">\r\n              <p className=\"text-[var(--color-dark-blue)] leading-relaxed mb-4\">\r\n                By submitting this form, you consent to our use of the provided information to contact you regarding the security training program. We respect your privacy and will only use this information for program-related communications.\r\n              </p>\r\n              <label className=\"flex items-center gap-3 cursor-pointer\">\r\n                <input\r\n                  type=\"checkbox\"\r\n                  name=\"disclaimer\"\r\n                  checked={formData.disclaimer}\r\n                  onChange={handleInputChange}\r\n                  required\r\n                  className=\"w-4 h-4 text-[var(--color-blue)] border-gray-300 rounded focus:ring-[var(--color-blue)]\"\r\n                />\r\n                <span className=\"text-[var(--color-dark-blue)] font-medium\">Yes, I agree to the disclaimer</span>\r\n              </label>\r\n            </div>\r\n\r\n            {/* Personal Information */}\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n              <div>\r\n                <label className=\"block text-[var(--color-dark-blue)] font-medium mb-2\">Full Name *</label>\r\n                <div className=\"relative\">\r\n                  <User className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-[var(--color-blue)]\" />\r\n                  <input\r\n                    type=\"text\"\r\n                    name=\"fullName\"\r\n                    value={formData.fullName}\r\n                    onChange={handleInputChange}\r\n                    required\r\n                    className=\"w-full pl-10 pr-4 py-3 bg-white border border-gray-200 rounded-xl text-[var(--color-dark-blue)] placeholder-gray-400 focus:outline-none focus:border-[var(--color-blue)] focus:ring-2 focus:ring-[var(--color-blue)]/20 transition-all\"\r\n                    placeholder=\"Enter your full name\"\r\n                  />\r\n                </div>\r\n              </div>\r\n\r\n              <div>\r\n                <label className=\"block text-[var(--color-dark-blue)] font-medium mb-2\">Email Address *</label>\r\n                <div className=\"relative\">\r\n                  <Mail className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-[var(--color-blue)]\" />\r\n                  <input\r\n                    type=\"email\"\r\n                    name=\"email\"\r\n                    value={formData.email}\r\n                    onChange={handleInputChange}\r\n                    required\r\n                    className=\"w-full pl-10 pr-4 py-3 bg-white border border-gray-200 rounded-xl text-[var(--color-dark-blue)] placeholder-gray-400 focus:outline-none focus:border-[var(--color-blue)] focus:ring-2 focus:ring-[var(--color-blue)]/20 transition-all\"\r\n                    placeholder=\"Enter your email address\"\r\n                  />\r\n                </div>\r\n              </div>\r\n\r\n              <div>\r\n                <label className=\"block text-[var(--color-dark-blue)] font-medium mb-2\">Phone Number</label>\r\n                <div className=\"relative\">\r\n                  <Phone className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-[var(--color-blue)]\" />\r\n                  <input\r\n                    type=\"tel\"\r\n                    name=\"phone\"\r\n                    value={formData.phone}\r\n                    onChange={handleInputChange}\r\n                    className=\"w-full pl-10 pr-4 py-3 bg-white border border-gray-200 rounded-xl text-[var(--color-dark-blue)] placeholder-gray-400 focus:outline-none focus:border-[var(--color-blue)] focus:ring-2 focus:ring-[var(--color-blue)]/20 transition-all\"\r\n                    placeholder=\"Enter your phone number\"\r\n                  />\r\n                </div>\r\n              </div>\r\n\r\n              <div>\r\n                <label className=\"block text-[var(--color-dark-blue)] font-medium mb-2\">LinkedIn Profile</label>\r\n                <div className=\"relative\">\r\n                  <Linkedin className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-[var(--color-blue)]\" />\r\n                  <input\r\n                    type=\"url\"\r\n                    name=\"linkedin\"\r\n                    value={formData.linkedin}\r\n                    onChange={handleInputChange}\r\n                    className=\"w-full pl-10 pr-4 py-3 bg-white border border-gray-200 rounded-xl text-[var(--color-dark-blue)] placeholder-gray-400 focus:outline-none focus:border-[var(--color-blue)] focus:ring-2 focus:ring-[var(--color-blue)]/20 transition-all\"\r\n                    placeholder=\"Enter your LinkedIn profile URL\"\r\n                  />\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        );\r\n\r\n      case 2:\r\n        return (\r\n          <div className=\"space-y-6\">\r\n            <div className=\"text-center mb-8\">\r\n              <h3 className=\"text-2xl font-bold text-[var(--color-dark-blue)] mb-2\">Step 2 of {totalSteps}</h3>\r\n              <p className=\"text-[var(--foreground-secondary)]\">Professional Background & Interest</p>\r\n            </div>\r\n            \r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n              <div>\r\n                <label className=\"block text-[var(--color-dark-blue)] font-medium mb-2\">Current Occupation</label>\r\n                <div className=\"relative\">\r\n                  <Briefcase className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-[var(--color-blue)]\" />\r\n                  <input\r\n                    type=\"text\"\r\n                    name=\"occupation\"\r\n                    value={formData.occupation}\r\n                    onChange={handleInputChange}\r\n                    className=\"w-full pl-10 pr-4 py-3 bg-white border border-gray-200 rounded-xl text-[var(--color-dark-blue)] placeholder-gray-400 focus:outline-none focus:border-[var(--color-blue)] focus:ring-2 focus:ring-[var(--color-blue)]/20 transition-all\"\r\n                    placeholder=\"Enter your current occupation\"\r\n                  />\r\n                </div>\r\n              </div>\r\n\r\n              <div>\r\n                <label className=\"block text-[var(--color-dark-blue)] font-medium mb-2\">Years of Experience</label>\r\n                <div className=\"relative\">\r\n                  <BookOpen className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-[var(--color-blue)]\" />\r\n                  <input\r\n                    type=\"text\"\r\n                    name=\"experience\"\r\n                    value={formData.experience}\r\n                    onChange={handleInputChange}\r\n                    className=\"w-full pl-10 pr-4 py-3 bg-white border border-gray-200 rounded-xl text-[var(--color-dark-blue)] placeholder-gray-400 focus:outline-none focus:border-[var(--color-blue)] focus:ring-2 focus:ring-[var(--color-blue)]/20 transition-all\"\r\n                    placeholder=\"e.g., 2 years in IT, 1 year in cybersecurity\"\r\n                  />\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"md:col-span-2\">\r\n                <label className=\"block text-[var(--color-dark-blue)] font-medium mb-2\">Highest Educational Qualification</label>\r\n                <div className=\"relative\">\r\n                  <GraduationCap className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-[var(--color-blue)]\" />\r\n                  <input\r\n                    type=\"text\"\r\n                    name=\"education\"\r\n                    value={formData.education}\r\n                    onChange={handleInputChange}\r\n                    className=\"w-full pl-10 pr-4 py-3 bg-white border border-gray-200 rounded-xl text-[var(--color-dark-blue)] placeholder-gray-400 focus:outline-none focus:border-[var(--color-blue)] focus:ring-2 focus:ring-[var(--color-blue)]/20 transition-all\"\r\n                    placeholder=\"e.g., Bachelor's in Computer Science\"\r\n                  />\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"md:col-span-2\">\r\n                <label className=\"block text-[var(--color-dark-blue)] font-medium mb-2\">Why are you interested in this security training program?</label>\r\n                <textarea\r\n                  name=\"interest\"\r\n                  value={formData.interest}\r\n                  onChange={handleInputChange}\r\n                  rows={4}\r\n                  className=\"w-full px-4 py-3 bg-white border border-gray-200 rounded-xl text-[var(--color-dark-blue)] placeholder-gray-400 focus:outline-none focus:border-[var(--color-blue)] focus:ring-2 focus:ring-[var(--color-blue)]/20 transition-all resize-none\"\r\n                  placeholder=\"Tell us about your interest in cybersecurity training...\"\r\n                />\r\n              </div>\r\n\r\n              <div>\r\n                <label className=\"block text-[var(--color-dark-blue)] font-medium mb-2\">How did you hear about us?</label>\r\n                <div className=\"relative\">\r\n                  <MessageSquare className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-[var(--color-blue)]\" />\r\n                  <input\r\n                    type=\"text\"\r\n                    name=\"source\"\r\n                    value={formData.source}\r\n                    onChange={handleInputChange}\r\n                    className=\"w-full pl-10 pr-4 py-3 bg-white border border-gray-200 rounded-xl text-[var(--color-dark-blue)] placeholder-gray-400 focus:outline-none focus:border-[var(--color-blue)] focus:ring-2 focus:ring-[var(--color-blue)]/20 transition-all\"\r\n                    placeholder=\"e.g., Social media, referral, search engine\"\r\n                  />\r\n                </div>\r\n              </div>\r\n\r\n              <div>\r\n                <label className=\"block text-[var(--color-dark-blue)] font-medium mb-2\">Do you have security skills?</label>\r\n                <div className=\"space-y-3\">\r\n                  <label className=\"flex items-center gap-3 cursor-pointer\">\r\n                    <input\r\n                      type=\"radio\"\r\n                      name=\"securitySkills\"\r\n                      value=\"yes\"\r\n                      checked={formData.securitySkills === 'yes'}\r\n                      onChange={handleInputChange}\r\n                      className=\"w-4 h-4 text-[var(--color-blue)] border-gray-300 focus:ring-[var(--color-blue)]\"\r\n                    />\r\n                    <span className=\"text-[var(--color-dark-blue)]\">Yes</span>\r\n                  </label>\r\n                  <label className=\"flex items-center gap-3 cursor-pointer\">\r\n                    <input\r\n                      type=\"radio\"\r\n                      name=\"securitySkills\"\r\n                      value=\"no\"\r\n                      checked={formData.securitySkills === 'no'}\r\n                      onChange={handleInputChange}\r\n                      className=\"w-4 h-4 text-[var(--color-blue)] border-gray-300 focus:ring-[var(--color-blue)]\"\r\n                    />\r\n                    <span className=\"text-[var(--color-dark-blue)]\">No</span>\r\n                  </label>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        );\r\n\r\n      case 3:\r\n        return (\r\n          <div className=\"space-y-6\">\r\n            <div className=\"text-center mb-8\">\r\n              <h3 className=\"text-2xl font-bold text-[var(--color-dark-blue)] mb-2\">Step 3 of {totalSteps}</h3>\r\n              <p className=\"text-[var(--foreground-secondary)]\">Certification & Terms</p>\r\n            </div>\r\n            \r\n            {/* Certification */}\r\n            <div className=\"bg-gradient-to-br from-[var(--color-blue)]/10 to-[var(--color-blue-secondary)]/10 rounded-2xl p-6 border border-[var(--color-blue)]/20\">\r\n              <p className=\"text-[var(--color-dark-blue)] leading-relaxed mb-4\">\r\n                I hereby certify that all the information provided in this form is true and correct to the best of my knowledge. I understand that any false or misleading information may result in the rejection of my application.\r\n              </p>\r\n              \r\n              <label className=\"flex items-center gap-3 cursor-pointer\">\r\n                <input\r\n                  type=\"checkbox\"\r\n                  name=\"certification\"\r\n                  checked={formData.certification}\r\n                  onChange={handleInputChange}\r\n                  required\r\n                  className=\"w-4 h-4 text-[var(--color-blue)] border-gray-300 rounded focus:ring-[var(--color-blue)]\"\r\n                />\r\n                <span className=\"text-[var(--color-dark-blue)] font-medium\">Yes, I certify</span>\r\n              </label>\r\n            </div>\r\n\r\n            {/* Terms and Conditions */}\r\n            <div className=\"bg-gradient-to-br from-[var(--color-blue)]/10 to-[var(--color-blue-secondary)]/10 rounded-2xl p-6 border border-[var(--color-blue)]/20\">\r\n              <p className=\"text-[var(--color-dark-blue)] leading-relaxed mb-4\">\r\n                By checking this box, you agree to our terms and conditions and privacy policy regarding course access and communications.\r\n              </p>\r\n              <a \r\n                href=\"https://securitylit.com/TrainingTermsAndCondition.pdf\" \r\n                target=\"_blank\" \r\n                rel=\"noopener noreferrer\"\r\n                className=\"inline-flex items-center gap-2 text-[var(--color-blue)] hover:text-[var(--color-blue-secondary)] transition-colors mb-4\"\r\n              >\r\n                <span>View Terms and Conditions</span>\r\n                <ExternalLink className=\"w-4 h-4\" />\r\n              </a>\r\n              \r\n              <label className=\"flex items-center gap-3 cursor-pointer\">\r\n                <input\r\n                  type=\"checkbox\"\r\n                  name=\"terms\"\r\n                  checked={formData.terms}\r\n                  onChange={handleInputChange}\r\n                  required\r\n                  className=\"w-4 h-4 text-[var(--color-blue)] border-gray-300 rounded focus:ring-[var(--color-blue)]\"\r\n                />\r\n                <span className=\"text-[var(--color-dark-blue)] font-medium\">Yes, I agree to the terms and conditions</span>\r\n              </label>\r\n            </div>\r\n          </div>\r\n        );\r\n\r\n      default:\r\n        return null;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <section className=\"py-24 bg-gradient-to-br from-[var(--color-blue)]/10 to-[var(--color-blue-secondary)]/10 relative overflow-hidden\">\r\n      {/* Background Pattern */}\r\n      <div className=\"absolute inset-0\">\r\n        <div className=\"absolute top-0 left-0 w-full h-full bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23000000%22%20fill-opacity%3D%220.03%22%3E%3Ccircle%20cx%3D%2230%22%20cy%3D%2230%22%20r%3D%221%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-50\"></div>\r\n      </div>\r\n\r\n      <div className=\"relative z-10 container mx-auto px-4 max-w-4xl\">\r\n        {/* Header */}\r\n        <motion.div \r\n          initial={{ opacity: 0, y: 30 }}\r\n          whileInView={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.8 }}\r\n          viewport={{ once: true }}\r\n          className=\"text-center mb-16\"\r\n        >\r\n          <motion.div \r\n            initial={{ opacity: 0, scale: 0.8 }}\r\n            whileInView={{ opacity: 1, scale: 1 }}\r\n            transition={{ delay: 0.2, duration: 0.6 }}\r\n            viewport={{ once: true }}\r\n            className=\"inline-flex items-center bg-gradient-to-r from-[var(--color-blue)]/10 to-[var(--color-blue-secondary)]/10 px-6 py-3 rounded-full mb-8 border border-[var(--color-blue)]/20\"\r\n          >\r\n            <Shield className=\"w-5 h-5 text-[var(--color-blue)] mr-3\" />\r\n            <span className=\"text-sm font-semibold text-[var(--color-blue)]\">Training Application</span>\r\n          </motion.div>\r\n          \r\n          <motion.h2 \r\n            initial={{ opacity: 0, y: 20 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ delay: 0.4, duration: 0.6 }}\r\n            viewport={{ once: true }}\r\n            className=\"text-4xl lg:text-5xl font-bold text-[var(--color-dark-blue)] mb-8\"\r\n          >\r\n            Complete Your\r\n            <span className=\"block bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] bg-clip-text text-transparent\">\r\n              Training Application\r\n            </span>\r\n          </motion.h2>\r\n          \r\n          <motion.p \r\n            initial={{ opacity: 0, y: 20 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ delay: 0.6, duration: 0.6 }}\r\n            viewport={{ once: true }}\r\n            className=\"text-lg text-[var(--foreground-secondary)] max-w-3xl mx-auto leading-relaxed\"\r\n          >\r\n            Join our comprehensive cybersecurity training program. Complete this 3-step application to get started on your cybersecurity journey.\r\n          </motion.p>\r\n        </motion.div>\r\n\r\n        {/* Progress Bar */}\r\n        <div className=\"mb-12\">\r\n          <div className=\"flex justify-between items-center mb-4\">\r\n            <span className=\"text-sm font-medium text-[var(--color-dark-blue)]\">Progress</span>\r\n            <span className=\"text-sm font-medium text-[var(--color-blue)]\">{currentStep} of {totalSteps}</span>\r\n          </div>\r\n          <div className=\"w-full bg-gray-200 rounded-full h-2\">\r\n            <motion.div \r\n              className=\"bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] h-2 rounded-full\"\r\n              initial={{ width: 0 }}\r\n              animate={{ width: `${(currentStep / totalSteps) * 100}%` }}\r\n              transition={{ duration: 0.5 }}\r\n            />\r\n          </div>\r\n        </div>\r\n\r\n        {/* Form Container */}\r\n        <motion.div \r\n          initial={{ opacity: 0, y: 30 }}\r\n          whileInView={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.8 }}\r\n          viewport={{ once: true }}\r\n          className=\"bg-white rounded-3xl p-8 lg:p-12 shadow-2xl border border-gray-100\"\r\n        >\r\n          <form onSubmit={handleSubmit}>\r\n            <AnimatePresence mode=\"wait\">\r\n              <motion.div\r\n                key={currentStep}\r\n                initial={{ opacity: 0, x: 20 }}\r\n                animate={{ opacity: 1, x: 0 }}\r\n                exit={{ opacity: 0, x: -20 }}\r\n                transition={{ duration: 0.3 }}\r\n              >\r\n                {renderStep()}\r\n              </motion.div>\r\n            </AnimatePresence>\r\n\r\n            {/* Navigation Buttons */}\r\n            <div className=\"flex justify-between items-center mt-12 pt-8 border-t border-gray-200\">\r\n              <motion.button\r\n                type=\"button\"\r\n                onClick={prevStep}\r\n                disabled={currentStep === 1}\r\n                className={`flex items-center gap-2 px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${\r\n                  currentStep === 1\r\n                    ? 'text-gray-400 cursor-not-allowed'\r\n                    : 'text-[var(--color-dark-blue)] hover:bg-gray-100'\r\n                }`}\r\n                whileHover={currentStep !== 1 ? { scale: 1.02 } : {}}\r\n                whileTap={currentStep !== 1 ? { scale: 0.98 } : {}}\r\n              >\r\n                <ChevronLeft className=\"w-5 h-5\" />\r\n                Previous\r\n              </motion.button>\r\n\r\n              {currentStep < totalSteps ? (\r\n                <motion.button\r\n                  type=\"button\"\r\n                  onClick={nextStep}\r\n                  className=\"flex items-center gap-2 bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] text-white px-8 py-3 rounded-xl font-semibold hover:shadow-lg transition-all duration-300\"\r\n                  whileHover={{ scale: 1.02 }}\r\n                  whileTap={{ scale: 0.98 }}\r\n                >\r\n                  Next\r\n                  <ChevronRight className=\"w-5 h-5\" />\r\n                </motion.button>\r\n              ) : (\r\n                <motion.button\r\n                  type=\"submit\"\r\n                  className=\"flex items-center gap-2 bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] text-white px-8 py-3 rounded-xl font-semibold hover:shadow-lg transition-all duration-300\"\r\n                  whileHover={{ scale: 1.02 }}\r\n                  whileTap={{ scale: 0.98 }}\r\n                >\r\n                  <Shield className=\"w-5 h-5\" />\r\n                  Submit Application\r\n                  <ArrowRight className=\"w-5 h-5\" />\r\n                </motion.button>\r\n              )}\r\n            </div>\r\n          </form>\r\n        </motion.div>\r\n      </div>\r\n    </section>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,YAAY;QACZ,UAAU;QACV,OAAO;QACP,OAAO;QACP,UAAU;QACV,YAAY;QACZ,YAAY;QACZ,WAAW;QACX,UAAU;QACV,QAAQ;QACR,gBAAgB;QAChB,eAAe;QACf,OAAO;IACT;IAEA,MAAM,aAAa;IAEnB,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM;QAC/C,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE,SAAS,aAAa,UAAU;YAC1C,CAAC;IACH;IAEA,MAAM,WAAW;QACf,IAAI,cAAc,YAAY;YAC5B,eAAe,cAAc;QAC/B;IACF;IAEA,MAAM,WAAW;QACf,IAAI,cAAc,GAAG;YACnB,eAAe,cAAc;QAC/B;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,QAAQ,GAAG,CAAC,mBAAmB;IACjC;IAEA,MAAM,aAAa;QACjB,OAAQ;YACN,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;8CAEpB,6LAAC;oCAAG,WAAU;;wCAAwD;wCAAW;;;;;;;8CACjF,6LAAC;oCAAE,WAAU;8CAAqC;;;;;;;;;;;;sCAIpD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAAqD;;;;;;8CAGlE,6LAAC;oCAAM,WAAU;;sDACf,6LAAC;4CACC,MAAK;4CACL,MAAK;4CACL,SAAS,SAAS,UAAU;4CAC5B,UAAU;4CACV,QAAQ;4CACR,WAAU;;;;;;sDAEZ,6LAAC;4CAAK,WAAU;sDAA4C;;;;;;;;;;;;;;;;;;sCAKhE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAuD;;;;;;sDACxE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,6LAAC;oDACC,MAAK;oDACL,MAAK;oDACL,OAAO,SAAS,QAAQ;oDACxB,UAAU;oDACV,QAAQ;oDACR,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;8CAKlB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAuD;;;;;;sDACxE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,6LAAC;oDACC,MAAK;oDACL,MAAK;oDACL,OAAO,SAAS,KAAK;oDACrB,UAAU;oDACV,QAAQ;oDACR,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;8CAKlB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAuD;;;;;;sDACxE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,6LAAC;oDACC,MAAK;oDACL,MAAK;oDACL,OAAO,SAAS,KAAK;oDACrB,UAAU;oDACV,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;8CAKlB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAuD;;;;;;sDACxE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,6LAAC;oDACC,MAAK;oDACL,MAAK;oDACL,OAAO,SAAS,QAAQ;oDACxB,UAAU;oDACV,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQ1B,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;;wCAAwD;wCAAW;;;;;;;8CACjF,6LAAC;oCAAE,WAAU;8CAAqC;;;;;;;;;;;;sCAGpD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAuD;;;;;;sDACxE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,+MAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;8DACrB,6LAAC;oDACC,MAAK;oDACL,MAAK;oDACL,OAAO,SAAS,UAAU;oDAC1B,UAAU;oDACV,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;8CAKlB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAuD;;;;;;sDACxE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,iNAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,6LAAC;oDACC,MAAK;oDACL,MAAK;oDACL,OAAO,SAAS,UAAU;oDAC1B,UAAU;oDACV,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;8CAKlB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,WAAU;sDAAuD;;;;;;sDACxE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,2NAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;8DACzB,6LAAC;oDACC,MAAK;oDACL,MAAK;oDACL,OAAO,SAAS,SAAS;oDACzB,UAAU;oDACV,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;8CAKlB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,WAAU;sDAAuD;;;;;;sDACxE,6LAAC;4CACC,MAAK;4CACL,OAAO,SAAS,QAAQ;4CACxB,UAAU;4CACV,MAAM;4CACN,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAIhB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAuD;;;;;;sDACxE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,2NAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;8DACzB,6LAAC;oDACC,MAAK;oDACL,MAAK;oDACL,OAAO,SAAS,MAAM;oDACtB,UAAU;oDACV,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;8CAKlB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAuD;;;;;;sDACxE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,WAAU;;sEACf,6LAAC;4DACC,MAAK;4DACL,MAAK;4DACL,OAAM;4DACN,SAAS,SAAS,cAAc,KAAK;4DACrC,UAAU;4DACV,WAAU;;;;;;sEAEZ,6LAAC;4DAAK,WAAU;sEAAgC;;;;;;;;;;;;8DAElD,6LAAC;oDAAM,WAAU;;sEACf,6LAAC;4DACC,MAAK;4DACL,MAAK;4DACL,OAAM;4DACN,SAAS,SAAS,cAAc,KAAK;4DACrC,UAAU;4DACV,WAAU;;;;;;sEAEZ,6LAAC;4DAAK,WAAU;sEAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQ9D,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;;wCAAwD;wCAAW;;;;;;;8CACjF,6LAAC;oCAAE,WAAU;8CAAqC;;;;;;;;;;;;sCAIpD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAAqD;;;;;;8CAIlE,6LAAC;oCAAM,WAAU;;sDACf,6LAAC;4CACC,MAAK;4CACL,MAAK;4CACL,SAAS,SAAS,aAAa;4CAC/B,UAAU;4CACV,QAAQ;4CACR,WAAU;;;;;;sDAEZ,6LAAC;4CAAK,WAAU;sDAA4C;;;;;;;;;;;;;;;;;;sCAKhE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAAqD;;;;;;8CAGlE,6LAAC;oCACC,MAAK;oCACL,QAAO;oCACP,KAAI;oCACJ,WAAU;;sDAEV,6LAAC;sDAAK;;;;;;sDACN,6LAAC,yNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;;8CAG1B,6LAAC;oCAAM,WAAU;;sDACf,6LAAC;4CACC,MAAK;4CACL,MAAK;4CACL,SAAS,SAAS,KAAK;4CACvB,UAAU;4CACV,QAAQ;4CACR,WAAU;;;;;;sDAEZ,6LAAC;4CAAK,WAAU;sDAA4C;;;;;;;;;;;;;;;;;;;;;;;;YAMtE;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;;;;;;;;;;0BAGjB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,aAAa;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCACpC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAEV,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCAAK,WAAU;kDAAiD;;;;;;;;;;;;0CAGnE,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;gCACR,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;oCACX;kDAEC,6LAAC;wCAAK,WAAU;kDAAiH;;;;;;;;;;;;0CAKnI,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;gCACP,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;0CACX;;;;;;;;;;;;kCAMH,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAoD;;;;;;kDACpE,6LAAC;wCAAK,WAAU;;4CAAgD;4CAAY;4CAAK;;;;;;;;;;;;;0CAEnF,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,OAAO;oCAAE;oCACpB,SAAS;wCAAE,OAAO,AAAC,GAAmC,OAAjC,AAAC,cAAc,aAAc,KAAI;oCAAG;oCACzD,YAAY;wCAAE,UAAU;oCAAI;;;;;;;;;;;;;;;;;kCAMlC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;kCAEV,cAAA,6LAAC;4BAAK,UAAU;;8CACd,6LAAC,4LAAA,CAAA,kBAAe;oCAAC,MAAK;8CACpB,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,MAAM;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC3B,YAAY;4CAAE,UAAU;wCAAI;kDAE3B;uCANI;;;;;;;;;;8CAWT,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;4CACZ,MAAK;4CACL,SAAS;4CACT,UAAU,gBAAgB;4CAC1B,WAAW,AAAC,0FAIX,OAHC,gBAAgB,IACZ,qCACA;4CAEN,YAAY,gBAAgB,IAAI;gDAAE,OAAO;4CAAK,IAAI,CAAC;4CACnD,UAAU,gBAAgB,IAAI;gDAAE,OAAO;4CAAK,IAAI,CAAC;;8DAEjD,6LAAC,uNAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;gDAAY;;;;;;;wCAIpC,cAAc,2BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;4CACZ,MAAK;4CACL,SAAS;4CACT,WAAU;4CACV,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,UAAU;gDAAE,OAAO;4CAAK;;gDACzB;8DAEC,6LAAC,yNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;;;;;;iEAG1B,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;4CACZ,MAAK;4CACL,WAAU;4CACV,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,UAAU;gDAAE,OAAO;4CAAK;;8DAExB,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAY;8DAE9B,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxC;GApcwB;KAAA", "debugId": null}}]}