"use client";
import React from "react";
import Image from "next/image";

const logos = [
  { src: "/seclit-logo-1.png", alt: "SecurityLit" },
  { src: "/seclit-logo-1.png", alt: "Enterprise Corp" },
  { src: "/seclit-logo-1.png", alt: "Tech Solutions" },
  { src: "/seclit-logo-1.png", alt: "Global Systems" },
  { src: "/seclit-logo-1.png", alt: "Innovation Labs" },
  { src: "/seclit-logo-1.png", alt: "Digital Ventures" },
  { src: "/seclit-logo-1.png", alt: "Future Tech" },
  { src: "/seclit-logo-1.png", alt: "Smart Solutions" },
];

const LogoCarouselSection = () => (
  <section className="w-full bg-[var(--color-brand-navy)] py-16 px-4 overflow-hidden">
    <div className="max-w-7xl mx-auto">
      {/* Header */}
      <div className="text-center mb-12">
        <h2 className="text-brand-light-blue text-3xl sm:text-4xl font-bold mb-4">
          Trusted by Leading Organizations
        </h2>
        <p className="text-blue-100 text-lg max-w-2xl mx-auto">
          Join hundreds of companies that rely on SecurityLit to protect their digital assets
        </p>
      </div>

      {/* Carousel Container */}
      <div className="relative">
        {/* Gradient Overlays */}
        <div className="absolute left-0 top-0 w-20 h-full bg-gradient-to-r from-[var(--color-brand-navy)] to-transparent z-10"></div>
        <div className="absolute right-0 top-0 w-20 h-full bg-gradient-to-l from-[var(--color-brand-navy)] to-transparent z-10"></div>
        
        {/* Carousel Track */}
        <div className="overflow-hidden">
          <div className="carousel-track flex gap-8 sm:gap-12 md:gap-16 items-center">
            {/* First set of logos */}
            {logos.map((logo, idx) => (
              <div 
                key={`first-${idx}`} 
                className="flex-shrink-0 group hover:scale-105 transition-transform duration-300"
              >
                <div className="w-44 h-28 bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 border border-white/20 p-6 flex items-center justify-center group-hover:border-[var(--color-brand-blue)]/50">
                  <Image
                    src={logo.src}
                    alt={logo.alt}
                    width={140}
                    height={80}
                    className="object-contain w-full h-full filter grayscale hover:grayscale-0 transition-all duration-300 opacity-70 group-hover:opacity-100"
                    priority={idx === 0}
                  />
                </div>
              </div>
            ))}
            
            {/* Second set of logos for seamless loop */}
            {logos.map((logo, idx) => (
              <div 
                key={`second-${idx}`} 
                className="flex-shrink-0 group hover:scale-105 transition-transform duration-300"
              >
                <div className="w-44 h-28 bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 border border-white/20 p-6 flex items-center justify-center group-hover:border-[var(--color-brand-blue)]/50">
                  <Image
                    src={logo.src}
                    alt={logo.alt}
                    width={140}
                    height={80}
                    className="object-contain w-full h-full filter grayscale hover:grayscale-0 transition-all duration-300 opacity-70 group-hover:opacity-100"
                    priority={false}
                  />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>

    <style jsx global>{`
      .carousel-track {
        animation: carousel-scroll 40s linear infinite;
      }
      
      @keyframes carousel-scroll {
        0% { transform: translateX(0); }
        100% { transform: translateX(-50%); }
      }
      
      .carousel-track:hover {
        animation-play-state: paused;
      }
      
      @media (max-width: 768px) {
        .carousel-track {
          animation: carousel-scroll 30s linear infinite;
        }
      }
      
      @media (prefers-reduced-motion: reduce) {
        .carousel-track {
          animation: none;
        }
      }
    `}</style>
  </section>
);

export default LogoCarouselSection;
