 
"use client";
import React from 'react';

const Hero = () => {
  const scrollToForm = () => {
    const formSection = document.getElementById('apply-form');
    if (formSection) {
      formSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section className="  relative w-full min-h-[90vh] flex items-center justify-center overflow-hidden bg-[#fbf7ff]">
      {/* Content */}
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 sm:py-24 lg:py-26">
        <div className="text-center">
          {/* Badge */}
          <div className="inline-flex items-center space-x-2 bg-[#027bfc]/10 px-4 py-2 rounded-lg border border-[#027bfc]/20">
            <span className="w-2 h-2 bg-[#027bfc] rounded-full animate-pulse"></span>
            <span className="text-sm font-medium text-[#027bfc]">Startup Program</span>
          </div>

          {/* Main heading */}
          <h1 className="mt-8 text-4xl sm:text-5xl md:text-6xl lg:text-6xl font-semibold text-slate-900 leading-tight tracking-tight">
            Capture The Bug for <span className="text-ctb-light-blue">Startups</span>
          </h1>

          {/* Subheading */}
          <p className="mt-6 text-lg sm:text-xl md:text-2xl text-slate-600 max-w-3xl mx-auto">
            Ship fast and earn trust with{' '}
            <span className="text-[#027bfc]">
              real-time pentesting
            </span>
          </p>

          {/* Credit card */}
          <div className="mt-12 max-w-md mx-auto">
            <div className="relative">
              {/* Card shadow */}
              <div className="absolute -inset-1 bg-gradient-to-r from-[#027bfc] to-[#027bfc]/80 rounded-2xl blur opacity-20"></div>
              
              {/* Main card */}
              <div className="relative bg-white rounded-2xl p-8 border border-slate-200 shadow-lg">
                <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-6">
                  {/* Left side */}
                  <div className="text-center md:text-left">
                    <div className="inline-flex items-center gap-2">
                      <div className="w-12 h-12 rounded-full bg-[#027bfc]/10 flex items-center justify-center">
                        <svg className="w-6 h-6 text-[#027bfc]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <div>
                        <p className="text-3xl md:text-4xl font-bold text-[#027bfc]">$1,000</p>
                        <p className="text-slate-600 mt-1 text-sm md:text-base">Pentest Credit</p>
                      </div>
                    </div>
                  </div>

                  {/* Right side */}
                  <div className="text-center md:text-right">
                    <div className="inline-block bg-[#027bfc]/5 px-4 py-2 rounded-lg border border-[#027bfc]/10">
                      <p className="text-[#027bfc] font-medium text-sm">Limited Time Offer</p>
                      <p className="text-slate-500 text-xs mt-1">For Early-Stage Teams</p>
                    </div>
                  </div>
                </div>

                {/* Card footer */}
                <div className="mt-6 pt-6 border-t border-slate-100">
                  <div className="flex items-center justify-center gap-2 text-slate-500 text-sm">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span>Valid for 30 days after claim</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Description */}
          <p className="mt-12 text-base sm:text-lg text-slate-600 max-w-3xl mx-auto leading-relaxed">
            Secure your product, impress enterprise buyers, and get audit-ready - with real, manual pentesting built for startups. Whether you&apos;re prepping for SOC 2, onboarding your first big customer, or just shipping fast and want to stay secure, we&apos;ve got your back.
          </p>

          {/* CTA Button */}
          <div className="mt-12">
            <button 
              onClick={scrollToForm}
              className="px-12 py-4 bg-[#027bfc] hover:bg-[#027bfc]/90 text-white font-semibold rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl flex items-center justify-center gap-2 mx-auto"
            >
              Claim Your Credit
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
