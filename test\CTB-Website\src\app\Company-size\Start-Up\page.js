import Startup from './startup';

 
export const metadata = {
  title: "Capture The Bug | Pentesting for Startups Made Simple",
  description: "Move fast and stay secure. Capture The Bug helps startups meet security requirements, pass audits, and ship safely with expert-led PTaaS.",
  keywords: "startup pentesting, pentest for startups, PTaaS for startups, security compliance for startups, SOC 2 readiness, fast pentesting, developer-friendly security testing, Capture The Bug",
  robots: "index, follow",
  icons: {
    icon: '/favicon.ico',
  },
  openGraph: {
    title: "Capture The Bug | Pentesting for Startups Made Simple",
    type: "website",
    url: "https://capturethebug.xyz/Company-size/Start-Up",
    description: "Whether you're raising your first round or closing enterprise deals, Capture The Bug helps you meet security requirements with real human-led pentesting-on your schedule, inside your workflow.",
    images: "https://i.ibb.co/1fqYtprh/Screenshot-2025-06-19-113707.png",
  },
  twitter: {
    card: 'summary_large_image',
    title: "Capture The Bug | Pentesting for Startups Made Simple",
    description: "Get dev-friendly, fast, and human-led pentesting. Capture The Bug helps startups launch secure apps and meet compliance with ease.",
    images: "https://i.ibb.co/1fqYtprh/Screenshot-2025-06-19-113707.png",
  }
}; 

export default function Page() {
  return <Startup />;
}
