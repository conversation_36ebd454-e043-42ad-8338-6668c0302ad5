import { NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';
import { isBusinessEmail } from '../../utils/emailValidator';
import { sendEmail } from '../../utils/send-email';
import { generateDownloadToken } from '../download-report/route';

// Constants
const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB max file size
const MAX_SUBMISSIONS = 10000; // Maximum number of submissions to keep
const SUBMISSIONS_PATH = path.join(process.cwd(), 'data', 'submissions.json');

// Helper function to ensure data directory exists
async function ensureDataDirectory() {
  const dataDir = path.join(process.cwd(), 'data');
  try {
    await fs.access(dataDir);
  } catch {
    await fs.mkdir(dataDir, { recursive: true });
  }
}

// Helper function to read submissions file
async function readSubmissions() {
  try {
    const content = await fs.readFile(SUBMISSIONS_PATH, 'utf8');
    return JSON.parse(content);
  } catch (error) {
    if (error.code === 'ENOENT') {
      return [];
    }
    throw error;
  }
}

// Helper function to write submissions file
async function writeSubmissions(submissions) {
  // Check file size before writing
  const content = JSON.stringify(submissions, null, 2);
  if (content.length > MAX_FILE_SIZE) {
    // If file is too large, keep only the most recent submissions
    submissions = submissions.slice(-MAX_SUBMISSIONS);
  }
  
  // Write to a temporary file first
  const tempPath = `${SUBMISSIONS_PATH}.tmp`;
  await fs.writeFile(tempPath, JSON.stringify(submissions, null, 2));
  
  // Atomic rename
  await fs.rename(tempPath, SUBMISSIONS_PATH);
}

export async function POST(request) {
  try {
    // Ensure data directory exists
    await ensureDataDirectory();

    // Parse and validate request data
    const data = await request.json();
    if (!data || typeof data !== 'object') {
      return NextResponse.json(
        { error: 'Invalid submission data' },
        { status: 400 }
      );
    }

    // Required fields per form type
    const requiredFieldsMap = {
      'download-report': ['businessEmail', 'firstName', 'lastName', 'jobTitle', 'company'],
      'request-demo': ['firstName', 'lastName', 'businessEmail', 'company', 'phoneNumber', 'industry', 'message'],
      'request-quote': ['firstName', 'lastName', 'businessEmail', 'company', 'message'],
    };

    const formType = data.formType;
    if (!formType || !requiredFieldsMap[formType]) {
      return NextResponse.json(
        { error: 'Missing or unknown formType.' },
        { status: 400 }
      );
    }

    // Validate required fields for this formType
    const requiredFields = requiredFieldsMap[formType];
    const missingFields = requiredFields.filter(field => !data[field] || String(data[field]).trim() === '');
    if (missingFields.length > 0) {
      return NextResponse.json(
        { error: `Missing required field(s): ${missingFields.join(', ')}` },
        { status: 400 }
      );
    }

    // Email validation
    const emailToValidate = data.businessEmail || data.email || null;
    if (!emailToValidate || !isBusinessEmail(emailToValidate)) {
      return NextResponse.json(
        { error: 'Please use a valid business email address.' },
        { status: 400 }
      );
    }

    // Add timestamp if not present
    const submissionWithTimestamp = {
      ...data,
      timestamp: data.timestamp || new Date().toISOString()
    };

    // Read existing submissions
    const submissions = await readSubmissions();

    // Add new submission
    submissions.push(submissionWithTimestamp);

    // Write back to file
    await writeSubmissions(submissions);

    // Send email with form data
    const subject = data.subject || 'New Form Submission';
    const body = Object.entries(data).map(([key, value]) => `${key}: ${value}`).join('\n');
    await sendEmail(subject, body);

    // If this is a download-report form, generate a one-time token and return it
    if (formType === 'download-report') {
      const token = generateDownloadToken();
      return NextResponse.json({ success: true, token });
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error storing submission or sending email:', error);
    return NextResponse.json(
      { error: 'Failed to store submission or send email' },
      { status: 500 }
    );
  }
} 