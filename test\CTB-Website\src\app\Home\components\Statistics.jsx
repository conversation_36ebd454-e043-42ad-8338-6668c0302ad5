"use client";

import React, { useState, useEffect, useRef } from 'react';
import Button from '../../common/buttons/Button';
import Link from 'next/link';
import { motion, useInView } from 'framer-motion';

const statsData = [
  {
    id: 'roi',
    value: "4x",
    numericValue: 4,
    suffix: "x",
    description: "Faster Fixes",
    detail: "Engineering teams reduce time-to-remediate critical vulnerabilities by up to 4x"
  },
  {
    id: 'payback',
    value: "< 3",
    numericValue: 3,
    prefix: "< ",
    unit: "Hours",
    description: "to Insight",
    detail: "Validated vulnerability reports, delivered in under 3 hours - no more waiting weeks for results"
  },
  {
    id: 'productivity',
    value: "90",
    numericValue: 90,
    suffix: "%",
    description: "Less Noise",
    detail: "Expert triage cuts out 90% of false positives compared to traditional pentesting tools"
  },
];

// Counter hook for number animation
const useCounter = (end, duration = 2000, shouldStart = false) => {
  const [count, setCount] = useState(0);

  useEffect(() => {
    if (!shouldStart) return;

    let startTime;
    let animationFrame;

    const animate = (timestamp) => {
      if (!startTime) startTime = timestamp;
      const progress = Math.min((timestamp - startTime) / duration, 1);
      
      // Easing function for smooth animation
      const easeOutCubic = 1 - Math.pow(1 - progress, 3);
      setCount(Math.floor(end * easeOutCubic));

      if (progress < 1) {
        animationFrame = requestAnimationFrame(animate);
      } else {
        setCount(end);
      }
    };

    animationFrame = requestAnimationFrame(animate);

    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
      }
    };
  }, [end, duration, shouldStart]);

  return count;
};

// Animated Counter Component
const AnimatedCounter = ({ stat, shouldAnimate, delay = 0 }) => {
  const [startAnimation, setStartAnimation] = useState(false);
  const counter = useCounter(stat.numericValue, 2000, startAnimation);

  useEffect(() => {
    if (shouldAnimate) {
      const timer = setTimeout(() => {
        setStartAnimation(true);
      }, delay);
      return () => clearTimeout(timer);
    }
  }, [shouldAnimate, delay]);

  return (
    <div className="flex items-center justify-center">
      <span className="text-5xl sm:text-6xl md:text-7xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-primary-blue to-secondary-blue">
        {stat.prefix && stat.prefix}
        {startAnimation ? counter : 0}
        {stat.suffix && stat.suffix}
      </span>
      {stat.unit && (
        <span className="text-2xl sm:text-3xl font-semibold text-secondary-blue ml-1">
          {stat.unit}
        </span>
      )}
    </div>
  );
};

// Enhanced animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
      delayChildren: 0.3
    }
  }
};

const headerVariants = {
  hidden: { opacity: 0, y: -20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      type: "spring",
      stiffness: 100,
      damping: 15,
      duration: 0.6
    }
  }
};

const statCardVariants = {
  hidden: { opacity: 0, y: 40, scale: 0.95 },
  visible: i => ({
    opacity: 1,
    y: 0,
    scale: 1,
    transition: {
      type: "spring",
      stiffness: 80,
      damping: 20,
      delay: i * 0.15,
      duration: 0.8
    }
  })
};

const valueVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      type: "spring",
      stiffness: 100,
      damping: 15,
      delay: 0.3,
      duration: 0.5
    }
  }
};

const lineVariants = {
  hidden: { width: 0 },
  visible: {
    width: "100%",
    transition: {
      duration: 1,
      ease: "easeInOut",
      delay: 0.5
    }
  }
};

const buttonVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      type: "spring",
      stiffness: 100,
      damping: 15,
      delay: 0.8
    }
  }
};

export default function Statistics() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, amount: 0.3 });

  return (
    <section className="w-full py-24 sm:py-28 md:py-32 bg-gradient-to-b from-white to-ctb-bg-light/70 relative overflow-hidden">
      {/* Premium background elements */}
      <div className="absolute inset-0 bg-[url('/images/grid-pattern.svg')] bg-repeat opacity-[0.03]"></div>
      <div className="absolute -left-64 top-1/3 w-96 h-96 rounded-full bg-primary-blue/5 blur-3xl"></div>
      <div className="absolute -right-64 bottom-1/3 w-96 h-96 rounded-full bg-ctb-green-50/5 blur-3xl"></div>
      
      {/* Main content container */}
      <motion.div 
        ref={ref}
        key="content"
        className="max-w-screen-2xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8 relative z-10"
        variants={containerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.1 }}
      >
        {/* Header with premium styling */}
        <motion.div 
          className="text-center mb-16 sm:mb-20"
          variants={headerVariants}
        >
          <span className="inline-block px-4 py-1.5 mb-4 rounded-full bg-primary-blue/10 text-primary-blue text-sm font-medium tracking-wide">MEASURABLE IMPACT</span>
          <h2 className="text-3xl sm:text-4xl md:text-5xl font-[500] text-tertiary-blue mb-6">
            Real Results with{" "}
            <span className="text-primary-blue font-[600] relative">
              Capture The Bug
              <motion.span 
                className="absolute -bottom-1 left-0 right-0 h-[3px] bg-primary-blue/60"
                variants={lineVariants}
              ></motion.span>
            </span>
          </h2>
          <p className="text-base sm:text-lg text-secondary-blue/90 max-w-3xl mx-auto leading-relaxed">
            Our platform delivers measurable security improvements from day one, with validated results that demonstrate real ROI.
          </p>
        </motion.div>

        {/* Premium Stats Grid */}
        <div className="md:px-4 lg:px-8 xl:px-16">
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-8 md:gap-10 lg:gap-12">
          {statsData.map((stat, index) => (
            <motion.div
              key={stat.id}
              custom={index}
              variants={statCardVariants}
              className="group relative bg-white rounded-2xl shadow-xl overflow-hidden border border-primary-blue/5 hover:shadow-2xl transition-all duration-700"
            >
              {/* Card background effects */}
              <div className="absolute inset-0 bg-gradient-to-br from-white via-white to-ctb-bg-light/30 opacity-80"></div>
              <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-primary-blue to-secondary-blue transform origin-left scale-x-0 group-hover:scale-x-100 transition-transform duration-700"></div>
              <div className="absolute -right-16 -top-16 w-32 h-32 bg-primary-blue/5 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-700"></div>
              
              <div className="relative p-8 sm:p-10 flex flex-col items-center">
                {/* Stat Value with premium styling and counter animation */}
                <motion.div 
                  className="text-center mb-5"
                  variants={valueVariants}
                >
                  <AnimatedCounter 
                    stat={stat} 
                    shouldAnimate={isInView}
                    delay={index * 300} // Staggered delay for each counter
                  />
                  <h3 className="text-xl sm:text-2xl font-semibold text-tertiary-blue mt-2">
                    {stat.description}
                  </h3>
                </motion.div>
                
                {/* Divider line */}
                <div className="w-16 h-0.5 bg-gradient-to-r from-primary-blue/60 to-secondary-blue/60 rounded-full my-3"></div>
                
                {/* Description with improved typography */}
                <p className="text-base sm:text-lg text-gray-600 text-center leading-relaxed">
                  {stat.detail}
                </p>
                
                {/* Subtle dot pattern */}
                <div className="absolute right-4 bottom-4 grid grid-cols-3 gap-1 opacity-30 group-hover:opacity-60 transition-opacity duration-500">
                  {[...Array(6)].map((_, i) => (
                    <div 
                      key={i} 
                      className="w-1 h-1 rounded-full bg-primary-blue"
                      style={{
                        opacity: ((i % 3) * 0.2) + 0.2
                      }}
                    />
                  ))}
                </div>
              </div>
            </motion.div>
          ))}
          </div>
        </div>

        {/* Premium Call to Action Button */}
        <motion.div
          className="text-center mt-12 sm:mt-16"
          variants={buttonVariants}
        >
          <Button
            href="/Request-Demo"
            variant="primary"
            size="lg"
            className="px-6 py-3 sm:px-8 sm:py-4 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
            rightIcon={
              <svg className="ml-2 w-5 h-5 transform group-hover:translate-x-1 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
              </svg>
            }
          >
            See Real Results
          </Button>
        </motion.div>
      </motion.div>
    </section>
  );
}