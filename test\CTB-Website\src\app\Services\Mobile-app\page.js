import Mobileapp from "./mobileapp";

 
export const metadata = {
  title: "Capture The Bug | Mobile App Penetration Testing Services",
  description:
    "Capture The Bug provides expert-led pentesting for iOS and Android apps. Detect API misconfigurations, insecure storage, authentication flaws, and more through our modern PTaaS platform.",
 keywords: "mobile app penetration testing, iOS and Android pentesting, OWASP mobile top 10, mobile API security, insecure local storage, auth testing, mobile PTaaS, Capture The Bug",
  robots: "index, follow",
  icons: {
    icon: "/favicon.ico",
  },
  openGraph: {
    title: "Capture The Bug | Mobile App Penetration Testing Services",
    type: "website",
    url: "https://capturethebug.xyz/Services/Mobile-app",
    description:
      "Get in-depth security testing for iOS and Android apps with Capture The Bug. Find real vulnerabilities in mobile APIs, local storage, authentication, and more.",
    images: "https://i.ibb.co/Mkwc2dkr/Screenshot-2025-06-18-200019.png",
  },
  twitter: {
    card: "summary_large_image",
    title: "Capture The Bug | Mobile App Pentesting",
    description:
      "On-demand pentesting for Android and iOS apps-uncover API flaws, auth issues, and insecure storage with Capture The Bug’s PTaaS model.",
    images: "https://i.ibb.co/Mkwc2dkr/Screenshot-2025-06-18-200019.png",
  },
};


export default function Page() {
  return   <Mobileapp /> ;
}
