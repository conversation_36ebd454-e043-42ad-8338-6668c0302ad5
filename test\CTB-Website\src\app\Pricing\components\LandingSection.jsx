"use client";

import React from 'react';
import Button from "@/app/common/buttons/Button";

const pricingPlans = [
  {
    id: "starter",
    name: "Starter",
    price: 18000,
    currency: "USD",
    period: "yr",
    description: "Initial 15-day pentest + 1-2 day(s)/mo ongoing",
    buttonText: "Choose Starter",
    bestFor: "Best for: MVP-stage SaaS, small sites",
    popular: false,
    isEnterprise: false
  },
  {
    id: "growth",
    name: "Growth",
    price: 27000,
    currency: "USD",
    period: "yr",
    description: "Initial 15-day pentest + 2 day(s)/mo ongoing",
    buttonText: "Choose Growth",
    bestFor: "Best for: Scaling apps, Series A/B",
    popular: true,
    isEnterprise: false
  },
  {
    id: "enterprise",
    name: "Enterprise",
    price: null,
    currency: "USD",
    period: "yr",
    description: "Custom pentesting solution tailored to your enterprise needs",
    buttonText: "Talk to Sales",
    bestFor: "Best for: Large enterprises",
    popular: false,
    isEnterprise: true
  }
];

export default function LandingPage() {
  const formatPrice = (price) => {
    return new Intl.NumberFormat('en-US', {
      style: 'decimal',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price);
  };

  return (
    <section className="w-full py-16 sm:py-20 md:py-24 bg-[#fbf7ff]">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12 sm:mb-16">
          <h2 className="text-3xl sm:text-4xl md:text-5xl font-semibold text-[#06258d] mb-6">
            Penetration Testing Subscription Plans
          </h2>
          <p className="text-lg sm:text-xl text-[#06258d] max-w-3xl mx-auto leading-relaxed">
            Move beyond one-off tests and secure your product lifecycle with predictable, ongoing coverage.
          </p>
        </div>

        {/* Pricing Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 lg:gap-8">
          {pricingPlans.map((plan) => (
            <div
              key={plan.id}
              className={`relative bg-white rounded-2xl p-8 transition-all duration-300 flex flex-col h-full ${
                plan.popular 
                  ? 'border-2 shadow-xl transform scale-105' 
                  : 'border border-gray-200 shadow-lg hover:shadow-xl'
              }`}
              style={{
                borderColor: plan.popular ? '#027bfd' : undefined
              }}
            >
              {/* Popular Badge */}
              {plan.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <span 
                    className="text-white px-4 py-1 rounded-full text-sm font-semibold"
                    style={{ backgroundColor: '#027bfd' }}
                  >
                    Most Popular
                  </span>
                </div>
              )}

              {/* Plan Name */}
              <div className="text-center mb-6 flex-grow">
                <h3 className="text-2xl font-bold text-gray-900 mb-4">
                  {plan.name}
                </h3>
                
                {/* Price - Fixed height container for consistency */}
                <div className="mb-4 h-20 flex flex-col justify-center">
                  {plan.isEnterprise ? (
                    <div className="text-4xl sm:text-5xl font-bold text-[#1e83fb]">
                      Custom
                    </div>
                  ) : (
                    <>
                      <span 
                        className="text-4xl sm:text-5xl font-bold"
                        style={{ color: plan.popular ? '#032391' : plan.id === "starter" ? '#1e83fb' : '#1f2937' }}
                      >
                        ${formatPrice(plan.price)}
                      </span>
                      <span className="text-lg text-gray-600">
                        {plan.currency} / {plan.period}
                      </span>
                    </>
                  )}
                </div>

                {/* Description - Fixed height for consistency */}
                <div className="h-16 flex items-center justify-center">
                  <p className="text-gray-600 text-sm leading-relaxed">
                    {plan.description}
                  </p>
                </div>
              </div>

              {/* CTA Button - Consistent styling */}
              <div className="mb-6 mt-auto">
                <Button
                  variant={plan.popular ? 'primary' : plan.id === "starter" || plan.isEnterprise ? 'secondary' : 'primary'}
                  fullWidth={true}
                  className="py-3 px-6 font-semibold text-base"
                  href={plan.isEnterprise ? '/Request-Demo' : '/signup'}
                >
                  {plan.buttonText}
                </Button>
              </div>

              {/* Best For - Consistent positioning */}
              <div className="text-center">
                <p className="text-sm text-gray-500">
                  {plan.bestFor}
                </p>
              </div>
            </div>
          ))}
        </div>

        {/* Enhanced Footer Section */}
        <div className="text-center mt-12 sm:mt-16">
          <div className="inline-flex items-center gap-3 border-2 border-[#027bfd] text-gray-700 px-6 py-3 rounded-full">
            <div 
              className="w-2 h-2 rounded-full"
              style={{ backgroundColor: '#027bfd' }}
            ></div>
            <span className="font-medium">
              All plans include comprehensive security testing and detailed reporting
            </span>
          </div>
        </div>
      </div>
    </section>
  );
}
