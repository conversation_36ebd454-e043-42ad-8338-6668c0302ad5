"use client";

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Search, Filter, Calendar, Tag, Star, ArrowRight, ChevronDown } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';
import { 
  blogs, 
  getAllCategories, 
  getAllTags, 
  searchBlogs, 
  getBlogsByCategory, 
  getBlogsByTag 
} from '../Blogs';

// Get categories and tags from centralized blog data
const categories = ['All Categories', ...getAllCategories()];
const tags = getAllTags();

export default function BlogListing() {
  const [filteredBlogs, setFilteredBlogs] = useState(blogs);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All Categories');
  const [selectedTags, setSelectedTags] = useState([]);
  const [showFeatured, setShowFeatured] = useState(false);
  const [sortBy, setSortBy] = useState('date');
  const [visibleCount, setVisibleCount] = useState(6);
  const [showFilters, setShowFilters] = useState(false);

  // Filter and sort blogs
  useEffect(() => {
    let filtered = blogs;

    // Search filter
    if (searchTerm) {
      filtered = searchBlogs(searchTerm);
    }

    // Category filter
    if (selectedCategory !== 'All Categories') {
      filtered = filtered.filter(blog => blog.category === selectedCategory);
    }

    // Tags filter
    if (selectedTags.length > 0) {
      filtered = filtered.filter(blog =>
        selectedTags.some(tag => blog.tags.includes(tag))
      );
    }

    // Featured filter
    if (showFeatured) {
      filtered = filtered.filter(blog => blog.featured);
    }

    // Sort
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'date':
          return new Date(b.date) - new Date(a.date);
        case 'title':
          return a.title.localeCompare(b.title);
        case 'readTime':
          return parseInt(a.readTime) - parseInt(b.readTime);
        default:
          return 0;
      }
    });

    setFilteredBlogs(filtered);
    setVisibleCount(6); // Reset visible count when filters change
  }, [searchTerm, selectedCategory, selectedTags, showFeatured, sortBy]);

  const handleLoadMore = () => {
    setVisibleCount(prev => prev + 6);
  };

  const toggleTag = (tag) => {
    setSelectedTags(prev =>
      prev.includes(tag)
        ? prev.filter(t => t !== tag)
        : [...prev, tag]
    );
  };

  const clearFilters = () => {
    setSearchTerm('');
    setSelectedCategory('All Categories');
    setSelectedTags([]);
    setShowFeatured(false);
    setSortBy('date');
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <div className="bg-gradient-to-br from-[var(--color-dark-blue)] to-[var(--color-dark-blue-hover)] text-white py-20 pt-32">
        <div className="container mx-auto px-6 lg:px-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center"
          >
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
              SecurityLit Blog
            </h1>
            <p className="text-xl md:text-2xl text-gray-200 mb-8 max-w-3xl mx-auto">
              Latest cybersecurity insights, industry updates, and expert analysis from our security professionals
            </p>
                         <div className="flex flex-wrap justify-center gap-4 text-sm">
               <div className="bg-white/10 backdrop-blur-sm rounded-full px-4 py-2">
                 <span className="font-semibold">{blogs.length}+</span> Articles
               </div>
               <div className="bg-white/10 backdrop-blur-sm rounded-full px-4 py-2">
                 <span className="font-semibold">{categories.length - 1}+</span> Categories
               </div>
               <div className="bg-white/10 backdrop-blur-sm rounded-full px-4 py-2">
                 <span className="font-semibold">{tags.length}+</span> Tags
               </div>
             </div>
          </motion.div>
        </div>
      </div>

      {/* Filters Section */}
      <div className="bg-gray-50 border-b">
        <div className="container mx-auto px-6 lg:px-16 py-8">
          <div className="flex flex-col lg:flex-row gap-6">
            {/* Search Bar */}
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="text"
                  placeholder="Search articles..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--color-blue)] focus:border-transparent"
                />
              </div>
            </div>
            {/* Filter Toggle */}
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-2 px-4 py-3 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <Filter className="w-5 h-5" />
              Filters
              <ChevronDown className={`w-4 h-4 transition-transform ${showFilters ? 'rotate-180' : ''}`} />
            </button>
          </div>
          {/* Advanced Filters */}
          {showFilters && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="mt-6 p-6 bg-white rounded-lg border border-gray-200"
            >
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {/* Category Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Category</label>
                  <select
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--color-blue)] focus:border-transparent"
                  >
                    {categories.map(category => (
                      <option key={category} value={category}>{category}</option>
                    ))}
                  </select>
                </div>
                {/* Sort By */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Sort By</label>
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--color-blue)] focus:border-transparent"
                  >
                    <option value="date">Date</option>
                    <option value="title">Title</option>
                    <option value="readTime">Read Time</option>
                  </select>
                </div>
                {/* Featured Filter */}
                <div className="flex items-center">
                  <label className="flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={showFeatured}
                      onChange={(e) => setShowFeatured(e.target.checked)}
                      className="mr-2 w-4 h-4 text-[var(--color-blue)] focus:ring-[var(--color-blue)] border-gray-300 rounded"
                    />
                    <span className="text-sm font-medium text-gray-700">Featured Only</span>
                  </label>
                </div>
                {/* Clear Filters */}
                <div className="flex items-end">
                  <button
                    onClick={clearFilters}
                    className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors"
                  >
                    Clear All Filters
                  </button>
                </div>
              </div>
              {/* Tags Filter */}
              <div className="mt-6">
                <label className="block text-sm font-medium text-gray-700 mb-3">Tags</label>
                <div className="flex flex-wrap gap-2">
                  {tags.map(tag => (
                    <button
                      key={tag}
                      onClick={() => toggleTag(tag)}
                      className={`px-3 py-1 text-sm rounded-full border transition-colors ${
                        selectedTags.includes(tag)
                          ? 'bg-[var(--color-blue)] text-white border-[var(--color-blue)]'
                          : 'bg-white text-gray-700 border-gray-300 hover:border-[var(--color-blue)]'
                      }`}
                    >
                      {tag}
                    </button>
                  ))}
                </div>
              </div>
            </motion.div>
          )}
        </div>
      </div>

      {/* Blog Sections */}
      <div className="container mx-auto px-6 lg:px-16 py-12">
        {/* Trending Blogs Section */}
        <div className="mb-16">
          <h2 className="text-2xl md:text-3xl font-bold text-[var(--color-dark-blue)] mb-8">Trending Blogs</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredBlogs.filter(blog => blog.featured).length === 0 && (
              <div className="col-span-full text-center text-gray-400">No trending blogs found.</div>
            )}
            {filteredBlogs.filter(blog => blog.featured).map((blog, index) => (
              <motion.article
                key={blog.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden group border-2 border-[var(--color-yellow)]"
              >
                {/* Blog Image */}
                <div className="relative h-56 overflow-hidden">
                  <Image
                    src={blog.image}
                    alt={blog.title}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute top-4 left-4 bg-[var(--color-yellow)] text-[var(--color-dark-blue)] px-3 py-1 rounded-full text-sm font-semibold flex items-center gap-1">
                    <Star className="w-4 h-4" />
                    Trending
                  </div>
                  <div className="absolute top-4 right-4 bg-black/50 text-white px-3 py-1 rounded-full text-sm">
                    {blog.readTime}
                  </div>
                </div>
                {/* Blog Content */}
                <div className="p-6">
                  <div className="mb-3">
                    <span className="text-sm text-[var(--color-blue)] font-medium">
                      {blog.category}
                    </span>
                  </div>
                  <h3 className="text-xl font-bold text-[var(--color-dark-blue)] mb-3 line-clamp-2 group-hover:text-[var(--color-blue)] transition-colors">
                    {blog.title}
                  </h3>
                  <p className="text-gray-600 mb-4 line-clamp-3">
                    {blog.excerpt}
                  </p>
                  <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                    <span>{blog.author}</span>
                    <span>{formatDate(blog.date)}</span>
                  </div>
                  <div className="flex flex-wrap gap-2 mb-4">
                    {blog.tags.slice(0, 3).map(tag => (
                      <span
                        key={tag}
                        className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full"
                      >
                        {tag}
                      </span>
                    ))}
                    {blog.tags.length > 3 && (
                      <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                        +{blog.tags.length - 3}
                      </span>
                    )}
                  </div>
                  <Link
                    href={`/blogs/${blog.slug}`}
                    className="inline-flex items-center gap-2 text-[var(--color-blue)] font-semibold hover:text-[var(--color-dark-blue)] transition-colors group/link"
                  >
                    Read More
                    <ArrowRight className="w-4 h-4 group-hover/link:translate-x-1 transition-transform" />
                  </Link>
                </div>
              </motion.article>
            ))}
          </div>
        </div>
        {/* Latest Blogs Section */}
        <div>
          <h2 className="text-2xl md:text-3xl font-bold text-[var(--color-dark-blue)] mb-8">Latest Blogs</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            {filteredBlogs.filter(blog => !blog.featured).length === 0 && (
              <div className="col-span-full text-center text-gray-400">No latest blogs found.</div>
            )}
            {filteredBlogs.filter(blog => !blog.featured).slice(0, visibleCount).map((blog, index) => (
              <motion.article
                key={blog.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden group"
              >
                {/* Blog Image */}
                <div className="relative h-48 overflow-hidden">
                  <Image
                    src={blog.image}
                    alt={blog.title}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute top-4 right-4 bg-black/50 text-white px-3 py-1 rounded-full text-sm">
                    {blog.readTime}
                  </div>
                </div>
                {/* Blog Content */}
                <div className="p-6">
                  <div className="mb-3">
                    <span className="text-sm text-[var(--color-blue)] font-medium">
                      {blog.category}
                    </span>
                  </div>
                  <h3 className="text-xl font-bold text-[var(--color-dark-blue)] mb-3 line-clamp-2 group-hover:text-[var(--color-blue)] transition-colors">
                    {blog.title}
                  </h3>
                  <p className="text-gray-600 mb-4 line-clamp-3">
                    {blog.excerpt}
                  </p>
                  <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                    <span>{blog.author}</span>
                    <span>{formatDate(blog.date)}</span>
                  </div>
                  <div className="flex flex-wrap gap-2 mb-4">
                    {blog.tags.slice(0, 3).map(tag => (
                      <span
                        key={tag}
                        className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full"
                      >
                        {tag}
                      </span>
                    ))}
                    {blog.tags.length > 3 && (
                      <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                        +{blog.tags.length - 3}
                      </span>
                    )}
                  </div>
                  <Link
                    href={`/blogs/${blog.slug}`}
                    className="inline-flex items-center gap-2 text-[var(--color-blue)] font-semibold hover:text-[var(--color-dark-blue)] transition-colors group/link"
                  >
                    Read More
                    <ArrowRight className="w-4 h-4 group-hover/link:translate-x-1 transition-transform" />
                  </Link>
                </div>
              </motion.article>
            ))}
          </div>
          {/* Load More Button */}
          {visibleCount < filteredBlogs.filter(blog => !blog.featured).length && (
            <div className="text-center">
              <button
                onClick={handleLoadMore}
                className="px-8 py-3 bg-[var(--color-blue)] text-white rounded-lg hover:bg-[var(--color-dark-blue)] transition-colors font-semibold"
              >
                Load More Articles
              </button>
            </div>
          )}
        </div>
        {/* No Results */}
        {filteredBlogs.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <Search className="w-16 h-16 mx-auto" />
            </div>
            <h3 className="text-xl font-semibold text-gray-600 mb-2">No articles found</h3>
            <p className="text-gray-500 mb-6">Try adjusting your search criteria or filters</p>
            <button
              onClick={clearFilters}
              className="px-6 py-2 bg-[var(--color-blue)] text-white rounded-lg hover:bg-[var(--color-dark-blue)] transition-colors"
            >
              Clear All Filters
            </button>
          </div>
        )}
      </div>
    </div>
  );
} 