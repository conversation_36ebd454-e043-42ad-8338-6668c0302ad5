{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/training/components/HeroBento.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/training/components/HeroBento.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/training/components/HeroBento.jsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6S,GAC1U,2EACA", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/training/components/HeroBento.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/training/components/HeroBento.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/training/components/HeroBento.jsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyR,GACtT,uDACA", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/training/components/CurriculumHighlights.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/training/components/CurriculumHighlights.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/training/components/CurriculumHighlights.jsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAwT,GACrV,sFACA", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/training/components/CurriculumHighlights.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/training/components/CurriculumHighlights.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/training/components/CurriculumHighlights.jsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoS,GACjU,kEACA", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/training/components/WhoCanJoin.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/training/components/WhoCanJoin.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/training/components/WhoCanJoin.jsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8S,GAC3U,4EACA", "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/training/components/WhoCanJoin.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/training/components/WhoCanJoin.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/training/components/WhoCanJoin.jsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA0R,GACvT,wDACA", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/training/components/StudentLogos.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/training/components/StudentLogos.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/training/components/StudentLogos.jsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgT,GAC7U,8EACA", "debugId": null}}, {"offset": {"line": 126, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/training/components/StudentLogos.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/training/components/StudentLogos.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/training/components/StudentLogos.jsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA4R,GACzT,0DACA", "debugId": null}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 146, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/training/components/PremiumCTA.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/training/components/PremiumCTA.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/training/components/PremiumCTA.jsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8S,GAC3U,4EACA", "debugId": null}}, {"offset": {"line": 158, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/training/components/PremiumCTA.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/training/components/PremiumCTA.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/training/components/PremiumCTA.jsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA0R,GACvT,wDACA", "debugId": null}}, {"offset": {"line": 170, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 178, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/training/components/TrainingAccessForm.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/training/components/TrainingAccessForm.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/training/components/TrainingAccessForm.jsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAsT,GACnV,oFACA", "debugId": null}}, {"offset": {"line": 190, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/training/components/TrainingAccessForm.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/training/components/TrainingAccessForm.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/training/components/TrainingAccessForm.jsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkS,GAC/T,gEACA", "debugId": null}}, {"offset": {"line": 202, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 210, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/training/content/trainingContent.js"], "sourcesContent": ["// Content configuration for different training pages\n\nexport const cybersecurityTrainingContent = {\n  hero: {\n    breadcrumbItems: [\n      {\n        name: \"Home\",\n        url: \"/\",\n        iconKey: \"home\",\n        description: \"Return to homepage\"\n      },\n      {\n        name: \"Cybersecurity Training\",\n        url: \"/CybersecurityTraining\",\n        current: true,\n        iconKey: \"graduation-cap\",\n        description: \"Elite Security Training - Launch Your Cyber Security Career\"\n      }\n    ],\n    title: \"Elite Security Training\",\n    subtitle: \"Launch Your Cyber Security Career\",\n    tagline: \"Free and Premium Pathways\",\n    description: \"Dive into the world of penetration testing with our refined program. Designed for aspiring security professionals with complete pentesting skills and real-time live projects.\",\n    keyBenefits: [\n      \"Complete pentesting skills with real-time live projects\",\n      \"Latest tools and topics in cybersecurity\",\n      \"Free and premium pathways available\"\n    ],\n    buttons: [\n      {\n        text: \"Enroll Now\",\n        href: \"#form\",\n        primary: true\n      },\n      {\n        text: \"Program Details\",\n        href: \"/CyberSecTraining\",\n        primary: false\n      }\n    ],\n    heroImage: \"/images/p1s1.png\"\n  },\n  curriculumHighlights: {\n    sectionTitle: \"Security Lit Presents\",\n    title: \"Explore Our Comprehensive Curriculum\",\n    description: \"Dive into essential areas such as web application security, network penetration testing, cloud security, API security, and ethical hacking fundamentals.\",\n    brochureLink: \"/Brochure%20Security%20Training.pdf\",\n    highlights: [\n      {\n        title: \"Conduct Advanced Security Assessments\",\n        description: \"In the free tier, you'll gain the skills to perform comprehensive security assessments, vulnerability analyses, and penetration testing on various systems and networks, applying both theoretical knowledge and practical techniques learned during the training.\"\n      },\n      {\n        title: \"Join Elite Cybersecurity Teams\",\n        description: \"As a premium member, you'll be prepared to join similar high-level security teams, armed with industry-relevant skills and insider knowledge.\"\n      },\n      {\n        title: \"Pursue Diverse Cybersecurity Careers\",\n        description: \"Whether you choose the free or premium path, you'll explore various cybersecurity career options, understanding the roles and responsibilities in different organizations. This knowledge will help you make informed decisions about your career trajectory in the cybersecurity field.\"\n      }\n    ]\n  },\n  whoCanJoin: {\n    title: \"So Who Can Take Up This Training?\",\n    description: \"Our program is designed to be accessible and beneficial for a wide range of learners, from beginners to experienced professionals. Whether you're looking to start your cybersecurity journey or take it to new heights, we've got you covered.\",\n    targetAudience: [\n      {\n        title: \"Cybersecurity Enthusiasts\",\n        description: \"Whether you're new to the field or have some experience, our program caters to learners of all levels. Start with the free tier to build a strong foundation, then upgrade to the premium tier for a more immersive, mentor-guided experience.\"\n      },\n      {\n        title: \"Information Security Professionals\",\n        description: \"If you have been practicing cybersecurity topics, you can join the premium tier to take your skills to the next level through hands-on projects and industry-relevant training.\"\n      },\n      {\n        title: \"Career Switchers\",\n        description: \"Looking to transition into the exciting world of cybersecurity? Our program provides the knowledge and practical experience you need to kickstart your career in this high-demand field.\"\n      }\n    ],\n    bottomImage: \"/images/TrainingHacker.png\"\n  },\n  learningModules: {\n    title: \"Here's A Short Teaser Of What You Will Learn\",\n    modules: [\n      {\n        title: \"Web Application Security\",\n        description: \"Fortifying the digital frontline against cyber threats.\"\n      },\n      {\n        title: \"API & Network Security\",\n        description: \"Safeguarding the backbone of modern interconnected systems.\"\n      },\n      {\n        title: \"Practical Skills Development\",\n        description: \"Honing real-world cybersecurity expertise through hands-on learning.\"\n      },\n      {\n        title: \"Soft Skill & Professional Growth\",\n        description: \"Cultivating the human element in technical cybersecurity roles.\"\n      },\n      {\n        title: \"Real World Environment Navigation\",\n        description: \"Mastering the art of securing complex, live digital ecosystems.\"\n      },\n      {\n        title: \"Active Directory & Cloud Security\",\n        description: \"Protecting the nerve centers of enterprise and cloud infrastructures.\"\n      },\n      {\n        title: \"Continuous Learning & Adaption\",\n        description: \"Staying ahead in the ever-evolving cybersecurity landscape.\"\n      },\n      {\n        title: \"Report Writing Skills\",\n        description: \"Crafting clear, concise, and impactful cybersecurity documentation for stakeholders at all levels.\"\n      }\n    ]\n  },\n  studentLogos: {\n    title: \"Our Trained Students Work At\",\n    companies: [\n      {\n        name: \"AIA NZ\",\n        logo: \"/images/aia_nz_logo.jpg\",\n        alt: \"AIA New Zealand\"\n      },\n      {\n        name: \"Data Torque\",\n        logo: \"/images/data_torque_ltd_logo.jpg\",\n        alt: \"Data Torque Ltd\"\n      }\n    ]\n  },\n  premiumCTA: {\n    title: \"Premium Tier Benefits\",\n    description: \"As a Premium member, you'll work directly with SecurityLit on live penetration testing projects. This hands-on experience will immerse you in real-time cybersecurity challenges, teaching you how professional pentests are conducted. You'll learn the intricacies of report writing, client communication, and industry best practices. Upon completion, you'll receive an experience letter, validating your practical skills and boosting your career prospects in the cybersecurity field.\",\n    benefits: [\n      \"Access to advance Lab Subscription (TryHackMe, HackTheBox)\",\n      \"Personalized mentorship and guided learning\",\n      \"3 months of hands-on experience with SecurityLit\",\n      \"Professional report writing training\",\n      \"Experience letter upon completion\"\n    ],\n    upgradeTitle: \"Upgrade to the Premium Tier\",\n    upgradeDescription: \"Take your cybersecurity training to the next level with our premium tier. Unlock exclusive access to advanced labs, personalized mentorship, and hands-on projects that will transform your skills and career.\",\n    contactEmail: \"mailto:<EMAIL>,<EMAIL>\"\n  }\n};\n\nexport const cyberSecTrainingContent = {\n  hero: {\n    breadcrumbItems: [\n      {\n        name: \"Home\",\n        url: \"/\",\n        iconKey: \"home\",\n        description: \"Return to homepage\"\n      },\n      {\n        name: \"Cybersecurity Training\",\n        url: \"/CybersecurityTraining\",\n        iconKey: \"graduation-cap\",\n        description: \"Elite Security Training\"\n      },\n      {\n        name: \"Curriculum\",\n        url: \"/CyberSecTraining\",\n        current: true,\n        iconKey: \"book-open\",\n        description: \"Detailed Cybersecurity Training Curriculum\"\n      }\n    ],\n    sectionTitle: \"Security Lit Presents\",\n    title: \"Are you searching for training in Cyber Security field?\",\n    description: \"We are among the few companies in India offering internships across different sectors of Cyber Security. Check out real-life Cyber Security projects, get awesome experience to kickstart your career in cyber security and totally change your life!\",\n    buttons: [\n      {\n        text: \"Enroll Now\",\n        href: \"/CybersecurityTraining#form\",\n        primary: true\n      },\n      {\n        text: \"Contact Us\",\n        href: \"mailto:<EMAIL>,<EMAIL>\",\n        primary: false\n      }\n    ],\n    heroImage: \"/images/p2s1.png\",\n    secondaryImage: \"/images/p2s2.png\"\n  },\n  programStructure: {\n    title: \"Program Structure\",\n    description: \"We provide a job-focused, hands-on curriculum designed to take participants from foundational to advanced cybersecurity skills across three comprehensive phases:\",\n    phases: [\n      {\n        phase: \"Phase 1\",\n        title: \"Web And API Penetration Testing\",\n        description: \"Our program introduces participants to VAPT (Vulnerability Assessment and Penetration Testing) for mobile and web applications, focusing on identifying and mitigating security flaws. With the growing reliance on mobile apps and APIs, security is critical for organizations.\",\n        image: \"/images/p2s3.png\",\n        benefits: [\n          \"Assignments\",\n          \"Labs\",\n          \"Mentoring (Premium Only)\",\n          \"Hands-On Real Project (Premium Only)\"\n        ]\n      },\n      {\n        phase: \"Phase 2\",\n        title: \"Network and Cloud Penetration Testing\",\n        description: \"This phase focuses on network security, equipping participants with the skills to assess and secure organizational networks and endpoints. The emphasis is on identifying vulnerabilities in network infrastructure and developing robust defenses.\",\n        image: \"/images/p2s4.png\",\n        benefits: [\n          \"Assignments\",\n          \"Labs\",\n          \"Mentoring (Premium Only)\",\n          \"Hands-On Real Project (Premium Only)\"\n        ]\n      },\n      {\n        phase: \"Phase 3\",\n        title: \"Cloud Security\",\n        description: \"This phase introduces participants to essential cloud security practices, focusing on securing AWS environments. With more organizations moving to the cloud, understanding foundational cloud security principles is crucial for safeguarding cloud-based infrastructure.\",\n        image: \"/images/p2s5.png\",\n        benefits: [\n          \"Assignments\",\n          \"Labs\",\n          \"Mentoring (Premium Only)\",\n          \"Hands-On Real Project (Premium Only)\"\n        ]\n      }\n    ]\n  },\n  testimonials: {\n    title: \"Real Feedback from Professionals Who Completed Our Security Training\",\n    testimonials: [\n      {\n        name: \"Keziah Achshah Guha\",\n        role: \"Information Security Analyst at DataTorque Ltd\",\n        content: \"SecurityLit transformed my perspective on IT security. Through identifying risks for organizations, I've grown in my role and feel more capable now. The supportive work environment and knowledgeable colleagues at SecurityLit have been invaluable.\",\n        rating: 5\n      },\n      {\n        name: \"Anindya Roy\",\n        role: \"Associate Penetration Tester at SecurityLit\",\n        content: \"The training boosted my expertise and confidence, helping me excel as a pentester. Hands-on projects deepened my skills in web security and advanced exploitation, which I now apply regularly, along with improved communication skills.\",\n        rating: 5\n      },\n      {\n        name: \"Krishna Dinkar Biradar\",\n        role: \"Associate Penetration Tester at SecurityLit\",\n        content: \"This training improved my approach to pentesting, enhancing my process, documentation, and teamwork. Collaborating on tests and following a structured process was far more effective than my previous ad-hoc methods in bug hunting.\",\n        rating: 5\n      },\n      {\n        name: \"Rini Sebastian\",\n        role: \"Information Security Analyst at AIA NZ\",\n        content: \"I believe I got the job thanks to the training, support, and feedback from Ankita and the SecurityLit team, which helped me excel in the interviews. Thank you for all the guidance that led me to this role.\",\n        rating: 5\n      }\n    ]\n  },\n  contactForm: {\n    title: \"Start Your Training Now!\",\n    subtitle: \"Become part of the next generation of cybersecurity professionals with SecurityLit.\",\n    description: \"SecurityLit offers an industry-aligned training program designed to equip you with the skills needed to succeed in today's cybersecurity landscape.\",\n    features: [\n      \"Industry-aligned curriculum\",\n      \"Hands-on practical experience\",\n      \"Expert mentorship and guidance\",\n      \"Job-ready skills development\"\n    ]\n  }\n};\n"], "names": [], "mappings": "AAAA,qDAAqD;;;;;AAE9C,MAAM,+BAA+B;IAC1C,MAAM;QACJ,iBAAiB;YACf;gBACE,MAAM;gBACN,KAAK;gBACL,SAAS;gBACT,aAAa;YACf;YACA;gBACE,MAAM;gBACN,KAAK;gBACL,SAAS;gBACT,SAAS;gBACT,aAAa;YACf;SACD;QACD,OAAO;QACP,UAAU;QACV,SAAS;QACT,aAAa;QACb,aAAa;YACX;YACA;YACA;SACD;QACD,SAAS;YACP;gBACE,MAAM;gBACN,MAAM;gBACN,SAAS;YACX;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,SAAS;YACX;SACD;QACD,WAAW;IACb;IACA,sBAAsB;QACpB,cAAc;QACd,OAAO;QACP,aAAa;QACb,cAAc;QACd,YAAY;YACV;gBACE,OAAO;gBACP,aAAa;YACf;YACA;gBACE,OAAO;gBACP,aAAa;YACf;YACA;gBACE,OAAO;gBACP,aAAa;YACf;SACD;IACH;IACA,YAAY;QACV,OAAO;QACP,aAAa;QACb,gBAAgB;YACd;gBACE,OAAO;gBACP,aAAa;YACf;YACA;gBACE,OAAO;gBACP,aAAa;YACf;YACA;gBACE,OAAO;gBACP,aAAa;YACf;SACD;QACD,aAAa;IACf;IACA,iBAAiB;QACf,OAAO;QACP,SAAS;YACP;gBACE,OAAO;gBACP,aAAa;YACf;YACA;gBACE,OAAO;gBACP,aAAa;YACf;YACA;gBACE,OAAO;gBACP,aAAa;YACf;YACA;gBACE,OAAO;gBACP,aAAa;YACf;YACA;gBACE,OAAO;gBACP,aAAa;YACf;YACA;gBACE,OAAO;gBACP,aAAa;YACf;YACA;gBACE,OAAO;gBACP,aAAa;YACf;YACA;gBACE,OAAO;gBACP,aAAa;YACf;SACD;IACH;IACA,cAAc;QACZ,OAAO;QACP,WAAW;YACT;gBACE,MAAM;gBACN,MAAM;gBACN,KAAK;YACP;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,KAAK;YACP;SACD;IACH;IACA,YAAY;QACV,OAAO;QACP,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;QACD,cAAc;QACd,oBAAoB;QACpB,cAAc;IAChB;AACF;AAEO,MAAM,0BAA0B;IACrC,MAAM;QACJ,iBAAiB;YACf;gBACE,MAAM;gBACN,KAAK;gBACL,SAAS;gBACT,aAAa;YACf;YACA;gBACE,MAAM;gBACN,KAAK;gBACL,SAAS;gBACT,aAAa;YACf;YACA;gBACE,MAAM;gBACN,KAAK;gBACL,SAAS;gBACT,SAAS;gBACT,aAAa;YACf;SACD;QACD,cAAc;QACd,OAAO;QACP,aAAa;QACb,SAAS;YACP;gBACE,MAAM;gBACN,MAAM;gBACN,SAAS;YACX;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,SAAS;YACX;SACD;QACD,WAAW;QACX,gBAAgB;IAClB;IACA,kBAAkB;QAChB,OAAO;QACP,aAAa;QACb,QAAQ;YACN;gBACE,OAAO;gBACP,OAAO;gBACP,aAAa;gBACb,OAAO;gBACP,UAAU;oBACR;oBACA;oBACA;oBACA;iBACD;YACH;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,aAAa;gBACb,OAAO;gBACP,UAAU;oBACR;oBACA;oBACA;oBACA;iBACD;YACH;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,aAAa;gBACb,OAAO;gBACP,UAAU;oBACR;oBACA;oBACA;oBACA;iBACD;YACH;SACD;IACH;IACA,cAAc;QACZ,OAAO;QACP,cAAc;YACZ;gBACE,MAAM;gBACN,MAAM;gBACN,SAAS;gBACT,QAAQ;YACV;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,SAAS;gBACT,QAAQ;YACV;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,SAAS;gBACT,QAAQ;YACV;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,SAAS;gBACT,QAAQ;YACV;SACD;IACH;IACA,aAAa;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;AACF", "debugId": null}}, {"offset": {"line": 489, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/CybersecurityTraining/page.jsx"], "sourcesContent": ["import React from 'react';\nimport HeroBento from '../training/components/HeroBento';\nimport CurriculumHighlights from '../training/components/CurriculumHighlights';\nimport Who<PERSON>anJoin from '../training/components/WhoCanJoin';\nimport LearningModules from '../training/components/LearningModules';\n\nimport StudentLogos from '../training/components/StudentLogos';\nimport PremiumCTA from '../training/components/PremiumCTA';\nimport TrainingAccessForm from '../training/components/TrainingAccessForm';\nimport { cybersecurityTrainingContent } from '../training/content/trainingContent';\n\nexport const metadata = {\n  title: \"Elite Security Training | Launch Your Cyber Security Career - SecurityLit\",\n  description: \"Dive into the world of penetration testing with our refined program. Complete pentesting skills, real-time live projects, and latest tools. Free and premium pathways available.\",\n  keywords: \"cybersecurity training, penetration testing, ethical hacking, security training, VAPT training, cybersecurity course, pentesting course\",\n  robots: \"index, follow\",\n  openGraph: {\n    title: \"Elite Security Training | Launch Your Cyber Security Career - SecurityLit\",\n    description: \"Dive into the world of penetration testing with our refined program. Complete pentesting skills, real-time live projects, and latest tools.\",\n    url: \"https://securitylit.com/CybersecurityTraining\",\n    siteName: \"SecurityLit\",\n    images: [\n      {\n        url: \"/images/p1s1.png\",\n        width: 1200,\n        height: 630,\n        alt: \"SecurityLit Cybersecurity Training\",\n      },\n    ],\n    locale: \"en_GB\",\n    type: \"website\",\n  },\n};\n\nexport default function CybersecurityTrainingPage() {\n  return (\n    <div className=\"min-h-screen bg-white\">\n      {/* Hero Bento Section */}\n      <HeroBento content={cybersecurityTrainingContent.hero} />\n\n      {/* Curriculum Highlights */}\n      <CurriculumHighlights content={cybersecurityTrainingContent.curriculumHighlights} />\n\n      {/* Who Can Join */}\n      <WhoCanJoin content={cybersecurityTrainingContent.whoCanJoin} />\n\n      {/* Learning Modules */}\n      <LearningModules content={cybersecurityTrainingContent.learningModules} />\n\n      {/* Student Logos */}\n      <StudentLogos content={cybersecurityTrainingContent.studentLogos} />\n\n      {/* Premium CTA */}\n      <PremiumCTA content={cybersecurityTrainingContent.premiumCTA} />\n\n      {/* Training Access Form */}\n      <TrainingAccessForm />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;;;;;;AAGA;AACA;AACA;AACA;;;;;;;;;;;AAEO,MAAM,WAAW;IACtB,OAAO;IACP,aAAa;IACb,UAAU;IACV,QAAQ;IACR,WAAW;QACT,OAAO;QACP,aAAa;QACb,KAAK;QACL,UAAU;QACV,QAAQ;YACN;gBACE,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,KAAK;YACP;SACD;QACD,QAAQ;QACR,MAAM;IACR;AACF;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,kJAAA,CAAA,UAAS;gBAAC,SAAS,oJAAA,CAAA,+BAA4B,CAAC,IAAI;;;;;;0BAGrD,8OAAC,6JAAA,CAAA,UAAoB;gBAAC,SAAS,oJAAA,CAAA,+BAA4B,CAAC,oBAAoB;;;;;;0BAGhF,8OAAC,mJAAA,CAAA,UAAU;gBAAC,SAAS,oJAAA,CAAA,+BAA4B,CAAC,UAAU;;;;;;0BAG5D,8OAAC;gBAAgB,SAAS,oJAAA,CAAA,+BAA4B,CAAC,eAAe;;;;;;0BAGtE,8OAAC,qJAAA,CAAA,UAAY;gBAAC,SAAS,oJAAA,CAAA,+BAA4B,CAAC,YAAY;;;;;;0BAGhE,8OAAC,mJAAA,CAAA,UAAU;gBAAC,SAAS,oJAAA,CAAA,+BAA4B,CAAC,UAAU;;;;;;0BAG5D,8OAAC,2JAAA,CAAA,UAAkB;;;;;;;;;;;AAGzB", "debugId": null}}]}