import React from "react";
import { Shield, Users, Target } from "lucide-react";
 
export default function  Security() {
  return (
    <> 
  <div className="bg-white py-16 sm:py-24">
  <div className="container mx-auto px-4 md:px-24">
    <div className="mb-16">
      <div className="text-secondary-blue text-xl font-bold mb-5">
        How Capture The Bug Helps
      </div>
      <h2 className="text-4xl sm:text-5xl font-bold text-[#010D2C] leading-tight mb-5">
Security Built for Cloud-Native and SaaS-First Organizations      </h2>
      <p className="text-[#6B7280] text-lg font-medium leading-relaxed  ">
Whether you&apos;re managing a global SaaS platform or orchestrating a complex cloud microservices environment, misconfigurations and logic flaws can be exploited in minutes.
 <span className="font-bold"> Capture The Bug helps you detect and fix vulnerabilities across your SDLC-without slowing down release cycles.</span>We partner with cloud-native engineering teams to test what matters: multi-tenant security, CI/CD pipelines, and scalable, automated risk coverage.
      </p>
    </div>

          <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 lg:gap-8">
            <div className="bg-white border-l-4 border-[#58CC02] pl-4 sm:pl-6 pr-4 sm:pr-6 py-6 sm:py-8 rounded-r-lg shadow-sm hover:shadow-md transition-shadow duration-300">
              <div className="">
                <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-[#58CC02] to-[#4BAF02] rounded-lg flex items-center justify-center mb-4 sm:mb-6">
                  <Shield className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                </div>
                <h3 className="text-xl sm:text-2xl font-bold text-[#010D2C] mb-3 sm:mb-4">
                  Cloud-Native Application Testing
                </h3>
                <p className="text-[#6B7280] text-base sm:text-lg leading-relaxed">
We assess your cloud stack from infrastructure to runtime-identifying misconfigurations in <span className="font-bold">AWS</span>, <span className="font-bold">GCP</span>, and <span className="font-bold">Azure environments</span>, as well as container exposure, leaked secrets, and insecure IAM roles. Our pentests focus on securing your <span className="font-bold">CI pipeline</span>, <span className="font-bold">Kubernetes workloads</span>, and <span className="font-bold">API surfaces</span> before attackers do.
                </p>
              </div>
            </div>

            <div className="bg-white border-l-4 border-[#58CC02] pl-4 sm:pl-6 pr-4 sm:pr-6 py-6 sm:py-8 rounded-r-lg shadow-sm hover:shadow-md transition-shadow duration-300">
              <div className="">
                <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-[#58CC02] to-[#4BAF02] rounded-lg flex items-center justify-center mb-4 sm:mb-6">
                  <Users className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                </div>
                <h3 className="text-xl sm:text-2xl font-bold text-[#010D2C] mb-3 sm:mb-4">
                   CI/CD Security Automation          
                </h3>
                <p className="text-[#6B7280] text-base sm:text-lg leading-relaxed">
               We shift security left-integrating continuous on-demand pentesting and red teaming into your CI/CD workflows without slowing sprint velocity. Our assessments reveal <span className="font-bold">hardcoded secrets</span>, <span className="font-bold">risky merges</span>, <span className="font-bold">insecure packages</span>, and <span className="font-bold">privilege leaks</span> across dev pipelines. You get contextual, developer-friendly reports aligned to sprint cycles and release velocity.
                </p>
              </div>
            </div>

            <div className="bg-white border-l-4 border-[#58CC02] pl-4 sm:pl-6 pr-4 sm:pr-6 py-6 sm:py-8 rounded-r-lg shadow-sm hover:shadow-md transition-shadow duration-300 md:col-span-2 xl:col-span-1">
              <div className="">
                <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-[#58CC02] to-[#4BAF02] rounded-lg flex items-center justify-center mb-4 sm:mb-6">
                  <Target className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                </div>
                <h3 className="text-xl sm:text-2xl font-bold text-[#010D2C] mb-3 sm:mb-4">
                   Multi-Tenant SaaS Security
                </h3>
                <p className="text-[#6B7280] text-base sm:text-lg leading-relaxed">
We simulate <span className="font-bold">tenant-to-tenant privilege escalation</span>, <span className="font-bold">misconfigured object-level access</span>, and <span className="font-bold">role tampering</span> across shared SaaS environments. Our tests validate enforcement of isolation boundaries and zero-trust design across your tenancy logic-ensuring your platform scales securely without risk of customer data bleed.                </p>
              </div>
            </div>
          </div>
        </div>
      </div> 
    </>
  );
}