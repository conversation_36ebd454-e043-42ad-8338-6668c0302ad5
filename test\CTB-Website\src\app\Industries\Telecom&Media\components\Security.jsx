import React from "react";
import { Shield, Users, Target } from "lucide-react";
 
export default function Security() {
  return (
    <> 
  <div className="bg-white py-16 sm:py-24">
  <div className="container mx-auto px-4 md:px-24">
    <div className="mb-16">
      <div className="text-secondary-blue text-xl font-bold mb-5">
        How Capture The Bug Helps
      </div>
      <h2 className="text-4xl sm:text-5xl font-bold text-[#010D2C] leading-tight mb-5">
Building cyber resilience across global telcos and media ecosystems      </h2>
      <p className="text-[#6B7280] text-lg font-medium leading-relaxed  ">
Telecoms and media providers operate at massive scale-handling millions of subscriber sessions, customer records, real-time data streams, and billing operations.
We help identify vulnerabilities across your stack, simulate zero-day threats, and validate the security of systems powering connectivity and content distribution.
From ISPs and fiber providers to VoIP carriers and OTT platforms-we protect your most valuable assets.      </p>
    </div>

   <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 lg:gap-8">
            <div className="bg-white border-l-4 border-[#58CC02] pl-4 sm:pl-6 pr-4 sm:pr-6 py-6 sm:py-8 rounded-r-lg shadow-sm hover:shadow-md transition-shadow duration-300">
              <div className="">
                <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-[#58CC02] to-[#4BAF02] rounded-lg flex items-center justify-center mb-4 sm:mb-6">
                  <Shield className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                </div>
                <h3 className="text-xl sm:text-2xl font-bold text-[#010D2C] mb-3 sm:mb-4">
            5G & Core Network Security
          </h3>
          <p className="text-[#6B7280] text-md leading-relaxed mb-3">
             We test <span className="font-bold">4G/5G cores</span>, <span className="font-bold">VoIP protocols</span> and <span className="font-bold">SS7 signaling</span>for real-world risks like SIM swapping, protocol abuse, and session hijacking-ensuring your backbone systems resist compromise and maintain subscriber trust.
          </p>
        </div>
      </div>

      <div className="bg-white border-l-4 border-[#58CC02] pl-4 sm:pl-6 pr-4 sm:pr-6 py-6 sm:py-8 rounded-r-lg shadow-sm hover:shadow-md transition-shadow duration-300">
                  <div className="">
                    <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-[#58CC02] to-[#4BAF02] rounded-lg flex items-center justify-center mb-4 sm:mb-6">
                      <Users className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                    </div>
                <h3 className="text-xl sm:text-2xl font-bold text-[#010D2C] mb-3 sm:mb-4">
            Media Platform & Subscriber Data Protection
          </h3>
         <p className="text-[#6B7280] text-md leading-relaxed mb-3">
             We secure <span className="font-bold">OTT platforms</span>, <span className="font-bold">CMS backends</span> and <span className="font-bold">user APIs</span>by simulating real-world data leaks, takeover attempts, and logic flaws-safeguarding session integrity, identity access, and privacy compliance.
          </p>
        </div>
      </div>

  <div className="bg-white border-l-4 border-[#58CC02] pl-4 sm:pl-6 pr-4 sm:pr-6 py-6 sm:py-8 rounded-r-lg shadow-sm hover:shadow-md transition-shadow duration-300 md:col-span-2 xl:col-span-1">
              <div className="">
                <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-[#58CC02] to-[#4BAF02] rounded-lg flex items-center justify-center mb-4 sm:mb-6">
                  <Target className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                </div>
                <h3 className="text-xl sm:text-2xl font-bold text-[#010D2C] mb-3 sm:mb-4">
            Global Telecom Compliance Readiness
          </h3>
         <p className="text-[#6B7280] text-md leading-relaxed mb-3">
             Our testing aligns with telecom security mandates. You receive prioritized, audit-ready remediation plans built for telecom-grade security teams.
          </p>
        </div>
      </div>
    </div>
  </div>
</div> 
</>
  );
}
