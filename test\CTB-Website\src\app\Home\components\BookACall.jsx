import React from "react";
import Button from "@/app/common/buttons/Button";
import Link from "next/link";

export default function BookACall() {
  return (
    <div className="relative overflow-hidden">
      {/* First background image */}
      <div
        className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-full h-full bg-contain bg-bottom bg-no-repeat"
        style={{
          backgroundImage: "url('/images/semi_circle.png')",
        }}
      ></div>

      {/* Second background image */}
      <div
        className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1/2 h-1/2 bg-contain bg-bottom bg-no-repeat"
        style={{
          backgroundImage: "url('/images/small_semi_circle.png')",
        }}
      ></div>

      {/* Content */}
      <div className="relative bg-opacity-80 md:pt-20 md:pb-20 pt-10 pb-10 flex flex-col items-center md:gap-10 gap-10 px-4 md:px-0">
        <div className="Title md:text-5xl text-2xl pb-2 md:px-72  text-center bg-gradient-to-r from-[#062575] via-[#0B45DB] to-[#0B45DB] bg-clip-text text-transparent font-bold">
          Say NO To Outdated Penetration Testing Methods
        </div>
        <div className="subTitle text-slate-700 text-lg text-center">
          Top-Quality Security Solutions Without the Price Tag or Complexity
        </div>
        <Button
          href="/Request-Demo"
          variant="primary"
        >
          Request Demo
        </Button>

        {/* <div className="checkedTrusts flex flex-row items-center gap-10">
          <div className="eachTrust flex flex-row gap-2 items-center">
            <TiTick color="blue" size={30} />
            <div className="content md:text-lg text-sm">Manual Penetration Testing</div>
          </div>

          <div className="eachTrust flex flex-row gap-2 items-center">
            <TiTick color="blue" size={30} />
            <div className="content md:text-lg text-sm">
              Full time penetration testers
            </div>
          </div>

          <div className="eachTrust flex flex-row gap-2 items-center">
            <TiTick color="blue" size={30} />
            <div className="content md:text-lg text-sm">Remediation Support</div>
          </div>
        </div> */}
      </div>
    </div>
  );
}
