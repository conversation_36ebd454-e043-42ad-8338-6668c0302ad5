import React from "react";
import { ArrowRight } from "lucide-react";
 
const Button = ({ href, variant = "primary", size = "md", rightIcon, className = "", children, ...props }) => {
  const baseClasses = "inline-flex items-center justify-center gap-2 font-medium transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2";
  
  const variants = {
    primary: "bg-gradient-to-r from-[#58CC02] to-[#4BAF02] hover:from-[#4BAF02] hover:to-[#3A8C02] text-black",
    outline: "border-2 border-[#027bfb] text-[#027bfb] hover:bg-[#027bfb] hover:text-white"
  };
  
  const sizes = {
    md: "px-6 py-3 text-base",
    lg: "px-8 py-4 text-lg"
  };
  
  return (
    <a
      href={href}
      className={`${baseClasses} ${variants[variant]} ${sizes[size]} ${className}`}
      {...props}
    >
      {children}
      {rightIcon}
    </a>
  );
};
 
export default function Landing() {
  return (
    <>
     <div className="bg-gradient-to-b from-[#0A0A0A] via-[#010D2C] to-[#000000] min-h-screen">
      <div className="relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0" style={{
            backgroundImage: `
              linear-gradient(90deg, #58CC02 1px, transparent 1px),
              linear-gradient(0deg, #58CC02 1px, transparent 1px)
            `,
            backgroundSize: '60px 60px'
          }}></div>
        </div>
        <div className="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none">
          <div className="absolute h-px bg-gradient-to-r from-transparent via-[#027bfb] to-transparent opacity-30 animate-pulse" style={{ top: '20%', left: '-100%', width: '200%' }} />
          <div className="absolute h-px bg-gradient-to-r from-transparent via-[#027bfb] to-transparent opacity-20 animate-pulse" style={{ top: '35%', left: '-100%', width: '200%', animationDelay: '0.5s' }} />
          <div className="absolute h-px bg-gradient-to-r from-transparent via-[#027bfb] to-transparent opacity-25 animate-pulse" style={{ top: '50%', left: '-100%', width: '200%', animationDelay: '1s' }} />
          <div className="absolute h-px bg-gradient-to-r from-transparent via-[#027bfb] to-transparent opacity-20 animate-pulse" style={{ top: '65%', left: '-100%', width: '200%', animationDelay: '1.5s' }} />
          <div className="absolute h-px bg-gradient-to-r from-transparent via-[#027bfb] to-transparent opacity-30 animate-pulse" style={{ top: '80%', left: '-100%', width: '200%', animationDelay: '2s' }} />
        </div>
        <div className="container mx-auto px-4 md:px-12 py-20 sm:py-28 lg:py-36 relative">
          <div className="max-w-6xl mx-auto text-center">
            <div className="inline-flex items-center gap-3 bg-gradient-to-r from-[#062575] to-[#0835A7] text-[#58CC02] px-8 py-4 rounded-full text-base font-semibold mb-12 border border-[#58CC02] border-opacity-40 shadow-lg backdrop-blur-sm">
              <div className="w-2 h-2 bg-[#58CC02] rounded-full animate-pulse"></div>
              <span>AI Security & Infrastructure</span>
            </div>
            
              <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-black text-white leading-relaxed mb-6 sm:mb-8">
              <span className="block font-bold">Security for </span>
                <span className="block text-[#58CC02] bg-clip-text mt-2 sm:mt-4 whitespace-nowrap font-bold">Models, Data Pipelines </span>
              <span className="block text-xl sm:text-3xl md:text-4xl lg:text-5xl mt-2 sm:mt-4 text-[#027bfb] font-medium  ">
& ML Infrastructure
              </span>
              </h1>
            
            <p className="text-gray-300 text-lg md:text-xl leading-relaxed mb-16 max-w-3xl mx-auto font-light">
Protect your LLMs, training data, and inference pipelines from adversarial threats-before they&apos;re exploited.             </p>
            
            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
              <Button
                href="/Request-Demo"
                variant="primary"
                size="lg"
                rightIcon={<ArrowRight className="h-5 w-5" />}
                className="bg-gradient-to-r from-[#58CC02] to-[#4BAF02] hover:from-[#4BAF02] hover:to-[#3A8C02] text-black px-10 py-5 text-lg font-bold rounded-xl transition-all duration-300 shadow-2xl hover:shadow-[#58CC02]/30 hover:scale-105 transform"
              >
                Launch AI Security Assessment
              </Button>
              <Button
                href="/Download-Report"
                variant="outline"
                size="lg"
                rightIcon={<ArrowRight className="h-5 w-5" />}
                className="border-2 border-[#027bfb] text-[#027bfb] hover:bg-[#027bfb] hover:text-white px-10 py-5 text-lg font-semibold rounded-xl transition-all duration-300 hover:shadow-lg backdrop-blur-sm"
              >
                Download Offensive Security Report 2025
              </Button>
            </div>
             
          </div>
        </div>
      </div>
    </div>  
</>
  );
}