{"version": 3, "file": "restError.js", "sourceRoot": "", "sources": ["../../src/restError.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,OAAO,EAAE,MAAM,iBAAiB,CAAC;AAE1C,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAC3C,OAAO,EAAE,SAAS,EAAE,MAAM,qBAAqB,CAAC;AAEhD,MAAM,cAAc,GAAG,IAAI,SAAS,EAAE,CAAC;AAwBvC;;GAEG;AACH,MAAM,OAAO,SAAU,SAAQ,KAAK;IAoClC,YAAY,OAAe,EAAE,UAA4B,EAAE;QACzD,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,WAAW,CAAC;QACxB,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;QACzB,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;QAErC,qFAAqF;QACrF,iGAAiG;QACjG,+FAA+F;QAC/F,kCAAkC;QAClC,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,SAAS,EAAE,EAAE,KAAK,EAAE,OAAO,CAAC,OAAO,EAAE,UAAU,EAAE,KAAK,EAAE,CAAC,CAAC;QACtF,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,UAAU,EAAE,EAAE,KAAK,EAAE,OAAO,CAAC,QAAQ,EAAE,UAAU,EAAE,KAAK,EAAE,CAAC,CAAC;QAExF,0CAA0C;QAC1C,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,MAAM,EAAE;YAClC,KAAK,EAAE,GAAG,EAAE;gBACV,uGAAuG;gBACvG,0BAA0B;gBAC1B,OAAO,cAAc,IAAI,CAAC,OAAO,OAAO,cAAc,CAAC,QAAQ,iCAC1D,IAAI,KACP,OAAO,EAAE,IAAI,CAAC,OAAO,EACrB,QAAQ,EAAE,IAAI,CAAC,QAAQ,IACvB,EAAE,CAAC;YACP,CAAC;YACD,UAAU,EAAE,KAAK;SAClB,CAAC,CAAC;QAEH,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC;IACnD,CAAC;;AA/DD;;;;GAIG;AACa,4BAAkB,GAAW,oBAAoB,CAAC;AAClE;;;GAGG;AACa,qBAAW,GAAW,aAAa,CAAC;AAwDtD;;;GAGG;AACH,MAAM,UAAU,WAAW,CAAC,CAAU;IACpC,IAAI,CAAC,YAAY,SAAS,EAAE,CAAC;QAC3B,OAAO,IAAI,CAAC;IACd,CAAC;IACD,OAAO,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,WAAW,CAAC;AAC9C,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { isError } from \"./util/error.js\";\nimport type { PipelineRequest, PipelineResponse } from \"./interfaces.js\";\nimport { custom } from \"./util/inspect.js\";\nimport { Sanitizer } from \"./util/sanitizer.js\";\n\nconst errorSanitizer = new Sanitizer();\n\n/**\n * The options supported by RestError.\n */\nexport interface RestErrorOptions {\n  /**\n   * The code of the error itself (use statics on RestError if possible.)\n   */\n  code?: string;\n  /**\n   * The HTTP status code of the request (if applicable.)\n   */\n  statusCode?: number;\n  /**\n   * The request that was made.\n   */\n  request?: PipelineRequest;\n  /**\n   * The response received (if any.)\n   */\n  response?: PipelineResponse;\n}\n\n/**\n * A custom error type for failed pipeline requests.\n */\nexport class RestError extends Error {\n  /**\n   * Something went wrong when making the request.\n   * This means the actual request failed for some reason,\n   * such as a DNS issue or the connection being lost.\n   */\n  static readonly REQUEST_SEND_ERROR: string = \"REQUEST_SEND_ERROR\";\n  /**\n   * This means that parsing the response from the server failed.\n   * It may have been malformed.\n   */\n  static readonly PARSE_ERROR: string = \"PARSE_ERROR\";\n\n  /**\n   * The code of the error itself (use statics on RestError if possible.)\n   */\n  public code?: string;\n  /**\n   * The HTTP status code of the request (if applicable.)\n   */\n  public statusCode?: number;\n  /**\n   * The request that was made.\n   * This property is non-enumerable.\n   */\n  public request?: PipelineRequest;\n  /**\n   * The response received (if any.)\n   * This property is non-enumerable.\n   */\n  public response?: PipelineResponse;\n  /**\n   * Bonus property set by the throw site.\n   */\n  public details?: unknown;\n\n  constructor(message: string, options: RestErrorOptions = {}) {\n    super(message);\n    this.name = \"RestError\";\n    this.code = options.code;\n    this.statusCode = options.statusCode;\n\n    // The request and response may contain sensitive information in the headers or body.\n    // To help prevent this sensitive information being accidentally logged, the request and response\n    // properties are marked as non-enumerable here. This prevents them showing up in the output of\n    // JSON.stringify and console.log.\n    Object.defineProperty(this, \"request\", { value: options.request, enumerable: false });\n    Object.defineProperty(this, \"response\", { value: options.response, enumerable: false });\n\n    // Logging method for util.inspect in Node\n    Object.defineProperty(this, custom, {\n      value: () => {\n        // Extract non-enumerable properties and add them back. This is OK since in this output the request and\n        // response get sanitized.\n        return `RestError: ${this.message} \\n ${errorSanitizer.sanitize({\n          ...this,\n          request: this.request,\n          response: this.response,\n        })}`;\n      },\n      enumerable: false,\n    });\n\n    Object.setPrototypeOf(this, RestError.prototype);\n  }\n}\n\n/**\n * Typeguard for RestError\n * @param e - Something caught by a catch clause.\n */\nexport function isRestError(e: unknown): e is RestError {\n  if (e instanceof RestError) {\n    return true;\n  }\n  return isError(e) && e.name === \"RestError\";\n}\n"]}