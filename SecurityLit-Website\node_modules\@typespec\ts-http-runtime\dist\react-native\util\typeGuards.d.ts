export declare function isNodeReadableStream(x: unknown): x is NodeJS.ReadableStream;
export declare function isWebReadableStream(x: unknown): x is ReadableStream;
export declare function isBinaryBody(body: unknown): body is Uint8Array | NodeJS.ReadableStream | ReadableStream<Uint8Array> | (() => NodeJS.ReadableStream) | (() => ReadableStream<Uint8Array>) | Blob;
export declare function isReadableStream(x: unknown): x is ReadableStream | NodeJS.ReadableStream;
export declare function isBlob(x: unknown): x is Blob;
//# sourceMappingURL=typeGuards.d.ts.map