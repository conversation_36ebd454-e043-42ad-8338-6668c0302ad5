'use client';
import React from 'react'
import Head from 'next/head'
import Landing from './components/Landing.jsx'
import Testimonial from './components/Testimonial'
import CompanyShowcaseWithTestimonials from './components/CompanyTestimonials.jsx';
import ComplianceHeroSection from './components/Compliance.jsx';
import SuccessStoriesSection from './components/SuccessStories.jsx';
import BreadcrumbNavigation from '@/app/common/components/BreadcrumbNavigation';
import { createCustomBreadcrumbs } from '@/app/common/hooks/useBreadcrumbs';
 


function page() {
    const breadcrumbs = createCustomBreadcrumbs([
      {
        name: "Customers",
        url: "/Customers",
        current: true,
        iconKey: "customers",
        description: "See how leading security teams trust Capture The Bug"
      }
    ]);

    return (
      <>
        <Head>
          <title>Capture The Bug | Trusted by Leading Security Teams</title>
          <meta name="description" content="Discover why security teams rely on Capture The Bug for expert-led PTaaS, faster remediation, and compliance-ready reporting." />
          <meta name="keywords" content="PTaaS for security teams, penetration testing platform, compliance-ready pentesting, Capture The Bug case studies, enterprise cybersecurity testing, trusted pentesting provider, customer security success" />
          <meta name="robots" content="index, follow" />
          <link rel="icon" href="/favicon.ico" />

          {/* Open Graph */}
          <meta property="og:title" content="Capture The Bug | Trusted by Leading Security Teams" />
          <meta property="og:type" content="website" />
          <meta property="og:url" content="https://capturethebug.xyz/Customers" />
          <meta property="og:description" content="Security teams choose Capture The Bug to test smarter, fix faster, and stay compliant with scalable, expert-led penetration testing." />
          <meta property="og:image" content="https://i.ibb.co/xZSZgNx/Screenshot-2025-06-18-223818.png" />

          {/* Twitter */}
          <meta name="twitter:card" content="summary_large_image" />
          <meta name="twitter:title" content="Capture The Bug | Trusted by Leading Security Teams" />
          <meta name="twitter:description" content="See how Capture The Bug empowers security teams to remediate faster and maintain continuous compliance through expert PTaaS." />
          <meta name="twitter:image" content="https://i.ibb.co/xZSZgNx/Screenshot-2025-06-18-223818.png" />
        </Head>

        <div className="relative">
          {/* Breadcrumb Navigation - positioned absolutely at the top */}
          <div className="absolute top-0 left-0 right-0 pt-2 sm:pt-2 md:pt-5 xl:pt-14 z-10">
            <div className="max-w-7xl px-2 sm:px-2 md:px-16">
              <BreadcrumbNavigation items={breadcrumbs} />
            </div>
          </div>

          <Landing />
          <Testimonial/>
          <CompanyShowcaseWithTestimonials/>
          <ComplianceHeroSection/>
          <SuccessStoriesSection/>
        </div>
      </>
    )
  }
  
  export default page