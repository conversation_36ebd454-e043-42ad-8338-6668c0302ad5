 import React from 'react' 
 import Landing from './components/Landing'; 
 import Benefits from './components/Benefits';
import BlogSection from '@/app/Home/components/Blogs';
import PartnersList from '@/app/Product/Penetration-Testing/components/PartnersList';
 
export const metadata = {
  title: "Partner Program - Join Capture The Bug | Cybersecurity Partnership",
  description: "Join our partner program and expand your cybersecurity services. Offer penetration testing and security solutions to your clients with our platform.",
  keywords: "cybersecurity partnership, penetration testing reseller, security partner program, vCISO services, managed security services, PTaaS partner",
  openGraph: {
    title: "Partner Program - Join Capture The Bug | Cybersecurity Partnership",
    type: "website",
    url: "https://capturethebug.xyz/Company/Partner-With-Us",
    description: "Join our partner program and expand your cybersecurity services. Offer penetration testing and security solutions to your clients with our platform.",
    images: "https://i.postimg.cc/7GfBbcfP/partner-with-us.png",
  },
  twitter: {
    card: "summary_large_image",
    title: "Partner Program - Join Capture The Bug | Cybersecurity Partnership",
    description: "Join our partner program and expand your cybersecurity services. Offer penetration testing and security solutions to your clients with our platform.",
    images: "https://i.postimg.cc/7GfBbcfP/partner-with-us.png",
  },
};
 
 export default function page() {
   return (
     <div>
       <title>Partner Program - Join Capture The Bug | Cybersecurity Partnership</title>
       <Landing />
       <PartnersList/>
       <Benefits />
       <BlogSection/>
      </div>
   )
 }
 