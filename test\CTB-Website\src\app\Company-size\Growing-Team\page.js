import GrowingTeam from './growing';

export const metadata = {
  title: "Capture The Bug | Security Testing for Growing Teams",
  description: "Accelerate your growth with scalable PTaaS. Continuous testing, seamless integrations, and compliance-ready reports for fast-moving teams.",
keywords: "startup penetration testing, continuous security testing, web app pentesting, scalable PTaaS, compliance reporting, DevSecOps security, Capture The Bug, security for development teams",  robots: "index, follow",
  icons: {
    icon: '/favicon.ico',
  },
  openGraph: {
    title: "Capture The Bug | Security Testing for Growing Teams",
    type: "website",
    url: "https://capturethebug.xyz/Company-size/Growing-Team",
    description: "Scale securely with Capture The Bug. Continuous testing, easy integrations, and fast compliance reporting for growing dev teams.",
    images: "https://i.ibb.co/cSV28dhG/Screenshot-2025-06-18-220557.png",
  },
  twitter: {
    card: 'summary_large_image',
    title: "Capture The Bug | Security Testing for Growing Teams",
    description: "Capture The Bug helps fast-growing teams secure their apps with expert-led PTaaS, automation, and compliance-friendly reports.",
    images: "https://i.ibb.co/cSV28dhG/Screenshot-2025-06-18-220557.png",
  }
};

export default function Page() {
  return <GrowingTeam />;
}
