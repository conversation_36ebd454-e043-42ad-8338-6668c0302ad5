"use client";

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Crown, ArrowRight, CheckCircle, Star, Zap, Shield, Mail } from 'lucide-react';

const premiumFeatures = [
  "Access to advance Lab Subscription (TryHackMe, HackTheBox)",
  "Personalized mentorship and guided learning",
  "3 months of hands-on experience with SecurityLit",
  "Professional report writing training",
  "Experience letter upon completion"
];

export default function PremiumCTA() {
  const [showForm, setShowForm] = useState(false);

  const handleEmailContact = () => {
    window.location.href = 'mailto:<EMAIL>';
  };

  return (
    <>
      {/* Main Premium Section */}
      <section className="py-24 bg-gradient-to-br from-[var(--color-dark-blue)] via-[var(--color-dark-blue-hover)] to-[var(--color-dark-blue-bg)] relative overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-0 left-0 w-full h-full bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.03%22%3E%3Ccircle%20cx%3D%2230%22%20cy%3D%2230%22%20r%3D%221%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-50"></div>
          <div className="absolute top-20 left-10 w-72 h-72 bg-[var(--color-blue)]/20 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob"></div>
          <div className="absolute bottom-20 right-10 w-72 h-72 bg-[var(--color-blue-secondary)]/20 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000"></div>
        </div>

        <div className="relative z-10 container mx-auto px-4 max-w-7xl">
          {/* Header */}
          <motion.div 
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <motion.div 
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.2, duration: 0.6 }}
              viewport={{ once: true }}
              className="inline-flex items-center bg-[var(--color-yellow)]/20 px-4 py-2 rounded-full mb-6"
            >
              <Crown className="w-4 h-4 text-[var(--color-yellow)] mr-2" />
              <span className="text-sm font-medium text-[var(--color-yellow)]">Premium Tier Benefits</span>
            </motion.div>
            
            <motion.h2 
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4, duration: 0.6 }}
              viewport={{ once: true }}
              className="text-4xl lg:text-5xl font-bold text-white mb-6"
            >
              As a Premium member, you'll work directly with
              <span className="block bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] bg-clip-text text-transparent">
                SecurityLit
              </span>
            </motion.h2>
            
            <motion.p 
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6, duration: 0.6 }}
              viewport={{ once: true }}
              className="text-lg text-white/80 max-w-4xl mx-auto leading-relaxed"
            >
              on live penetration testing projects. This hands-on experience will immerse you in real-time cybersecurity challenges, teaching you how professional pentests are conducted. You'll learn the intricacies of report writing, client communication, and industry best practices. Upon completion, you'll receive an experience letter, validating your practical skills and boosting your career prospects in the cybersecurity field.
            </motion.p>
          </motion.div>

          {/* Premium Features Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-16">
            {/* Features List */}
            <motion.div 
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="space-y-6"
            >
              {premiumFeatures.map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -30 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1, duration: 0.6 }}
                  viewport={{ once: true }}
                  className="flex items-center gap-4"
                >
                  <div className="w-12 h-12 bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-2xl flex items-center justify-center shadow-lg">
                    <CheckCircle className="w-6 h-6 text-[var(--color-yellow)]" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-white">{feature}</h3>
                  </div>
                </motion.div>
              ))}
            </motion.div>

            {/* Contact Card */}
            <motion.div 
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="relative"
            >
              <div className="bg-white/10 backdrop-blur-xl rounded-3xl p-8 border border-white/20 shadow-2xl">
                <div className="text-center space-y-6">
                  {/* Premium Badge */}
                  <div className="inline-flex items-center gap-2 bg-gradient-to-r from-[var(--color-yellow)] to-[var(--color-yellow-hover)] text-white px-6 py-3 rounded-full">
                    <Crown className="w-5 h-5" />
                    <span className="font-semibold">Premium Plan</span>
                  </div>

                  {/* Contact Information */}
                  <div>
                    <div className="text-2xl font-bold text-white mb-2">
                      Get Custom Pricing
                    </div>
                    <p className="text-white/70">Contact our team for personalized pricing and consultation</p>
                  </div>

                  {/* Contact Email */}
                  <div className="bg-white/10 rounded-2xl p-4">
                    <div className="flex items-center justify-center gap-3">
                      <Mail className="w-5 h-5 text-[var(--color-blue)]" />
                      <span className="text-white font-medium"><EMAIL></span>
                    </div>
                  </div>

                  {/* Features */}
                  <div className="space-y-3">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-white/70">All Basic Features</span>
                      <CheckCircle className="w-4 h-4 text-green-400" />
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-white/70">Advanced Modules</span>
                      <CheckCircle className="w-4 h-4 text-green-400" />
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-white/70">1-on-1 Mentoring</span>
                      <CheckCircle className="w-4 h-4 text-green-400" />
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-white/70">Certification</span>
                      <CheckCircle className="w-4 h-4 text-green-400" />
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-white/70">Priority Support</span>
                      <CheckCircle className="w-4 h-4 text-green-400" />
                    </div>
                  </div>

                  {/* CTA Button */}
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={handleEmailContact}
                    className="w-full bg-gradient-to-r from-[var(--color-yellow)] to-[var(--color-yellow-hover)] hover:from-[var(--color-yellow-hover)] hover:to-[var(--color-yellow)] text-white py-4 px-8 rounded-2xl font-bold text-lg shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center gap-2"
                  >
                    <Mail className="w-5 h-5" />
                    Contact for Premium Pricing
                    <ArrowRight className="w-5 h-5" />
                  </motion.button>

                  <p className="text-xs text-white/50">
                    Get personalized pricing and consultation
                  </p>
                </div>
              </div>

              {/* Floating Elements */}
              <motion.div
                animate={{ y: [-10, 10, -10] }}
                transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
                className="absolute -top-4 -right-4 w-8 h-8 bg-[var(--color-blue)] rounded-full opacity-80"
              />
              <motion.div
                animate={{ y: [10, -10, 10] }}
                transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
                className="absolute -bottom-4 -left-4 w-6 h-6 bg-[var(--color-blue-secondary)] rounded-full opacity-80"
              />
            </motion.div>
          </div>

          {/* Bottom CTA */}
          <motion.div 
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8, duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mt-16"
          >
            <p className="text-lg text-white/70 mb-4">
              Take your cybersecurity training to the next level with our premium tier. Contact our team for personalized pricing and consultation to unlock exclusive access to advanced labs, personalized mentorship, and hands-on projects.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Contact Form Modal */}
      {showForm && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm"
          onClick={() => setShowForm(false)}
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            transition={{ type: "spring", damping: 25, stiffness: 300 }}
            className="relative bg-white rounded-3xl shadow-2xl max-w-md w-full p-8"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="text-center space-y-6">
              <div className="w-16 h-16 bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-3xl flex items-center justify-center mx-auto">
                <Mail className="w-8 h-8 text-[var(--color-yellow)]" />
              </div>
              
              <div>
                <h3 className="text-2xl font-bold text-[var(--color-dark-blue)] mb-2">Contact Our Team</h3>
                <p className="text-[var(--foreground-secondary)]">Get personalized pricing and consultation for premium tier</p>
              </div>

              <form className="space-y-4">
                <input
                  type="email"
                  placeholder="Enter your email"
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:border-[var(--color-blue)]"
                />
                <input
                  type="text"
                  placeholder="Full name"
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:border-[var(--color-blue)]"
                />
                <textarea
                  placeholder="Tell us about your requirements"
                  rows="3"
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:border-[var(--color-blue)] resize-none"
                />
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="w-full bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] text-white py-3 px-6 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  Send Message
                </motion.button>
              </form>

              <div className="text-sm text-[var(--foreground-secondary)]">
                Or email directly to: <a href="mailto:<EMAIL>" className="text-[var(--color-blue)] hover:underline"><EMAIL></a>
              </div>

              <button
                onClick={() => setShowForm(false)}
                className="text-[var(--foreground-secondary)] hover:text-[var(--color-dark-blue)] transition-colors"
              >
                Close
              </button>
            </div>
          </motion.div>
        </motion.div>
      )}
    </>
  );
} 