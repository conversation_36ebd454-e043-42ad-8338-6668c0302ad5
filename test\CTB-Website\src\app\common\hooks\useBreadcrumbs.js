"use client";
import { usePathname } from 'next/navigation';
import { useMemo } from 'react';

/**
 * Hook to automatically generate breadcrumbs based on current route
 * Returns breadcrumb items array for the BreadcrumbNavigation component
 */
export const useBreadcrumbs = (customConfig = {}) => {
  const pathname = usePathname();

  const breadcrumbs = useMemo(() => {
    // Always start with home
    const baseBreadcrumb = {
      name: "Home",
      url: "/",
      iconKey: "home",
      description: "Return to homepage"
    };

    // Handle root path
    if (pathname === '/') {
      return [{ ...baseBreadcrumb, current: true }];
    }

    // Split pathname into segments
    const segments = pathname.split('/').filter(Boolean);
    const breadcrumbItems = [baseBreadcrumb];

    // Route-specific configurations
    const routeConfigs = {
      'Blogs': {
        name: 'Blog',
        iconKey: 'blogs',
        description: 'Cybersecurity insights and penetration testing guidance'
      },
      'Services': {
        name: 'Services',
        iconKey: 'services',
        description: 'Professional cybersecurity and penetration testing services'
      },
      'Web-app': {
        name: 'Web Application Testing',
        iconKey: 'web-app',
        description: 'Comprehensive web application penetration testing services'
      },
      'Mobile-app': {
        name: 'Mobile Application Testing',
        iconKey: 'mobile-app',
        description: 'Expert mobile application security testing services'
      },
      'Network-pentest': {
        name: 'Network Penetration Testing',
        iconKey: 'network-pentest',
        description: 'Advanced network security assessment and penetration testing'
      },
      'API-pentest': {
        name: 'API Penetration Testing',
        iconKey: 'api-pentest',
        description: 'Specialized API security testing and vulnerability assessment'
      },
      'Company': {
        name: 'Company',
        iconKey: 'company',
        description: 'Learn about Capture The Bug'
      },
      'About-Us': {
        name: 'About Us',
        description: 'Our mission, team, and cybersecurity expertise'
      },
      'Contact-Us': {
        name: 'Contact Us',
        iconKey: 'contact-us',
        description: 'Get in touch with our cybersecurity experts'
      },
      'Press': {
        name: 'Press',
        description: 'Latest news and press releases'
      },
      'Locations': {
        name: 'Locations',
        iconKey: 'locations',
        description: 'Global cybersecurity services across AU, NZ, and US'
      },
      'nz': {
        name: 'New Zealand',
        description: 'New Zealand cybersecurity and penetration testing services'
      },
      'au': {
        name: 'Australia',
        description: 'Australia cybersecurity and penetration testing services'
      },
      'us': {
        name: 'United States',
        description: 'United States cybersecurity and penetration testing services'
      },
      'Product': {
        name: 'Product',
        iconKey: 'product',
        description: 'Explore our cybersecurity platform and tools'
      },
      'Penetration-Testing': {
        name: 'Penetration Testing',
        description: 'Advanced penetration testing platform and services'
      },
      'Pricing': {
        name: 'Pricing',
        iconKey: 'pricing',
        description: 'Transparent pricing for cybersecurity services'
      },
      'Customers': {
        name: 'Customers',
        iconKey: 'customers',
        description: 'Customer success stories and testimonials'
      },
      'Download-Report': {
        name: 'Download Report',
        iconKey: 'download-report',
        description: 'Download cybersecurity reports and resources'
      },
      'Company-size': {
        name: 'Company Size',
        description: 'Cybersecurity solutions by company size'
      },
      'Start-Up': {
        name: 'Startup',
        description: 'Cybersecurity solutions for startups'
      },
      'Growing-Team': {
        name: 'Growing Team',
        description: 'Cybersecurity solutions for growing teams'
      },
      'Enterprise': {
        name: 'Enterprise',
        description: 'Enterprise cybersecurity solutions'
      },
      'Industries': {
        name: 'Industries',
        description: 'Industry-specific cybersecurity solutions'
      },
      'Request-Demo': {
        name: 'Request Demo',
        description: 'Request a demo of our cybersecurity platform'
      },
      'How-it-works': {
        name: 'How It Works',
        description: 'Learn how our penetration testing process works'
      },
      'Case-Studies': {
        name: 'Case Studies',
        description: 'Real-world cybersecurity success stories'
      }
    };

    // Build breadcrumbs from segments
    let currentPath = '';
    
    segments.forEach((segment, index) => {
      currentPath += `/${segment}`;
      const isLast = index === segments.length - 1;
      const config = routeConfigs[segment] || customConfig[segment] || {};
      
      // Use custom name if provided, otherwise format segment
      const name = config.name || segment.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
      
      breadcrumbItems.push({
        name,
        url: currentPath,
        current: isLast,
        iconKey: config.iconKey,
        description: config.description || `${name} page`
      });
    });

    return breadcrumbItems;
  }, [pathname, customConfig]);

  return breadcrumbs;
};

/**
 * Helper function to create custom breadcrumbs for specific pages
 */
export const createCustomBreadcrumbs = (items) => {
  const baseBreadcrumb = {
    name: "Home",
    url: "/",
    iconKey: "home",
    description: "Return to homepage"
  };

  return [baseBreadcrumb, ...items];
};

/**
 * Blog-specific breadcrumb generator
 */
export const createBlogBreadcrumbs = (blogTitle, blogSlug) => {
  return createCustomBreadcrumbs([
    {
      name: "Blog",
      url: "/Blogs",
      iconKey: "blogs",
      description: "Cybersecurity insights and penetration testing guidance"
    },
    {
      name: blogTitle,
      url: `/Blogs/${blogSlug}`,
      current: true,
      description: `${blogTitle} - Expert cybersecurity insights and penetration testing guidance`
    }
  ]);
};

/**
 * Service-specific breadcrumb generator
 */
export const createServiceBreadcrumbs = (serviceTitle, serviceSlug) => {
  return createCustomBreadcrumbs([
    {
      name: "Services",
      url: "/Services",
      iconKey: "services",
      description: "Professional cybersecurity and penetration testing services"
    },
    {
      name: serviceTitle,
      url: `/Services/${serviceSlug}`,
      current: true,
      iconKey: serviceSlug,
      description: `${serviceTitle} - Professional cybersecurity testing services`
    }
  ]);
};

/**
 * Company Size-specific breadcrumb generator
 */
export const createCompanySizeBreadcrumbs = (sizeTitle, sizeSlug) => {
  return createCustomBreadcrumbs([
    {
      name: "Company Size",
      url: "/Company-size",
      iconKey: "company",
      description: "Cybersecurity solutions for different company sizes"
    },
    {
      name: sizeTitle,
      url: `/Company-size/${sizeSlug}`,
      current: true,
      description: `${sizeTitle} cybersecurity solutions and penetration testing services`
    }
  ]);
};

/**
 * Industry-specific breadcrumb generator
 */
export const createIndustryBreadcrumbs = (industryTitle, industrySlug) => {
  return createCustomBreadcrumbs([
    {
      name: "Industries",
      url: "/Industries",
      iconKey: "company",
      description: "Industry-specific cybersecurity solutions"
    },
    {
      name: industryTitle,
      url: `/Industries/${industrySlug}`,
      current: true,
      description: `${industryTitle} cybersecurity solutions and penetration testing services`
    }
  ]);
};

export default useBreadcrumbs;
