// Enhanced design system for location pages with modern, professional styling
export const designSystem = {
  // Section spacing with improved hierarchy
  section: {
    padding: "py-20 px-4", // Increased vertical padding for better breathing room
    paddingSmall: "py-12 px-4", // Alternative smaller padding
    maxWidth: "max-w-7xl mx-auto", // Consistent max width
    gap: "gap-8", // Consistent gap between elements
    gapLarge: "gap-12", // Larger gap for major sections
  },

  // Enhanced container styles with modern aesthetics using brand colors
  container: {
    card: "bg-white/95 backdrop-blur-sm rounded-3xl p-8 shadow-lg border border-gray-100/50 hover:shadow-xl hover:border-primary-blue/20 transition-all duration-500",
    cardSmall: "bg-white/95 backdrop-blur-sm rounded-2xl p-6 shadow-md border border-gray-100/50 hover:shadow-lg hover:border-primary-blue/15 transition-all duration-400",
    cardMinimal: "bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-gray-200/50 hover:bg-white hover:shadow-lg transition-all duration-300",
    gradient: "bg-gradient-to-br from-primary-blue via-primary-blue to-secondary-blue rounded-3xl p-8 text-white shadow-xl",
    gradientSubtle: "bg-gradient-to-br from-primary-blue/5 to-secondary-blue/5 rounded-2xl p-6 border border-primary-blue/10",
    backgroundLight: "bg-gradient-to-b from-white via-gray-50/30 to-white",
    backgroundDark: "bg-gradient-to-b from-gray-50/50 via-white to-gray-50/30",
    backgroundPattern: "bg-white bg-[radial-gradient(circle_at_1px_1px,rgba(8,53,167,0.03)_1px,transparent_0)] bg-[length:24px_24px]",
  },

  // Enhanced grid layouts with better responsiveness
  grid: {
    twoColumn: "grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-start",
    threeColumn: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8",
    fourColumn: "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6",
    autoFit: "grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 lg:gap-8",
    masonry: "columns-1 md:columns-2 lg:columns-3 gap-6 space-y-6",
  },

  // Enhanced typography with better hierarchy
  typography: {
    sectionTitle: "text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6 leading-tight tracking-tight",
    sectionSubtitle: "text-lg md:text-xl text-gray-600 max-w-3xl mx-auto mb-16 leading-relaxed",
    cardTitle: "text-xl md:text-2xl font-bold text-gray-900 mb-4 leading-tight",
    cardSubtitle: "text-gray-600 mb-6 leading-relaxed",
    smallTitle: "text-lg font-semibold text-gray-900 mb-3 leading-tight",
    bodyText: "text-gray-700 leading-relaxed",
    caption: "text-sm text-gray-500",
    highlight: "text-primary-blue font-semibold",
  },

  // Enhanced icon containers with modern styling using brand colors
  icon: {
    large: "bg-gradient-to-br from-primary-blue/10 to-primary-blue/5 w-16 h-16 rounded-2xl flex items-center justify-center mb-6 group-hover:from-primary-blue/20 group-hover:to-primary-blue/10 transition-all duration-300 shadow-sm",
    medium: "bg-gradient-to-br from-primary-blue/10 to-primary-blue/5 w-12 h-12 rounded-xl flex items-center justify-center group-hover:from-primary-blue/20 group-hover:to-primary-blue/10 transition-all duration-300",
    small: "bg-primary-blue/10 w-10 h-10 rounded-xl flex items-center justify-center group-hover:bg-primary-blue/20 transition-colors duration-300",
    minimal: "w-8 h-8 text-primary-blue group-hover:text-secondary-blue transition-colors duration-300",
  },

  // Enhanced animations with smoother transitions
  animation: {
    fadeInUp: {
      initial: { opacity: 0, y: 30 },
      animate: { opacity: 1, y: 0 },
      transition: { duration: 0.8, ease: "easeOut" }
    },
    fadeInScale: {
      initial: { opacity: 0, scale: 0.95 },
      animate: { opacity: 1, scale: 1 },
      transition: { duration: 0.6, ease: "easeOut" }
    },
    staggerContainer: {
      hidden: { opacity: 0 },
      show: {
        opacity: 1,
        transition: { staggerChildren: 0.15, delayChildren: 0.1 }
      }
    },
    staggerItem: {
      hidden: { opacity: 0, y: 30 },
      show: {
        opacity: 1,
        y: 0,
        transition: { duration: 0.6, ease: "easeOut" }
      }
    },
    slideInLeft: {
      hidden: { opacity: 0, x: -30 },
      show: { opacity: 1, x: 0 }
    },
    slideInRight: {
      hidden: { opacity: 0, x: 30 },
      show: { opacity: 1, x: 0 }
    }
  },

  // Enhanced color palette using brand colors from tailwind config
  colors: {
    // Using brand colors from tailwind.config.js
    primary: "primary-blue", // #0835A7
    primaryLight: "ctb-light-blue", // #027bfb
    primaryDark: "secondary-blue", // #062575
    secondary: "secondary-blue", // #062575
    tertiary: "tertiary-blue", // #010D2C
    accent: "ctb-green-50", // #58CC02
    warning: "#f59e0b",
    danger: "#ef4444",
    success: "ctb-green-50", // #58CC02
    gray: {
      25: "#fcfcfd",
      50: "#f9fafb",
      100: "#f3f4f6",
      200: "#e5e7eb",
      300: "#d1d5db",
      400: "#9ca3af",
      500: "#6b7280",
      600: "#4b5563",
      700: "#374151",
      800: "#1f2937",
      900: "#111827"
    }
  },

  // Enhanced spacing system
  spacing: {
    xs: "0.5rem",
    sm: "1rem",
    md: "1.5rem",
    lg: "2rem",
    xl: "3rem",
    "2xl": "4rem",
    "3xl": "6rem",
  },

  // Enhanced button styles using brand colors
  button: {
    primary: "bg-primary-blue hover:bg-secondary-blue text-white font-semibold px-8 py-4 rounded-2xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5",
    secondary: "bg-white hover:bg-gray-50 text-primary-blue font-semibold px-8 py-4 rounded-2xl border-2 border-primary-blue transition-all duration-300 hover:shadow-lg",
    ghost: "text-primary-blue hover:text-secondary-blue font-semibold px-6 py-3 rounded-xl hover:bg-primary-blue/5 transition-all duration-300",
  }
};

// Enhanced helper functions for consistent styling
export const getSectionWrapper = (backgroundType = 'light', paddingSize = 'default') => {
  const backgrounds = {
    light: designSystem.container.backgroundLight,
    dark: designSystem.container.backgroundDark,
    pattern: designSystem.container.backgroundPattern,
    white: 'bg-white',
    gray: 'bg-gray-50'
  };

  const padding = paddingSize === 'small'
    ? designSystem.section.paddingSmall
    : designSystem.section.padding;

  return `${padding} ${backgrounds[backgroundType] || backgrounds.light}`;
};

// Enhanced card styles with more options
export const getCardStyles = (variant = 'default') => {
  const variants = {
    default: designSystem.container.card,
    small: designSystem.container.cardSmall,
    minimal: designSystem.container.cardMinimal,
    gradient: designSystem.container.gradientSubtle,
  };

  return variants[variant] || variants.default;
};

// Helper for consistent icon styling
export const getIconStyles = (size = 'medium') => {
  const sizes = {
    small: designSystem.icon.small,
    medium: designSystem.icon.medium,
    large: designSystem.icon.large,
    minimal: designSystem.icon.minimal,
  };

  return sizes[size] || sizes.medium;
};

// Helper for consistent button styling
export const getButtonStyles = (variant = 'primary') => {
  const variants = {
    primary: designSystem.button.primary,
    secondary: designSystem.button.secondary,
    ghost: designSystem.button.ghost,
  };

  return variants[variant] || variants.primary;
};

// Helper for consistent grid layouts
export const getGridStyles = (columns = 'three') => {
  const grids = {
    one: 'grid grid-cols-1 gap-6',
    two: designSystem.grid.twoColumn,
    three: designSystem.grid.threeColumn,
    four: designSystem.grid.fourColumn,
    auto: designSystem.grid.autoFit,
    masonry: designSystem.grid.masonry,
  };

  return grids[columns] || grids.three;
};
