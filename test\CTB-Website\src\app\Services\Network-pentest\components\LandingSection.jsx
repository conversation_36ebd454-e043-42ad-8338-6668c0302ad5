import Button from "@/app/common/buttons/Button";
import Image from "next/image";
import Link from "next/link";
import React from "react";
import { ArrowRight, Shield, Zap, Target } from "lucide-react";

 
export default function LandingSection() {
  return (
    <div className="bg-[#F8F5F7]">
      <div className="container mx-auto px-4 md:px-12   flex flex-col items-center text-center">

        <div className="flex flex-col gap-3 md:gap-4 max-w-3xl py-16 sm:py-24 lg:py-26">
          <h1 className="text-xl sm:text-2xl md:text-4xl font-bold text-ctb-blue-150 leading-tight md:leading-relaxed px-2 sm:px-0">

             A Modern Way to do <span className="text-blue-700">Network Penetration Testing </span> </h1>

          <p className="text-[#7B6B9C] text-sm sm:text-base md:text-lg leading-relaxed px-2 sm:px-0">
           We deliver premium pentesting solution, cloud-delivered pentesting for internal, external, and thick client networks-mapped to your assets and continuously tracked in a centralized dashboard.
          </p>

          <div className="flex justify-center gap-4 mt-2 md:mt-0">
            <Button
                          href="/Request-Demo"
                          variant="success"
                          size="lg"
                          rightIcon={<ArrowRight className="h-4 w-4 sm:h-5 sm:w-5" />}
                          className="text-base sm:text-lg px-6 sm:px-8"
                        >
                          Get Started Today
                        </Button>
          </div>
        </div>
      </div>

      <div className="relative w-full overflow-hidden min-h-[50px] sm:min-h-[400px]">
              <div
                className="absolute inset-0 w-full mt-[30px] sm:mt-[80px] md:mt-[80px] h-full bg-no-repeat"
                style={{
                  backgroundImage: 'url(/images/serviceBG.png)',
                  backgroundPosition: "top left",
                  backgroundSize: 'cover'
                }}
              />
      
                <div className="relative z-10 flex justify-center pt-0 sm:pt-12 md:pt-0 pb-0 sm:pb-12 md:pb-10">
                       <div className="container mx-auto px-4 md:px-12">
                         <Image
                    src="/images/Home_Dashboard.svg"
                           alt="Large Display"
                           width={1120}
                           height={980}
                           className="w-full max-w-[280px] sm:max-w-[400px] md:max-w-[600px] rounded-3xl lg:max-w-[800px] xl:max-w-[1200px] mx-auto"
                           priority
                           quality={90}
                         />
                       </div>
                     </div>
                   </div>

          <div className="w-full bg-ctb-blue-350 pb-6 sm:pb-8 md:pb-10 pt-8 sm:pt-0">
        <div className="container mx-auto px-4 md:px-12 flex flex-col items-center text-center">
          <div className="max-w-3xl px-4 sm:px-0">
            <h2 className="text-ctb-green-50 text-lg sm:text-xl md:text-2xl lg:text-3xl xl:text-4xl mb-2 sm:mb-3 md:mb-4 
            leading-tight sm:leading-relaxed font-semibold">
              <span className="block sm:inline">Reduce Network Vulnerabilities by Up to 70%</span> 
            </h2>
            <p className="text-white text-xs sm:text-sm md:text-base leading-relaxed px-2 sm:px-0">
              Continuous assessment, Actionable reports, Enterprise-grade security.
            </p>
          </div>
        </div>
      </div>
      
      </div>
     
  );
}
