import React from "react";
import PartnersList from "@/app/Product/Penetration-Testing/components/PartnersList";
import BlogSection from "@/app/Home/components/Blogs";
import Landing from "./components/Landing";
import Security from "./components/Security";
import Testimonial from "../Testimonial";
 
export const metadata = {
  title: "Capture The Bug | AI & ML Infrastructure Security  ",
  description:
    "Protect your LLMs, machine learning pipelines, and training data from adversarial threats. Capture The Bug delivers tailored offensive security for AI systems.",
  keywords:
    "AI security testing, ML infrastructure penetration testing, LLM security assessment, AI model protection, adversarial ML threats, data pipeline vulnerability assessment, AI red teaming, Capture The Bug",
  robots: "index, follow",
  icons: {
    icon: "/favicon.ico",
  },
  openGraph: {
    title: "Capture The Bug | AI & ML Infrastructure Security",
    type: "website",
    url: "https://capturethebug.xyz/Industries/AISecurity&Infrastructure",
    description:
      "Capture The Bug helps you secure your AI systems by testing models, pipelines, and infrastructure for adversarial risks-before they’re exploited.",
    images: "https://ibb.co/wNMKw4sY", 
  },
  twitter: {
    card: "summary_large_image",
    title: "Capture The Bug | AI & ML Infrastructure Security",
    description:
      "Shield your AI systems from threats. Capture The Bug offers offensive security testing for LLMs, data pipelines, and ML environments.",
    images: "https://ibb.co/wNMKw4sY",
  },
};

const testimonial = {
  company: "Yabble",
  logo: "/images/yabble_logo.png",
  quote: "The team at Capture The Bug have been amazing and super easy to work with. In reality, security testing is ongoing, and needs to be effective yet cost efficient. I love the CTB platform format over traditional pen testing, not sure I could go back!",
  author: "Lorraine Guerin",
  position: "Chief Product Officer "
};

export default function AISecurity() {
  return (
    <>
    <Landing/>
   <Security/>
   <PartnersList/> 
   <Testimonial
   company={testimonial.company}
   logo={testimonial.logo}
   quote={testimonial.quote}
   author={testimonial.author}
   position={testimonial.position}
   logoSize={{ width: 140, height: 100 }}
   logoStyle={{ marginLeft: 0 }}
      />
     <BlogSection/> 
</>
  );
}