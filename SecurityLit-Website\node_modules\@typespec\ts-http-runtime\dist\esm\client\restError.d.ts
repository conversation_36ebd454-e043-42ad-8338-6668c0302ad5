import { RestError } from "../restError.js";
import type { PathUncheckedResponse } from "./common.js";
/**
 * Creates a rest error from a PathUnchecked response
 */
export declare function createRestError(response: PathUncheckedResponse): RestError;
/**
 * Creates a rest error from an error message and a PathUnchecked response
 */
export declare function createRestError(message: string, response: PathUncheckedResponse): RestError;
//# sourceMappingURL=restError.d.ts.map