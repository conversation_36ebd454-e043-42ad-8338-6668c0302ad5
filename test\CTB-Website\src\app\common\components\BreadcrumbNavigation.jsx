"use client";
import React, { useState } from 'react';
import Link from 'next/link';
import { ChevronRight, Home, FileText, Users, Settings, MapPin, Shield, Code, Smartphone, Globe, DollarSign, Building, Phone, Download } from 'lucide-react';
import { motion } from 'framer-motion';

/**
 * Truncated Text Component for Mobile Breadcrumbs
 * Shows truncated text on mobile with click to expand functionality
 */
const TruncatedText = ({ text, maxLength = 25, onExpansionChange }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const handleToggle = () => {
    const newExpanded = !isExpanded;
    setIsExpanded(newExpanded);
    if (onExpansionChange) {
      onExpansionChange(newExpanded);
    }
  };

  if (text.length <= maxLength) {
    return <span>{text}</span>;
  }

  return (
    <span
      className="cursor-pointer"
      onClick={handleToggle}
      title={isExpanded ? "Click to collapse" : "Click to expand"}
    >
      <span className="sm:hidden">
        {isExpanded ? text : `${text.substring(0, maxLength)}...`}
      </span>
      <span className="hidden sm:inline">
        {text}
      </span>
    </span>
  );
};

/**
 * Universal Breadcrumb Navigation Component with SEO structured data
 * Provides clear navigation hierarchy and improves SEO across all pages
 */
const BreadcrumbNavigation = ({ items, className = "" }) => {
  const [hasExpandedText, setHasExpandedText] = useState(false);

  // Check if this is a blog page for conditional padding
  const isBlogPage = items?.some(item =>
    item.url?.includes('/Blogs') ||
    item.iconKey === 'blogs' ||
    className?.includes('blog')
  );
  // Icon mapping for different page types
  const iconMap = {
    home: <Home className="w-4 h-4" />,
    blogs: <FileText className="w-4 h-4" />,
    customers: <Users className="w-4 h-4" />,
    company: <Building className="w-4 h-4" />,
    locations: <MapPin className="w-4 h-4" />,
    services: <Shield className="w-4 h-4" />,
    'web-app': <Code className="w-4 h-4" />,
    'mobile-app': <Smartphone className="w-4 h-4" />,
    'network-pentest': <Globe className="w-4 h-4" />,
    'api-pentest': <Code className="w-4 h-4" />,
    product: <Shield className="w-4 h-4" />,
    pricing: <DollarSign className="w-4 h-4" />,
    'contact-us': <Phone className="w-4 h-4" />,
    'download-report': <Download className="w-4 h-4" />,
    settings: <Settings className="w-4 h-4" />
  };

  // Generate structured data for breadcrumbs
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": items.map((item, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": item.name,
      "item": item.current ? undefined : `https://capturethebug.xyz${item.url}`
    }))
  };

  return (
    <>
      {/* Structured Data for SEO */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      
      {/* Simple Breadcrumb Navigation */}
      <nav
        className={`bg-transparent py-2 sm:py-3 ${
          isBlogPage
            ? hasExpandedText
              ? 'pb-6 sm:pb-4'
              : 'pb-3 sm:pb-4'
            : 'pb-2 sm:pb-3'
        } ${className}`}
        aria-label="Breadcrumb navigation"
      >
        <div className="max-w-7xl mx-auto px-4">
          <motion.ol
            className={`flex items-center space-x-1 text-xs sm:text-sm ${
              className?.includes('text-white')
                ? 'text-white'
                : className?.includes('text-blue-600')
                ? 'text-blue-600'
                : 'text-gray-600'
            }`}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.4 }}
          >
            {items.map((item, index) => (
              <motion.li
                key={index}
                className="flex items-center"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
              >
                {index > 0 && (
                  <ChevronRight
                    className={`w-3 h-3 mx-1 ${
                      className?.includes('text-white')
                        ? 'text-white/70'
                        : className?.includes('text-blue-600')
                        ? 'text-blue-400'
                        : 'text-gray-400'
                    }`}
                    aria-hidden="true"
                  />
                )}

                {item.current ? (
                  <span
                    className={`font-medium flex items-center gap-1 ${
                      className?.includes('text-white')
                        ? 'text-white'
                        : className?.includes('text-blue-600')
                        ? 'text-blue-600'
                        : 'text-gray-900'
                    }`}
                    aria-current="page"
                    title={item.description}
                  >
                    {item.iconKey === 'home' && (
                      <Home className={`w-3 h-3 ${
                        className?.includes('text-white')
                          ? 'text-white'
                          : className?.includes('text-blue-600')
                          ? 'text-blue-600'
                          : 'text-blue-600'
                      }`} />
                    )}
                    {item.iconKey === 'blogs' && (
                      <FileText className={`w-3 h-3 ${
                        className?.includes('text-white')
                          ? 'text-white'
                          : className?.includes('text-blue-600')
                          ? 'text-blue-600'
                          : 'text-blue-600'
                      }`} />
                    )}
                    <TruncatedText
                      text={item.name}
                      onExpansionChange={setHasExpandedText}
                    />
                  </span>
                ) : (
                  <Link
                    href={item.url}
                    className={`transition-colors duration-200 flex items-center gap-1 hover:underline ${
                      className?.includes('text-white')
                        ? 'text-white hover:text-white/80'
                        : className?.includes('text-blue-600')
                        ? 'text-blue-600 hover:text-blue-800'
                        : 'text-blue-600 hover:text-blue-800'
                    }`}
                    title={item.description}
                  >
                    {item.iconKey === 'home' && (
                      <Home className={`w-3 h-3 ${
                        className?.includes('text-white')
                          ? 'text-white'
                          : className?.includes('text-blue-600')
                          ? 'text-blue-600'
                          : 'text-current'
                      }`} />
                    )}
                    {item.iconKey === 'blogs' && (
                      <FileText className={`w-3 h-3 ${
                        className?.includes('text-white')
                          ? 'text-white'
                          : className?.includes('text-blue-600')
                          ? 'text-blue-600'
                          : 'text-current'
                      }`} />
                    )}
                    <TruncatedText
                      text={item.name}
                      onExpansionChange={setHasExpandedText}
                    />
                  </Link>
                )}
              </motion.li>
            ))}
          </motion.ol>
        </div>
      </nav>
    </>
  );
};

// Helper function to generate breadcrumb items for different page types
export const generateBreadcrumbs = (pageType, params = {}) => {
  const baseBreadcrumb = {
    name: "Home",
    url: "/",
    iconKey: "home",
    description: "Return to homepage"
  };

  const breadcrumbConfigs = {
    blog: (slug) => [
      baseBreadcrumb,
      {
        name: "Blog",
        url: "/Blogs",
        iconKey: "blogs",
        description: "View all blog posts"
      },
      {
        name: params.title || "Blog Post",
        url: `/Blogs/${slug}`,
        current: true,
        description: `${params.title || 'Blog post'} - Cybersecurity insights and penetration testing guidance`
      }
    ],
    
    service: (service) => [
      baseBreadcrumb,
      {
        name: "Services",
        url: "/Services",
        iconKey: "services",
        description: "View all cybersecurity services"
      },
      {
        name: params.title || service,
        url: `/Services/${service}`,
        current: true,
        iconKey: service,
        description: `${params.title || service} penetration testing services`
      }
    ],

    location: (country) => [
      baseBreadcrumb,
      {
        name: "Locations",
        url: "/Locations",
        iconKey: "locations",
        description: "View all locations"
      },
      {
        name: params.countryName || country,
        url: `/Locations/${country}`,
        current: true,
        description: `${params.countryName || country} cybersecurity services`
      }
    ],

    company: (page) => [
      baseBreadcrumb,
      {
        name: "Company",
        url: "/Company",
        iconKey: "company",
        description: "Learn about Capture The Bug"
      },
      {
        name: params.title || page,
        url: `/Company/${page}`,
        current: true,
        description: params.description || `${page} information`
      }
    ],

    'company-size': (size) => [
      baseBreadcrumb,
      {
        name: "Company Size",
        url: "/Company-size",
        iconKey: "company",
        description: "Solutions for different company sizes"
      },
      {
        name: params.title || size,
        url: `/Company-size/${size}`,
        current: true,
        description: params.description || `${size} cybersecurity solutions`
      }
    ],

    industry: (industry) => [
      baseBreadcrumb,
      {
        name: "Industries",
        url: "/Industries",
        iconKey: "company",
        description: "Industry-specific cybersecurity solutions"
      },
      {
        name: params.title || industry,
        url: `/Industries/${industry}`,
        current: true,
        description: params.description || `${industry} cybersecurity solutions`
      }
    ],

    product: (product) => [
      baseBreadcrumb,
      {
        name: "Product",
        url: "/Product",
        iconKey: "product",
        description: "Explore our cybersecurity products"
      },
      {
        name: params.title || product,
        url: `/Product/${product}`,
        current: true,
        description: params.description || `${product} product information`
      }
    ],

    simple: (pageName, url) => [
      baseBreadcrumb,
      {
        name: pageName,
        url: url,
        current: true,
        iconKey: params.iconKey,
        description: params.description || `${pageName} page`
      }
    ]
  };

  return breadcrumbConfigs[pageType] || (() => [baseBreadcrumb]);
};

export default BreadcrumbNavigation;
