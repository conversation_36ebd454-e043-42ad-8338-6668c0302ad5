"use client";
import React from "react";
import Image from "next/image";

const TrustedTestimonials = () => {
  const trustedCompanies = [
    {
      name: "Microsoft",
      logo: "/images/iso27001-logo.png", // Using existing logo as placeholder
      alt: "Microsoft Logo"
    },
    {
      name: "Amazon", 
      logo: "/images/pci-dss-logo.png",
      alt: "Amazon Logo"
    },
    {
      name: "Google",
      logo: "/images/crest-logo.png", 
      alt: "Google Logo"
    },
    {
      name: "Apple",
      logo: "/images/hipaa-logo.png",
      alt: "Apple Logo"
    }
  ];

  const testimonials = [
    {
      name: "<PERSON>",
      title: "CTO at TechCorp",
      photo: "/images/ankita-dhakar.jpeg", // Using existing team photo
      quote: "SecurityLit's comprehensive security solutions have transformed our approach to cybersecurity. Their real-time monitoring and incident response capabilities are unmatched."
    },
    {
      name: "<PERSON>", 
      title: "CISO at DataFlow",
      photo: "/images/akash-verma.jpeg", // Using existing team photo
      quote: "The level of expertise and dedication from the SecurityLit team is exceptional. They've helped us maintain the highest security standards while scaling our operations."
    },
    {
      name: "<PERSON>",
      title: "VP Security at CloudNet", 
      photo: "/images/archana-verma.jpeg", // Using existing team photo
      quote: "Implementing SecurityLit's PTaaS solution was a game-changer for our organization. The continuous testing and real-time alerts have significantly improved our security posture."
    }
  ];

  return (
    <section className="w-full">
             {/* Dark Section - Trusted by Industry Leaders */}
       <div className="bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 py-20">
         <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
           <div className="text-center mb-10">
                         <h2 className="text-3xl md:text-4xl font-bold text-white mb-3">
               Trusted by Industry Leaders
             </h2>
             <p className="text-lg text-gray-300 max-w-3xl mx-auto">
               Join hundreds of leading organizations that trust SecurityLit for their cybersecurity needs
             </p>
          </div>
          
                     {/* Company Logos */}
           <div className="flex flex-wrap justify-center items-center gap-6 md:gap-8">
             {trustedCompanies.map((company, index) => (
               <div 
                 key={index}
                 className="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 rounded-xl p-4 md:p-6 hover:bg-gray-800/70 transition-all duration-300 group"
               >
                                   <div className="w-20 h-20 md:w-24 md:h-24 mx-auto mb-3 bg-white rounded-lg flex items-center justify-center p-3 group-hover:scale-110 transition-transform duration-300">
                    <Image
                      src={company.logo}
                      alt={company.alt}
                      width={64}
                      height={64}
                      className="w-12 h-12 md:w-16 md:h-16 object-contain"
                    />
                  </div>
                                   <p className="text-white font-medium text-center text-sm md:text-base">
                    {company.name}
                  </p>
              </div>
            ))}
          </div>
        </div>
      </div>

             {/* White Section - Testimonials */}
       <div className="bg-white py-20 border-t-4 border-purple-500">
         <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
           <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {testimonials.map((testimonial, index) => (
                                            <div 
                 key={index}
                 className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100"
               >
                 {/* Testimonial Photo */}
                 <div className="flex items-center mb-6">
                   <div className="w-14 h-14 rounded-full overflow-hidden mr-3">
                     <Image
                       src={testimonial.photo}
                       alt={testimonial.name}
                       width={56}
                       height={56}
                       className="w-full h-full object-cover"
                     />
                   </div>
                   <div>
                     <h3 className="font-bold text-gray-900 text-base">
                       {testimonial.name}
                     </h3>
                     <p className="text-orange-500 font-medium text-sm">
                       {testimonial.title}
                     </p>
                   </div>
                 </div>
                 
                                   {/* Testimonial Quote */}
                  <blockquote className="text-gray-600 leading-relaxed text-sm pt-4">
                    "{testimonial.quote}"
                  </blockquote>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default TrustedTestimonials; 