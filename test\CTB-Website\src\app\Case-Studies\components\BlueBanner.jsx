import WhiteButton from "@/app/common/buttons/WhiteButton";
import React from "react";

export default function BlueBanner() {
  return (
    <div>
      <div className="flex md:flex-row flex-col items-center bg-gradient-to-r from-[#062575] via-[#0B45DB] to-[#0B45DB] md:py-[50px] md:px-10 p-10 md:gap-20 gap-">
        <div className="flex flex-col items-start">
          <div className="Title md:text-4xl text-2xl font-bold text-white md:w-[90%] w-full md:leading-[60px] leading-[40px]">
            Ready To Get Started?
          </div>

          <div className="subTitle md:text-lg text-base text-white mt-4">
          Discover how Capture The Bug enables organizations to stay ahead of cyber threats with agile security testing, helping them manage risks, enhance resilience, and grow confidently.
          </div>
        </div>

        <div className="Button md:pt-2 md:ml-auto pt-10">
          <a href="https://outlook.office.com/bookwithme/user/<EMAIL>?anonymous&ep=pcard">
            <WhiteButton className="rounded-md font-semibold px-6 py-2 w-max flex items-center justify-center">
              <span className="font-montserrat text-[18px] px-4 py-2 font-semibold leading-[28px] text-[#062575] text-center whitespace-nowrap">
                Talk to an Expert
              </span>
            </WhiteButton>
          </a>
        </div>
      </div>
    </div>
  );
}
