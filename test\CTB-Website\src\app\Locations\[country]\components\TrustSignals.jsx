"use client";
import React from 'react';
import { motion } from 'framer-motion';
import {
  Users,
  Star,
  Building2,
  TrendingUp,
  Search
} from 'lucide-react';
import { designSystem } from '../styles/designSystem';
import Image from 'next/image';

/**
 * Enhanced Trust Signals Component
 * Displays certifications, client logos, security badges, and trust indicators
 * with modern, professional design and improved visual hierarchy
 */
const TrustSignals = () => {

  // Enhanced trust statistics with better visual impact
  const trustStats = [
    {
      icon: <Users className="w-6 h-6 text-primary-blue" />,
      number: "100+",
      label: "Clients Secured",
      description: "Businesses globally",
      trend: "+23%",
      color: "from-primary-blue/10 to-primary-blue/5"
    },
    {
      icon: <Search className="w-6 h-6 text-primary-blue" />,
      number: "10,000+",
      label: "Vulnerabilities Found",
      description: "Critical security issues identified",
      trend: "+15%",
      color: "from-primary-blue/10 to-secondary-blue/5"
    },
    {
      icon: <TrendingUp className="w-6 h-6 text-primary-blue" />,
      number: "99.9%",
      label: "Client Satisfaction",
      description: "Based on client feedback surveys",
      trend: "+2%",
      color: "from-primary-blue/10 to-secondary-blue/5"
    },
    {
      icon: <Building2 className="w-6 h-6 text-primary-blue" />,
      number: "7+",
      label: "Countries Served",
      description: "Global reach and expertise",
      trend: "Growing",
      color: "from-primary-blue/10 to-secondary-blue/5"
    }
  ];




  // Certification badges using actual images from /public/images/certifications/
  const certificationBadges = [
    {
      name: "OSCP",
      logo: "/images/certifications/oscp.png",
      alt: "Offensive Security Certified Professional",
      description: "Advanced penetration testing certification"
    },
    {
      name: "CEH",
      logo: "/images/certifications/CEH-logo-2.webp",
      alt: "Certified Ethical Hacker",
      description: "Ethical hacking and penetration testing"
    },
    {
      name: "eJPTv2",
      logo: "/images/certifications/ejptv2.png",
      alt: "eLearnSecurity Junior Penetration Tester v2",
      description: "Junior penetration testing certification"
    },
    {
      name: "CAP",
      logo: "/images/CAP.png",
      alt: "Certified Authorization Professional",
      description: "Risk management framework certification"
    }
  ];

  return (
    <section className="py-16 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="text-center mb-12"
        >
          <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            Trusted by Leading Organizations Worldwide
          </h2>
          <p className="text-base text-gray-600 max-w-2xl mx-auto">
            Our commitment to excellence and security has earned us the trust of
            businesses globally, from startups to Fortune 500 companies.
          </p>
        </motion.div>

        {/* Trust Statistics */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16"
        >
          {trustStats.map((stat, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className="text-center h-full"
            >
              <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300 group h-full min-h-[220px] flex flex-col justify-between">
                <div className="flex flex-col items-center">
                  <div className="w-12 h-12 rounded-xl flex items-center justify-center mx-auto mb-4 bg-gradient-to-br from-primary-blue/10 to-primary-blue/5 group-hover:scale-105 transition-transform duration-300">
                    <div className="w-6 h-6 text-primary-blue">
                      {React.cloneElement(stat.icon, { className: "w-6 h-6 text-primary-blue" })}
                    </div>
                  </div>

                  <div className="mb-3">
                    <div className="text-2xl md:text-3xl font-bold text-gray-900 mb-1 leading-none">
                      {stat.number}
                      <span className="text-ctb-green-50 text-sm ml-2 font-semibold">{stat.trend}</span>
                    </div>
                  </div>

                  <div className="text-base font-semibold text-gray-900 mb-2">
                    {stat.label}
                  </div>
                </div>
                <div className="text-xs text-gray-600 leading-relaxed mt-auto">
                  {stat.description}
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Our Security Experts Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="mb-16"
        >
          <div className="text-center mb-10">
            <h3 className={designSystem.typography.sectionTitle}>
              Our World Class Security Experts
            </h3>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Left Column - CVE Hunters & Expertise */}
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="space-y-6 h-full"
            >
              <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300">
                <div className="mb-4">
                  <h4 className={designSystem.typography.smallTitle}>
                    CVE Hunters: <span className="text-ctb-green-50">20+</span>
                  </h4>
                  <p className="text-ctb-green-50 font-semibold mb-1 text-base">vulnerabilities discovered</p>
                  <p className="text-gray-600 font-medium mb-2 text-base">and counting</p>
                </div>
                <p className={designSystem.typography.bodyText}>
                  We find the bugs before the bad guys do
                </p>
              </div>

              <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300">
                <div className="mb-4">
                  <h4 className={designSystem.typography.smallTitle}>
                    Constantly learning, <span className="text-ctb-green-50">always</span>
                  </h4>
                  <p className="text-ctb-green-50 font-semibold mb-2 text-base">improving</p>
                </div>
                <p className={designSystem.typography.bodyText}>
                  Our team stays ahead of the curve in the ever-evolving world of web security
                </p>
              </div>
            </motion.div>

            {/* Right Column - Certifications */}
            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="text-center"
            >
              <h4 className={designSystem.typography.cardTitle}>
                Our <span className="text-ctb-green-50">Professional Certifications</span>
              </h4>

              <div className="grid grid-cols-2 gap-6 mt-8 max-w-md mx-auto">
                {certificationBadges.map((cert, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, scale: 0.8 }}
                    whileInView={{ opacity: 1, scale: 1 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.4, delay: 0.6 + index * 0.1 }}
                    className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300 flex flex-col items-center justify-center h-[140px] w-full group"
                    title={cert.description}
                  >
                    <div className="w-16 h-16 mx-auto mb-4 flex items-center justify-center relative">
                      <Image
                        src={cert.logo}
                        alt={cert.alt}
                        width={64}
                        height={64}
                        className="object-contain group-hover:scale-105 transition-transform duration-300"
                        priority={index < 2}
                      />
                    </div>
                    <p className="text-sm font-semibold text-gray-900 text-center leading-tight">{cert.name}</p>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          </div>
        </motion.div>





        {/* Client Testimonial Quote */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          whileInView={{ opacity: 1, scale: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="bg-gradient-to-br from-[#0835A7] to-[#0835A7]/90 text-white p-8 md:p-12 lg:p-16 rounded-2xl text-center relative overflow-hidden"
        >
          {/* Enhanced background decoration */}
          <div className="absolute top-0 right-0 w-64 h-64 bg-white/5 rounded-full -translate-x-20 -translate-y-20 blur-3xl"></div>
          <div className="absolute bottom-0 left-0 w-48 h-48 bg-white/5 rounded-full translate-x-10 translate-y-10 blur-2xl"></div>
          <div className="absolute top-1/2 left-1/2 w-24 h-24 bg-white/3 rounded-full -translate-x-12 -translate-y-12"></div>

          <div className="relative z-10">
            {/* Star rating with animation */}
            <motion.div
              className="flex justify-center mb-6"
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              {[...Array(5)].map((_, i) => (
                <motion.div
                  key={i}
                  initial={{ opacity: 0, rotate: -180 }}
                  whileInView={{ opacity: 1, rotate: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: 0.3 + i * 0.1 }}
                >
                  <Star className="w-5 h-5 text-ctb-green-50 fill-ctb-green-50 mx-1" />
                </motion.div>
              ))}
            </motion.div>

            <motion.blockquote
              className="text-lg md:text-xl lg:text-2xl font-medium mb-6 max-w-3xl mx-auto leading-relaxed"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              &ldquo;Capture The Bug has efficiently and affordably helped us meet our cybersecurity goals.
              Their tailored solutions and proactive approach have fortified our defenses, providing peace of mind.
              The real-time bug reports and their dedicated assistance ensure we are vigilant against cyber threats.&rdquo;
            </motion.blockquote>

            <motion.div
              className="text-white/90"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.6 }}
            >
              <div className="font-semibold text-base mb-1">Nathan Taylor</div>
              <div className="text-sm text-white/70">Chief Operating Officer, PARTLY</div>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default TrustSignals;
