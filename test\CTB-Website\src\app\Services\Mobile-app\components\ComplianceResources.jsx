'use client'
import React, { useState } from 'react';
import { FaCheckCircle } from 'react-icons/fa';
import Image from 'next/image';

const complianceOptions = [
  {
    title: "Speed And Scale",
    description: "Launch penetration tests in days, not weeks. Get findings delivered directly into your development and security workflows for rapid issue resolution.",
    image: "/images/Bug_Bounty.jpg"
  },
  {
    title: "Higher Impact Results",
    description: "Exceed compliance standards by empowering pentesters to deliver high-impact results tailored to your mobile app's unique needs.",
    image: "/api/placeholder/800/600"
  },
  {
    title: "Deep Customization",
    description: "Customize your testing with a team built specifically for your mobile app. Mix and match test types, methodologies, durations, and more to suit your exact requirements.",
    image: "/api/placeholder/800/600"
  },
  {
    title: "Real-Time Insights",
    description: "Monitor findings and pentester progress in real-time through our dynamic PTaaS Dashboard, ensuring complete visibility into your mobile app's security posture.",
    image: "/api/placeholder/800/600"
  }
];

const ComplianceResources = () => {
  const [selectedOption, setSelectedOption] = useState(0);

  return (
    <div className="flex flex-col w-full min-h-screen bg-white items-center py-10">
      {/* Title and Subtitle Section */}
      <div className="w-full text-center p-8">
        <h2 className="text-sm font-semibold mb-2 text-gray-500">OUR APPROACH</h2>
        <h1 className="text-3xl font-bold mb-8 text-blue-700">Strengthen Mobile App Security with Expert Penetration Testing Solutions
        </h1>
      </div>
      
      {/* Content Section */}
      <div className="flex flex-col lg:flex-row w-full p-8">
        {/* Options List */}
        <div className="lg:w-1/2 p-4">
          {complianceOptions.map((option, index) => (
            <div 
              key={index} 
              className={`mb-6 p-4 rounded-lg cursor-pointer transition-all duration-300 ${selectedOption === index ? 'bg-gray-100' : 'hover:bg-gray-50'}`}
              onClick={() => setSelectedOption(index)}
            >
              <div className="flex items-start">
                <div className={`w-1 h-16 mr-4 ${selectedOption === index ? 'bg-blue-500' : 'bg-transparent'}`}></div>
                <div>
                  <h3 className="text-lg font-semibold mb-2">{option.title}</h3>
                  <p className="text-sm text-gray-600">{option.description}</p>
                </div>
                {selectedOption === index && (
                  <FaCheckCircle className="text-blue-500 ml-auto" size={20} />
                )}
              </div>
            </div>
          ))}
        </div>
        
        {/* Image Display */}
        <div className="lg:w-1/2 flex items-center justify-center p-4">
          <Image 
            src={complianceOptions[selectedOption].image} 
            alt={complianceOptions[selectedOption].title}
            className="max-w-full max-h-full object-cover rounded-lg shadow-xl"
            width={800}
            height={600}
          />
        </div>
      </div>
    </div>
  );
};

export default ComplianceResources;
