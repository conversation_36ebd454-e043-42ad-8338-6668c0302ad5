(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/app/common/buttons/BrandButtons.jsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// src/app/common/BrandButtons.jsx
__turbopack_context__.s({
    "AccentButton": ()=>AccentButton,
    "CallToActionButton": ()=>CallToActionButton,
    "LinkButton": ()=>LinkButton,
    "OutlinedButton": ()=>OutlinedButton,
    "OutlinedCallToActionButton": ()=>OutlinedCallToActionButton,
    "OutlinedLinkButton": ()=>OutlinedLinkButton,
    "PrimaryButton": ()=>PrimaryButton,
    "SecondaryButton": ()=>SecondaryButton,
    "SubmitButton": ()=>SubmitButton
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
;
function PrimaryButton(param) {
    let { children, className = "", ...props } = param;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
        className: "btn-primary text-white px-8 py-3 rounded-lg font-bold text-lg shadow-lg flex items-center justify-center gap-2 cursor-pointer ".concat(className),
        ...props,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/app/common/buttons/BrandButtons.jsx",
        lineNumber: 8,
        columnNumber: 5
    }, this);
}
_c = PrimaryButton;
function SecondaryButton(param) {
    let { children, className = "", ...props } = param;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
        className: "btn-secondary text-white px-8 py-3 rounded-lg font-bold text-lg shadow-md flex items-center justify-center gap-2 cursor-pointer ".concat(className),
        ...props,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/app/common/buttons/BrandButtons.jsx",
        lineNumber: 20,
        columnNumber: 5
    }, this);
}
_c1 = SecondaryButton;
function OutlinedButton(param) {
    let { children, className = "", ...props } = param;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
        className: "btn-outlined text-[var(--color-dark-blue)] px-8 py-3 rounded-lg font-bold text-lg shadow-md flex items-center justify-center gap-2 cursor-pointer ".concat(className),
        ...props,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/app/common/buttons/BrandButtons.jsx",
        lineNumber: 32,
        columnNumber: 5
    }, this);
}
_c2 = OutlinedButton;
function AccentButton(param) {
    let { children, className = "", ...props } = param;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
        className: "btn-accent text-[var(--color-gray)] px-8 py-3 rounded-lg font-bold text-lg shadow-md flex items-center justify-center gap-2 cursor-pointer ".concat(className),
        ...props,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/app/common/buttons/BrandButtons.jsx",
        lineNumber: 44,
        columnNumber: 5
    }, this);
}
_c3 = AccentButton;
function SubmitButton(param) {
    let { children, className = "", ...props } = param;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
        type: "submit",
        className: "w-full bg-[var(--color-blue)] hover:bg-[var(--color-blue-secondary)] text-white py-4 px-6 rounded-lg font-semibold text-lg transition-all shadow-lg hover:shadow-xl ".concat(className),
        ...props,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/app/common/buttons/BrandButtons.jsx",
        lineNumber: 56,
        columnNumber: 5
    }, this);
}
_c4 = SubmitButton;
function CallToActionButton(param) {
    let { children, className = "", ...props } = param;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
        className: "bg-white text-[var(--color-blue)] px-6 py-3 rounded-lg font-semibold hover:bg-gray-50 transition-colors ".concat(className),
        ...props,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/app/common/buttons/BrandButtons.jsx",
        lineNumber: 69,
        columnNumber: 5
    }, this);
}
_c5 = CallToActionButton;
function OutlinedCallToActionButton(param) {
    let { children, className = "", ...props } = param;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
        className: "border-2 border-white text-white px-6 py-3 rounded-lg font-semibold hover:bg-white hover:text-[var(--color-blue)] transition-colors ".concat(className),
        ...props,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/app/common/buttons/BrandButtons.jsx",
        lineNumber: 81,
        columnNumber: 5
    }, this);
}
_c6 = OutlinedCallToActionButton;
function LinkButton(param) {
    let { href, children, className = "", ...props } = param;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
        href: href,
        className: "bg-white text-[var(--color-blue)] px-6 py-3 rounded-lg font-semibold hover:bg-gray-50 transition-colors inline-block text-center ".concat(className),
        ...props,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/app/common/buttons/BrandButtons.jsx",
        lineNumber: 93,
        columnNumber: 5
    }, this);
}
_c7 = LinkButton;
function OutlinedLinkButton(param) {
    let { href, children, className = "", ...props } = param;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
        href: href,
        className: "border-2 border-white text-white px-6 py-3 rounded-lg font-semibold hover:bg-white hover:text-[var(--color-blue)] transition-colors inline-block text-center ".concat(className),
        ...props,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/app/common/buttons/BrandButtons.jsx",
        lineNumber: 106,
        columnNumber: 5
    }, this);
}
_c8 = OutlinedLinkButton;
var _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8;
__turbopack_context__.k.register(_c, "PrimaryButton");
__turbopack_context__.k.register(_c1, "SecondaryButton");
__turbopack_context__.k.register(_c2, "OutlinedButton");
__turbopack_context__.k.register(_c3, "AccentButton");
__turbopack_context__.k.register(_c4, "SubmitButton");
__turbopack_context__.k.register(_c5, "CallToActionButton");
__turbopack_context__.k.register(_c6, "OutlinedCallToActionButton");
__turbopack_context__.k.register(_c7, "LinkButton");
__turbopack_context__.k.register(_c8, "OutlinedLinkButton");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/common/Header.jsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// src/app/common/Header.jsx
__turbopack_context__.s({
    "default": ()=>Header
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$common$2f$buttons$2f$BrandButtons$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app/common/buttons/BrandButtons.jsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/fa/index.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
function Header() {
    var _navLinks_find, _navLinks_find1;
    _s();
    const [mobileMenuOpen, setMobileMenuOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [servicesDropdownOpen, setServicesDropdownOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [resourcesDropdownOpen, setResourcesDropdownOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [openMobileDropdown, setOpenMobileDropdown] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isScrolled, setIsScrolled] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isVisible, setIsVisible] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const [lastScrollY, setLastScrollY] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    // CHANGED: Added a separate ref for the dropdown panel
    const dropdownTriggerRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const dropdownPanelRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    // Professional scroll detection for navbar hide/show and glass effect
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Header.useEffect": ()=>{
            let ticking = false;
            let scrollThreshold = 5; // Minimum scroll distance to trigger hide/show
            const handleScroll = {
                "Header.useEffect.handleScroll": ()=>{
                    if (!ticking) {
                        requestAnimationFrame({
                            "Header.useEffect.handleScroll": ()=>{
                                const currentScrollY = window.scrollY;
                                const scrollDifference = Math.abs(currentScrollY - lastScrollY);
                                // Glass effect
                                setIsScrolled(currentScrollY > 50);
                                // Only trigger hide/show if scroll difference is significant
                                if (scrollDifference > scrollThreshold) {
                                    // Navbar hide/show logic with improved threshold
                                    if (currentScrollY > lastScrollY && currentScrollY > 80) {
                                        // Scrolling down - hide navbar
                                        setIsVisible(false);
                                        console.log('Hiding navbar - scrolling down', currentScrollY);
                                    } else if (currentScrollY < lastScrollY || currentScrollY <= 80) {
                                        // Scrolling up or near top - show navbar
                                        setIsVisible(true);
                                        console.log('Showing navbar - scrolling up or at top', currentScrollY);
                                    }
                                    setLastScrollY(currentScrollY);
                                }
                                ticking = false;
                            }
                        }["Header.useEffect.handleScroll"]);
                        ticking = true;
                    }
                }
            }["Header.useEffect.handleScroll"];
            window.addEventListener('scroll', handleScroll, {
                passive: true
            });
            return ({
                "Header.useEffect": ()=>window.removeEventListener('scroll', handleScroll)
            })["Header.useEffect"];
        }
    }["Header.useEffect"], [
        lastScrollY
    ]);
    // Navigation links structure (no changes)
    const navLinks = [
        {
            name: "Services",
            dropdown: [
                {
                    name: "Virtual CISO (vCISO)",
                    description: "Expert cybersecurity leadership and strategic guidance",
                    href: "/services/vciso"
                },
                {
                    name: "Red Teaming",
                    description: "Simulated cyberattacks to test and strengthen defenses",
                    href: "/services/Red-Teaming"
                },
                {
                    name: "AWS and Azure Configuration",
                    description: "Secure cloud infrastructure setup and management",
                    href: "/services/aws-azure"
                },
                {
                    name: "Web3 Audits (Pentest)",
                    description: "Comprehensive security audits for blockchain projects",
                    href: "/services/web3-audits"
                },
                {
                    name: "VDP",
                    description: "Vulnerability Disclosure Program management",
                    href: "/services/vdp"
                },
                {
                    name: "VAPT",
                    description: "Vulnerability Assessment and Penetration Testing",
                    href: "/services/vapt"
                },
                {
                    name: "Bug Bounty (Through Capture The Bug)",
                    description: "Crowdsourced security testing platform",
                    href: "/services/bug-bounty"
                },
                {
                    name: "Compliance Pre Assessment",
                    description: "Get audit-ready with compliance health checks",
                    href: "/services/compliance"
                },
                {
                    name: "Office365 Assessment",
                    description: "Security evaluation of Microsoft Office 365 environment",
                    href: "/services/office365"
                },
                {
                    name: "Cloud Assessment",
                    description: "Comprehensive cloud security evaluation",
                    href: "/services/cloud-assessment"
                },
                {
                    name: "Google WorkSpace Assessment",
                    description: "Security assessment of Google Workspace setup",
                    href: "/services/google-workspace"
                },
                {
                    name: "Incident response",
                    description: "Rapid response to security incidents and crisis management",
                    href: "/services/incident-response"
                },
                {
                    name: "Source code review",
                    description: "In-depth analysis of application source code security",
                    href: "/services/source-code-review"
                }
            ]
        },
        {
            name: "Security Training",
            href: "/CybersecurityTraining"
        },
        {
            name: "Product",
            href: "/product"
        },
        {
            name: "Resources",
            dropdown: [
                {
                    name: "Blogs",
                    description: "Latest cybersecurity insights and industry updates",
                    href: "/blogs"
                },
                {
                    name: "News",
                    description: "Latest security news and updates",
                    href: "/news"
                }
            ]
        },
        {
            name: "About Us",
            href: "/about"
        }
    ];
    // CHANGED: Updated the effect to check both refs
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "Header.useEffect": ()=>{
            function handleClickOutside(event) {
                if (dropdownTriggerRef.current && !dropdownTriggerRef.current.contains(event.target) && dropdownPanelRef.current && !dropdownPanelRef.current.contains(event.target)) {
                    setServicesDropdownOpen(false);
                    setResourcesDropdownOpen(false);
                }
            }
            document.addEventListener("mousedown", handleClickOutside);
            return ({
                "Header.useEffect": ()=>{
                    document.removeEventListener("mousedown", handleClickOutside);
                }
            })["Header.useEffect"];
        }
    }["Header.useEffect"], []); // Dependencies are not needed as refs don't trigger re-renders
    // REMOVED: The complex handleDownload function is no longer needed.
    const NavLink = (param)=>{
        let { item } = param;
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            href: item.href,
            className: "px-4 py-2 hover:text-[var(--color-yellow)] transition-colors font-medium text-white",
            children: item.name
        }, void 0, false, {
            fileName: "[project]/src/app/common/Header.jsx",
            lineNumber: 116,
            columnNumber: 5
        }, this);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("header", {
        className: "fixed top-0 left-0 w-full z-[9999] py-2 mb-16 md:mb-0 transition-all duration-300 ease-in-out ".concat(isVisible ? 'translate-y-0 opacity-100' : '-translate-y-full opacity-0'),
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "w-full max-w-7xl mx-auto px-2 sm:px-4 lg:px-0 relative",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "navbar-glass ".concat(isScrolled ? 'scrolled' : '', " rounded-4xl shadow-lg border border-gray-700"),
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center justify-between h-16 px-6",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex-shrink-0",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                        href: "/",
                                        className: "flex items-center gap-2",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                            src: "/seclit-logo-white.png",
                                            alt: "SecurityLit Logo",
                                            className: "h-12 w-auto"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/common/Header.jsx",
                                            lineNumber: 129,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/common/Header.jsx",
                                        lineNumber: 128,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/app/common/Header.jsx",
                                    lineNumber: 127,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("nav", {
                                    className: "hidden lg:flex items-center space-x-8",
                                    children: navLinks.map((link)=>link.dropdown ? // CHANGED: Attached the trigger ref here
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "relative",
                                            ref: link.name === "Services" ? dropdownTriggerRef : null,
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                onClick: ()=>{
                                                    if (link.name === "Services") {
                                                        setServicesDropdownOpen(!servicesDropdownOpen);
                                                        setResourcesDropdownOpen(false);
                                                    } else if (link.name === "Resources") {
                                                        setResourcesDropdownOpen(!resourcesDropdownOpen);
                                                        setServicesDropdownOpen(false);
                                                    }
                                                },
                                                onMouseEnter: ()=>{
                                                    if (link.name === "Services") {
                                                        setServicesDropdownOpen(true);
                                                        setResourcesDropdownOpen(false);
                                                    } else if (link.name === "Resources") {
                                                        setResourcesDropdownOpen(true);
                                                        setServicesDropdownOpen(false);
                                                    }
                                                },
                                                className: "flex items-center gap-2 px-4 py-2 hover:text-[var(--color-yellow)] transition-colors font-medium text-white",
                                                children: [
                                                    link.name,
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FaChevronDown"], {
                                                        className: "h-3 w-3 transition-transform duration-300 ".concat(link.name === "Services" && servicesDropdownOpen || link.name === "Resources" && resourcesDropdownOpen ? 'transform rotate-180' : '')
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/common/Header.jsx",
                                                        lineNumber: 165,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/common/Header.jsx",
                                                lineNumber: 143,
                                                columnNumber: 22
                                            }, this)
                                        }, link.name, false, {
                                            fileName: "[project]/src/app/common/Header.jsx",
                                            lineNumber: 138,
                                            columnNumber: 38
                                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(NavLink, {
                                            item: link
                                        }, link.name, false, {
                                            fileName: "[project]/src/app/common/Header.jsx",
                                            lineNumber: 169,
                                            columnNumber: 19
                                        }, this))
                                }, void 0, false, {
                                    fileName: "[project]/src/app/common/Header.jsx",
                                    lineNumber: 134,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "hidden lg:block",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$common$2f$buttons$2f$BrandButtons$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PrimaryButton"], {
                                        onClick: ()=>window.location.href = '/contact',
                                        className: "px-3 py-1.5 lg:px-4 lg:py-2 text-sm",
                                        children: "Contact Us"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/common/Header.jsx",
                                        lineNumber: 176,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/app/common/Header.jsx",
                                    lineNumber: 175,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "lg:hidden",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        onClick: ()=>setMobileMenuOpen(!mobileMenuOpen),
                                        className: "text-white",
                                        children: mobileMenuOpen ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FaTimes"], {
                                            className: "h-6 w-6"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/common/Header.jsx",
                                            lineNumber: 184,
                                            columnNumber: 35
                                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FaBars"], {
                                            className: "h-6 w-6"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/common/Header.jsx",
                                            lineNumber: 184,
                                            columnNumber: 69
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/common/Header.jsx",
                                        lineNumber: 183,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/app/common/Header.jsx",
                                    lineNumber: 182,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/common/Header.jsx",
                            lineNumber: 125,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/common/Header.jsx",
                        lineNumber: 124,
                        columnNumber: 9
                    }, this),
                    servicesDropdownOpen && // CHANGED: Attached the panel ref here
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        ref: dropdownPanelRef,
                        className: "absolute top-full left-1/2 transform -translate-x-1/2 mt-0 w-11/12 max-w-6xl z-[10000]",
                        onMouseEnter: ()=>setServicesDropdownOpen(true),
                        onMouseLeave: ()=>setServicesDropdownOpen(false),
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "h-2 bg-transparent"
                            }, void 0, false, {
                                fileName: "[project]/src/app/common/Header.jsx",
                                lineNumber: 200,
                                columnNumber: 14
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "bg-white rounded-2xl shadow-xl py-6 border border-gray-200 fade-in-up",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "grid grid-cols-1 lg:grid-cols-4 gap-6 px-8",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "lg:col-span-3",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",
                                                children: (_navLinks_find = navLinks.find((link)=>link.dropdown)) === null || _navLinks_find === void 0 ? void 0 : _navLinks_find.dropdown.map((item, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                        href: item.href,
                                                        onClick: ()=>setServicesDropdownOpen(false),
                                                        className: "block w-full text-left p-3 hover:bg-gray-50 transition-colors rounded-lg group",
                                                        style: {
                                                            animationDelay: "".concat(index * 0.05, "s")
                                                        },
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                                className: "font-semibold text-[var(--color-dark-blue)] mb-1 group-hover:text-[var(--color-blue)] transition-colors text-sm",
                                                                children: item.name
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/common/Header.jsx",
                                                                lineNumber: 213,
                                                                columnNumber: 26
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                className: "text-xs text-gray-600 leading-relaxed",
                                                                children: item.description
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/common/Header.jsx",
                                                                lineNumber: 216,
                                                                columnNumber: 26
                                                            }, this)
                                                        ]
                                                    }, item.name, true, {
                                                        fileName: "[project]/src/app/common/Header.jsx",
                                                        lineNumber: 206,
                                                        columnNumber: 24
                                                    }, this))
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/common/Header.jsx",
                                                lineNumber: 204,
                                                columnNumber: 20
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/common/Header.jsx",
                                            lineNumber: 203,
                                            columnNumber: 18
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "lg:col-span-1 lg:border-l lg:border-gray-200 lg:pl-6",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-lg p-4 text-white",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "mb-4",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                                            src: "/brochure-cover.png",
                                                            alt: "SecurityLit Brochure Cover",
                                                            className: "w-full h-32 object-cover rounded-lg shadow-lg"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/common/Header.jsx",
                                                            lineNumber: 227,
                                                            columnNumber: 24
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/common/Header.jsx",
                                                        lineNumber: 225,
                                                        columnNumber: 22
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                        className: "font-bold text-lg mb-2",
                                                        children: "Download Brochure"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/common/Header.jsx",
                                                        lineNumber: 233,
                                                        columnNumber: 22
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        className: "text-sm text-white/90 mb-4",
                                                        children: "Get our comprehensive service guide"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/common/Header.jsx",
                                                        lineNumber: 234,
                                                        columnNumber: 22
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                                        href: "/seclit-brochure.pdf",
                                                        download: "seclit-brochure.pdf",
                                                        onClick: ()=>setServicesDropdownOpen(false),
                                                        className: "bg-white text-[var(--color-blue)] px-4 py-2 rounded-lg font-semibold text-sm hover:bg-gray-50 transition-colors block text-center w-full",
                                                        children: "Download →"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/common/Header.jsx",
                                                        lineNumber: 236,
                                                        columnNumber: 22
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/common/Header.jsx",
                                                lineNumber: 224,
                                                columnNumber: 20
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/common/Header.jsx",
                                            lineNumber: 223,
                                            columnNumber: 18
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/common/Header.jsx",
                                    lineNumber: 202,
                                    columnNumber: 16
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/common/Header.jsx",
                                lineNumber: 201,
                                columnNumber: 14
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/common/Header.jsx",
                        lineNumber: 193,
                        columnNumber: 12
                    }, this),
                    resourcesDropdownOpen && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "absolute top-full left-1/2 transform -translate-x-1/2 mt-0 w-11/12 max-w-4xl z-[10000]",
                        onMouseEnter: ()=>setResourcesDropdownOpen(true),
                        onMouseLeave: ()=>setResourcesDropdownOpen(false),
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "h-2 bg-transparent"
                            }, void 0, false, {
                                fileName: "[project]/src/app/common/Header.jsx",
                                lineNumber: 259,
                                columnNumber: 14
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "bg-white rounded-2xl shadow-xl py-6 border border-gray-200 fade-in-up",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "grid grid-cols-1 lg:grid-cols-2 gap-6 px-8",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "lg:col-span-1",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "grid grid-cols-1 gap-4",
                                                children: (_navLinks_find1 = navLinks.find((link)=>link.name === "Resources")) === null || _navLinks_find1 === void 0 ? void 0 : _navLinks_find1.dropdown.map((item, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                        href: item.href,
                                                        onClick: ()=>setResourcesDropdownOpen(false),
                                                        className: "block w-full text-left p-3 hover:bg-gray-50 transition-colors rounded-lg group",
                                                        style: {
                                                            animationDelay: "".concat(index * 0.1, "s")
                                                        },
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                                className: "font-semibold text-[var(--color-dark-blue)] mb-1 group-hover:text-[var(--color-blue)] transition-colors text-sm",
                                                                children: item.name
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/common/Header.jsx",
                                                                lineNumber: 272,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                className: "text-xs text-gray-600 leading-relaxed",
                                                                children: item.description
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/common/Header.jsx",
                                                                lineNumber: 275,
                                                                columnNumber: 23
                                                            }, this)
                                                        ]
                                                    }, item.name, true, {
                                                        fileName: "[project]/src/app/common/Header.jsx",
                                                        lineNumber: 265,
                                                        columnNumber: 21
                                                    }, this))
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/common/Header.jsx",
                                                lineNumber: 263,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/common/Header.jsx",
                                            lineNumber: 262,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "lg:col-span-1 lg:border-l lg:border-gray-200 lg:pl-6",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-lg p-4 text-white",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                        className: "font-bold text-lg mb-2",
                                                        children: "Stay Updated"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/common/Header.jsx",
                                                        lineNumber: 284,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        className: "text-sm text-white/90 mb-4",
                                                        children: "Get the latest cybersecurity insights and news"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/common/Header.jsx",
                                                        lineNumber: 285,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                                        href: "/subscribe",
                                                        className: "bg-white text-[var(--color-blue)] px-4 py-2 rounded-lg font-semibold text-sm hover:bg-gray-50 transition-colors block text-center w-full",
                                                        children: "Subscribe →"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/common/Header.jsx",
                                                        lineNumber: 286,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/common/Header.jsx",
                                                lineNumber: 283,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/common/Header.jsx",
                                            lineNumber: 282,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/common/Header.jsx",
                                    lineNumber: 261,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/common/Header.jsx",
                                lineNumber: 260,
                                columnNumber: 14
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/common/Header.jsx",
                        lineNumber: 253,
                        columnNumber: 12
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/common/Header.jsx",
                lineNumber: 123,
                columnNumber: 7
            }, this),
            mobileMenuOpen && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "lg:hidden absolute top-24 left-4 right-4 bg-gray-800 rounded-2xl shadow-lg z-[10000] border border-gray-700 max-h-[80vh] overflow-y-auto fade-in-up",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("nav", {
                    className: "flex flex-col items-center space-y-2 px-4 py-6",
                    children: [
                        navLinks.map((link, idx)=>link.dropdown ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "w-full text-center",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        onClick: ()=>{
                                            if (openMobileDropdown === idx) {
                                                setOpenMobileDropdown(null);
                                            } else {
                                                setOpenMobileDropdown(idx);
                                            }
                                        },
                                        className: "w-full flex items-center justify-center gap-2 px-4 py-3 hover:text-[var(--color-dark-blue)] transition-colors font-medium text-white",
                                        children: [
                                            link.name,
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FaChevronDown"], {
                                                className: "h-3 w-3 transition-transform duration-300 ".concat(openMobileDropdown === idx ? 'transform rotate-180' : '')
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/common/Header.jsx",
                                                lineNumber: 318,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/common/Header.jsx",
                                        lineNumber: 307,
                                        columnNumber: 20
                                    }, this),
                                    openMobileDropdown === idx && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "w-full bg-white rounded-lg py-4 mt-2 border border-gray-200 fade-in-up",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "grid grid-cols-1 gap-3 px-4 mb-4 max-h-[60vh] overflow-y-auto",
                                                children: link.dropdown.map((item, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                        href: item.href,
                                                        onClick: ()=>{
                                                            setOpenMobileDropdown(null);
                                                            setMobileMenuOpen(false);
                                                        },
                                                        className: "block w-full text-left p-3 hover:bg-gray-50 transition-colors rounded-lg",
                                                        style: {
                                                            animationDelay: "".concat(index * 0.05, "s")
                                                        },
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                                className: "font-semibold text-[var(--color-dark-blue)] mb-1 text-sm",
                                                                children: item.name
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/common/Header.jsx",
                                                                lineNumber: 334,
                                                                columnNumber: 29
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                className: "text-xs text-gray-600",
                                                                children: item.description
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/common/Header.jsx",
                                                                lineNumber: 335,
                                                                columnNumber: 29
                                                            }, this)
                                                        ]
                                                    }, item.name, true, {
                                                        fileName: "[project]/src/app/common/Header.jsx",
                                                        lineNumber: 324,
                                                        columnNumber: 27
                                                    }, this))
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/common/Header.jsx",
                                                lineNumber: 322,
                                                columnNumber: 23
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "border-t border-gray-200 pt-4 px-4",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-lg p-4 text-white",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "mb-4",
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                                                src: "/brochure-cover.png",
                                                                alt: "SecurityLit Brochure Cover",
                                                                className: "w-full h-32 object-cover rounded-lg shadow-lg"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/common/Header.jsx",
                                                                lineNumber: 342,
                                                                columnNumber: 30
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/common/Header.jsx",
                                                            lineNumber: 341,
                                                            columnNumber: 54
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                            className: "font-bold text-lg mb-2",
                                                            children: "Download Brochure"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/common/Header.jsx",
                                                            lineNumber: 348,
                                                            columnNumber: 27
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "text-sm text-white/90 mb-4",
                                                            children: "Get our comprehensive service guide"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/common/Header.jsx",
                                                            lineNumber: 349,
                                                            columnNumber: 27
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                                            href: "/seclit-brochure.pdf",
                                                            download: "seclit-brochure.pdf",
                                                            onClick: ()=>{
                                                                setOpenMobileDropdown(null);
                                                                setMobileMenuOpen(false);
                                                            },
                                                            className: "bg-white text-[var(--color-blue)] px-4 py-2 rounded-lg font-semibold text-sm hover:bg-gray-50 transition-colors block text-center w-full",
                                                            children: "Download →"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/common/Header.jsx",
                                                            lineNumber: 351,
                                                            columnNumber: 27
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/common/Header.jsx",
                                                    lineNumber: 340,
                                                    columnNumber: 25
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/common/Header.jsx",
                                                lineNumber: 339,
                                                columnNumber: 23
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/common/Header.jsx",
                                        lineNumber: 321,
                                        columnNumber: 21
                                    }, this)
                                ]
                            }, link.name, true, {
                                fileName: "[project]/src/app/common/Header.jsx",
                                lineNumber: 306,
                                columnNumber: 17
                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                href: link.href,
                                onClick: ()=>setMobileMenuOpen(false),
                                className: "block w-full text-center px-4 py-3 hover:bg-gray-700 rounded-lg font-medium text-white",
                                children: link.name
                            }, link.name, false, {
                                fileName: "[project]/src/app/common/Header.jsx",
                                lineNumber: 368,
                                columnNumber: 17
                            }, this)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "pt-4 w-full",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$common$2f$buttons$2f$BrandButtons$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PrimaryButton"], {
                                className: "w-full px-4 py-2 text-sm",
                                onClick: ()=>window.location.href = '/contact',
                                children: "Contact Us"
                            }, void 0, false, {
                                fileName: "[project]/src/app/common/Header.jsx",
                                lineNumber: 374,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/common/Header.jsx",
                            lineNumber: 373,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/common/Header.jsx",
                    lineNumber: 303,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/common/Header.jsx",
                lineNumber: 302,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/common/Header.jsx",
        lineNumber: 122,
        columnNumber: 5
    }, this);
}
_s(Header, "ZMcpwiygjRLJI42igtjnZlO7T54=");
_c = Header;
var _c;
__turbopack_context__.k.register(_c, "Header");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_app_common_17b69f76._.js.map