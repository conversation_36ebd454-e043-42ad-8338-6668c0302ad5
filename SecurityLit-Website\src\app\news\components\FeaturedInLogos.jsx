import React from "react";

const logos = [
  {
    src: "/images/news/csc_sponsor.png",
    alt: "CSC Sponsor",
  },
  {
    src: "/images/news/ankita-feature-nzbusiness.png",
    alt: "NZ Business Feature",
  },
  {
    src: "/images/news/reseller_news.jpeg",
    alt: "Reseller News",
  },
  {
    src: "/images/news/ankita_feature_wis.jpeg",
    alt: "Security Magazine NZ",
  },
  {
    src: "/images/news/wis_rising_star.png",
    alt: "Women in Security Awards NZSM",
  },
  {
    src: "/images/news/jozsef_digitrend.png",
    alt: "Digitrend Feature",
  },
  {
    src: "/images/news/top_women_in_malaysia.jpg",
    alt: "Women in Security Malaysia",
  },
  {
    src: "/images/news/securitylit_feature_wis.png",
    alt: "Women in Security Magazine",
  },
];

export default function FeaturedInLogos() {
  return (
    <section className="bg-white py-6 shadow-sm border-b border-gray-100">
      <div className="container mx-auto px-4 max-w-7xl flex flex-col items-center">
        <div className="flex items-center gap-3 mb-3">
          <span className="text-lg font-bold text-[var(--color-dark-blue)] tracking-wide uppercase">Featured In</span>
          <span className="w-8 h-1 bg-[var(--color-yellow)] rounded-full" />
        </div>
        <div className="flex flex-wrap justify-center items-center gap-8 md:gap-12 w-full">
          {logos.map((logo, idx) => (
            <img
              key={idx}
              src={logo.src}
              alt={logo.alt}
              className="h-10 md:h-12 max-w-[120px] object-contain grayscale hover:grayscale-0 transition duration-300"
              loading="lazy"
            />
          ))}
        </div>
      </div>
    </section>
  );
}