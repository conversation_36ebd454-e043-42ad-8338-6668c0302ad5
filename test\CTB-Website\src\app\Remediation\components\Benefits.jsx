import React from 'react';
import { Shield, RotateCcw, Activity, FileText } from 'lucide-react';

const BenefitCard = ({ icon: Icon, title, subtitle, description, iconBg, iconColor }) => (
  <div className="flex flex-col p-8 sm:p-10 md:p-12 bg-gradient-to-br from-primary-blue/10 to-primary-blue/5 backdrop-blur-sm rounded-xl shadow-xl border border-primary-blue/10 relative overflow-hidden group">
    <div className="absolute inset-0 bg-gradient-to-br from-white/[0.01] to-white/[0.02] opacity-0 group-hover:opacity-100 transition-opacity duration-1000"></div>
    <div className="absolute -right-20 -top-20 w-40 h-40 bg-primary-blue/5 rounded-full blur-2xl"></div>
    <div className="absolute top-1/2 right-0 w-1 h-16 bg-gradient-to-b from-ctb-green-50/0 via-ctb-green-50/60 to-ctb-green-50/0 transform -translate-y-1/2"></div>
    
    <div className="relative z-10">
      <div className="w-12 h-12 rounded-xl flex items-center justify-center flex-shrink-0 mb-4" style={{background: iconBg}}>
        <Icon size={24} style={{color: iconColor}} />
      </div>
      <div>
        <h3 className="text-xl font-semibold text-white mb-1">{title}</h3>
        <p className="text-ctb-green-50 text-sm font-medium mb-2">{subtitle}</p>
        <p className="text-ctb-bg-light/90 text-sm leading-relaxed">{description}</p>
      </div>
    </div>
  </div>
);

export default function Benefits() {
  const benefits = [
    {
      icon: Shield,
      title: "Centralized Dashboard",
      subtitle: "All vulnerabilities in one place",
      description: "View and manage all reported vulnerabilities in one place. Sort by severity, assign to devs, and keep remediation on track.",
      iconBg: 'linear-gradient(135deg, #58CC02 0%, #027bfb 100%)',
      iconColor: '#010D2C'
    },
    {
      icon: RotateCcw,
      title: "Unlimited Retesting",
      subtitle: "Validate fixes as often as needed",
      description: "Validate fixes as often as needed. Our platform lets researchers confirm patches until the issue is truly resolved.",
      iconBg: 'linear-gradient(135deg, #58CC02 0%, #027bfb 100%)',
      iconColor: '#010D2C'
    },
    {
      icon: Activity,
      title: "Smart Tracking",
      subtitle: "Real-time status updates",
      description: "Track status from \"triaged\" to \"resolved\" in real time. No more follow-ups - everyone stays aligned automatically.",
      iconBg: 'linear-gradient(135deg, #58CC02 0%, #027bfb 100%)',
      iconColor: '#D4E2FF'
    },
    {
      icon: FileText,
      title: "Audit Logging",
      subtitle: "Complete remediation visibility",
      description: "Capture every step of the remediation process for full visibility, compliance, and peace of mind.",
      iconBg: 'linear-gradient(135deg, #58CC02 0%, #027bfb 100%)',
      iconColor: '#D4E2FF'
    }
  ];

  return (
    <section className="w-full py-16 sm:py-20 md:py-24 bg-gradient-to-b from-tertiary-blue via-tertiary-blue to-secondary-blue/90 relative overflow-hidden">
      {/* Decorative lines and blobs */}
      <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-primary-blue/20 via-ctb-green-50/30 to-primary-blue/20"></div>
      <div className="absolute inset-0 bg-[url('/images/grid-pattern.svg')] bg-repeat opacity-5"></div>
      <div className="absolute -top-40 -right-40 w-80 h-80 bg-primary-blue/10 rounded-full blur-3xl"></div>
      <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-primary-blue/10 rounded-full blur-3xl"></div>
 
      <div className="relative z-10 px-4 py-5 max-w-7xl mx-auto">
        <div className="text-center mb-16 md:mb-20">
          <div className="inline-flex items-center bg-primary-blue/10 backdrop-blur-sm border border-primary-blue/10 rounded-full px-4 sm:px-6 md:px-8 py-2 sm:py-3 mb-6 sm:mb-8 md:mb-10 lg:mb-12">
            <div className="w-2 h-2 sm:w-3 sm:h-3 bg-ctb-green-50 rounded-full mr-2 sm:mr-3 md:mr-4"></div>
            <span className="text-ctb-green-50 text-xs sm:text-sm font-semibold tracking-widest uppercase">
              BENEFITS
            </span>
          </div>
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            How <span className="text-ctb-green-50">Capture The Bug</span> Accelerates Vulnerability Remediation
          </h2>
          <p className="text-ctb-bg-light/90 mt-4 text-md max-w-3xl mx-auto">
            We help fast growing SaaS and enterprise security teams go from <span className="text-ctb-green-50 font-semibold">triage to fix</span> faster - without the chaos of email threads, missed SLAs, or tool fragmentation. Discover our <a href="/Pricing" className="text-ctb-green-50 hover:underline font-semibold">flexible pricing plans</a> designed for teams of all sizes.
          </p>
        </div>
 
        {/* Mobile: Single column */}
        <div className="block md:hidden">
          <div className="grid grid-cols-1 gap-6">
            {benefits.map((benefit, index) => (
              <BenefitCard
                key={index}
                icon={benefit.icon}
                title={benefit.title}
                subtitle={benefit.subtitle}
                description={benefit.description}
                iconBg={benefit.iconBg}
                iconColor={benefit.iconColor}
              />
            ))}
          </div>
        </div>
 
        {/* Desktop: Two rows layout */}
        <div className="hidden md:block">
          {/* First row - 2 cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-8 mb-6 md:mb-8">
            {benefits.slice(0, 2).map((benefit, index) => (
              <BenefitCard
                key={index}
                icon={benefit.icon}
                title={benefit.title}
                subtitle={benefit.subtitle}
                description={benefit.description}
                iconBg={benefit.iconBg}
                iconColor={benefit.iconColor}
              />
            ))}
          </div>
          
          {/* Second row - 2 cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-8">
            {benefits.slice(2, 4).map((benefit, index) => (
              <BenefitCard
                key={index + 2}
                icon={benefit.icon}
                title={benefit.title}
                subtitle={benefit.subtitle}
                description={benefit.description}
                iconBg={benefit.iconBg}
                iconColor={benefit.iconColor}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}