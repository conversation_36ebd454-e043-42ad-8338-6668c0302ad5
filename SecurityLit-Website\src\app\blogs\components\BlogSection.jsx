"use client";
import { motion } from 'framer-motion';
import React, { useState, useEffect, useMemo } from "react";
import { ArrowRight, ChevronRight, Clock, Calendar, Tag, Filter, Search, X, Sliders, ChevronDown } from "lucide-react";
import { format, subDays, parse, isAfter, isBefore, isEqual } from 'date-fns';
import Image from 'next/image';
import Link from 'next/link';
import { blogs } from '../Blogs';

const MOBILE_BREAKPOINT = 767;
const VISIBLE_BLOGS_COUNT = 6;

// Date range presets
const DATE_RANGES = [
  { label: 'Last 7 days', days: 7 },
  { label: 'Last 30 days', days: 30 },
  { label: 'Last 60 days', days: 60 },
  { label: 'Last 90 days', days: 90 },
  { label: 'Up to 360 days', days: 360 },
  { label: 'All time', days: null }
];

// Custom slider component for date range
const DateRangeSlider = ({ value, onChange, min, max }) => {
  const percentage = ((value - min) / (max - min)) * 100;
  
  return (
    <div className="relative w-full mt-4 mb-6">
      <div className="flex justify-between mb-2">
        <span className="text-sm font-medium text-gray-700">{min} days</span>
        <span className="text-sm font-medium text-[var(--color-blue)]">{value} days</span>
        <span className="text-sm font-medium text-gray-700">{max} days</span>
      </div>
      <div className="relative h-2">
        <div className="absolute inset-0 bg-gray-200 rounded-full"></div>
        <div 
          className="absolute inset-y-0 left-0 bg-[var(--color-blue)] rounded-full"
          style={{ width: `${percentage}%` }}
        ></div>
        <div 
          className="absolute top-1/2 w-5 h-5 bg-white border-2 border-[var(--color-blue)] rounded-full shadow-md transform -translate-x-1/2 -translate-y-1/2 transition-transform duration-150 hover:scale-110 cursor-pointer"
          style={{ left: `${percentage}%` }}
        ></div>
        <input
          type="range"
          min={min}
          max={max}
          value={value}
          onChange={e => onChange(parseInt(e.target.value))}
          className="absolute inset-0 w-full h-6 opacity-0 cursor-pointer z-10"
          style={{ height: '20px', top: '-10px' }}
        />
      </div>
    </div>
  );
};

// Custom tag pill component
const TagPill = ({ tag, selected, onClick }) => {
  return (
    <motion.button
      whileTap={{ scale: 0.95 }}
      onClick={() => onClick(tag)}
      className={`px-3 py-1.5 rounded-full text-sm font-medium transition-all duration-200 flex items-center gap-1.5 ${
        selected 
          ? 'bg-[var(--color-blue)]/10 text-[var(--color-blue)] border border-[var(--color-blue)]/20' 
          : 'bg-gray-100 text-gray-600 border border-transparent hover:bg-gray-200'
      }`}
    >
      {tag}
      {selected && (
        <X className="w-3 h-3" />
      )}
    </motion.button>
  );
};

const FeaturedBlogCard = ({ blog }) => {
  return (
    <div className="bg-gradient-to-br from-[var(--color-blue)]/5 to-[var(--color-blue)]/10 rounded-3xl overflow-hidden shadow-sm hover:shadow-xl transition-all duration-500 border border-[var(--color-yellow)] group">
      <div className="relative">
        <Image
          src={blog.image}
          alt={blog.title}
          className="w-full h-48 sm:h-56 lg:h-60 object-cover group-hover:scale-105 transition-transform duration-500"
          width={600}
          height={240}
        />
        <div className="absolute top-3 sm:top-4 left-3 sm:left-4">
          <span className="bg-[var(--color-yellow)] text-[var(--color-dark-blue)] px-2 sm:px-3 py-1 rounded-full text-xs font-medium shadow-lg">
            Featured
          </span>
        </div>
        <div className="absolute top-3 sm:top-4 right-3 sm:right-4">
          <span className="bg-white/90 backdrop-blur-sm text-gray-700 px-2 sm:px-3 py-1 rounded-full text-xs font-medium flex items-center gap-1 shadow-sm">
            <Clock className="w-3 h-3" />
            {blog.readTime}
          </span>
        </div>
      </div>
      <div className="p-4 sm:p-6 lg:p-8"> 
        <span className="text-gray-500 text-sm flex items-center gap-1 mb-3 sm:mb-4">
          <Calendar className="w-3 h-3" />
          {blog.date}
        </span> 
        <h3 className="text-lg sm:text-xl font-bold text-[var(--color-dark-blue)] mb-3 sm:mb-4 line-clamp-2 leading-tight group-hover:text-[var(--color-blue)] transition-colors">
          {blog.title}
        </h3>
        <p className="text-gray-600 mb-4 sm:mb-5 line-clamp-3 text-sm sm:text-base leading-relaxed">
          {blog.description}
        </p>
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3 sm:gap-0">
          <div className="flex gap-2 flex-wrap">
            {blog.tags?.slice(0, 2).map((tag) => (
              <span key={tag} className="bg-gray-100 text-gray-600 px-2 py-1 rounded-md text-xs">
                {tag}
              </span>
            ))}
          </div>
          <Link
            href={`/blogs/${blog.slug}`}
            className="group/link text-sm flex items-center text-[var(--color-blue)] hover:text-[var(--color-dark-blue)] font-semibold transition-colors shrink-0"
          >
            Read Article
            <ArrowRight className="ml-2 w-4 sm:w-5 h-4 sm:h-5 group-hover/link:translate-x-1 transition-transform" />
          </Link>
        </div>
      </div>
    </div>
  );
};

const BlogCard = ({ blog }) => {
  return (
    <div className="bg-white rounded-2xl overflow-hidden shadow-sm hover:shadow-lg transition-all duration-300 h-full flex flex-col border border-gray-100 group">
      <div className="relative">
        <Image
          src={blog.image}
          alt={blog.title}
          className="w-full h-48 sm:h-56 lg:h-60 object-cover group-hover:scale-105 transition-transform duration-300"
          width={600}
          height={240}
        />
        <div className="absolute top-3 right-3">
          <span className="bg-white/90 backdrop-blur-sm text-gray-700 px-2 py-1 rounded-full text-xs font-medium flex items-center gap-1 shadow-sm">
            <Clock className="w-3 h-3" />
            {blog.readTime}
          </span>
        </div>
      </div>
      <div className="p-4 sm:p-6 flex-1 flex flex-col">
        <div className="flex items-center gap-2 mb-3">
          <span className="text-gray-500 text-xs flex items-center gap-1">
            <Calendar className="w-3 h-3" />
            {blog.date}
          </span>
        </div>
        <h3 className="text-base sm:text-lg font-semibold text-[var(--color-dark-blue)] mb-3 line-clamp-2 leading-tight group-hover:text-[var(--color-blue)] transition-colors">
          {blog.title}
        </h3>
        <p className="text-gray-600 text-sm sm:text-base flex-1 line-clamp-3 leading-relaxed mb-4">
          {blog.description}
        </p>
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3 sm:gap-0 mt-auto">
          <div className="flex gap-1 flex-wrap">
            {blog.tags?.slice(0, 2).map((tag) => (
              <span key={tag} className="bg-gray-50 text-gray-600 px-2 py-1 rounded text-xs">
                {tag}
              </span>
            ))}
          </div>
          <Link
            href={`/blogs/${blog.slug}`}
            className="group/link flex text-sm items-center text-[var(--color-blue)] hover:text-[var(--color-dark-blue)] font-medium transition-colors shrink-0"
          >
            Read more
            <ArrowRight className="ml-1 w-4 h-4 group-hover/link:translate-x-1 transition-transform" />
          </Link>
        </div>
      </div>
    </div>
  );
};

// Filter panel component
const FilterPanel = ({
  searchTerm,
  setSearchTerm,
  dateRange,
  setDateRange,
  selectedTags,
  setSelectedTags,
  customDateRange,
  setCustomDateRange,
  isFilterOpen,
  setIsFilterOpen,
  isMobile
}) => {

  // Get unique tags from all blogs
  const uniqueTags = useMemo(() => {
    const tagsSet = new Set();
    blogs.forEach(blog => {
      if (blog.tags) {
        blog.tags.forEach(tag => tagsSet.add(tag));
      }
    });
    return Array.from(tagsSet).sort();
  }, []);

  const handleTagToggle = (tag) => {
    setSelectedTags(prev => {
      if (prev.includes(tag)) {
        return prev.filter(t => t !== tag);
      } else {
        return [...prev, tag];
      }
    });
  };

  const clearFilters = () => {
    setSearchTerm('');
    setDateRange('All time');
    setSelectedTags([]);
    setCustomDateRange(360);
  };

  // Count active filters
  const activeFiltersCount =
    (searchTerm ? 1 : 0) +
    (dateRange !== 'All time' ? 1 : 0) +
    selectedTags.length;

  return (
    <div className="mb-8 sm:mb-12">
      {/* Search and filter toggle bar */}
      <div className="flex flex-col sm:flex-row gap-4 items-center mb-6">
        <div className="relative flex-1 w-full">
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="Search articles by title, description or tag..."
            className="w-full px-4 py-3 pl-12 rounded-xl border border-gray-200 focus:border-[var(--color-blue)] focus:ring-2 focus:ring-[var(--color-blue)]/10 transition-all duration-200"
          />
          <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
          {searchTerm && (
            <button
              onClick={() => setSearchTerm('')}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          )}
        </div>

        <button
          onClick={() => setIsFilterOpen(!isFilterOpen)}
          className="flex-shrink-0 flex items-center gap-2 px-5 py-3 bg-white border border-gray-200 hover:border-[var(--color-blue)] rounded-xl text-gray-700 font-medium transition-all duration-200"
        >
          <Filter className={`w-5 h-5 ${isFilterOpen ? 'text-[var(--color-blue)]' : 'text-gray-500'}`} />
          <span>Filters</span>
          {activeFiltersCount > 0 && (
            <span className="flex items-center justify-center w-5 h-5 bg-[var(--color-blue)]/10 text-[var(--color-blue)] text-xs font-semibold rounded-full">
              {activeFiltersCount}
            </span>
          )}
          <ChevronDown className={`w-4 h-4 ml-1 transition-transform duration-300 ${isFilterOpen ? 'rotate-180 text-[var(--color-blue)]' : 'text-gray-500'}`} />
        </button>
      </div>

      {/* Active filters display */}
      {activeFiltersCount > 0 && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex flex-wrap items-center gap-2 mb-4"
        >
          <span className="text-sm text-gray-500">Active filters:</span>

          {searchTerm && (
            <span className="bg-[var(--color-blue)]/10 text-[var(--color-blue)] px-3 py-1 rounded-full text-sm flex items-center gap-1.5">
              <Search className="w-3 h-3" />
              &ldquo;{searchTerm}&rdquo;
              <button onClick={() => setSearchTerm('')} className="hover:text-[var(--color-dark-blue)]">
                <X className="w-3.5 h-3.5" />
              </button>
            </span>
          )}

          {dateRange !== 'All time' && (
            <span className="bg-[var(--color-blue)]/10 text-[var(--color-blue)] px-3 py-1 rounded-full text-sm flex items-center gap-1.5">
              <Calendar className="w-3 h-3" />
              {dateRange}
              <button onClick={() => setDateRange('All time')} className="hover:text-[var(--color-dark-blue)]">
                <X className="w-3.5 h-3.5" />
              </button>
            </span>
          )}

          {selectedTags.map(tag => (
            <span key={tag} className="bg-[var(--color-blue)]/10 text-[var(--color-blue)] px-3 py-1 rounded-full text-sm flex items-center gap-1.5">
              <Tag className="w-3 h-3" />
              {tag}
              <button onClick={() => handleTagToggle(tag)} className="hover:text-[var(--color-dark-blue)]">
                <X className="w-3.5 h-3.5" />
              </button>
            </span>
          ))}

          <button
            onClick={clearFilters}
            className="text-sm text-gray-500 hover:text-gray-700 underline"
          >
            Clear all
          </button>
        </motion.div>
      )}

      {/* Filter content */}
      <motion.div
        initial={false}
        animate={{
          height: isFilterOpen ? 'auto' : 0,
          opacity: isFilterOpen ? 1 : 0,
          marginBottom: isFilterOpen ? '2rem' : 0
        }}
        transition={{ duration: 0.3 }}
        className="overflow-hidden"
      >
        <div className="bg-white rounded-xl border border-gray-200 p-6 shadow-sm">
          <div className="flex flex-col gap-6">
            {/* Date filter */}
            <div>
              <h3 className="text-sm font-semibold text-gray-700 mb-3 flex items-center gap-2">
                <Calendar className="w-4 h-4 text-[var(--color-blue)]" />
                Date Range
              </h3>

              <div className="flex flex-wrap gap-2 mb-4">
                {DATE_RANGES.map((range) => (
                  <button
                    key={range.label}
                    onClick={() => setDateRange(range.label)}
                    className={`px-3 py-1.5 rounded-full text-sm transition-all duration-200 ${
                      dateRange === range.label
                        ? 'bg-[var(--color-blue)]/10 text-[var(--color-blue)] font-medium shadow-sm'
                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                    }`}
                  >
                    {range.label}
                  </button>
                ))}
              </div>

              {dateRange === 'Up to 360 days' && (
                <DateRangeSlider
                  value={customDateRange}
                  onChange={setCustomDateRange}
                  min={7}
                  max={360}
                />
              )}
            </div>

            {/* Tags filter */}
            <div>
              <h3 className="text-sm font-semibold text-gray-700 mb-3 flex items-center gap-2">
                <Tag className="w-4 h-4 text-[var(--color-blue)]" />
                Filter by Tags
                {selectedTags.length > 0 && (
                  <span className="text-xs text-[var(--color-blue)] bg-[var(--color-blue)]/10 px-2 py-0.5 rounded-full">
                    {selectedTags.length} selected
                  </span>
                )}
              </h3>

              <div className="flex flex-wrap gap-2 max-h-36 overflow-y-auto pb-2 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                {uniqueTags.map((tag) => (
                  <motion.button
                    key={tag}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => handleTagToggle(tag)}
                    className={`px-3 py-1.5 rounded-full text-sm font-medium transition-all duration-200 flex items-center gap-1.5 ${
                      selectedTags.includes(tag)
                        ? 'bg-[var(--color-blue)]/10 text-[var(--color-blue)] border border-[var(--color-blue)]/20 shadow-sm'
                        : 'bg-gray-100 text-gray-600 border border-transparent hover:bg-gray-200'
                    }`}
                  >
                    {tag}
                    {selectedTags.includes(tag) && (
                      <X className="w-3 h-3" />
                    )}
                  </motion.button>
                ))}
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default function BlogSection() {
  const [showAllBlogs, setShowAllBlogs] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [dateRange, setDateRange] = useState("All time");
  const [customDateRange, setCustomDateRange] = useState(360);
  const [selectedTags, setSelectedTags] = useState([]);

  // Extract all unique tags from blogs
  const allTags = useMemo(() => {
    const tagsSet = new Set();
    blogs.forEach(blog => {
      if (blog.tags && Array.isArray(blog.tags)) {
        blog.tags.forEach(tag => tagsSet.add(tag));
      }
    });
    return Array.from(tagsSet).sort();
  }, []);

  useEffect(() => {
    const handleResize = () => {
      const mobile = window.innerWidth <= MOBILE_BREAKPOINT;
      setIsMobile(mobile);
    };
    handleResize();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // Parse blog dates and filter by date range
  const filteredBlogs = useMemo(() => {
    return blogs.filter((blog) => {
      // Search term filter
      const matchesSearch = searchTerm === "" ||
        blog.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        blog.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        blog.tags?.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));

      // Date filter
      let matchesDate = true;
      if (dateRange !== 'All time') {
        try {
          const blogDate = new Date(blog.date);
          const today = new Date();

          if (dateRange === 'Up to 360 days') {
            const cutoffDate = subDays(today, customDateRange);
            matchesDate = isAfter(blogDate, cutoffDate) || isEqual(blogDate, cutoffDate);
          } else {
            const days = DATE_RANGES.find(r => r.label === dateRange)?.days || null;
            if (days) {
              const cutoffDate = subDays(today, days);
              matchesDate = isAfter(blogDate, cutoffDate) || isEqual(blogDate, cutoffDate);
            }
          }
        } catch (e) {
          console.error("Error parsing date:", blog.date);
          matchesDate = true;
        }
      }

      // Tags filter
      const matchesTags = selectedTags.length === 0 ||
        selectedTags.every(tag => blog.tags?.includes(tag));

      return matchesSearch && matchesDate && matchesTags;
    });
  }, [searchTerm, dateRange, customDateRange, selectedTags]);

  const featuredBlogs = filteredBlogs.filter(blog => blog.featured);
  const regularBlogs = filteredBlogs.filter(blog => !blog.featured);

  const visibleRegularBlogs = showAllBlogs ? regularBlogs : regularBlogs.slice(0, VISIBLE_BLOGS_COUNT);

  // Check if any filter is active
  const isFilterActive = searchTerm !== "" || dateRange !== "All time" || selectedTags.length > 0;

  return (
    <div className="bg-gray-50 py-16 sm:py-24 lg:py-26 px-4 sm:px-6 lg:px-8 relative">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            <div className="max-w-7xl mx-auto">
              {/* Header Section */}
              <div className="text-center mb-12 sm:mb-16">
              <div className="inline-flex items-center gap-2 bg-[var(--color-blue)]/10 text-[var(--color-blue)] px-3 sm:px-4 py-2 rounded-full text-sm font-medium mb-4 sm:mb-6">
                <Tag className="w-4 h-4" />
                Industry Insights & Expertise
              </div>
              <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-[var(--color-dark-blue)] mb-4 sm:mb-6 px-4">
                Security Insights Hub
              </h1>
              <p className="text-base sm:text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed px-4">
                Curated content for the security professional: We cover the latest on frameworks, threats, and cybersecurity trends to keep your organization ahead of emerging challenges.
              </p>
              </div>

              {/* Filter Panel */}
              <FilterPanel
                searchTerm={searchTerm}
                setSearchTerm={setSearchTerm}
                dateRange={dateRange}
                setDateRange={setDateRange}
                selectedTags={selectedTags}
                setSelectedTags={setSelectedTags}
                customDateRange={customDateRange}
                setCustomDateRange={setCustomDateRange}
                isFilterOpen={isFilterOpen}
                setIsFilterOpen={setIsFilterOpen}
                isMobile={isMobile}
              />

              {/* Featured Blogs Section */}
              {featuredBlogs.length > 0 && (
                <motion.div
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                  className="mb-16 sm:mb-20"
                >
                  <h2 className="text-2xl sm:text-3xl font-bold text-[var(--color-dark-blue)] mb-8 sm:mb-12 text-center">
                    Featured Articles
                  </h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8">
                    {featuredBlogs.map((blog, index) => (
                      <motion.div
                        key={blog.id}
                        initial={{ y: 20, opacity: 0 }}
                        animate={{ y: 0, opacity: 1 }}
                        transition={{ duration: 0.6, delay: 0.1 * index }}
                      >
                        <FeaturedBlogCard blog={blog} />
                      </motion.div>
                    ))}
                  </div>
                </motion.div>
              )}

              {/* Regular Blogs Section */}
              {regularBlogs.length > 0 && (
                <motion.div
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ duration: 0.6, delay: 0.3 }}
                >
                  <h2 className="text-2xl sm:text-3xl font-bold text-[var(--color-dark-blue)] mb-8 sm:mb-12 text-center">
                    Latest Articles
                  </h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 mb-12">
                    {visibleRegularBlogs.map((blog, index) => (
                      <motion.div
                        key={blog.id}
                        initial={{ y: 20, opacity: 0 }}
                        animate={{ y: 0, opacity: 1 }}
                        transition={{ duration: 0.6, delay: 0.1 * index }}
                      >
                        <BlogCard blog={blog} />
                      </motion.div>
                    ))}
                  </div>

                  {/* Load More Button */}
                  {!showAllBlogs && regularBlogs.length > VISIBLE_BLOGS_COUNT && (
                    <div className="text-center">
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => setShowAllBlogs(true)}
                        className="px-8 py-4 bg-[var(--color-blue)] text-white rounded-xl hover:bg-[var(--color-dark-blue)] transition-all duration-300 font-semibold shadow-lg hover:shadow-xl"
                      >
                        Load More Articles
                      </motion.button>
                    </div>
                  )}
                </motion.div>
              )}

              {/* No Results */}
              {filteredBlogs.length === 0 && (
                <motion.div
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ duration: 0.6 }}
                  className="text-center py-16"
                >
                  <div className="text-gray-400 mb-6">
                    <Search className="w-16 h-16 mx-auto" />
                  </div>
                  <h3 className="text-2xl font-semibold text-gray-600 mb-4">No articles found</h3>
                  <p className="text-gray-500 mb-8 max-w-md mx-auto">
                    Try adjusting your search criteria or filters to find what you're looking for.
                  </p>
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => {
                      setSearchTerm('');
                      setDateRange('All time');
                      setSelectedTags([]);
                      setCustomDateRange(360);
                    }}
                    className="px-6 py-3 bg-[var(--color-blue)] text-white rounded-lg hover:bg-[var(--color-dark-blue)] transition-colors font-semibold"
                  >
                    Clear All Filters
                  </motion.button>
                </motion.div>
              )}
            </div>
          </motion.div>
        </motion.div>
    </div>
  );
}
