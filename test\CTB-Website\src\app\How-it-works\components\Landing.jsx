import Button from "@/app/common/buttons/Button";
import Image from "next/image";
import Link from "next/link";
import React from "react";
import { ArrowRight } from "lucide-react";

const logos = [
  { src: '/images/cronberry_logo.png', alt: "cronberry Logo" },
  { src: '/images/lawvu.jpg', alt: "lawvu Logo" },
  { src: '/images/Bonnet_logo.jpeg', alt: "Bonnet Logo" },
  { src: '/images/forsite_logo.png', alt: "forsite Logo" },
  { src: '/images/cronberry_logo.png', alt: "cronberry Logo" },
];

export default function LandingSection() {
  return (
    <div className="bg-[#F8F5F7]">
      <div className="container mx-auto px-4 md:px-12 flex flex-col items-center text-center">

        <div className="flex flex-col gap-3 md:gap-4 max-w-3xl py-16 sm:py-24 lg:py-26">
          <h1 className="text-xl sm:text-2xl md:text-4xl font-bold text-ctb-blue-150 leading-tight md:leading-relaxed px-2 sm:px-0">

           A Clear Path from <span className="text-primary-blue">Setup to Secure</span>  </h1>

          <p className="text-[#7B6B9C] text-sm sm:text-base md:text-lg leading-relaxed px-2 sm:px-0">
           We&apos;ve streamlined the entire pentesting lifecycle into a simple, transparent process. See exactly how our platform takes you from initial setup to a hardened security posture, with full visibility at every step. 
          </p>

          <div className="flex justify-center gap-4 mt-2 md:mt-0">
            <Button
              href="/Request-Demo"
              variant="success"
              size="lg"
              rightIcon={<ArrowRight className="h-4 w-4 sm:h-5 sm:w-5" />}
              className="text-base sm:text-lg px-6 sm:px-8"
            >
              Request a demo
            </Button>
          </div>
        </div>
      </div>

      <div className="relative w-full overflow-hidden min-h-[240px] sm:min-h-[300px]">

  <div className="relative z-10 flex justify-center pt-8 sm:pt-12 md:pt-0 pb-8 sm:pb-12 md:pb-10">
           <div className="container mx-auto px-4 md:px-12">
             <Image
               src="/images/penetration-testing-dashboard.svg"
               alt="Penetration testing dashboard interface showing comprehensive security assessment and vulnerability management capabilities"
               width={1120}
               height={980}
               className="w-full max-w-[280px] sm:max-w-[400px] md:max-w-[600px] rounded-3xl lg:max-w-[800px] xl:max-w-[1200px] mx-auto"
               priority
               quality={90}
             />
           </div>
         </div>
      </div>
 
    </div>
  );
}