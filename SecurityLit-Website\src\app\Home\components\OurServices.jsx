"use client";
import React, { useState } from "react";
import Image from "next/image";
import Link from "next/link";


export default function SecurityChallengesSection() {
  const [currentSlide, setCurrentSlide] = useState(0);

const allChallenges = [
    {
    title: "Prepare for compliance success",
    description: "Streamline your audit readiness with tailored assessments. Identify gaps and align with ISO, SOC 2, and other standards confidently and efficiently.",
    buttonText: "Cloud Security",
    linkText: "Discover our Cloud Security Services",
    linkHref: "/compliance-services",
    image: "/images/homepagecards/1.svg",
    alt: "Cloud security infrastructure and monitoring"
  },
   {
    title: "Advanced threat simulation and security validation",
    description: "Conduct red team simulations that mimic sophisticated attackers. Evaluate your detection, response, and resilience under real adversarial conditions.",
    buttonText: "Security Architecture",
    linkText: "Discover our Red Team Services",
    linkHref: "/red-team-simulation",
    image: "/images/homepagecards/4.svg",
    alt: "Team of professionals collaborating in modern office environment"
  },
   {
    title: "Secure your blockchain and DeFi applications",
    description: "Perform in-depth audits of smart contracts, decentralized apps, and blockchain networks to prevent exploits and protect digital assets.",
    buttonText: "SOC Services",
    linkText: "Discover our SOC Services",
    linkHref: "/blockchain-security",
    image: "/images/homepagecards/2.svg",
    alt: "Security operations center with multiple monitors"
  },
  {
    title: "Find weaknesses before attackers do",
    description: "Simulate real-world attacks to identify hidden vulnerabilities in your systems. Strengthen your defenses before cybercriminals can take advantage of them.",
    buttonText: "Penetration Testing",
    image: "/images/homepagecards/6.svg",
    linkText: "Discover our pentesting services",
    linkHref: "/penetration-testing",
    alt: "Professional man working on computer in modern office"
  },
  {
    title: "Strategic cybersecurity leadership on demand",
    description: "Access experienced cybersecurity leaders who provide tailored strategy, compliance guidance, and executive-level insight without full-time cost.",
    buttonText: "Cyber Threat Intelligence",
    linkText: "Meet Our Security Leaders",
    linkHref: "/cybersecurity-leadership",
    image: "/images/homepagecards/3.svg",
    alt: "Cybersecurity analyst working on threat detection systems"
  }, 
  {
    title: "Rapid response when security incidents strike",
    description: "Get immediate support during a breach with expert-led investigation, containment, and recovery. We help you resume operations quickly and securely.",
    buttonText: "Compliance Services",
    linkText: "Discover our Compliance Services",
    linkHref: "/incident-response",
    image: "/images/homepagecards/5.svg",
    alt: "Compliance team reviewing security frameworks"
  },

];


  const slides = [
    allChallenges.slice(0, 3), // First slide - first 3 items
    allChallenges.slice(3, 6)  // Second slide - next 3 items
  ];

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % slides.length);
  };

  const goToSlide = (index) => {
    setCurrentSlide(index);
  };

  return (
    <section className="py-12 sm:py-16 lg:py-24 bg-gray-50 relative">
      {/* Background Image */}
      <div className="absolute inset-0 z-0">
        <Image
          src="/images/<EMAIL>"
          alt="Background pattern"
          fill
          className="object-cover opacity-60"
          priority
        />
      </div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Header Badge */}
        <div className="text-center mb-8 sm:mb-12">
          <div className="inline-flex items-center px-3 sm:px-4 py-2 rounded-full border border-gray-300 bg-white mb-6 sm:mb-8">
            <span className="text-gray-600 text-xs sm:text-sm">Backed by experience and results</span>
          </div>
          
          {/* Main Heading */}
          <h2 className="text-3xl font-bold text-slate-800 mb-4 sm:mb-6 leading-tight px-4 sm:px-0">
            Solving the security challenges that matter most
          </h2>
          
          {/* Subheading */}
          <p className="text-base sm:text-lg  text-gray-600 max-w-5xl mx-auto leading-relaxed px-4 sm:px-0">
            We're more than <span className="text-slate-800 font-medium">just another cyber security provider</span> - we're your trusted partner in identifying what matters, cutting through complexity, and delivering the protection your business needs to stay secure.
          </p>
        </div>

        {/* Mobile: Single card scroll, Desktop: Carousel */}
        <div className="relative">
          {/* Mobile View - Single card at a time */}
          <div className="block lg:hidden">
            <div className="px-4 py-6">
              <div className="flex overflow-x-auto snap-x snap-mandatory scrollbar-hide space-x-4 pb-4">
                {allChallenges.map((challenge, index) => (
                  <div 
                    key={index} 
                    className="flex-none w-80 sm:w-96 bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 relative z-10 snap-center"
                  >
                    {/* Image */}
                    <div className="relative overflow-hidden h-40 sm:h-48 rounded-t-2xl">
                      <Image
                        src={challenge.image}
                        alt={challenge.alt}
                        fill
                        className="object-cover hover:scale-105 transition-transform duration-300"
                        placeholder="blur"
                        blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q=="
                      />
                    </div>
                    
                    {/* Content */}
                    <div className="p-4 sm:p-6">
                      <h3 className="text-lg sm:text-xl font-bold text-slate-800 mb-3 leading-tight">
                        {challenge.title}
                      </h3>
                      <p className="text-gray-600 mb-4 sm:mb-6 leading-relaxed text-sm sm:text-base">
                        {challenge.description}
                      </p>
                      
                      {/* Button */}
                       <Link
              href={challenge.linkHref}
              className="inline-flex items-center text-sm text-blue-600 hover:text-blue-800 hover:underline font-medium transition-colors duration-200"
            >
              <span className="text-lg font-bold italic">{challenge.linkText}</span>
              <svg 
                className="ml-1 w-4 h-4 font-bold" 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path 
                  strokeLinecap="round" 
                  strokeLinejoin="round" 
                  strokeWidth={2} 
                  d="M9 5l7 7-7 7" 
                />
                <path 
                  strokeLinecap="round" 
                  strokeLinejoin="round" 
                  strokeWidth={2} 
                  d="M13 5l7 7-7 7" 
                />
              </svg>
            </Link>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Desktop View - Carousel with 3 cards */}
          <div className="hidden lg:block">
            <div className="px-4 py-8">
              <div 
                className="flex transition-transform duration-500 ease-in-out"
                style={{ transform: `translateX(-${currentSlide * 100}%)` }}
              >
                {slides.map((slideCards, slideIndex) => (
                  <div key={slideIndex} className="w-full flex-shrink-0 px-4">
                    <div className="grid grid-cols-1 xl:grid-cols-3 gap-6 xl:gap-8">
                      {slideCards.map((challenge, cardIndex) => (
                        <div 
                          key={cardIndex} 
                          className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 relative z-10"
                        >
                          {/* Image */}
                          <div className="relative overflow-hidden h-48 xl:h-48 rounded-t-2xl">
                            <Image
                              src={challenge.image}
                              alt={challenge.alt}
                              fill
                              className="object-cover hover:scale-105 transition-transform duration-300"
                              placeholder="blur"
                              blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q=="
                            />
                          </div>
                          
                          {/* Content */}
                          <div className="p-6">
                            <h3 className="text-lg xl:text-xl font-bold text-slate-800 mb-3 leading-tight">
                              {challenge.title}
                            </h3>
                            <p className="text-gray-600 mb-6 leading-relaxed text-sm xl:text-base">
                              {challenge.description}
                            </p>
                            
                              <Link
              href={challenge.linkHref}
              className="inline-flex items-center text-sm text-blue-600 hover:text-blue-800 hover:underline font-medium transition-colors duration-200"
            >
              <span className="text-lg font-bold italic">{challenge.linkText}</span>
              <svg 
                className="ml-1 w-4 h-4 font-bold" 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path 
                  strokeLinecap="round" 
                  strokeLinejoin="round" 
                  strokeWidth={2} 
                  d="M9 5l7 7-7 7" 
                />
                <path 
                  strokeLinecap="round" 
                  strokeLinejoin="round" 
                  strokeWidth={2} 
                  d="M13 5l7 7-7 7" 
                />
              </svg>
            </Link>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Navigation Dots - Only show on desktop */}
        <div className="hidden lg:flex justify-center items-center mt-6 lg:mt-8 space-x-3">
          {slides.map((_, index) => (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              className={`transition-all duration-300 rounded-full ${
                index === currentSlide 
                  ? 'w-6 lg:w-8 h-2 lg:h-3 bg-gray-600' 
                  : 'w-2 lg:w-3 h-2 lg:h-3 bg-gray-400 hover:bg-gray-500'
              }`}
            />
          ))}
        </div>

        {/* Scroll indicator for mobile */}
        <div className="block lg:hidden text-center mt-4">
          <p className="text-xs text-gray-500">Swipe to see more services</p>
        </div>
      </div>
    </section>
  );
}