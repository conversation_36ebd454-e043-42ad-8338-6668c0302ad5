"use client";
import React, { useState, useEffect } from "react";
import Image from "next/image";
import Button from "../../common/buttons/Button";
import { motion, useReducedMotion } from "framer-motion";
import AnnouncementBanner from "./AnnouncementBanner";

function Landing() {
  // State to track if component has mounted
  const [isMounted, setIsMounted] = useState(false);
  
  // Hook to detect if user prefers reduced motion
  const prefersReducedMotion = useReducedMotion();
  
  // Animation controls
  const shouldAnimate = isMounted && !prefersReducedMotion;
  
  // Ensure component is mounted before animations
  useEffect(() => {
    setIsMounted(true);
    
    // Safety timeout to force content visibility if animations get stuck
    const safetyTimeout = setTimeout(() => {
      const heroContent = document.getElementById('hero-content');
      if (heroContent) {
        heroContent.style.opacity = '1';
      }
    }, 1500); // Force visibility after 1.5s
    
    return () => clearTimeout(safetyTimeout);
  }, []);
  
  // Animation variants
  const containerAnimation = shouldAnimate 
    ? { 
        initial: { opacity: 0 },
        animate: { opacity: 1 },
        transition: { duration: 0.3 }
      } 
    : { initial: { opacity: 1 } };
    
  const textAnimation = shouldAnimate
    ? {
        initial: { opacity: 0, y: -15 },
        animate: { opacity: 1, y: 0 },
        transition: { duration: 0.4 }
      }
    : { initial: { opacity: 1 } };
    
  const paragraphAnimation = shouldAnimate
    ? {
        initial: { opacity: 0, y: 10 },
        animate: { opacity: 1, y: 0 },
        transition: { duration: 0.3 }
      }
    : { initial: { opacity: 1 } };
    
  const imageAnimation = shouldAnimate
    ? {
        initial: { opacity: 0, x: 20 },
        animate: { opacity: 1, x: 0 },
        transition: { duration: 0.3 }
      }
    : { initial: { opacity: 1 } };

   const arrowIcon = (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
      <path fillRule="evenodd" d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
    </svg>
  );

  return (
    <div className="min-h-screen bg-ctb-bg-light flex items-center">
      <motion.div 
        id="hero-content"
        {...containerAnimation}
        className="w-full"
      >
        <div className="w-full py-16 sm:py-24 lg:py-26 grid grid-cols-1 lg:grid-cols-2 gap-y-8 gap-x-4 items-center">
          {/* Left Hero Section */}
          <div className="px-4 sm:px-6 md:px-10 lg:pl-14 xl:pl-24 space-y-6 sm:space-y-8 z-10 max-w-full lg:max-w-[800px] w-full mx-auto">
            <div className="space-y-4 sm:space-y-6 md:space-y-8">
              <h1 className="font-bold leading-tight text-gray-900 text-left tracking-tight">
                <motion.span 
                  {...textAnimation}
                  className="block mb-1 sm:mb-2 text-3xl xs:text-4xl sm:text-4xl md:text-5xl lg:text-5xl"
                >
                  Scalable,
                </motion.span>
                <motion.span 
                  {...textAnimation}
                  transition={shouldAnimate ? { duration: 0.4, delay: 0.1 } : {}}
                  className="block mb-1 sm:mb-2 text-3xl xs:text-4xl sm:text-4xl md:text-5xl lg:text-5xl text-ctb-light-blue"
                >
                  Continuous Pentesting,
                </motion.span>
                <motion.span 
                  {...textAnimation}
                  transition={shouldAnimate ? { duration: 0.4, delay: 0.2 } : {}}
                  className="block text-3xl xs:text-4xl sm:text-4xl md:text-5xl lg:text-5xl"
                >
                  for Modern Security Teams
                </motion.span>
              </h1>

              <motion.p 
                {...paragraphAnimation}
                transition={shouldAnimate ? { duration: 0.3, delay: 0.3 } : {}}
                className="text-base sm:text-lg md:text-xl text-gray-600 leading-relaxed tracking-normal max-w-full md:max-w-[600px] text-left mt-2 sm:mt-4"
              >
                Capture The Bug&apos;s Penetration Testing as a Service (PTaaS) platform 
                <span className="hidden sm:inline"> eliminates the gaps between annual VAPT </span>
                <span className="sm:hidden"> eliminates gaps between VAPT </span> 
                cycles by delivering continuous security testing.
              </motion.p>
              
              <motion.p 
                {...paragraphAnimation}
                transition={shouldAnimate ? { duration: 0.3, delay: 0.4 } : {}}
                className="text-base sm:text-lg text-gray-600 leading-relaxed mt-0 sm:mt-1"
              >
                Whether you&apos;re pushing code weekly or prepping for your next audit, we help you stay secure and compliant-without slowing down. 
              </motion.p>

              <motion.div 
                {...paragraphAnimation}
                transition={shouldAnimate ? { duration: 0.3, delay: 0.5 } : {}}
                className="flex flex-col sm:flex-row gap-4 sm:gap-6 justify-center sm:justify-start pt-2 sm:pt-4"
              >
                <Button 
                  href="/Product/Penetration-Testing"
                  variant="primary"
                  size="md"
                >
                  EXPLORE PENTESTING SERVICES
                </Button>
                
                <Button 
                  href="/Request-Demo"
                  variant="secondary"
                  size="md"
                  rightIcon={arrowIcon}
                >
                  SCHEDULE A DEMO
                </Button>
              </motion.div>
            </div>
          </div>

          {/* Right Dashboard Image Section - Responsive */}
          <motion.div 
            {...imageAnimation}
            className="flex justify-center xl:justify-end items-center w-full h-full px-4 lg:px-0 xl:pr-0"
          >
            <div className="relative w-full max-w-[640px] h-[220px] sm:h-[300px] md:h-[380px] lg:h-[440px] xl:h-[520px] rounded-xl overflow-hidden bg-white border-2 border-gray-300">
              <Image
                src="/images/DashboardHome.svg"
                alt="Security Dashboard"
                fill
                priority
                className="object-cover object-top rounded-xl shadow-2xl"
                sizes="(max-width: 640px) 100vw, (max-width: 768px) 80vw, (max-width: 1024px) 50vw, 640px"
                quality={80}
              />
            </div>
          </motion.div>
        </div>
      </motion.div>
    </div>
  );
}

export default Landing;
