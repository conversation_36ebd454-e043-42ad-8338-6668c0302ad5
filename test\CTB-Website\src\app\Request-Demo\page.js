import RequestDemo from './RequestDemo';
 
export const metadata = {
  title: "Capture The Bug | Request a PTaaS Demo",
  description: "See how Capture The Bug delivers expert-led penetration testing with faster onboarding, deeper insights, and audit-ready reporting. Book your demo today.",
  keywords: "PTaaS demo, penetration testing request, cybersecurity trial, offensive security demo, application vulnerability assessment, SaaS security evaluation, secure code review, security audit walkthrough, cloud app pentest preview, Capture The Bug demo",
  robots: "index, follow",
  icons: {
    icon: '/favicon.ico',
  },
  openGraph: {
    title: "Capture The Bug | Request a Demo",
    type: "website",
    url: "https://capturethebug.xyz/Request-Demo",
    description: "Book your penetration test in minutes and go live in under 2 weeks. Experience PTaaS built for modern SaaS and security teams.",
    images: "https://postimg.cc/7J8w6772",
  },
  twitter: {
    card: 'summary_large_image',
    title: "Capture The Bug | Request a PTaaS Demo",
    description: "Get hands-on with Capture The Bug’s PTaaS platform. Fast, scalable, and tailored for security-conscious teams.",
    images: "https://postimg.cc/7J8w6772",
  }
};


export default function Page() {
  return <RequestDemo />;
}