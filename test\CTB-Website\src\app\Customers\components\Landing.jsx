'use client'; 

import Image from 'next/image';
import { motion } from 'framer-motion';
import { useState, useEffect } from 'react';

export default function CompanyShowcase() {
  const [isClient, setIsClient] = useState(false);
  const [gridData, setGridData] = useState({});

  useEffect(() => {
    setIsClient(true);
    
    // Generate grid data once on client side
    const generateGridData = () => {
      return Array.from({ length: 64 }, (_, i) => {
        const isColored = Math.random() > 0.7;
        const blinkSpeed = Math.random();
        let blinkClass = '';
        
        if (isColored) {
          if (blinkSpeed < 0.33) blinkClass = 'blink-fast';
          else if (blinkSpeed < 0.66) blinkClass = 'blink-medium';
          else blinkClass = 'blink-slow';
        }
        
        return {
          id: i,
          isColored,
          blinkClass,
          animationDelay: Math.random() * 6
        };
      });
    };

    setGridData({
      topRight: generateGridData(),
      bottomLeft: generateGridData().map(item => ({
        ...item,
        animationDelay: Math.random() * 8
      }))
    });
  }, []);

  return (
    <>
      <style jsx>{`
        @keyframes colorBlink {
          0%, 70% { 
            opacity: 0.3;
          }
          85% { 
            opacity: 0.8;
          }
          100% { 
            opacity: 0.3;
          }
        }
        
        @keyframes colorFade {
          0%, 60% { 
            opacity: 0.2;
          }
          80% { 
            opacity: 0.7;
          }
          100% { 
            opacity: 0.2;
          }
        }
        
        .blink-slow {
          animation: colorBlink 4s ease-in-out infinite;
        }
        
        .blink-medium {
          animation: colorBlink 3s ease-in-out infinite;
        }
        
        .blink-fast {
          animation: colorFade 2.5s ease-in-out infinite;
        }
      `}</style>

      <motion.div 
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
        className="min-h-screen w-full relative overflow-hidden bg-gray-50 flex flex-col items-center justify-center py-12 sm:py-16 md:py-24"
      >
        {/* Animated Grid Backgrounds - Hidden on mobile and tablet */}
        {isClient && gridData.topRight && (
          <>
            {/* Top right grid pattern - Hidden below lg */}
            <motion.div
              initial={{ x: -100, opacity: 0 }}
              animate={{ x: 0, opacity: 0.5 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="hidden lg:block absolute top-0 right-0 w-64 h-64 opacity-60 z-10"
            >
              <div className="grid grid-cols-8 gap-2 w-full h-full">
                {gridData.topRight.map((item) => (
                  <div 
                    key={item.id} 
                    className={`w-full h-full rounded-full ${
                      item.isColored ? `bg-blue-500 ${item.blinkClass}` : 'bg-gray-200'
                    }`}
                    style={{
                      animationDelay: `${item.animationDelay}s`
                    }}
                  ></div>
                ))}
              </div>
            </motion.div>

            {/* Bottom left grid pattern - Hidden below lg */}
            <motion.div
              initial={{ x: 100, opacity: 0 }}
              animate={{ x: 0, opacity: 0.5 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="hidden lg:block absolute bottom-0 left-0 w-64 h-64 opacity-60 z-10"
            >
              <div className="grid grid-cols-8 gap-2 w-full h-full">
                {gridData.bottomLeft.map((item) => (
                  <div 
                    key={item.id} 
                    className={`w-full h-full rounded-full ${
                      item.isColored ? `bg-green-500 ${item.blinkClass}` : 'bg-gray-200'
                    }`}
                    style={{
                      animationDelay: `${item.animationDelay}s`
                    }}
                  ></div>
                ))}
              </div>
            </motion.div>
          </>
        )}

        <div className="relative z-20 text-center leading-relaxed max-w-6xl mx-auto px-4 w-full">
          {/* Top row of company logos */}
          <motion.div 
            initial={{ y: -20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="flex justify-center items-center gap-2 sm:gap-3 md:gap-6 lg:gap-8 mb-4 sm:mb-6 md:mb-8 flex-wrap"
          >
            <div className="bg-white p-1.5 sm:p-2 md:p-3 lg:p-4 rounded-lg shadow-sm">
              <Image 
                src="/images/kademi_logo.png" 
                alt="kademi" 
                width={80} 
                height={32} 
                className="h-4 sm:h-5 md:h-6 lg:h-8 w-auto object-contain"
                style={{
                  width: 'clamp(50px, 15vw, 100px)',
                  height: 'auto'
                }}
              />
            </div>
            <div className="bg-white p-1.5 sm:p-2 md:p-3 lg:p-4 rounded-lg shadow-sm">
              <Image 
                src="/images/rafay_logo.png" 
                alt="rafay" 
                width={96} 
                height={32} 
                className="h-4 sm:h-5 md:h-6 lg:h-8 w-auto object-contain"
                style={{
                  width: 'clamp(60px, 18vw, 100px)',
                  height: 'auto'
                }}
              />
            </div>
            <div className="bg-white p-1.5 sm:p-2 md:p-3 lg:p-4 rounded-lg shadow-sm">
              <Image 
                src="/images/partly_logo.png" 
                alt="partly" 
                width={96} 
                height={32} 
                className="h-4 sm:h-5 md:h-6 lg:h-8 w-auto object-contain"
                style={{
                  width: 'clamp(60px, 18vw, 100px)',
                  height: 'auto'
                }}
              />
            </div>
          </motion.div>

          {/* Left side logo - hidden on mobile and tablet */}
          <motion.div 
            initial={{ x: -50, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="hidden lg:block absolute left-20 top-1/2 transform -translate-y-1/2 bg-white p-2 rounded-lg shadow-sm"
          >
            <Image 
              src="/images/yabble_logo.png" 
              alt="yabble" 
              width={64} 
              height={48} 
              className="h-10 lg:h-12 w-auto object-contain"
              style={{
                width: 'clamp(48px, 16vw, 100px)',
                height: 'auto'
              }}
            />
          </motion.div>

          {/* Main heading */}
          <motion.h1 
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.5 }}
            className="text-lg sm:text-xl md:text-2xl lg:text-4xl font-bold mb-4 sm:mb-6 md:mb-8 text-center px-2 sm:px-4 leading-relaxed"
          >
            <span className="text-gray-800 block sm:inline">Security teams choose </span> 
            <span className="text-blue-900">Capture The Bug</span>
            <br className="hidden sm:block" />
            <span className="text-gray-800 block sm:inline">
              to test smarter, fix faster, and stay compliant 
            </span>
          </motion.h1>

          {/* CTA Button */}
          <motion.div 
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.4, delay: 0.6 }}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="mb-6 sm:mb-8 md:mb-12"
          >
        <a href="/Request-Demo">
  <button className="bg-blue-900 hover:bg-blue-800 text-white px-4 py-1.5 sm:px-5 sm:py-2 md:px-6 md:py-2 rounded-lg font-semibold transition-colors duration-200 inline-flex items-center gap-2 text-sm sm:text-base">
    Request a demo
    <motion.svg 
      className="w-3 h-3 sm:w-4 sm:h-4"
      fill="none" 
      stroke="currentColor" 
      viewBox="0 0 24 24"
      animate={{ x: [0, 4, 0] }}
      transition={{ duration: 1.5, repeat: Infinity }}
    >
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
    </motion.svg>
  </button>
</a>
          </motion.div>

          {/* Bottom row of company logos */}
          <motion.div 
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.7 }}
            className="flex justify-center items-center gap-2 sm:gap-3 md:gap-6 lg:gap-8 flex-wrap"
          >
            <div className="bg-white p-1.5 sm:p-2 md:p-3 lg:p-4 rounded-lg shadow-sm">
              <Image 
                src="/images/parkable_logo.png" 
                alt="parkable" 
                width={96} 
                height={32} 
                className="h-4 sm:h-5 md:h-6 lg:h-8 w-auto object-contain"
                style={{
                  width: 'clamp(60px, 18vw, 100px)',
                  height: 'auto'
                }}
              />
            </div>
            <div className="bg-white p-1.5 sm:p-2 md:p-3 lg:p-4 rounded-lg shadow-sm">
              <Image 
                src="/images/orbit_logo.png" 
                alt="orbit" 
                width={80} 
                height={32} 
                className="h-4 sm:h-5 md:h-6 lg:h-8 w-auto object-contain"
                style={{
                  width: 'clamp(50px, 15vw, 100px)',
                  height: 'auto'
                }}
              />
            </div>
            <div className="bg-white p-1.5 sm:p-2 md:p-3 lg:p-4 rounded-lg shadow-sm">
              <Image 
                src="/images/forsite_logo.png" 
                alt="forsite" 
                width={96} 
                height={32} 
                className="h-4 sm:h-5 md:h-6 lg:h-8 w-auto object-contain"
                style={{
                  width: 'clamp(60px, 18vw, 100px)',
                  height: 'auto'
                }}
              />
            </div>
          </motion.div>

          {/* Right side logos - hidden on mobile and tablet */}
          <motion.div 
            initial={{ x: 50, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.8 }}
            className="hidden lg:block absolute right-8 top-20 transform -translate-y-1/2 space-y-4"
          >
            <div className="bg-white p-2 lg:p-3 rounded-lg shadow-sm">
              <Image 
                src="/images/cronberry_logo.png" 
                alt="cronberry" 
                width={64} 
                height={32} 
                className="h-6 sm:h-7 lg:h-8 w-auto object-contain"
                style={{
                  width: 'clamp(48px, 16vw, 100px)',
                  height: 'auto'
                }}
              />
            </div>
          </motion.div>

          <motion.div 
            initial={{ x: 50, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.9 }}
            className="hidden lg:block absolute right-4 top-2/3 transform -translate-y-2/3 space-y-4"
          >
            <div className="bg-white p-2 lg:p-3 rounded-lg shadow-sm">
              <Image 
                src="/images/lawvu.jpg" 
                alt="lawvu" 
                width={80} 
                height={32} 
                className="h-6 sm:h-7 lg:h-8 w-auto object-contain"
                style={{
                  width: 'clamp(60px, 20vw, 100px)',
                  height: 'auto'
                }}
              />
            </div>
            <div className="bg-white p-2 lg:p-3 rounded-lg shadow-sm">
              <Image 
                src="/images/Bonnet_logo.jpeg" 
                alt="Bonnet" 
                width={96} 
                height={32} 
                className="h-6 sm:h-7 lg:h-8 w-auto object-contain"
                style={{
                  width: 'clamp(72px, 24vw, 110px)',
                  height: 'auto'
                }}
              />
            </div>
          </motion.div>
        </div>
      </motion.div>
    </>
  );
}