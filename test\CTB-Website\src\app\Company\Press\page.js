import Press from './press';

export const metadata = {
  title: "Capture The Bug | Press & Media",
  description: "Explore the latest press coverage, announcements, and media assets from Capture The Bug - a trusted PTaaS leader redefining offensive security.",
  keywords: "Capture The Bug press, cybersecurity news, PTaaS media coverage, penetration testing in the news, security startup coverage, press kit Capture The Bug, offensive security company news",
  robots: "index, follow",
  icons: {
    icon: '/favicon.ico',
  },
  openGraph: {
    title: "Capture The Bug | Press & Media",
    type: "website",
    url: "https://capturethebug.xyz/Company/Press",
    description: "See how Capture The Bug is making headlines. Read the latest news, announcements, and press mentions of our PTaaS platform.",
    images: "https://i.ibb.co/RkF4YBz2/Screenshot-2025-06-19-122039.png",
  },
  twitter: {
    card: 'summary_large_image',
    title: "Capture The Bug | Press & Media",
    description: "Discover how Capture The Bug is featured in the news. From partnerships to product innovation, get our latest press coverage.",
    images: "https://i.ibb.co/RkF4YBz2/Screenshot-2025-06-19-122039.png",
  }
};

export default function Page() {
  return <Press />;
}