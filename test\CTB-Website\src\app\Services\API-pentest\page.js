import API from "./API";

export const metadata = {
  title: "API Penetration Testing Services | REST & GraphQL Security Testing | Capture The Bug",
  description:
    "Expert API penetration testing services for REST, GraphQL, and SOAP APIs. OWASP API Top 10 security testing, authentication bypass testing, and business logic vulnerability assessment. Real-time API security reporting and compliance-ready documentation. Trusted by 500+ companies.",
  keywords:
    "API penetration testing, REST API security testing, GraphQL security testing, SOAP API testing, OWASP API top 10, API security assessment, authentication bypass testing, authorization flaw testing, JWT token security testing, OAuth security testing, API rate limiting testing, input validation testing, API business logic testing, API security audit, API vulnerability scanner, API security compliance, microservices security testing, API gateway security, API endpoint testing, insecure API testing, broken authentication API, excessive data exposure API, lack of resources rate limiting, broken function level authorization, mass assignment API, security misconfiguration API, injection attacks API, improper assets management, insufficient logging monitoring API",
  robots: "index, follow",
  icons: {
    icon: "/favicon.ico",
  },
  openGraph: {
    title: "Capture The Bug | API Penetration Testing Services",
    type: "website",
    url: "https://capturethebug.xyz/Services/API-pentest",
    description:
      "In-depth API pentesting for REST and GraphQL endpoints. Identify auth flaws, insecure tokens, rate limit bypasses, and more-powered by Capture The Bug.",
    images: "https://i.ibb.co/TqPxpNY9/Screenshot-2025-06-18-201121.png",
  },
  twitter: {
    card: "summary_large_image",
    title: "Capture The Bug | API Security Testing",
    description:
      "Secure your APIs with Capture The Bug’s expert-led penetration testing. Detect auth issues, broken access controls, and more via PTaaS.",
    images: "https://i.ibb.co/TqPxpNY9/Screenshot-2025-06-18-201121.png",
  },
};

export default function Page() {
  return   <API />;
}
