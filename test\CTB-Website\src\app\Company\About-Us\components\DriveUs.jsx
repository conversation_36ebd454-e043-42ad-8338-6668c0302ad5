import Image from 'next/image';
import React from 'react';

const DriveUs = () => {
  return (
    <div className="bg-gradient-to-r from-[#062575] via-[#0B45DB] to-[#0B45DB] text-white p-10">
      <div className="container mx-auto flex flex-col lg:flex-row">
        <div className="lg:w-1/3 mb-8 lg:mb-0 relative overflow-hidden">
          <Image src="/images/vulnerability-scanner-interface.png" width={600} height={600} alt="Cybersecurity vulnerability scanner interface showing real-time security assessment and penetration testing capabilities" />
          <div className="absolute inset-0 overflow-hidden">
            <div className="animate-moveUpDown absolute inset-0">
              <Image src="/images/scanning.png" width={600} height={1200} alt="Animated security scanning overlay showing continuous vulnerability detection and assessment process" className="w-full h-full object-cover" />
            </div>
          </div>
        </div>
        <div className="lg:w-2/3 lg:pl-12 md:px-28 md:mt-8">
          <h2 className="text-4xl font-bold mb-4">What Drives Us</h2>
          <p className="mb-4 text-lg">
          At Capture The Bug, we’re committed to redefining penetration testing for businesses. We provide on-demand penetration testing services that overcome the traditional challenges of long wait times, high costs, and the lack of continuous support. By enabling seamless collaboration between developers and penetration testers, we dramatically enhance the efficiency and effectiveness of security remediations.

          </p>
          <p className="mb-4 text-lg">
          In today’s rapidly evolving cybersecurity landscape, where threats like zero-day exploits and continuous software updates are prevalent, a proactive and agile security strategy is essential. Our services go beyond the outdated annual testing model, offering continuous protection to ensure that your defenses are robust and up-to-date.

          </p>
          <p className='text-lg'>
          Our goal is to empower CISOs to lead with confidence, knowing that their web applications and critical infrastructures are secured against potential breaches and emerging threats.

          </p>
        </div>
      </div>
    </div>
  );
};

export default DriveUs;
