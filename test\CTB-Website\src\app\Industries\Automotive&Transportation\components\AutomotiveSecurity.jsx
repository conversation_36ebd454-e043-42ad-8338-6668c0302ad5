import React from "react";
import { Shield, Users, Target } from "lucide-react";
 
export default function AutomotiveSecurity() {
  return (
    <> 
  <div className="bg-white py-10 sm:py-16 md:py-24">
  <div className="container mx-auto px-4 sm:px-6 md:px-12 lg:px-24">
    <div className="mb-10 sm:mb-14 md:mb-16 text-center md:text-left">
      <div className="text-secondary-blue text-lg sm:text-xl font-bold mb-3 sm:mb-5">
        How Capture The Bug Helps
      </div>
      <h2 className="text-2xl sm:text-4xl md:text-5xl font-bold text-[#010D2C] leading-tight mb-4 sm:mb-5">
Futureproofing Smart Mobility Infrastructure      </h2>
      <p className="text-[#6B7280] text-base sm:text-lg md:text-xl font-medium leading-relaxed mt-5 mx-auto md:mx-0">
With fleets becoming more connected and city mobility growing more automated, new threats are constantly emerging. Capture The Bug helps mobility platforms-from telematics services to smart parking operators-find and fix security weaknesses across their digital ecosystem before attackers do.      </p>
    </div>

     <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
       <div className="bg-white border-l-4 border-[#58CC02] pl-4 sm:pl-6 pr-2 sm:pr-4 py-6 rounded-md shadow-sm">
        <div className="">
          <div className="w-10 h-10 bg-gradient-to-br from-[#58CC02] to-[#4BAF02] rounded-lg flex items-center justify-center mb-4">
            <Shield className="w-5 h-5 text-white" />
          </div>
          <h3 className="text-lg sm:text-xl font-bold text-[#010D2C] mb-2 sm:mb-3">
            End-to-end protection
          </h3>
          <p className="text-[#6B7280] text-base sm:text-lg leading-relaxed">
From GPS-enabled tracking systems and embedded vehicle devices to cloud dashboards and user mobile apps-we test every layer of your connected environment.
<span className="font-bold">This includes OTA pipelines, BLE-connected hardware, and real-time location services.</span>          </p>
        </div>
      </div>

       <div className="bg-white border-l-4 border-[#58CC02] pl-4 sm:pl-6 pr-2 sm:pr-4 py-6 rounded-md shadow-sm">
        <div className="">
          <div className="w-10 h-10 bg-gradient-to-br from-[#58CC02] to-[#4BAF02] rounded-lg flex items-center justify-center mb-4">
            <Users className="w-5 h-5 text-white" />
          </div>
          <h3 className="text-lg sm:text-xl font-bold text-[#010D2C] mb-2 sm:mb-3">
Real-world offensive testing
          </h3>
          <p className="text-[#6B7280] text-base sm:text-lg leading-relaxed">
Our red teamers replicate real attacker behavior across mobile apps, APIs, cloud logic, and hardware access points.
<span className="font-bold">We uncover issues like location spoofing, access bypass, and insecure device communications-before they&apos;re exploited.  </span>        </p>
        </div>
      </div>

       <div className="bg-white border-l-4 border-[#58CC02] pl-4 sm:pl-6 pr-2 sm:pr-4 py-6 rounded-md shadow-sm">
        <div className=" ">
          <div className="w-10 h-10 bg-gradient-to-br from-[#58CC02] to-[#4BAF02] rounded-lg flex items-center justify-center mb-4">
            <Target className="w-5 h-5 text-white" />
          </div>
          <h3 className="text-lg sm:text-xl font-bold text-[#010D2C] mb-2 sm:mb-3">
            Smart risk prioritization
          </h3>
          <p className="text-[#6B7280] text-base sm:text-lg leading-relaxed">
Our custom scoring model highlights vulnerabilities based on exploitability, severity, and operational impact.
<span className="font-bold"> So your teams can focus on what&apos;s critical-like protecting user access, location data, and vehicle control systems.  </span>        </p>
        </div>
      </div>
    </div>
 
  </div>
</div> 
</>

  );
}