"use client"
import React, { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';
import { motion } from 'framer-motion';
import LazyLoadWrapper from './common/components/LazyLoadWrapper';
// Import the Landing section eagerly as it's above the fold
import Landing from "./Home/components/LandingSection";

// Dynamically import all other components with default loading states
const PartnersList = dynamic(() => import('./Home/components/PartnersList'), {
  loading: () => <div className="w-full h-[300px] bg-gray-100 animate-pulse"></div>
});
const Features = dynamic(() => import('./Home/components/Features'), {
  loading: () => <div className="w-full h-[400px] bg-gray-100 animate-pulse"></div>
});
const CallToAction = dynamic(() => import('./Home/components/CallToAction'), {
  loading: () => <div className="w-full h-[200px] bg-gray-100 animate-pulse"></div>
});
const PublicationsList = dynamic(() => import('./Home/components/PublicationsList'), {
  loading: () => <div className="w-full h-[400px] bg-gray-100 animate-pulse"></div>
});
const Statistics = dynamic(() => import('./Home/components/Statistics'), {
  loading: () => <div className="w-full h-[300px] bg-gray-100 animate-pulse"></div>
});
const TestimonialSlider = dynamic(() => import('./Home/components/Testimonials'), {
  loading: () => <div className="w-full h-[400px] bg-gray-100 animate-pulse"></div>
});
const Table = dynamic(() => import('./Home/components/Table'), {
  loading: () => <div className="w-full h-[400px] bg-gray-100 animate-pulse"></div>
});
const Blogs = dynamic(() => import('./Home/components/Blogs'), {
  loading: () => <div className="w-full h-[500px] bg-gray-100 animate-pulse"></div>
});

export default function Home() {
  const [mounted, setMounted] = useState(false);
  
  // Handle initial mounting to prevent hydration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);
  
  if (!mounted) {
    return <div className="min-h-screen bg-gray-100 animate-pulse"></div>;
  }
  
  return (
    <>
      {/* Hero section */}
       <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.6, delay: 0.1 }}
      >
      <Landing />
      </motion.div>

      {/* Partners list */}
      <LazyLoadWrapper>
        <PartnersList />
      </LazyLoadWrapper>
      
      {/* Features */}
      <LazyLoadWrapper>
        <Features />
      </LazyLoadWrapper>
      
      {/* Call to action */}
      {/* <LazyLoadWrapper>
        <CallToAction />
      </LazyLoadWrapper> */}
      
      {/* Publications list */}
      <LazyLoadWrapper>
        <PublicationsList />
      </LazyLoadWrapper>
      
      {/* Statistics */}
      <LazyLoadWrapper>
        <Statistics />
      </LazyLoadWrapper>
      
      {/* Testimonials */}
      <LazyLoadWrapper>
        <TestimonialSlider />
      </LazyLoadWrapper>
      
      {/* Table */}
      <LazyLoadWrapper>
        <Table />
      </LazyLoadWrapper>

      {/* Blogs */}
      <LazyLoadWrapper>
        <Blogs />
      </LazyLoadWrapper>
    </>
  );
}

