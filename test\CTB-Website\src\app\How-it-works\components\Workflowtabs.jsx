"use client";
import React, { useState, useEffect } from 'react';
import { ArrowRight, Shield, Search, FileText, CheckCircle, Award, Zap, Users, Clock, Target, Lock, TrendingUp, UserCheck, Settings, Headphones, FileCheck, Scroll, RotateCcw, Brain, Wifi, Wrench, Link, Mail, RotateCw, CheckSquare, UserCog, Crosshair, CheckCircle2, Download, Trophy, Hammer } from 'lucide-react';

const tabs = [
  {
    id: 1,
    title: "Setup & Onboarding",
    icon: <Shield className="w-5 h-5" />,
    color: "blue-600",
    hash: "Setup",
    content: {
      title: "Setup & Onboarding",
      description: "Instantly launch your security journey. Our frictionless onboarding flow gets you from sign-up to scheduled testing without delays.",
      features: [
        { icon: <Zap className="w-4 h-4" />, text: "Lightning-fast account activation" },
        { icon: <UserCheck className="w-4 h-4" />, text: "Dedicated security consultant assigned" },
        { icon: <Settings className="w-4 h-4" />, text: "Environment setup" },
        { icon: <Headphones className="w-4 h-4" />, text: "24/7 priority support channel" }
      ],
      stats: { "Time to activate": "< 5 minutes", "Success Rate": "99.9%" }
    }
  },
  {
    id: 2,
    title: "Program Creation",
    icon: <Target className="w-5 h-5" />,
    color: "blue-600",
    hash: "Program",
    content: {
      title: "Program Creation",
      description: "Define goals. Lock scope. Prep your team. We tailor the test to your tech stack and risk posture-ensuring real-world, high-impact coverage.",
      features: [
        { icon: <Search className="w-4 h-4" />, text: "Asset & scope discovery (URLs, APIs, cloud, mobile)" },
        { icon: <Lock className="w-4 h-4" />, text: "Risk-based prioritization aligned with business impact" },
        { icon: <FileCheck className="w-4 h-4" />, text: "Rules of engagement finalized" },
        { icon: <Scroll className="w-4 h-4" />, text: "Legal docs/NDA handled securely" },
        { icon: <RotateCcw className="w-4 h-4" />, text: "Slack, Jira, GitHub integrations (optional)" }
      ],
      stats: {}
    }
  },
  {
    id: 3,
    title: "Active Penetration Testing",
    icon: <Zap className="w-5 h-5" />,
    color: "blue-600",
    hash: "Penetration",
    content: {
      title: "Active Penetration Testing",
      description: "Real testers. Real tactics. Real-time updates. Our offensive security engineers test your systems using industry-leading techniques.",
      features: [
        { icon: <Brain className="w-4 h-4" />, text: "OWASP, NIST, and CREST-aligned methodology" },
        { icon: <Wifi className="w-4 h-4" />, text: "Continuous vulnerability discovery" },
        { icon: <Wrench className="w-4 h-4" />, text: "Live updates via collaborative dashboard" },
        { icon: <Link className="w-4 h-4" />, text: "Exploit proof-of-concepts (where applicable)" },
        { icon: <Mail className="w-4 h-4" />, text: "Stakeholder communication loop (via Slack/Jira)" }
      ],
      stats: {}
    }
  },
  {
    id: 4,
    title: "Remediation & Retesting",
    icon: <TrendingUp className="w-5 h-5" />,
    color: "blue-600",
    hash: "Retesting",
    content: {
      title: "Remediation & Retesting",
      description: "Fix confidently. Verify quickly. Your team receives developer-friendly recommendations-and we validate the fixes.",
      features: [
        { icon: <Hammer className="w-4 h-4" />, text: "Actionable fix guidance with CVE/CWE mappings" },
        { icon: <RotateCw className="w-4 h-4" />, text: "Unlimited retesting within 30 days (or based on SLA)" },
        { icon: <CheckSquare className="w-4 h-4" />, text: "Patch verification via manual & automated checks" },
        { icon: <UserCog className="w-4 h-4" />, text: "GitHub-based remediation validation (optional)" }
      ],
      stats: {}
    }
  },
  {
    id: 5,
    title: "Final Report & Certificate",
    icon: <Award className="w-5 h-5" />,
    color: "blue-600",
    hash: "Certificate",
    content: {
      title: "Final Report & Certificate",
      description: "Showcase your security. Share your success. Wrap it all with a detailed report and a certificate that proves you're secure.",
      features: [
        { icon: <FileText className="w-4 h-4" />, text: "Executive summary & technical deep dive" },
        { icon: <Crosshair className="w-4 h-4" />, text: "Severity-based classification (CVSS v3 scoring)" },
        { icon: <CheckCircle2 className="w-4 h-4" />, text: "Compliance-ready mapping (SOC2, ISO 27001, PCI-DSS)" },
        { icon: <Download className="w-4 h-4" />, text: "Remediation verification & retest results" },
        { icon: <Trophy className="w-4 h-4" />, text: "Attestation Letter" }
      ],
      stats: {}
    }
  }
];


export default function PentestWorkflowTabs() {
  const [activeTab, setActiveTab] = useState(1);

  
  // Handle hash navigation
  useEffect(() => {
    const handleHashChange = () => {
      const hash = window.location.hash.replace('#', '');
      const tab = tabs.find(t => t.hash === hash);
      if (tab) {
        setActiveTab(tab.id);
      }
    };

    // Check hash on mount
    handleHashChange();

    // Listen for hash changes
    window.addEventListener('hashchange', handleHashChange);

    return () => {
      window.removeEventListener('hashchange', handleHashChange);
    };
  }, []);

  // Update URL hash when tab changes
  const handleTabChange = (tabId) => {
    const tab = tabs.find(t => t.id === tabId);
    if (tab) {
      setActiveTab(tabId);
      window.history.pushState(null, '', `#${tab.hash}`);
    }
  };
 
  const activeContent = tabs.find(tab => tab.id === activeTab)?.content;
  const activeTabData = tabs.find(tab => tab.id === activeTab);

  return (
    <div className="min-h-screen bg-slate-900 p-3 sm:p-6 lg:p-10  ">
      <div className="relative max-w-7xl mx-auto">
         <div className="text-center mb-8 sm:mb-12">
          <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-3 sm:mb-4 leading-tight">
            Discover Our{" "}
            <span className=" text-blue-600">
              Proven Pentesting Lifecycle
            </span>
          </h1>
          <p className="text-base sm:text-lg lg:text-xl text-slate-300 max-w-2xl mx-auto px-4 leading-relaxed">
            Follow our five-stage process that guides you from onboarding and testing to remediation and final certification, all on one collaborative platform.
          </p>
        </div>

         <div className="mb-8 sm:mb-12">
           <div className="sm:hidden space-y-2">
            {tabs.map((tab, index) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`w-full px-4 py-3 rounded-xl text-sm font-medium transition-all duration-300 flex items-center gap-3 ${
                  activeTab === tab.id
                    ? 'bg-blue-600 text-white shadow-lg'
                    : 'bg-slate-800/70 text-slate-300 hover:text-white hover:bg-slate-700/70'
                }`}
              >
                <span className="text-sm font-semibold bg-white/20 rounded-full w-6 h-6 flex items-center justify-center">
                  {index + 1}
                </span>
                <span className="text-left flex-1">{tab.title}</span>
                {activeTab === tab.id && <ArrowRight className="w-4 h-4" />}
              </button>
            ))}
          </div>

           <div className="hidden sm:flex flex-wrap justify-center gap-1 bg-slate-800/50 backdrop-blur-sm rounded-2xl p-2 max-w-6xl mx-auto border border-slate-700/50">
            {tabs.map((tab, index) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`relative px-3 lg:px-4 py-3 rounded-xl text-xs sm:text-sm font-medium transition-all duration-300 flex items-center gap-2 flex-1 min-w-0 ${
                  activeTab === tab.id
                    ? 'bg-ctb-green-50 text-white shadow-lg'
                    : 'text-slate-300 hover:text-white hover:bg-slate-700/50'
                }`}
              >
                <span className="text-xs sm:text-sm font-semibold">{index + 1}.</span>
                <span className="truncate text-xs sm:text-sm">{tab.title}</span>
              </button>
            ))}
          </div>
        </div>
 
        <div className="relative">
          <div className="absolute inset-0 bg-white/5 rounded-2xl sm:rounded-3xl backdrop-blur-sm"></div>
          <div className="relative bg-white/95 backdrop-blur-md rounded-2xl sm:rounded-3xl shadow-2xl border border-white/20 overflow-hidden">
             <div className="h-1 sm:h-2 bg-blue-600"></div>
            
            <div className="p-4 sm:p-6 lg:p-8 xl:p-12">
              <div className="grid lg:grid-cols-3 gap-6 lg:gap-8 items-start">
                 <div className="lg:col-span-2">
                  <div className="flex flex-col sm:flex-row sm:items-center gap-4 mb-6">
                    <div className="p-3 sm:p-4 rounded-xl sm:rounded-2xl bg-blue-600 self-start sm:self-auto">
                      {activeTabData?.icon && React.cloneElement(activeTabData.icon, {className: "w-6 h-6 sm:w-8 sm:h-8 text-white"})}
                    </div>
                    <div className="flex-1">
                      <h2 className="text-xl sm:text-2xl lg:text-3xl font-bold text-slate-800 mb-2 leading-tight">
                        {activeContent?.title}
                      </h2>
                      <div className="flex flex-wrap gap-2 sm:gap-4 text-xs sm:text-sm">
                        {activeContent?.stats && Object.entries(activeContent.stats).map(([key, value]) => (
                          <span key={key} className="px-2 sm:px-3 py-1 bg-slate-100 rounded-full text-slate-600 font-medium">
                            {key}: {value}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                  
                  <p className="text-slate-600 mb-6 sm:mb-8 leading-relaxed text-sm sm:text-base lg:text-lg">
                    {activeContent?.description}
                  </p>
                  
                  <div className="grid gap-3 sm:gap-4 md:grid-cols-2">
                    {activeContent?.features?.map((feature, index) => (
                      <div key={index} className="flex items-start gap-3 p-3 sm:p-4 rounded-lg sm:rounded-xl bg-slate-50 hover:bg-slate-100 transition-colors duration-200">
                        <CheckCircle className="w-4 h-4 sm:w-5 sm:h-5 text-blue-600 flex-shrink-0 mt-0.5" />
<span className="text-slate-700 font-medium text-sm sm:text-base">{feature.text}</span>                      </div>
                    ))}
                  </div>
                </div>

                 <div className="hidden lg:flex flex-col items-center justify-center">
                  <div className="relative">
                     <div className="absolute inset-0 animate-spin-slow">
                      <div className="w-24 h-24 xl:w-32 xl:h-32 rounded-full border-4 border-dashed border-blue-600/20"></div>
                    </div>
                    <div className="absolute inset-0 animate-spin-slow-reverse">
                      <div className="w-16 h-16 xl:w-24 xl:h-24 m-4 rounded-full border-2 border-dotted border-blue-600/30"></div>
                    </div>
                    
                     <div className="relative z-10 w-24 h-24 xl:w-32 xl:h-32 rounded-full bg-blue-600 flex items-center justify-center shadow-2xl">
                      {activeTabData?.icon && React.cloneElement(activeTabData.icon, {className: "w-8 h-8 xl:w-12 xl:h-12 text-white"})}
                    </div>
                  </div>
                  
                  <div className="mt-6 xl:mt-8 text-center">
                    <p className="text-slate-500 font-semibold text-base xl:text-lg">
                      Step {activeTab} of {tabs.length}
                    </p>
                    <div className="flex gap-2 mt-4 justify-center">
                      {tabs.map((_, index) => (
                        <div
                          key={index}
                          className={`w-2 h-2 xl:w-3 xl:h-3 rounded-full transition-all duration-300 ${
                            index + 1 === activeTab
                              ? 'bg-ctb-green-50 scale-125'
                              : 'bg-slate-300'
                          }`}
                        ></div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

               <div className="lg:hidden flex justify-center mt-6 sm:mt-8">
                <div className="relative">
                  <div className="w-16 h-16 sm:w-20 sm:h-20 rounded-full bg-blue-600 flex items-center justify-center shadow-lg">
                    {activeTabData?.icon && React.cloneElement(activeTabData.icon, {className: "w-6 h-6 sm:w-8 sm:h-8 text-white"})}
                  </div>
                </div>
                <div className="ml-4 flex flex-col justify-center">
                  <p className="text-slate-500 font-semibold text-sm">
                    Step {activeTab} of {tabs.length}
                  </p>
                  <div className="flex gap-1 mt-2">
                    {tabs.map((_, index) => (
                      <div
                        key={index}
                        className={`w-2 h-2 rounded-full transition-all duration-300 ${
                          index + 1 === activeTab
                            ? 'bg-blue-600'
                            : 'bg-slate-300'
                        }`}
                      ></div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
<div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center mt-8 sm:mt-12 px-4 sm:px-0">
  <a href="/Request-Demo" className="no-underline">
    <div className="px-6 sm:px-8 py-3 sm:py-4 bg-ctb-green-50 text-white rounded-xl sm:rounded-2xl font-semibold transition-all duration-300 flex items-center justify-center gap-2 sm:gap-3 hover:scale-105 text-sm sm:text-base">
      <ArrowRight className="w-4 h-4 sm:w-5 sm:h-5" />
      <span>Start Your Pentest Journey</span>
    </div>
  </a>

  <a href="/Download-Sample-Report" target="_blank" rel="noopener noreferrer" className="no-underline">
    <div className="px-6 sm:px-8 py-3 sm:py-4 bg-white/10 hover:bg-white/20 text-white rounded-xl sm:rounded-2xl font-semibold transition-all duration-300 flex items-center justify-center gap-2 sm:gap-3 backdrop-blur-sm border border-white/20 hover:border-white/40 hover:scale-105 text-sm sm:text-base">
      <FileText className="w-4 h-4 sm:w-5 sm:h-5" />
      <span>Download Sample Report</span>
    </div>
  </a>
</div>
</div>
      
      <style jsx>{`
        @keyframes spin-slow {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }
        @keyframes spin-slow-reverse {
          from { transform: rotate(360deg); }
          to { transform: rotate(0deg); }
        }
        .animate-spin-slow {
          animation: spin-slow 20s linear infinite;
        }
        .animate-spin-slow-reverse {
          animation: spin-slow-reverse 15s linear infinite;
        }
      `}</style>
    </div>
  );
}