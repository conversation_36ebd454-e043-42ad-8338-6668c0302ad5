'use client';
import React, { useEffect, useState, useRef } from 'react';
import PropTypes from 'prop-types';

const NumbersCard = ({ number, title, subtitle, icon = '' }) => {
  const [animatedNumber, setAnimatedNumber] = useState(0);
  const [inView, setInView] = useState(false);
  const ref = useRef(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setInView(true);
        }
      },
      { threshold: 0.5 }
    );

    const currentRef = ref.current;
    if (currentRef) {
      observer.observe(currentRef);
    }

    return () => {
      if (currentRef) {
        observer.unobserve(currentRef);
      }
    };
  }, []);

  useEffect(() => {
    if (inView) {
      let start = 0;
      const duration = 2000; // 2 seconds
      const incrementTime = duration / number;
      const animate = () => {
        if (start < number) {
          start++;
          setAnimatedNumber(start);
          setTimeout(animate, incrementTime);
        }
      };
      animate();
    }
  }, [inView, number]);

  return (
    <div ref={ref} className="flex flex-col items-center p-4 md:w-[243px] w-72 gap-2">
      <div className="flex items-center text-5xl font-bold bg-gradient-to-r from-[#062575] via-[#0B45DB] to-[#0B45DB] bg-clip-text text-transparent">
        {animatedNumber}
        <span>{icon}</span>
      </div>
      <div className="text-[14px] font-semibold text-center">{title}</div>
      <div className="text-[12px] text-gray-500 font-semibold text-center">{subtitle}</div>
    </div>
  );
};

NumbersCard.propTypes = {
  number: PropTypes.number.isRequired,
  title: PropTypes.string.isRequired,
  subtitle: PropTypes.string.isRequired,
  icon: PropTypes.string,
};

export default NumbersCard;