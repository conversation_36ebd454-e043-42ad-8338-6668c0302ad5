"use client";

import React from "react";
import { <PERSON>, Flag, Eye } from "lucide-react";

export default function OurPurpose() {
  const purposeItems = [
    {
      icon: <Users className="w-8 h-8 text-[var(--color-blue)]" />,
      title: "Our Purpose",
      subtitle: "Why we exist",
      description: "Our purpose is to provide small to medium-sized businesses with affordable cybersecurity services, tailored to their individual needs. So all businesses - big or small - can operate confidently and securely in cyberspace."
    },
    {
      icon: <Flag className="w-8 h-8 text-[var(--color-blue)]" />,
      title: "Our Mission",
      subtitle: "What we do",
      description: "Our mission is to provide cutting-edge cybersecurity solutions to combat the threats of today and tomorrow. So we can help make this world more cyber secure for everyone."
    },
    {
      icon: <Eye className="w-8 h-8 text-[var(--color-blue)]" />,
      title: "Our Vision",
      subtitle: "Where we are going",
      description: "Our vision is to create an environment where everyone feels safe and secure online. So individuals and businesses can confidently operate, interact, and collaborate in cyberspace without having to worry about the security of their digital assets or data."
    }
  ];

  return (
    <section className="bg-gray-50 py-20 lg:py-28">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-12 lg:mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-[var(--color-dark-blue)]">
            Our Purpose
          </h2>
          <p className="mt-4 text-lg text-gray-600 max-w-2xl mx-auto">
            Understanding why we exist, what we do, and where we're going in our mission to secure the digital world.
          </p>
        </div>

        {/* Purpose Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 lg:gap-12">
          {purposeItems.map((item, index) => (
            <div 
              key={index}
              className="bg-white rounded-lg shadow-md border border-gray-200 border-t-4 border-t-[var(--color-blue)] p-8 hover:shadow-lg transition-shadow duration-300"
            >
              {/* Icon */}
              <div className="mb-6">
                {item.icon}
              </div>

              {/* Content */}
              <div>
                <h3 className="text-xl font-bold text-[var(--color-dark-blue)] mb-2">
                  {item.title}
                </h3>
                <p className="text-[var(--color-blue)] font-semibold text-sm mb-4">
                  {item.subtitle}
                </p>
                <p className="text-gray-600 leading-relaxed">
                  {item.description}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
} 