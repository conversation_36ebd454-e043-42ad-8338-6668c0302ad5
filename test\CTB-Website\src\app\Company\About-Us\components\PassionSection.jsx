"use client";
import React from "react";
// You can use your own SVGs or icon library for the icons below

const principles = [
  {
    icon: (
      <svg className="w-7 h-7 text-[#06258d]" fill="none" stroke="currentColor" strokeWidth={2} viewBox="0 0 24 24">
        <circle cx="12" cy="12" r="10" stroke="currentColor" />
        <path d="M12 8v4l3 3" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" />
      </svg>
    ),
    label: "Put customers first",
  },
  {
    icon: (
      <svg className="w-7 h-7 text-[#06258d]" fill="none" stroke="currentColor" strokeWidth={2} viewBox="0 0 24 24">
        <path d="M12 21C12 21 4 13.5 4 8.5C4 5.5 6.5 3 9.5 3C11.24 3 12 4.5 12 4.5C12 4.5 12.76 3 14.5 3C17.5 3 20 5.5 20 8.5C20 13.5 12 21 12 21Z" stroke="currentColor" />
      </svg>
    ),
    label: "Win as one team",
  },
  {
    icon: (
      <svg className="w-7 h-7 text-[#06258d]" fill="none" stroke="currentColor" strokeWidth={2} viewBox="0 0 24 24">
        <path d="M5 13l4 4L19 7" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" />
      </svg>
    ),
    label: "Bias for action",
  },
  {
    icon: (
      <svg className="w-7 h-7 text-[#06258d]" fill="none" stroke="currentColor" strokeWidth={2} viewBox="0 0 24 24">
        <path d="M16 7a4 4 0 01-8 0" stroke="currentColor" />
        <circle cx="12" cy="12" r="10" stroke="currentColor" />
      </svg>
    ),
    label: "Lead with resilience",
  },
  {
    icon: (
      <svg className="w-7 h-7 text-[#06258d]" fill="none" stroke="currentColor" strokeWidth={2} viewBox="0 0 24 24">
        <rect x="4" y="4" width="16" height="16" rx="2" stroke="currentColor" />
        <path d="M8 9h8M8 13h6" stroke="currentColor" />
      </svg>
    ),
    label: "Decide with frameworks",
  },
  {
    icon: (
      <svg className="w-7 h-7 text-[#06258d]" fill="none" stroke="currentColor" strokeWidth={2} viewBox="0 0 24 24">
        <rect x="3" y="7" width="18" height="13" rx="2" stroke="currentColor" />
        <path d="M16 3v4M8 3v4" stroke="currentColor" />
      </svg>
    ),
    label: "Do what it says on the tin",
  },
];

export default function OperatingPrinciples() {
  return (
    <section className="w-full h-fit py-36 bg-gray-50">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-20 items-center">
        {/* Left Side */}
        <div className="ml-0 lg:ml-20">
          <h2 className="text-4xl md:text-4xl font-semibold text-[#06258d] mb-6">
            Our values are our <br />
            <span className="text-[#1e83fb] ">operating principles</span>
          </h2>
          <p className="text-md text-gray-700 mb-6">
            Capture The Bug&apos;s mission is to protect consumer data and restore trust in internet business. To do that, we&apos;re building an enduring company where all team members can do the best work of their careers. Building that enduring company together necessitates articulating how we work and how we make decisions – we call these our principles.
          </p>
          <p className="text-md text-gray-700">
            They&apos;re the core tenets that guide how we work, how we hire, and how we interact with customers and partners.
          </p>
        </div>
        {/* Right Side */} 
        <div className="flex flex-col items-center mx-auto lg:mr-20">
          <h3 className="text-2xl font-semibold text-[#06258d] mb-6 text-center">A Capture The Bug team member will…</h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 w-full">
            {principles.map((p, i) => (
              <div
                key={i}
                className="flex items-center gap-3 bg-white rounded-2xl border border-[#1e83fb] px-6 py-5 shadow-sm text-[#06258d] font-medium text-lg transition-all hover:shadow-lg hover:border-[#06258d] hover:bg-[#eaf4ff] cursor-pointer"
              >
                <span className="text-[#1e83fb]">{p.icon}</span>
                <span>{p.label}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
