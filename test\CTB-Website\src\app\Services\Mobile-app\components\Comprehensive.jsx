import Button from "@/app/common/buttons/Button";
import Image from "next/image";
import Link from "next/link";
import React from "react";

const BulletPoint = ({ imageSrc, content }) => (
    <div className="flex items-start mb-4">
      <Image src={imageSrc} alt="Checkmark icon indicating mobile application security testing benefit" className="w-6 h-6 mr-4 mt-1" width={24} height={24} />
      <p className="flex-1  text-slate-600">{content}</p>
    </div>
  );

export default function Comprehensive() {
    const bulletPoints = [
        {
          imageSrc: "/images/tick.png",
          content: "We start by understanding your network's specific architecture and security requirements."
        },
        {
          imageSrc: "/images/tick.png",
          content: "Our expert pentesters simulate real-world attacks to identify vulnerabilities that could be exploited by malicious actors."
        },
        {
          imageSrc: "/images/tick.png",
          content: "Once testing is complete, we provide a detailed report with prioritized findings and clear, actionable recommendations."
        }
      ];
  return (
    <div>
      <div className="md:p-12 p-8 flex md:flex-row flex-col justify-between md:py-20">
        <div className="Content md:w-[50%] gap-6 flex flex-col">
            <div className="font-bold">
            Our Approach 
            </div>
          <div className="Title md:text-3xl text-2xl font-bold bg-gradient-to-r from-[#062575] via-[#0B45DB] to-[#0B45DB] bg-clip-text text-transparent">
          Comprehensive Network Security Testing
          </div>

          <div className="subTitle2 leading-8 md:pr-10 text-slate-600 text-sm md:text-lg">
          At Capture The Bug, we provide in-depth network penetration testing designed to uncover vulnerabilities across your entire network infrastructure. Our approach is methodical, thorough, and tailored to meet the unique needs of your organization, ensuring that every potential entry point is scrutinized.
          </div>
            <div>
            {bulletPoints.map((point, index) => (
        <BulletPoint key={index} imageSrc={point.imageSrc} content={point.content} />
      ))}
            </div>
        </div>
        <div className="Image mt-10">
          <Image src="/images/network-img-4.png" width={550} height={500} alt="Mobile application security testing dashboard showing comprehensive vulnerability assessment and penetration testing results for mobile apps" />
        </div>
      </div>  
    </div>
  );
}
