"use client";
import { motion } from 'framer-motion';
import React from 'react'
import OurS<PERSON> from './components/OurStory'
import PassionSection from './components/PassionSection'
import Webinar from './components/Webinar'
import LandingMain from './components/LandingMain'
import Passion from './components/Passion'
import PartnersList from '@/app/Home/components/PartnersList'
import BreadcrumbNavigation from '@/app/common/components/BreadcrumbNavigation';
import { generateBreadcrumbs } from '@/app/common/components/BreadcrumbNavigation';



function page() {
  const breadcrumbs = generateBreadcrumbs('company', {
    title: 'About Us',
    description: 'Learn about Capture The Bug and our mission'
  })('About-Us');

  return (
    <div className="relative">
        <title>Capture The Bug | About us</title>

        {/* Breadcrumb Navigation - positioned absolutely at the top */}
        <div className="absolute top-0 left-0 right-0 pt-2 sm:pt-2 md:pt-5 xl:pt-14 z-10">
          <div className="max-w-7xl px-2 sm:px-2 md:px-16">
            <BreadcrumbNavigation items={breadcrumbs} />
          </div>
        </div>
         <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 0.5 }}
                  >
                    <motion.div
                      initial={{ y: 20, opacity: 0 }}
                      animate={{ y: 0, opacity: 1 }}
                      transition={{ duration: 0.6, delay: 0.1 }}
                    >
        <LandingMain/>
        </motion.div>
        <PartnersList/>
        {/* <BackgroundBoxesDemo/> */}
        <OurStory/>
        <Passion/>
        {/* <AboutSection/> */}
        {/* <AboutLanding/> */}
        {/* <PassionSection/> */}
        {/* <Mission/> */}
        {/* <DriveUs/> */}
        {/* <PublicationList/> */}
        <Webinar/>
        </motion.div>
    </div>
  )
}

export default page
