"use client";

import React from "react";
import Image from "next/image";
import BreadcrumbNavigation from "../../common/components/BreadcrumbNavigation";

export default function AboutHero() {
  const breadcrumbItems = [
    {
      name: "Home",
      url: "/",
      iconKey: "home",
      description: "Return to homepage"
    },
    {
      name: "About Us",
      url: "/about",
      current: true,
      iconKey: "company",
      description: "Learn about SecurityLit's story and mission"
    }
  ];

  const stats = [
    { number: "200+", label: "Organizations Secured" },
    { number: "1000+", label: "Security Assessments" },
    { number: "50+", label: "Expert Team Members" },
    { number: "99.9%", label: "Client Satisfaction" }
  ];



  return (
    <div className="min-h-screen bg-white">
      <div className="flex flex-col lg:flex-row min-h-screen">
        
        {/* Left Section - Image */}
        <div className="lg:w-1/2 min-h-[300px] lg:min-h-screen relative">
          <Image
            src="/images/about-us-image-2.jpg"
            alt="The SecurityLit team collaborating on cybersecurity projects"
            fill
            sizes="(max-width: 1024px) 100vw, 50vw"
            className="object-cover"
            priority
          />
          {/* Subtle overlay to enhance text readability on the image if needed */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
        </div>

        {/* Right Section - Content */}
        <div className="lg:w-1/2">
          {/* Inner container to manage padding and alignment */}
          <div className="max-w-2xl mx-auto p-8 pt-28 pb-16 lg:p-16 lg:pt-28 lg:pb-24">
            

            
            {/* Breadcrumb */}
            <div className="mb-8">
              <BreadcrumbNavigation items={breadcrumbItems} />
            </div>

            {/* Heading and Mission */}
            <h1 className="text-2xl lg:text-3xl font-bold text-[var(--color-dark-blue)] mb-4">
              Why We Exist
            </h1>
            <p className="text-lg text-[var(--color-blue)] font-semibold mb-8">
              Our team of skilled security researchers have a clear vision – to help keep your business safe from current and future cyber threats.
            </p>

            {/* Story Paragraphs */}
            <div className="space-y-6 text-gray-600 leading-relaxed mb-12">
              <p>
                As cybercrime continues to evolve, so must our response. Simply installing antivirus software and a basic firewall is no longer enough to fend off today's cyber threats and attacks.
              </p>
              <p>
                But a successful attack on your business can have catastrophic consequences for your operation and reputation.
              </p>
              <p>
                We don't want that to happen to you, and it's our mission to help keep your business safe online.
              </p>
            </div>

            {/* Stats Section */}
            <div className="pt-8 border-t border-gray-200">
              <div className="grid grid-cols-2 gap-8">
                {stats.map((stat) => (
                  <div key={stat.label}>
                    <p className="text-3xl lg:text-4xl font-bold text-[var(--color-dark-blue)]">
                      {stat.number}
                    </p>
                    <p className="text-sm text-gray-500 mt-1">
                      {stat.label}
                    </p>
                  </div>
                ))}
              </div>
            </div>

          </div>
        </div>
      </div>
    </div>
  );
}
