'use client'
import React, { useState } from 'react';

const FAQItem = ({ question, answer, isOpen, onToggle, uniqueId }) => (
  <div className="mb-4">
    <button
      onClick={onToggle}
      aria-expanded={isOpen}
      className="flex w-full justify-between items-center text-left bg-gray-100 p-4 rounded-lg transition-all"
    >
      <span className="md:text-lg text-sm font-medium text-gray-900">{question}</span>
      <span className="ml-4">
        {isOpen ? (
          <svg className="h-5 w-5 text-[#1e83fb]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
          </svg>
        ) : (
          <svg className="h-5 w-5 text-[#1e83fb]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
          </svg>
        )}
      </span>
    </button>
    <div
      className={`transition-all duration-300 ease-in-out overflow-hidden ${
        isOpen ? 'max-h-[1000px] opacity-100 py-4' : 'max-h-0 opacity-0 py-0'
      } bg-gray-50`}
    >
      <div className="px-4">
        <p className="md:text-lg text-sm text-gray-700">{answer}</p>
      </div>
    </div>
  </div>
);

const FAQ = () => {
  const [openItem, setOpenItem] = useState(null);

  const handleToggle = (uniqueId) => {
    setOpenItem(prev => (prev === uniqueId ? null : uniqueId));
  };

  const faqs = [
    {
      id: "start-first-pentest",
      question: "How do I start my first pentest?",
      answer: "Getting started is fast and straightforward. After a brief onboarding, you can define your test scope-like your web app's URL or API endpoints-directly on our platform. Just pick a start date from our calendar, and your pentest is scheduled."
    },
    {
      id: "after-scheduling",
      question: "What happens after I schedule a test?",
      answer: "Once your pentest begins, you can track its progress live from your dashboard. Our expert pentesters immediately start assessing your application, and as they discover vulnerabilities, the findings appear in real-time. This gives you instant visibility into your security posture from day one."
    },
    {
      id: "engineering-team-info",
      question: "How does my engineering team get the information they need to fix things?",
      answer: "Our platform is built for developers. Each vulnerability finding includes a risk score, clear steps to reproduce it, and actionable remediation advice. You can assign findings to team members or integrate with tools like Jira and Slack to push tickets directly into your existing workflow."
    },
    {
      id: "retesting-process",
      question: "What is the process for retesting a fixed vulnerability?",
      answer: "It's simple. Once your team has deployed a patch for a vulnerability, you can request a retest with a single click on the platform. Our team will then validate the fix. If successful, the finding is marked as \"Resolved,\" giving you a clear audit trail of your remediation efforts."
    },
    {
      id: "final-report",
      question: "When do we receive the final report?",
      answer: "You receive a final, compliance-ready report once the testing window is complete and all critical vulnerabilities have been successfully retested and resolved. In addition to the downloadable PDF report, your dashboard provides a continuous, up-to-date record of your security status."
    }
  ];

  return (
    <section className="w-full py-16 bg-white">
      <div className="max-w-6xl mx-auto px-4">
        <div className="flex flex-col md:flex-row items-stretch md:gap-20 min-h-[400px]">
          {/* Left: Centered FAQ label */}
          <div className="md:w-1/3 w-full flex items-center justify-center mb-8 md:mb-0">
            <h2 className="text-5xl md:text-7xl font-bold text-[#1e83fb] text-center">FAQ</h2>
          </div>
          {/* Right: FAQ List */}
          <div className="md:w-2/3 w-full flex flex-col justify-center">
            {faqs.map((faq) => (
              <FAQItem
                key={faq.id}
                uniqueId={faq.id}
                question={faq.question}
                answer={faq.answer}
                isOpen={openItem === faq.id}
                onToggle={() => handleToggle(faq.id)}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default FAQ;