"use client";

import React from "react";
import { Linkedin } from "lucide-react";
import Image from "next/image";

export default function AboutPartners() {
  // Partner information - you can update these details
  const partners = [
    {
      name: "Partner 1",
      title: "Strategic Partner",
      imageUrl: "/images/partner-1.jpg", // Replace with actual partner logo/image
      description: "Collaborating to deliver comprehensive cybersecurity solutions and innovative security strategies.",
      social: {
        linkedin: "https://www.linkedin.com/company/partner1/"
      }
    },
    {
      name: "Partner 2", 
      title: "Technology Partner",
      imageUrl: "/images/partner-2.jpg", // Replace with actual partner logo/image
      description: "Providing cutting-edge security technologies and tools to enhance our service offerings.",
      social: {
        linkedin: "https://www.linkedin.com/company/partner2/"
      }
    },
    {
      name: "Partner 3",
      title: "Certification Partner", 
      imageUrl: "/images/partner-3.jpg", // Replace with actual partner logo/image
      description: "Delivering industry-recognized certifications and training programs for cybersecurity professionals.",
      social: {
        linkedin: "https://www.linkedin.com/company/partner3/"
      }
    }
  ];

  return (
    <section className="bg-gray-50 py-20 lg:py-28">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-12 lg:mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-[var(--color-dark-blue)]">
            Our Strategic Partners
          </h2>
          <p className="mt-4 text-lg text-gray-600 max-w-2xl mx-auto">
            We collaborate with industry leaders to provide comprehensive cybersecurity solutions and drive innovation in digital security.
          </p>
        </div>

        {/* Partners Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-12">
          {partners.map((partner) => (
            <div 
              key={partner.name}
              className="bg-white rounded-xl shadow-md overflow-hidden transform hover:-translate-y-2 transition-transform duration-300"
            >
              <div className="relative h-64">
                <Image 
                  className="object-cover object-center" 
                  src={partner.imageUrl} 
                  alt={`Logo of ${partner.name} - ${partner.title}`}
                  fill
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                  style={{ objectPosition: 'center 30%' }}
                  onError={(e) => { 
                    e.target.src = 'https://via.placeholder.com/400x400.png?text=Partner+Logo'; 
                  }}
                />
                {/* LinkedIn Icon */}
                <a
                  href={partner.social.linkedin}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="absolute top-4 right-4 bg-white/80 backdrop-blur-sm p-2 rounded-full text-[var(--color-dark-blue)] hover:bg-white hover:text-[var(--color-blue)] transition-colors"
                  aria-label={`${partner.name}'s LinkedIn Profile`}
                >
                  <Linkedin className="w-5 h-5" />
                </a>
              </div>

              <div className="p-6">
                <h3 className="text-xl font-bold text-[var(--color-dark-blue)]">{partner.name}</h3>
                <p className="text-[var(--color-blue)] font-semibold mt-1">{partner.title}</p>
                <p className="text-gray-600 mt-4 text-sm leading-relaxed">
                  {partner.description}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
