"use client";
import React from 'react';
import { getLocationData, getLocalBusinessStructuredData } from '../data/locationData';

/**
 * Structured Data Component for SEO
 * Generates JSON-LD structured data for local business, services, and reviews
 */
const StructuredData = ({ country }) => {
  const locationData = getLocationData(country);
  const localBusinessData = getLocalBusinessStructuredData(country);

  // Organization structured data
  const organizationData = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "@id": "https://capturethebug.xyz#organization",
    "name": "Capture The Bug",
    "legalName": locationData.business.legalName,
    "url": "https://capturethebug.xyz",
    "logo": {
      "@type": "ImageObject",
      "url": "https://capturethebug.xyz/images/logo.png",
      "width": 300,
      "height": 100
    },
    "description": "Leading global penetration testing and cybersecurity services provider",
    "foundingDate": locationData.business.foundedYear,
    "numberOfEmployees": locationData.business.employeeCount,
    "industry": "Cybersecurity Services",
    "serviceArea": {
      "@type": "Country",
      "name": locationData.country
    },
    "hasCredential": locationData.business.certifications.map(cert => ({
      "@type": "EducationalOccupationalCredential",
      "name": cert
    })),
    "sameAs": [
      "https://www.linkedin.com/company/capture-the-bug",
      "https://twitter.com/capturethebug",
      "https://github.com/capturethebug"
    ]
  };

  // Service structured data
  const serviceData = {
    "@context": "https://schema.org",
    "@type": "Service",
    "name": "Penetration Testing Services",
    "description": `Professional penetration testing and cybersecurity services in ${locationData.country}`,
    "provider": {
      "@type": "Organization",
      "@id": "https://capturethebug.xyz#organization"
    },
    "serviceType": "Cybersecurity Services",
    "areaServed": {
      "@type": "Country",
      "name": locationData.country
    },
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": "Cybersecurity Services",
      "itemListElement": locationData.pricing.packages.map((pkg, index) => ({
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": pkg.name,
          "description": pkg.description
        },
        "price": pkg.price,
        "priceCurrency": locationData.currency,
        "availability": "https://schema.org/InStock",
        "validFrom": new Date().toISOString().split('T')[0]
      }))
    },
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.9",
      "reviewCount": "127",
      "bestRating": "5",
      "worstRating": "1"
    }
  };

  // Professional Service structured data
  const professionalServiceData = {
    "@context": "https://schema.org",
    "@type": "ProfessionalService",
    "name": locationData.business.name,
    "description": locationData.business.description,
    "url": `https://capturethebug.xyz/Locations/${country}`,
    "telephone": locationData.contact.headquarters.phone,
    "email": locationData.contact.headquarters.email,
    "address": {
      "@type": "PostalAddress",
      "streetAddress": locationData.contact.headquarters.address.streetAddress,
      "addressLocality": locationData.contact.headquarters.address.addressLocality,
      "addressRegion": locationData.contact.headquarters.address.addressRegion,
      "postalCode": locationData.contact.headquarters.address.postalCode,
      "addressCountry": locationData.contact.headquarters.address.addressCountry
    },
    "geo": {
      "@type": "GeoCoordinates",
      "latitude": country === 'nz' ? -36.8485 : country === 'au' ? -33.8688 : 40.7128,
      "longitude": country === 'nz' ? 174.7633 : country === 'au' ? 151.2093 : -74.0060
    },
    "openingHours": Object.entries(locationData.contact.headquarters.businessHours).map(([day, hours]) => 
      `${day.charAt(0).toUpperCase() + day.slice(1)} ${hours}`
    ),
    "priceRange": `${locationData.currency} ${locationData.pricing.startingPrice}+`,
    "paymentAccepted": "Cash, Credit Card, Bank Transfer",
    "currenciesAccepted": locationData.currency
  };

  // Website structured data
  const websiteData = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "@id": "https://capturethebug.xyz#website",
    "url": "https://capturethebug.xyz",
    "name": "Capture The Bug",
    "description": "Professional penetration testing and cybersecurity services",
    "publisher": {
      "@type": "Organization",
      "@id": "https://capturethebug.xyz#organization"
    },
    "potentialAction": {
      "@type": "SearchAction",
      "target": {
        "@type": "EntryPoint",
        "urlTemplate": "https://capturethebug.xyz/search?q={search_term_string}"
      },
      "query-input": "required name=search_term_string"
    }
  };

  return (
    <>
      {/* Local Business Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(localBusinessData) }}
      />
      
      {/* Organization Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(organizationData) }}
      />
      
      {/* Service Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(serviceData) }}
      />
      
      {/* Professional Service Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(professionalServiceData) }}
      />
      
      {/* Website Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(websiteData) }}
      />
    </>
  );
};

export default StructuredData;
