import React from "react";
import { ArrowRight } from 'lucide-react';
import Image from 'next/image';

const MediaItem = ({ linkUrl, image, title, description, date, isReversed }) => {
  return (
    <div className={`flex flex-col lg:flex-row items-start lg:items-center gap-6 md:gap-8 lg:gap-12 ${isReversed ? 'lg:flex-row-reverse' : ''} mb-12 md:mb-16 lg:mb-20`}>
     
      <div className="w-full lg:flex-1">
        <div className="relative w-full aspect-video rounded-lg overflow-hidden shadow-lg">
          <Image 
            src={image} 
            alt={title} 
            className="w-full h-full object-cover transition-transform duration-300 hover:scale-105" 
            width={640}
            height={360}
          />
        </div>
      </div>
 
      <div className="w-full lg:flex-1 space-y-3 md:space-y-4">
        <div className="inline-block bg-green-500 text-white text-xs font-medium px-3 py-1.5 rounded-full">
          {date}
        </div>
        
        <h2 className="text-lg sm:text-xl md:text-2xl lg:text-3xl font-semibold text-gray-900 leading-tight">
          {title}
        </h2>
        
        <p className="text-gray-600 text-sm sm:text-base md:text-base lg:text-lg leading-relaxed">
          {description}
        </p>
        
        <div className="pt-2">
          <a 
            href={linkUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center gap-2 bg-blue-600 text-white text-sm md:text-base font-medium px-4 py-2.5 md:px-6 md:py-3 rounded-md hover:bg-blue-700 focus:ring-4 focus:ring-blue-200 transition-all duration-300 transform hover:scale-105"
          >
            Read More
            <ArrowRight size={16} className="transition-transform duration-300 group-hover:translate-x-1" />
          </a>
        </div>
      </div>
    </div>
  );
};

const PressMediaSection = () => {
  const mediaData = [
    {
      linkUrl: "https://businessdesk.co.nz/article/technology/the-business-of-tech-podcast-startups-the-budget-and-bounties-for-bugs",
      image: "/images/webinar1.png",
      title: "The Business of Tech podcast: Startups, the budget, and bounties for bugs.",
      description: "Capture the Bug founder and CEO Ankita Dhakar explains why businesses should be putting out rewards for finders of flaws in their systems.",
      date: "25 May 2023",
      isReversed: false
    },
    {
      linkUrl: "https://nzentrepreneur.co.nz/capture-the-bug/",
      image: "/images/webinar2.png",
      title: "How is Capture The Bug helping high growth tech companies in New Zealand?",
      description: "Capture the Bug founder and CEO Ankita Dhakar explains why businesses should be putting out rewards for finders of flaws as Capture The Bug, we're revolutionising cybersecurity in ANZ with our cutting-edge Bug Bounty and Pentesting as a Service (PTaaS) performance in their systems.",
      date: "20 Sept 2023",
      isReversed: true
    },
    {
      linkUrl: "https://www.youtube.com/watch?v=8TRtJdxhEcU",
      image: "/images/webinar3.png",
      title: "The Business of Tech podcast: Startups, the budget, and bounties for bugs.",
      description: "Capture the Bug founder and CEO Ankita Dhakar explains why businesses should be putting out rewards for finders of flaws in their systems.",
      date: "26 Sept 2023",
      isReversed: false
    }
  ];

  return (
    <section className="bg-white py-12 md:py-16 lg:py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="space-y-8 md:space-y-12 lg:space-y-16">
          {mediaData.map((item, index) => (
            <MediaItem key={index} {...item} />
          ))}
        </div>
      </div>
    </section>
  );
};

export default PressMediaSection;