// src/app/layout.js

import { <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import Header from "@/app/common/Header";
import Footer from "@/app/common/Footer";

// Configure the Poppins font for headings
const poppins = Poppins({
  subsets: ["latin"],
  display: "swap",
  weight: ["100", "200", "300", "400", "500", "600", "700", "800", "900"],
  variable: "--font-poppins",
});

// Configure the Roboto font for paragraphs
const roboto = Roboto({
  subsets: ["latin"],
  display: "swap",
  weight: ["100", "300", "400", "500", "700", "900"],
  variable: "--font-roboto",
});

export const metadata = {
  metadataBase: new URL(process.env.NEXT_PUBLIC_SITE_URL || 'https://securitylit.com'),
  title: "Premier Cybersecurity Consulting & VAPT Services | SecurityLit",
  description: "SecurityLit offers expert VAPT, compliance, and cloud security consulting to protect businesses in Gurugram, Noida, Mumbai, and Bangalore from digital threats.",
  keywords: [
    "cybersecurity consulting",
    "VAPT services",
    "penetration testing",
    "vulnerability assessment",
    "cloud security",
    "compliance assessment",
    "red teaming",
    "virtual CISO",
    "security audit",
    "India cybersecurity"
  ],
  authors: [{ name: "SecurityLit Team" }],
  creator: "SecurityLit",
  publisher: "SecurityLit",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "/",
    title: "Premier Cybersecurity Consulting & VAPT Services | SecurityLit",
    description: "SecurityLit offers expert VAPT, compliance, and cloud security consulting to protect businesses in Gurugram, Noida, Mumbai, and Bangalore from digital threats.",
    siteName: "SecurityLit",
    images: [
      {
        url: "/og-image.png",
        width: 1200,
        height: 630,
        alt: "SecurityLit - Premier Cybersecurity Consulting Services",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Premier Cybersecurity Consulting & VAPT Services | SecurityLit",
    description: "SecurityLit offers expert VAPT, compliance, and cloud security consulting to protect businesses in Gurugram, Noida, Mumbai, and Bangalore from digital threats.",
    creator: "@security_lit",
    images: ["/twitter-image.png"],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  verification: {
    google: process.env.GOOGLE_SITE_VERIFICATION,
  },
};

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="theme-color" content="#3265fe" />
      </head>
      <body className={`${poppins.variable} ${roboto.variable} antialiased`}>
        <Header />
        <main>{children}</main>
        <Footer />
      </body>
    </html>
  );
}
