{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/common/components/BreadcrumbNavigation.jsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState } from 'react';\r\nimport Link from 'next/link';\r\nimport { ChevronRight, Home, FileText, Users, Settings, MapPin, Shield, Code, Smartphone, Globe, DollarSign, Building, Phone, Download } from 'lucide-react';\r\nimport { motion } from 'framer-motion';\r\n\r\n/**\r\n * Truncated Text Component for Mobile Breadcrumbs\r\n * Shows truncated text on mobile with click to expand functionality\r\n */\r\nconst TruncatedText = ({ text, maxLength = 25, onExpansionChange }) => {\r\n  const [isExpanded, setIsExpanded] = useState(false);\r\n\r\n  const handleToggle = () => {\r\n    const newExpanded = !isExpanded;\r\n    setIsExpanded(newExpanded);\r\n    if (onExpansionChange) {\r\n      onExpansionChange(newExpanded);\r\n    }\r\n  };\r\n\r\n  if (text.length <= maxLength) {\r\n    return <span>{text}</span>;\r\n  }\r\n\r\n  return (\r\n    <span\r\n      className=\"cursor-pointer\"\r\n      onClick={handleToggle}\r\n      title={isExpanded ? \"Click to collapse\" : \"Click to expand\"}\r\n    >\r\n      <span className=\"sm:hidden\">\r\n        {isExpanded ? text : `${text.substring(0, maxLength)}...`}\r\n      </span>\r\n      <span className=\"hidden sm:inline\">\r\n        {text}\r\n      </span>\r\n    </span>\r\n  );\r\n};\r\n\r\n/**\r\n * Structured Data Component for SEO\r\n */\r\nconst BreadcrumbStructuredData = ({ items }) => {\r\n  const structuredData = {\r\n    \"@context\": \"https://schema.org\",\r\n    \"@type\": \"BreadcrumbList\",\r\n    \"itemListElement\": items.map((item, index) => ({\r\n      \"@type\": \"ListItem\",\r\n      \"position\": index + 1,\r\n      \"name\": item.name,\r\n      \"item\": item.current ? undefined : `https://securitylit.com${item.url}`\r\n    }))\r\n  };\r\n\r\n  return (\r\n    <script\r\n      type=\"application/ld+json\"\r\n      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}\r\n    />\r\n  );\r\n};\r\n\r\n/**\r\n * Universal Breadcrumb Navigation Component with SEO structured data\r\n * Provides clear navigation hierarchy and improves SEO across all pages\r\n */\r\nconst BreadcrumbNavigation = ({ items, className = \"\" }) => {\r\n  const [hasExpandedText, setHasExpandedText] = useState(false);\r\n\r\n  // Check if this is a blog page for conditional padding\r\n  const isBlogPage = items?.some(item =>\r\n    item.url?.includes('/Blogs') ||\r\n    item.iconKey === 'blogs' ||\r\n    className?.includes('blog')\r\n  );\r\n\r\n  return (\r\n    <>\r\n      {/* Structured Data for SEO */}\r\n      <BreadcrumbStructuredData items={items} />\r\n      \r\n      {/* Simple Breadcrumb Navigation */}\r\n      <nav\r\n        className={`bg-transparent py-2 sm:py-3 ${\r\n          isBlogPage\r\n            ? hasExpandedText\r\n              ? 'pb-6 sm:pb-4'\r\n              : 'pb-3 sm:pb-4'\r\n            : 'pb-2 sm:pb-3'\r\n        } ${className}`}\r\n        aria-label=\"Breadcrumb navigation\"\r\n      >\r\n        <div className=\"max-w-7xl mx-auto px-4\">\r\n          <motion.ol\r\n            className={`flex items-center space-x-1 text-xs sm:text-sm ${\r\n              className?.includes('text-white')\r\n                ? 'text-white'\r\n                : className?.includes('text-blue-600')\r\n                ? 'text-blue-600'\r\n                : 'text-gray-600'\r\n            }`}\r\n            initial={{ opacity: 0 }}\r\n            animate={{ opacity: 1 }}\r\n            transition={{ duration: 0.4 }}\r\n          >\r\n            {items.map((item, index) => (\r\n              <motion.li\r\n                key={index}\r\n                className=\"flex items-center\"\r\n                initial={{ opacity: 0 }}\r\n                animate={{ opacity: 1 }}\r\n                transition={{ duration: 0.3, delay: index * 0.1 }}\r\n              >\r\n                {index > 0 && (\r\n                  <ChevronRight\r\n                    className={`w-3 h-3 mx-1 ${\r\n                      className?.includes('text-white')\r\n                        ? 'text-white/70'\r\n                        : className?.includes('text-blue-600')\r\n                        ? 'text-blue-400'\r\n                        : 'text-gray-400'\r\n                    }`}\r\n                    aria-hidden=\"true\"\r\n                  />\r\n                )}\r\n\r\n                {item.current ? (\r\n                  <span\r\n                    className={`font-medium flex items-center gap-1 ${\r\n                      className?.includes('text-white')\r\n                        ? 'text-white'\r\n                        : className?.includes('text-blue-600')\r\n                        ? 'text-blue-600'\r\n                        : 'text-gray-900'\r\n                    }`}\r\n                    aria-current=\"page\"\r\n                    title={item.description}\r\n                  >\r\n                    {item.iconKey === 'home' && (\r\n                      <Home className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-blue-600'\r\n                      }`} />\r\n                    )}\r\n                    {item.iconKey === 'blogs' && (\r\n                      <FileText className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-blue-600'\r\n                      }`} />\r\n                    )}\r\n                    {item.iconKey === 'contact-us' && (\r\n                      <Phone className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-blue-600'\r\n                      }`} />\r\n                    )}\r\n                    <TruncatedText\r\n                      text={item.name}\r\n                      onExpansionChange={setHasExpandedText}\r\n                    />\r\n                  </span>\r\n                ) : (\r\n                  <Link\r\n                    href={item.url}\r\n                    className={`transition-colors duration-200 flex items-center gap-1 hover:underline ${\r\n                      className?.includes('text-white')\r\n                        ? 'text-white hover:text-white/80'\r\n                        : className?.includes('text-blue-600')\r\n                        ? 'text-blue-600 hover:text-blue-800'\r\n                        : 'text-blue-600 hover:text-blue-800'\r\n                    }`}\r\n                    title={item.description}\r\n                  >\r\n                    {item.iconKey === 'home' && (\r\n                      <Home className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-current'\r\n                      }`} />\r\n                    )}\r\n                    {item.iconKey === 'blogs' && (\r\n                      <FileText className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-current'\r\n                      }`} />\r\n                    )}\r\n                    {item.iconKey === 'contact-us' && (\r\n                      <Phone className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-current'\r\n                      }`} />\r\n                    )}\r\n                    <TruncatedText\r\n                      text={item.name}\r\n                      onExpansionChange={setHasExpandedText}\r\n                    />\r\n                  </Link>\r\n                )}\r\n              </motion.li>\r\n            ))}\r\n          </motion.ol>\r\n        </div>\r\n      </nav>\r\n    </>\r\n  );\r\n};\r\n\r\n// Client-side helper function for generating breadcrumb items\r\nexport const generateBreadcrumbs = (pageType, params = {}) => {\r\n  const baseBreadcrumb = {\r\n    name: \"Home\",\r\n    url: \"/\",\r\n    iconKey: \"home\",\r\n    description: \"Return to homepage\"\r\n  };\r\n\r\n  const breadcrumbConfigs = {\r\n    blog: (slug) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Blog\",\r\n        url: \"/Blogs\",\r\n        iconKey: \"blogs\",\r\n        description: \"View all blog posts\"\r\n      },\r\n      {\r\n        name: params.title || \"Blog Post\",\r\n        url: `/Blogs/${slug}`,\r\n        current: true,\r\n        description: `${params.title || 'Blog post'} - Cybersecurity insights and penetration testing guidance`\r\n      }\r\n    ],\r\n    \r\n    service: (service) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Services\",\r\n        url: \"/Services\",\r\n        iconKey: \"services\",\r\n        description: \"View all cybersecurity services\"\r\n      },\r\n      {\r\n        name: params.title || service,\r\n        url: `/Services/${service}`,\r\n        current: true,\r\n        iconKey: service,\r\n        description: `${params.title || service} penetration testing services`\r\n      }\r\n    ],\r\n\r\n    location: (country) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Locations\",\r\n        url: \"/Locations\",\r\n        iconKey: \"locations\",\r\n        description: \"View all locations\"\r\n      },\r\n      {\r\n        name: params.countryName || country,\r\n        url: `/Locations/${country}`,\r\n        current: true,\r\n        description: `${params.countryName || country} cybersecurity services`\r\n      }\r\n    ],\r\n\r\n    company: (page) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Company\",\r\n        url: \"/Company\",\r\n        iconKey: \"company\",\r\n        description: \"Learn about SecurityLit\"\r\n      },\r\n      {\r\n        name: params.title || page,\r\n        url: `/Company/${page}`,\r\n        current: true,\r\n        description: params.description || `${page} information`\r\n      }\r\n    ],\r\n\r\n    'company-size': (size) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Company Size\",\r\n        url: \"/Company-size\",\r\n        iconKey: \"company\",\r\n        description: \"Solutions for different company sizes\"\r\n      },\r\n      {\r\n        name: params.title || size,\r\n        url: `/Company-size/${size}`,\r\n        current: true,\r\n        description: params.description || `${size} cybersecurity solutions`\r\n      }\r\n    ],\r\n\r\n    industry: (industry) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Industries\",\r\n        url: \"/Industries\",\r\n        iconKey: \"company\",\r\n        description: \"Industry-specific cybersecurity solutions\"\r\n      },\r\n      {\r\n        name: params.title || industry,\r\n        url: `/Industries/${industry}`,\r\n        current: true,\r\n        description: params.description || `${industry} cybersecurity solutions`\r\n      }\r\n    ],\r\n\r\n    product: (product) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Product\",\r\n        url: \"/Product\",\r\n        iconKey: \"product\",\r\n        description: \"Explore our cybersecurity products\"\r\n      },\r\n      {\r\n        name: params.title || product,\r\n        url: `/Product/${product}`,\r\n        current: true,\r\n        description: params.description || `${product} product information`\r\n      }\r\n    ],\r\n\r\n    simple: (pageName, url) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: pageName,\r\n        url: url,\r\n        current: true,\r\n        iconKey: params.iconKey,\r\n        description: params.description || `${pageName} page`\r\n      }\r\n    ]\r\n  };\r\n\r\n  return breadcrumbConfigs[pageType] || (() => [baseBreadcrumb]);\r\n};\r\n\r\nexport default BreadcrumbNavigation; "], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;;AAMA;;;CAGC,GACD,MAAM,gBAAgB,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,EAAE,iBAAiB,EAAE;IAChE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,eAAe;QACnB,MAAM,cAAc,CAAC;QACrB,cAAc;QACd,IAAI,mBAAmB;YACrB,kBAAkB;QACpB;IACF;IAEA,IAAI,KAAK,MAAM,IAAI,WAAW;QAC5B,qBAAO,8OAAC;sBAAM;;;;;;IAChB;IAEA,qBACE,8OAAC;QACC,WAAU;QACV,SAAS;QACT,OAAO,aAAa,sBAAsB;;0BAE1C,8OAAC;gBAAK,WAAU;0BACb,aAAa,OAAO,GAAG,KAAK,SAAS,CAAC,GAAG,WAAW,GAAG,CAAC;;;;;;0BAE3D,8OAAC;gBAAK,WAAU;0BACb;;;;;;;;;;;;AAIT;AAEA;;CAEC,GACD,MAAM,2BAA2B,CAAC,EAAE,KAAK,EAAE;IACzC,MAAM,iBAAiB;QACrB,YAAY;QACZ,SAAS;QACT,mBAAmB,MAAM,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;gBAC7C,SAAS;gBACT,YAAY,QAAQ;gBACpB,QAAQ,KAAK,IAAI;gBACjB,QAAQ,KAAK,OAAO,GAAG,YAAY,CAAC,uBAAuB,EAAE,KAAK,GAAG,EAAE;YACzE,CAAC;IACH;IAEA,qBACE,8OAAC;QACC,MAAK;QACL,yBAAyB;YAAE,QAAQ,KAAK,SAAS,CAAC;QAAgB;;;;;;AAGxE;AAEA;;;CAGC,GACD,MAAM,uBAAuB,CAAC,EAAE,KAAK,EAAE,YAAY,EAAE,EAAE;IACrD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,uDAAuD;IACvD,MAAM,aAAa,OAAO,KAAK,CAAA,OAC7B,KAAK,GAAG,EAAE,SAAS,aACnB,KAAK,OAAO,KAAK,WACjB,WAAW,SAAS;IAGtB,qBACE;;0BAEE,8OAAC;gBAAyB,OAAO;;;;;;0BAGjC,8OAAC;gBACC,WAAW,CAAC,4BAA4B,EACtC,aACI,kBACE,iBACA,iBACF,eACL,CAAC,EAAE,WAAW;gBACf,cAAW;0BAEX,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;wBACR,WAAW,CAAC,+CAA+C,EACzD,WAAW,SAAS,gBAChB,eACA,WAAW,SAAS,mBACpB,kBACA,iBACJ;wBACF,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,YAAY;4BAAE,UAAU;wBAAI;kCAE3B,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;gCAER,WAAU;gCACV,SAAS;oCAAE,SAAS;gCAAE;gCACtB,SAAS;oCAAE,SAAS;gCAAE;gCACtB,YAAY;oCAAE,UAAU;oCAAK,OAAO,QAAQ;gCAAI;;oCAE/C,QAAQ,mBACP,8OAAC,sNAAA,CAAA,eAAY;wCACX,WAAW,CAAC,aAAa,EACvB,WAAW,SAAS,gBAChB,kBACA,WAAW,SAAS,mBACpB,kBACA,iBACJ;wCACF,eAAY;;;;;;oCAIf,KAAK,OAAO,iBACX,8OAAC;wCACC,WAAW,CAAC,oCAAoC,EAC9C,WAAW,SAAS,gBAChB,eACA,WAAW,SAAS,mBACpB,kBACA,iBACJ;wCACF,gBAAa;wCACb,OAAO,KAAK,WAAW;;4CAEtB,KAAK,OAAO,KAAK,wBAChB,8OAAC,mMAAA,CAAA,OAAI;gDAAC,WAAW,CAAC,QAAQ,EACxB,WAAW,SAAS,gBAChB,eACA,WAAW,SAAS,mBACpB,kBACA,iBACJ;;;;;;4CAEH,KAAK,OAAO,KAAK,yBAChB,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAW,CAAC,QAAQ,EAC5B,WAAW,SAAS,gBAChB,eACA,WAAW,SAAS,mBACpB,kBACA,iBACJ;;;;;;4CAEH,KAAK,OAAO,KAAK,8BAChB,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAW,CAAC,QAAQ,EACzB,WAAW,SAAS,gBAChB,eACA,WAAW,SAAS,mBACpB,kBACA,iBACJ;;;;;;0DAEJ,8OAAC;gDACC,MAAM,KAAK,IAAI;gDACf,mBAAmB;;;;;;;;;;;iGAIvB,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAM,KAAK,GAAG;wCACd,WAAW,CAAC,uEAAuE,EACjF,WAAW,SAAS,gBAChB,mCACA,WAAW,SAAS,mBACpB,sCACA,qCACJ;wCACF,OAAO,KAAK,WAAW;;4CAEtB,KAAK,OAAO,KAAK,wBAChB,8OAAC,mMAAA,CAAA,OAAI;gDAAC,WAAW,CAAC,QAAQ,EACxB,WAAW,SAAS,gBAChB,eACA,WAAW,SAAS,mBACpB,kBACA,gBACJ;;;;;;4CAEH,KAAK,OAAO,KAAK,yBAChB,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAW,CAAC,QAAQ,EAC5B,WAAW,SAAS,gBAChB,eACA,WAAW,SAAS,mBACpB,kBACA,gBACJ;;;;;;4CAEH,KAAK,OAAO,KAAK,8BAChB,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAW,CAAC,QAAQ,EACzB,WAAW,SAAS,gBAChB,eACA,WAAW,SAAS,mBACpB,kBACA,gBACJ;;;;;;0DAEJ,8OAAC;gDACC,MAAM,KAAK,IAAI;gDACf,mBAAmB;;;;;;;;;;;;;+BAxGpB;;;;;;;;;;;;;;;;;;;;;;AAmHrB;AAGO,MAAM,sBAAsB,CAAC,UAAU,SAAS,CAAC,CAAC;IACvD,MAAM,iBAAiB;QACrB,MAAM;QACN,KAAK;QACL,SAAS;QACT,aAAa;IACf;IAEA,MAAM,oBAAoB;QACxB,MAAM,CAAC,OAAS;gBACd;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,CAAC,OAAO,EAAE,MAAM;oBACrB,SAAS;oBACT,aAAa,GAAG,OAAO,KAAK,IAAI,YAAY,0DAA0D,CAAC;gBACzG;aACD;QAED,SAAS,CAAC,UAAY;gBACpB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,CAAC,UAAU,EAAE,SAAS;oBAC3B,SAAS;oBACT,SAAS;oBACT,aAAa,GAAG,OAAO,KAAK,IAAI,QAAQ,6BAA6B,CAAC;gBACxE;aACD;QAED,UAAU,CAAC,UAAY;gBACrB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,WAAW,IAAI;oBAC5B,KAAK,CAAC,WAAW,EAAE,SAAS;oBAC5B,SAAS;oBACT,aAAa,GAAG,OAAO,WAAW,IAAI,QAAQ,uBAAuB,CAAC;gBACxE;aACD;QAED,SAAS,CAAC,OAAS;gBACjB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,CAAC,SAAS,EAAE,MAAM;oBACvB,SAAS;oBACT,aAAa,OAAO,WAAW,IAAI,GAAG,KAAK,YAAY,CAAC;gBAC1D;aACD;QAED,gBAAgB,CAAC,OAAS;gBACxB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,CAAC,cAAc,EAAE,MAAM;oBAC5B,SAAS;oBACT,aAAa,OAAO,WAAW,IAAI,GAAG,KAAK,wBAAwB,CAAC;gBACtE;aACD;QAED,UAAU,CAAC,WAAa;gBACtB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,CAAC,YAAY,EAAE,UAAU;oBAC9B,SAAS;oBACT,aAAa,OAAO,WAAW,IAAI,GAAG,SAAS,wBAAwB,CAAC;gBAC1E;aACD;QAED,SAAS,CAAC,UAAY;gBACpB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,CAAC,SAAS,EAAE,SAAS;oBAC1B,SAAS;oBACT,aAAa,OAAO,WAAW,IAAI,GAAG,QAAQ,oBAAoB,CAAC;gBACrE;aACD;QAED,QAAQ,CAAC,UAAU,MAAQ;gBACzB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,SAAS,OAAO,OAAO;oBACvB,aAAa,OAAO,WAAW,IAAI,GAAG,SAAS,KAAK,CAAC;gBACvD;aACD;IACH;IAEA,OAAO,iBAAiB,CAAC,SAAS,IAAI,CAAC,IAAM;YAAC;SAAe;AAC/D;uCAEe", "debugId": null}}, {"offset": {"line": 386, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/training/components/HeroBento.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport { Shield, Play, ArrowRight, GraduationCap, CheckCircle, Users, Star, Clock, Award } from 'lucide-react';\r\nimport BreadcrumbNavigation from '../../common/components/BreadcrumbNavigation';\r\n\r\nexport default function HeroBento({ content }) {\r\n  // Default content for backward compatibility\r\n  const defaultContent = {\r\n    breadcrumbItems: [\r\n      {\r\n        name: \"Home\",\r\n        url: \"/\",\r\n        iconKey: \"home\",\r\n        description: \"Return to homepage\"\r\n      },\r\n      {\r\n        name: \"Training\",\r\n        url: \"/training\",\r\n        current: true,\r\n        iconKey: \"graduation-cap\",\r\n        description: \"Explore SecurityLit's cybersecurity training programs\"\r\n      }\r\n    ],\r\n    title: \"Elite Security Training\",\r\n    subtitle: \"Launch Your Cyber Security Career\",\r\n    tagline: \"Free and Premium Pathways\",\r\n    description: \"Dive into the world of penetration testing with our refined program. Designed for aspiring security professionals with complete pentesting skills and real-time live projects.\",\r\n    keyBenefits: [\r\n      \"Complete pentesting skills with real-time live projects\",\r\n      \"Latest tools and topics in cybersecurity\",\r\n      \"Free and premium pathways available\"\r\n    ],\r\n    buttons: [\r\n      {\r\n        text: \"Start Free Training\",\r\n        href: \"#form\",\r\n        primary: true\r\n      },\r\n      {\r\n        text: \"Upgrade to Premium\",\r\n        href: \"#premium\",\r\n        primary: false\r\n      }\r\n    ],\r\n    heroImage: \"/images/p1s1.png\"\r\n  };\r\n\r\n  const heroContent = content || defaultContent;\r\n  const { breadcrumbItems, title, subtitle, tagline, description, keyBenefits, buttons, heroImage, sectionTitle, secondaryImage } = heroContent;\r\n\r\n  const trustStats = [\r\n    { value: \"500+\", label: \"Students Trained\", icon: Users },\r\n    { value: \"98%\", label: \"Success Rate\", icon: Star },\r\n    { value: \"83\", label: \"Total Lessons\", icon: Clock },\r\n    { value: \"11\", label: \"Course Sections\", icon: Award }\r\n  ];\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-white\">\r\n      <div className=\"flex flex-col lg:flex-row min-h-screen relative\">\r\n\r\n        {/* Left Section - Optimized Content with Rounded Corner */}\r\n        <div className=\"w-full lg:w-1/2 bg-gradient-to-br from-[var(--color-dark-blue)] to-[var(--color-dark-blue-hover)] relative overflow-hidden lg:rounded-br-[100px]\">\r\n          {/* Background Pattern */}\r\n          <div className=\"absolute inset-0 bg-cover bg-center bg-no-repeat opacity-10\"\r\n               style={{ backgroundImage: 'url(/images/hexagon.svg)' }}>\r\n          </div>\r\n          <div className=\"absolute inset-0 bg-[var(--color-dark-blue)]/90 lg:rounded-br-[100px]\"></div>\r\n          \r\n          <div className=\"relative z-10 flex justify-center px-4 sm:px-6 lg:px-12 py-8 sm:py-12 lg:pt-20 lg:pb-8 min-h-screen lg:h-full\">\r\n            <div className=\"max-w-lg w-full flex flex-col justify-center\">\r\n              {/* Breadcrumb */}\r\n              <div className=\"mb-4 mt-2 lg:mt-0\">\r\n                <BreadcrumbNavigation items={breadcrumbItems} className=\"text-white\" />\r\n              </div>\r\n\r\n              {/* Main Heading */}\r\n              <h1 className=\"text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-4 leading-tight font-poppins\">\r\n                {title.includes('Security') ? (\r\n                  <>\r\n                    Elite Security\r\n                    <span className=\"block bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] bg-clip-text text-transparent\">\r\n                      Training\r\n                    </span>\r\n                  </>\r\n                ) : (\r\n                  title\r\n                )}\r\n              </h1>\r\n\r\n              {/* Subtitle */}\r\n              {subtitle && (\r\n                <h2 className=\"text-lg sm:text-xl lg:text-2xl font-bold text-white mb-6 leading-tight font-poppins\">\r\n                  {subtitle}\r\n                </h2>\r\n              )}\r\n\r\n              {/* Description */}\r\n              <p className=\"text-sm sm:text-base text-white/90 mb-8 leading-relaxed font-roboto\">\r\n                Dive Into The World Of Penetration Testing With Our Refined Program\r\n              </p>\r\n\r\n              {/* CTA Buttons */}\r\n              <div className=\"flex flex-col gap-3\">\r\n                <a\r\n                  href=\"#form\"\r\n                  className=\"bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] text-white px-6 py-3 rounded-lg font-semibold text-base transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center gap-2 font-poppins\"\r\n                >\r\n                  Enroll Now\r\n                </a>\r\n\r\n                <a\r\n                  href=\"/CyberSecTraining\"\r\n                  className=\"bg-white/10 backdrop-blur-sm text-white border-2 border-white/20 hover:bg-white/20 px-6 py-3 rounded-lg font-semibold text-base transition-all duration-300 flex items-center justify-center gap-2 font-poppins\"\r\n                >\r\n                  Program Details\r\n                </a>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n\r\n\r\n        {/* Right Section - Enhanced Visual Focus */}\r\n        <div className=\"w-full lg:w-1/2 bg-white\">\r\n          <div className=\"max-w-2xl mx-auto px-4 sm:px-6 lg:px-12 py-8 sm:py-12 lg:pt-20 lg:pb-8 min-h-screen lg:h-full flex flex-col justify-center\">\r\n            \r\n            {/* Enhanced Hero Visual */}\r\n            <motion.div \r\n              initial={{ opacity: 0, scale: 0.9 }}\r\n              animate={{ opacity: 1, scale: 1 }}\r\n              transition={{ duration: 1, delay: 0.3 }}\r\n              className=\"relative mb-6\"\r\n            >\r\n              <div className=\"relative bg-gradient-to-br from-[var(--color-blue)]/10 to-[var(--color-blue-secondary)]/10 rounded-2xl p-4 sm:p-6 lg:p-8 border-2 border-[var(--color-blue)]/20\">\r\n                <div className=\"absolute inset-0 bg-gradient-to-br from-[var(--color-blue)]/5 to-[var(--color-blue-secondary)]/5 rounded-2xl\"></div>\r\n                \r\n                <div className=\"relative z-10 text-center\">\r\n                  <div className=\"w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-xl flex items-center justify-center mx-auto mb-4 shadow-lg\">\r\n                    <img\r\n                      src=\"/SecurityLit_Icon_White.png\"\r\n                      alt=\"SecurityLit Logo\"\r\n                      className=\"w-8 h-8 sm:w-12 sm:h-12 object-contain\"\r\n                    />\r\n                  </div>\r\n\r\n                  <h3 className=\"text-lg sm:text-xl font-bold text-[var(--color-dark-blue)] mb-2\">\r\n                    SecurityLit Presents\r\n                  </h3>\r\n                  <p className=\"text-[var(--foreground-secondary)] text-sm sm:text-base\">\r\n                    Professional cybersecurity training designed by industry experts\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            </motion.div>\r\n\r\n            {/* Trust Indicators */}\r\n            <div className=\"grid grid-cols-2 gap-3 sm:gap-4 mb-6\">\r\n              {trustStats.map((stat, index) => (\r\n                <div key={index} className=\"text-center\">\r\n                  <div className=\"w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-br from-[var(--color-blue)]/10 to-[var(--color-blue-secondary)]/10 rounded-lg flex items-center justify-center mx-auto mb-2\">\r\n                    <stat.icon className=\"w-4 h-4 sm:w-5 sm:h-5 text-[var(--color-blue)]\" />\r\n                  </div>\r\n                  <div className=\"text-xl sm:text-2xl lg:text-3xl font-bold text-[var(--color-dark-blue)] mb-1 font-poppins\">\r\n                    {stat.value}\r\n                  </div>\r\n                  <div className=\"text-[var(--foreground-secondary)] text-xs font-medium font-roboto\">\r\n                    {stat.label}\r\n                  </div>\r\n                </div>\r\n              ))}\r\n            </div>\r\n\r\n\r\n\r\n            {secondaryImage && (\r\n              <div className=\"text-center mt-6\">\r\n                <img\r\n                  src={secondaryImage}\r\n                  alt=\"Cybersecurity Expert\"\r\n                  className=\"w-full max-w-md mx-auto rounded-lg\"\r\n                />\r\n              </div>\r\n            )}\r\n\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AALA;;;;;;AAOe,SAAS,UAAU,EAAE,OAAO,EAAE;IAC3C,6CAA6C;IAC7C,MAAM,iBAAiB;QACrB,iBAAiB;YACf;gBACE,MAAM;gBACN,KAAK;gBACL,SAAS;gBACT,aAAa;YACf;YACA;gBACE,MAAM;gBACN,KAAK;gBACL,SAAS;gBACT,SAAS;gBACT,aAAa;YACf;SACD;QACD,OAAO;QACP,UAAU;QACV,SAAS;QACT,aAAa;QACb,aAAa;YACX;YACA;YACA;SACD;QACD,SAAS;YACP;gBACE,MAAM;gBACN,MAAM;gBACN,SAAS;YACX;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,SAAS;YACX;SACD;QACD,WAAW;IACb;IAEA,MAAM,cAAc,WAAW;IAC/B,MAAM,EAAE,eAAe,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,SAAS,EAAE,YAAY,EAAE,cAAc,EAAE,GAAG;IAElI,MAAM,aAAa;QACjB;YAAE,OAAO;YAAQ,OAAO;YAAoB,MAAM,oMAAA,CAAA,QAAK;QAAC;QACxD;YAAE,OAAO;YAAO,OAAO;YAAgB,MAAM,kMAAA,CAAA,OAAI;QAAC;QAClD;YAAE,OAAO;YAAM,OAAO;YAAiB,MAAM,oMAAA,CAAA,QAAK;QAAC;QACnD;YAAE,OAAO;YAAM,OAAO;YAAmB,MAAM,oMAAA,CAAA,QAAK;QAAC;KACtD;IAED,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAGb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;4BACV,OAAO;gCAAE,iBAAiB;4BAA2B;;;;;;sCAE1D,8OAAC;4BAAI,WAAU;;;;;;sCAEf,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,2JAAA,CAAA,UAAoB;4CAAC,OAAO;4CAAiB,WAAU;;;;;;;;;;;kDAI1D,8OAAC;wCAAG,WAAU;kDACX,MAAM,QAAQ,CAAC,4BACd;;gDAAE;8DAEA,8OAAC;oDAAK,WAAU;8DAAiH;;;;;;;2DAKnI;;;;;;oCAKH,0BACC,8OAAC;wCAAG,WAAU;kDACX;;;;;;kDAKL,8OAAC;wCAAE,WAAU;kDAAsE;;;;;;kDAKnF,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,MAAK;gDACL,WAAU;0DACX;;;;;;0DAID,8OAAC;gDACC,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAWT,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAGb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAG,OAAO;gCAAI;gCACtC,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDAEf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDACC,KAAI;wDACJ,KAAI;wDACJ,WAAU;;;;;;;;;;;8DAId,8OAAC;oDAAG,WAAU;8DAAkE;;;;;;8DAGhF,8OAAC;oDAAE,WAAU;8DAA0D;;;;;;;;;;;;;;;;;;;;;;;0CAQ7E,8OAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,MAAM,sBACrB,8OAAC;wCAAgB,WAAU;;0DACzB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;;;;;;0DAEvB,8OAAC;gDAAI,WAAU;0DACZ,KAAK,KAAK;;;;;;0DAEb,8OAAC;gDAAI,WAAU;0DACZ,KAAK,KAAK;;;;;;;uCARL;;;;;;;;;;4BAgBb,gCACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,KAAK;oCACL,KAAI;oCACJ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU5B", "debugId": null}}, {"offset": {"line": 763, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/training/components/CurriculumHighlights.jsx"], "sourcesContent": ["\"use client\";\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { BookOpen, Brain, Code, Compass, Target, Lock, Zap, Users, Shield, ArrowRight, Star, Clock, Award } from 'lucide-react';\nimport { PrimaryButton } from '../../common/buttons/BrandButtons';\n\nconst curriculumItems = [\n  {\n    icon: BookOpen,\n    title: \"Advanced Assessment\",\n    description: \"Master penetration testing methodologies and tools\",\n    color: \"from-[var(--color-blue)] to-[var(--color-blue-secondary)]\",\n    delay: 0.1,\n    size: \"large\",\n    position: \"top-left\",\n    features: [\"OWASP Top 10\", \"Vulnerability Assessment\", \"Report Writing\"]\n  },\n  {\n    icon: Brain,\n    title: \"API Security\",\n    description: \"Learn to secure REST APIs and GraphQL endpoints\",\n    color: \"from-[var(--color-dark-blue)] to-[var(--color-dark-blue-hover)]\",\n    delay: 0.2,\n    size: \"medium\",\n    position: \"top-right\",\n    features: [\"Authentication\", \"Authorization\", \"Rate Limiting\"]\n  },\n  {\n    icon: Code,\n    title: \"Hands-on Labs\",\n    description: \"Real-world experience with live environments\",\n    color: \"from-[var(--color-blue)] to-[var(--color-blue-secondary)]\",\n    delay: 0.3,\n    size: \"medium\",\n    position: \"middle-left\",\n    features: [\"Live Environments\", \"Real Scenarios\", \"Practice Labs\"]\n  },\n  {\n    icon: Compass,\n    title: \"Navigation & Recon\",\n    description: \"Advanced reconnaissance and information gathering\",\n    color: \"from-[var(--color-blue)] to-[var(--color-blue-secondary)]\",\n    delay: 0.4,\n    size: \"small\",\n    position: \"middle-center\",\n    features: [\"OSINT\", \"Network Mapping\", \"Footprinting\"]\n  },\n  {\n    icon: Target,\n    title: \"Role-based Training\",\n    description: \"Specialized paths for different career goals\",\n    color: \"from-[var(--color-dark-blue)] to-[var(--color-dark-blue-subtle)]\",\n    delay: 0.5,\n    size: \"large\",\n    position: \"middle-right\",\n    features: [\"Penetration Tester\", \"Security Analyst\", \"Security Engineer\"]\n  },\n  {\n    icon: Lock,\n    title: \"Authentication & Auth\",\n    description: \"Deep dive into modern authentication systems\",\n    color: \"from-[var(--color-blue)] to-[var(--color-blue-secondary)]\",\n    delay: 0.6,\n    size: \"medium\",\n    position: \"bottom-left\",\n    features: [\"OAuth 2.0\", \"JWT\", \"Multi-Factor Auth\"]\n  }\n];\n\nexport default function CurriculumHighlights() {\n  return (\n    <section className=\"py-12 sm:py-16 lg:py-24 bg-white relative overflow-hidden\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0\">\n        <div className=\"absolute top-20 left-10 w-72 h-72 bg-[var(--color-blue)]/5 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob\"></div>\n        <div className=\"absolute top-40 right-10 w-72 h-72 bg-[var(--color-yellow)]/5 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-2000\"></div>\n        <div className=\"absolute -bottom-8 left-20 w-72 h-72 bg-[var(--color-blue-secondary)]/5 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-4000\"></div>\n      </div>\n\n      <div className=\"relative z-10 container mx-auto px-4 max-w-7xl\">\n        {/* Header */}\n        <motion.div \n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-20\"\n        >\n          <motion.div \n            initial={{ opacity: 0, scale: 0.8 }}\n            whileInView={{ opacity: 1, scale: 1 }}\n            transition={{ delay: 0.2, duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"inline-flex items-center bg-gradient-to-r from-[var(--color-blue)]/10 to-[var(--color-blue-secondary)]/10 px-6 py-3 rounded-full mb-8 border border-[var(--color-blue)]/20\"\n          >\n            <Zap className=\"w-5 h-5 text-[var(--color-blue)] mr-3\" />\n            <span className=\"text-sm font-semibold text-[var(--color-blue)]\">Cybersecurity Training</span>\n          </motion.div>\n          \n          <motion.h2 \n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.4, duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"text-2xl sm:text-3xl lg:text-4xl xl:text-5xl 2xl:text-6xl font-bold text-[var(--color-dark-blue)] mb-8 leading-tight\"\n          >\n            Explore Our\n            <span className=\"block bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] bg-clip-text text-transparent\">\n              Comprehensive Curriculum\n            </span>\n          </motion.h2>\n          \n          <motion.p \n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.6, duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"text-base sm:text-lg lg:text-xl text-[var(--foreground-secondary)] max-w-4xl mx-auto leading-relaxed px-4\"\n          >\n            Dive into essential areas such as web application security, network penetration testing, cloud security, API security,\n            and ethical hacking fundamentals.\n          </motion.p>\n        </motion.div>\n\n        {/* Professional Bento Grid - Fixed Layout */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-12 gap-4 sm:gap-6 mb-12 sm:mb-16 lg:mb-20\" style={{ gridTemplateRows: 'repeat(3, minmax(240px, auto))' }}>\n          {/* Large Card - Advanced Assessment */}\n            <motion.div\n            initial={{ opacity: 0, scale: 0.9 }}\n            whileInView={{ opacity: 1, scale: 1 }}\n            transition={{ delay: 0.1, duration: 0.8 }}\n              viewport={{ once: true }}\n              whileHover={{ \n                y: -8,\n                scale: 1.02,\n              transition: { duration: 0.3 }\n            }}\n            className=\"col-span-1 lg:col-span-8 lg:row-span-2 group relative\"\n          >\n            <div className=\"relative h-full bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-3xl p-6 lg:p-8 overflow-hidden\">\n              {/* Background Pattern */}\n              <div className=\"absolute inset-0 bg-[url('/images/hexagon.svg')] bg-cover opacity-10\"></div>\n              <div className=\"absolute inset-0 bg-gradient-to-br from-[var(--color-blue)]/90 to-[var(--color-blue-secondary)]/90\"></div>\n              \n              <div className=\"relative z-10 h-full flex flex-col justify-between\">\n                <div className=\"flex-1\">\n                  <div className=\"flex items-start gap-4 mb-6\">\n                    <div className=\"w-14 h-14 lg:w-16 lg:h-16 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center flex-shrink-0 shadow-lg\">\n                      <BookOpen className=\"w-7 h-7 lg:w-8 lg:h-8 text-white\" />\n                    </div>\n                    <div className=\"flex-1 min-w-0\">\n                      <h3 className=\"text-2xl lg:text-3xl font-bold text-white drop-shadow mb-3 leading-tight\">Advanced Assessment</h3>\n                      <p className=\"text-white/90 text-base lg:text-lg leading-relaxed\">Master penetration testing methodologies and tools</p>\n                    </div>\n                  </div>\n                  \n                  {/* Image and Features Grid */}\n                  <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6\">\n                    {/* Features */}\n                    <div className=\"space-y-4\">\n                      {curriculumItems[0].features.map((feature, index) => (\n                        <div key={index} className=\"bg-white/15 backdrop-blur-sm rounded-2xl p-4 lg:p-5 border border-white/25 shadow-lg\">\n                          <div className=\"text-white font-semibold text-sm lg:text-base mb-2 leading-tight\">{feature}</div>\n                          <div className=\"text-white/70 text-sm leading-relaxed\">\n                            {feature === \"OWASP Top 10\" && \"Industry-standard web application security risks\"}\n                            {feature === \"Vulnerability Assessment\" && \"Systematic identification and analysis\"}\n                            {feature === \"Report Writing\" && \"Professional documentation and recommendations\"}\n                          </div>\n                        </div>\n                      ))}\n                    </div>\n                    \n                    {/* Image */}\n                    <div className=\"relative\">\n                      <div className=\"relative h-full min-h-[140px] lg:min-h-[180px] bg-white/15 backdrop-blur-sm rounded-2xl overflow-hidden border border-white/25 shadow-lg\">\n                        <div className=\"absolute inset-0 bg-gradient-to-br from-white/5 to-white/10\"></div>\n                        <div className=\"relative z-10 h-full flex items-center justify-center p-4\">\n                          <img \n                            src=\"/images/about-us-image-3.jpg\" \n                            alt=\"Cybersecurity Assessment\" \n                            className=\"w-full h-full object-cover rounded-xl\"\n                          />\n                          <div className=\"absolute inset-0 bg-gradient-to-br from-[var(--color-blue)]/20 to-[var(--color-blue-secondary)]/20 rounded-xl\"></div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n                \n                <div className=\"flex items-center justify-between pt-6 border-t border-white/20\">\n                  <div className=\"flex items-center gap-4 text-white/90\">\n                    <div className=\"flex items-center gap-2 bg-white/10 px-3 py-2 rounded-xl\">\n                      <Clock className=\"w-4 h-4\" />\n                      <span className=\"text-sm font-medium\">40+ hours</span>\n                    </div>\n                    <div className=\"flex items-center gap-2 bg-white/10 px-3 py-2 rounded-xl\">\n                      <Star className=\"w-4 h-4\" />\n                      <span className=\"text-sm font-medium\">Advanced</span>\n                    </div>\n                  </div>\n                  <ArrowRight className=\"w-6 h-6 text-white group-hover:translate-x-2 transition-transform\" />\n                </div>\n              </div>\n            </div>\n          </motion.div>\n\n          {/* Medium Card - API Security */}\n                <motion.div \n            initial={{ opacity: 0, scale: 0.9 }}\n            whileInView={{ opacity: 1, scale: 1 }}\n            transition={{ delay: 0.2, duration: 0.8 }}\n            viewport={{ once: true }}\n            whileHover={{ \n              y: -8,\n              scale: 1.02,\n              transition: { duration: 0.3 }\n            }}\n            className=\"col-span-12 lg:col-span-4 row-span-1 group relative\"\n          >\n            <div className=\"relative h-full bg-gradient-to-br from-[var(--color-dark-blue)] to-[var(--color-dark-blue-hover)] rounded-3xl p-6 overflow-hidden\">\n              <div className=\"absolute inset-0 bg-[url('/images/hexagon.svg')] bg-cover opacity-5\"></div>\n              <div className=\"absolute inset-0 bg-gradient-to-br from-[var(--color-dark-blue)]/95 to-[var(--color-dark-blue-hover)]/95\"></div>\n              \n              <div className=\"relative z-10 h-full flex flex-col justify-between\">\n                <div className=\"flex-1\">\n                  <div className=\"w-12 h-12 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center mb-4 shadow-lg\">\n                    <Brain className=\"w-6 h-6 text-white\" />\n                  </div>\n                  <h3 className=\"text-xl font-bold text-white mb-3 leading-tight\">API Security</h3>\n                  <p className=\"text-white/80 text-sm mb-4 leading-relaxed\">Learn to secure REST APIs and GraphQL endpoints</p>\n                  \n                  <div className=\"space-y-3\">\n                    {curriculumItems[1].features.map((feature, index) => (\n                      <div key={index} className=\"flex items-center gap-3 bg-white/10 rounded-xl p-3\">\n                        <div className=\"w-2 h-2 bg-[var(--color-yellow)] rounded-full flex-shrink-0\"></div>\n                        <span className=\"text-white/90 text-sm font-medium leading-tight\">{feature}</span>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n                \n                <ArrowRight className=\"w-5 h-5 text-white/60 group-hover:translate-x-1 transition-transform mt-4\" />\n              </div>\n            </div>\n                </motion.div>\n\n          {/* Medium Card - Hands-on Labs */}\n          <motion.div\n            initial={{ opacity: 0, scale: 0.9 }}\n            whileInView={{ opacity: 1, scale: 1 }}\n            transition={{ delay: 0.3, duration: 0.8 }}\n            viewport={{ once: true }}\n            whileHover={{ \n              y: -8,\n              scale: 1.02,\n              transition: { duration: 0.3 }\n            }}\n            className=\"col-span-12 lg:col-span-4 row-span-1 group relative\"\n          >\n            <div className=\"relative h-full bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-3xl p-6 overflow-hidden\">\n              <div className=\"absolute inset-0 bg-[url('/images/hexagon.svg')] bg-cover opacity-5\"></div>\n              <div className=\"absolute inset-0 bg-gradient-to-br from-[var(--color-blue)]/95 to-[var(--color-blue-secondary)]/95\"></div>\n              \n              <div className=\"relative z-10 h-full flex flex-col justify-between\">\n                <div className=\"flex-1\">\n                  <div className=\"w-12 h-12 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center mb-4 shadow-lg\">\n                    <Code className=\"w-6 h-6 text-white\" />\n                  </div>\n                  <h3 className=\"text-xl font-bold text-white mb-3 leading-tight\">Hands-on Labs</h3>\n                  <p className=\"text-white/80 text-sm mb-4 leading-relaxed\">Real-world experience with live environments</p>\n                  \n                  <div className=\"space-y-3\">\n                    {curriculumItems[2].features.map((feature, index) => (\n                      <div key={index} className=\"flex items-center gap-3 bg-white/10 rounded-xl p-3\">\n                        <div className=\"w-2 h-2 bg-white rounded-full flex-shrink-0\"></div>\n                        <span className=\"text-white/90 text-sm font-medium leading-tight\">{feature}</span>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n                \n                <ArrowRight className=\"w-5 h-5 text-white/60 group-hover:translate-x-1 transition-transform mt-4\" />\n              </div>\n            </div>\n          </motion.div>\n\n          {/* Small Card - Navigation & Recon */}\n          <motion.div\n            initial={{ opacity: 0, scale: 0.9 }}\n            whileInView={{ opacity: 1, scale: 1 }}\n            transition={{ delay: 0.4, duration: 0.8 }}\n            viewport={{ once: true }}\n            whileHover={{ \n              y: -8,\n              scale: 1.02,\n              transition: { duration: 0.3 }\n            }}\n            className=\"col-span-12 lg:col-span-4 row-span-1 group relative\"\n          >\n            <div className=\"relative h-full bg-white rounded-3xl p-6 shadow-[0_8px_30px_rgb(0,0,0,0.12)] hover:shadow-[0_20px_40px_rgb(0,0,0,0.15)] transition-all duration-300 border border-gray-100\">\n              <div className=\"h-full flex flex-col justify-between\">\n                <div className=\"flex-1\">\n                  <div className=\"w-12 h-12 bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-2xl flex items-center justify-center mb-4 shadow-lg\">\n                    <Compass className=\"w-6 h-6 text-white\" />\n                  </div>\n                  <h3 className=\"text-xl font-bold text-[var(--color-dark-blue)] mb-3 leading-tight\">Navigation & Recon</h3>\n                  <p className=\"text-[var(--foreground-secondary)] text-sm leading-relaxed\">Advanced reconnaissance and information gathering</p>\n                </div>\n                \n                <ArrowRight className=\"w-5 h-5 text-[var(--color-blue)] group-hover:translate-x-1 transition-transform mt-4\" />\n              </div>\n              </div>\n            </motion.div>\n\n          {/* Large Card - Role-based Training */}\n          <motion.div\n            initial={{ opacity: 0, scale: 0.9 }}\n            whileInView={{ opacity: 1, scale: 1 }}\n            transition={{ delay: 0.5, duration: 0.8 }}\n            viewport={{ once: true }}\n            whileHover={{ \n              y: -8,\n              scale: 1.02,\n              transition: { duration: 0.3 }\n            }}\n            className=\"col-span-12 lg:col-span-8 row-span-2 group relative\"\n          >\n            <div className=\"relative h-full bg-gradient-to-br from-[var(--color-dark-blue)] to-[var(--color-dark-blue-subtle)] rounded-3xl p-6 lg:p-8 overflow-hidden\">\n              <div className=\"absolute inset-0 bg-[url('/images/hexagon.svg')] bg-cover opacity-10\"></div>\n              <div className=\"absolute inset-0 bg-gradient-to-br from-[var(--color-dark-blue)]/90 to-[var(--color-dark-blue-subtle)]/90\"></div>\n              \n              <div className=\"relative z-10 h-full flex flex-col justify-between\">\n                <div className=\"flex-1\">\n                  <div className=\"flex items-start gap-4 mb-6\">\n                    <div className=\"w-14 h-14 lg:w-16 lg:h-16 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center flex-shrink-0 shadow-lg\">\n                      <Target className=\"w-7 h-7 lg:w-8 lg:h-8 text-white\" />\n                    </div>\n                    <div className=\"flex-1 min-w-0\">\n                      <h3 className=\"text-2xl lg:text-3xl font-bold text-white drop-shadow mb-3 leading-tight\">Role-based Training</h3>\n                      <p className=\"text-white/90 text-base lg:text-lg leading-relaxed\">Specialized paths for different career goals</p>\n                    </div>\n                  </div>\n                  \n                  {/* Image and Features Grid */}\n                  <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6\">\n                    {/* Features */}\n                    <div className=\"space-y-4\">\n                      {curriculumItems[4].features.map((feature, index) => (\n                        <div key={index} className=\"bg-white/15 backdrop-blur-sm rounded-2xl p-4 lg:p-5 border border-white/25 shadow-lg\">\n                          <div className=\"text-white font-semibold text-sm lg:text-base mb-2 leading-tight\">{feature}</div>\n                          <div className=\"text-white/70 text-sm leading-relaxed\">\n                            {feature === \"Penetration Tester\" && \"Ethical hacking and security testing\"}\n                            {feature === \"Security Analyst\" && \"Threat detection and incident response\"}\n                            {feature === \"Security Engineer\" && \"Security architecture and implementation\"}\n                          </div>\n                        </div>\n                      ))}\n                    </div>\n                    \n                    {/* Image */}\n                    <div className=\"relative\">\n                      <div className=\"relative h-full min-h-[140px] lg:min-h-[180px] bg-white/15 backdrop-blur-sm rounded-2xl overflow-hidden border border-white/25 shadow-lg\">\n                        <div className=\"absolute inset-0 bg-gradient-to-br from-white/5 to-white/10\"></div>\n                        <div className=\"relative z-10 h-full flex items-center justify-center p-4\">\n                          <img \n                            src=\"/images/about-us-image-3.jpg\" \n                            alt=\"Cybersecurity Assessment\" \n                            className=\"w-full h-full object-cover rounded-xl\"\n                          />\n                          <div className=\"absolute inset-0 bg-gradient-to-br from-[var(--color-blue)]/20 to-[var(--color-blue-secondary)]/20 rounded-xl\"></div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n                \n                <div className=\"flex items-center justify-between pt-6 border-t border-white/20\">\n                  <div className=\"flex items-center gap-4 text-white/90\">\n                    <div className=\"flex items-center gap-2 bg-white/10 px-3 py-2 rounded-xl\">\n                      <Award className=\"w-4 h-4\" />\n                      <span className=\"text-sm font-medium\">Certified</span>\n                    </div>\n                    <div className=\"flex items-center gap-2 bg-white/10 px-3 py-2 rounded-xl\">\n                      <Users className=\"w-4 h-4\" />\n                      <span className=\"text-sm font-medium\">Career-focused</span>\n                    </div>\n                  </div>\n                  <ArrowRight className=\"w-6 h-6 text-white group-hover:translate-x-2 transition-transform\" />\n                </div>\n              </div>\n            </div>\n          </motion.div>\n\n          {/* Medium Card - Authentication & Auth */}\n          <motion.div\n            initial={{ opacity: 0, scale: 0.9 }}\n            whileInView={{ opacity: 1, scale: 1 }}\n            transition={{ delay: 0.6, duration: 0.8 }}\n            viewport={{ once: true }}\n            whileHover={{ \n              y: -8,\n              scale: 1.02,\n              transition: { duration: 0.3 }\n            }}\n            className=\"col-span-12 lg:col-span-4 row-span-1 group relative\"\n          >\n            <div className=\"relative h-full bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-3xl p-6 overflow-hidden\">\n              <div className=\"absolute inset-0 bg-[url('/images/hexagon.svg')] bg-cover opacity-5\"></div>\n              <div className=\"absolute inset-0 bg-gradient-to-br from-[var(--color-blue)]/95 to-[var(--color-blue-secondary)]/95\"></div>\n              \n              <div className=\"relative z-10 h-full flex flex-col justify-between\">\n                <div className=\"flex-1\">\n                  <div className=\"w-12 h-12 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center mb-4 shadow-lg\">\n                    <Lock className=\"w-6 h-6 text-white\" />\n                  </div>\n                  <h3 className=\"text-xl font-bold text-white mb-3 leading-tight\">Authentication & Auth</h3>\n                  <p className=\"text-white/80 text-sm mb-4 leading-relaxed\">Deep dive into modern authentication systems</p>\n                  \n                  <div className=\"space-y-3\">\n                    {curriculumItems[5].features.map((feature, index) => (\n                      <div key={index} className=\"flex items-center gap-3 bg-white/10 rounded-xl p-3\">\n                        <div className=\"w-2 h-2 bg-[var(--color-yellow)] rounded-full flex-shrink-0\"></div>\n                        <span className=\"text-white/90 text-sm font-medium leading-tight\">{feature}</span>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n                \n                <ArrowRight className=\"w-5 h-5 text-white/60 group-hover:translate-x-1 transition-transform mt-4\" />\n              </div>\n            </div>\n          </motion.div>\n        </div>\n\n        {/* Bottom CTA */}\n        <motion.div \n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.8, duration: 0.6 }}\n          viewport={{ once: true }}\n          className=\"text-center pb-8\"\n        >\n          <PrimaryButton className=\"inline-flex items-center gap-3 sm:gap-6 px-6 sm:px-10 py-4 sm:py-5 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer group\">\n            <Shield className=\"w-5 h-5 sm:w-6 sm:h-6\" />\n            <span className=\"font-semibold text-base sm:text-lg lg:text-xl\">Join 500+ Security Professionals</span>\n            <ArrowRight className=\"w-5 h-5 sm:w-6 sm:h-6 group-hover:translate-x-2 transition-transform\" />\n          </PrimaryButton>\n        </motion.div>\n      </div>\n    </section>\n  );\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AALA;;;;;;AAOA,MAAM,kBAAkB;IACtB;QACE,MAAM,8MAAA,CAAA,WAAQ;QACd,OAAO;QACP,aAAa;QACb,OAAO;QACP,OAAO;QACP,MAAM;QACN,UAAU;QACV,UAAU;YAAC;YAAgB;YAA4B;SAAiB;IAC1E;IACA;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;QACb,OAAO;QACP,OAAO;QACP,MAAM;QACN,UAAU;QACV,UAAU;YAAC;YAAkB;YAAiB;SAAgB;IAChE;IACA;QACE,MAAM,kMAAA,CAAA,OAAI;QACV,OAAO;QACP,aAAa;QACb,OAAO;QACP,OAAO;QACP,MAAM;QACN,UAAU;QACV,UAAU;YAAC;YAAqB;YAAkB;SAAgB;IACpE;IACA;QACE,MAAM,wMAAA,CAAA,UAAO;QACb,OAAO;QACP,aAAa;QACb,OAAO;QACP,OAAO;QACP,MAAM;QACN,UAAU;QACV,UAAU;YAAC;YAAS;YAAmB;SAAe;IACxD;IACA;QACE,MAAM,sMAAA,CAAA,SAAM;QACZ,OAAO;QACP,aAAa;QACb,OAAO;QACP,OAAO;QACP,MAAM;QACN,UAAU;QACV,UAAU;YAAC;YAAsB;YAAoB;SAAoB;IAC3E;IACA;QACE,MAAM,kMAAA,CAAA,OAAI;QACV,OAAO;QACP,aAAa;QACb,OAAO;QACP,OAAO;QACP,MAAM;QACN,UAAU;QACV,UAAU;YAAC;YAAa;YAAO;SAAoB;IACrD;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,aAAa;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCACpC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAEV,8OAAC,gMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;kDACf,8OAAC;wCAAK,WAAU;kDAAiD;;;;;;;;;;;;0CAGnE,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;gCACR,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;oCACX;kDAEC,8OAAC;wCAAK,WAAU;kDAAiH;;;;;;;;;;;;0CAKnI,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gCACP,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;0CACX;;;;;;;;;;;;kCAOH,8OAAC;wBAAI,WAAU;wBAA0E,OAAO;4BAAE,kBAAkB;wBAAiC;;0CAEjJ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACX,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,aAAa;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCACpC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACtC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,YAAY;oCACV,GAAG,CAAC;oCACJ,OAAO;oCACT,YAAY;wCAAE,UAAU;oCAAI;gCAC9B;gCACA,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;sDAEf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;;;;;;8EAEtB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAG,WAAU;sFAA2E;;;;;;sFACzF,8OAAC;4EAAE,WAAU;sFAAqD;;;;;;;;;;;;;;;;;;sEAKtE,8OAAC;4DAAI,WAAU;;8EAEb,8OAAC;oEAAI,WAAU;8EACZ,eAAe,CAAC,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBACzC,8OAAC;4EAAgB,WAAU;;8FACzB,8OAAC;oFAAI,WAAU;8FAAoE;;;;;;8FACnF,8OAAC;oFAAI,WAAU;;wFACZ,YAAY,kBAAkB;wFAC9B,YAAY,8BAA8B;wFAC1C,YAAY,oBAAoB;;;;;;;;2EAL3B;;;;;;;;;;8EAYd,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;;;;;;0FACf,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;wFACC,KAAI;wFACJ,KAAI;wFACJ,WAAU;;;;;;kGAEZ,8OAAC;wFAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8DAOzB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,oMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;sFACjB,8OAAC;4EAAK,WAAU;sFAAsB;;;;;;;;;;;;8EAExC,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,kMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;sFAChB,8OAAC;4EAAK,WAAU;sFAAsB;;;;;;;;;;;;;;;;;;sEAG1C,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACf,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,aAAa;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCACpC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,YAAY;oCACV,GAAG,CAAC;oCACJ,OAAO;oCACP,YAAY;wCAAE,UAAU;oCAAI;gCAC9B;gCACA,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;sDAEf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;;;;;;sEAEnB,8OAAC;4DAAG,WAAU;sEAAkD;;;;;;sEAChE,8OAAC;4DAAE,WAAU;sEAA6C;;;;;;sEAE1D,8OAAC;4DAAI,WAAU;sEACZ,eAAe,CAAC,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBACzC,8OAAC;oEAAgB,WAAU;;sFACzB,8OAAC;4EAAI,WAAU;;;;;;sFACf,8OAAC;4EAAK,WAAU;sFAAmD;;;;;;;mEAF3D;;;;;;;;;;;;;;;;8DAQhB,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0CAM5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,aAAa;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCACpC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,YAAY;oCACV,GAAG,CAAC;oCACJ,OAAO;oCACP,YAAY;wCAAE,UAAU;oCAAI;gCAC9B;gCACA,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;sDAEf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;sEAElB,8OAAC;4DAAG,WAAU;sEAAkD;;;;;;sEAChE,8OAAC;4DAAE,WAAU;sEAA6C;;;;;;sEAE1D,8OAAC;4DAAI,WAAU;sEACZ,eAAe,CAAC,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBACzC,8OAAC;oEAAgB,WAAU;;sFACzB,8OAAC;4EAAI,WAAU;;;;;;sFACf,8OAAC;4EAAK,WAAU;sFAAmD;;;;;;;mEAF3D;;;;;;;;;;;;;;;;8DAQhB,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0CAM5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,aAAa;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCACpC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,YAAY;oCACV,GAAG,CAAC;oCACJ,OAAO;oCACP,YAAY;wCAAE,UAAU;oCAAI;gCAC9B;gCACA,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,wMAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;;;;;;kEAErB,8OAAC;wDAAG,WAAU;kEAAqE;;;;;;kEACnF,8OAAC;wDAAE,WAAU;kEAA6D;;;;;;;;;;;;0DAG5E,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0CAM5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,aAAa;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCACpC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,YAAY;oCACV,GAAG,CAAC;oCACJ,OAAO;oCACP,YAAY;wCAAE,UAAU;oCAAI;gCAC9B;gCACA,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;sDAEf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;;;;;;8EAEpB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAG,WAAU;sFAA2E;;;;;;sFACzF,8OAAC;4EAAE,WAAU;sFAAqD;;;;;;;;;;;;;;;;;;sEAKtE,8OAAC;4DAAI,WAAU;;8EAEb,8OAAC;oEAAI,WAAU;8EACZ,eAAe,CAAC,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBACzC,8OAAC;4EAAgB,WAAU;;8FACzB,8OAAC;oFAAI,WAAU;8FAAoE;;;;;;8FACnF,8OAAC;oFAAI,WAAU;;wFACZ,YAAY,wBAAwB;wFACpC,YAAY,sBAAsB;wFAClC,YAAY,uBAAuB;;;;;;;;2EAL9B;;;;;;;;;;8EAYd,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;;;;;;0FACf,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;wFACC,KAAI;wFACJ,KAAI;wFACJ,WAAU;;;;;;kGAEZ,8OAAC;wFAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8DAOzB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,oMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;sFACjB,8OAAC;4EAAK,WAAU;sFAAsB;;;;;;;;;;;;8EAExC,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,oMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;sFACjB,8OAAC;4EAAK,WAAU;sFAAsB;;;;;;;;;;;;;;;;;;sEAG1C,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAO9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,aAAa;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCACpC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,YAAY;oCACV,GAAG,CAAC;oCACJ,OAAO;oCACP,YAAY;wCAAE,UAAU;oCAAI;gCAC9B;gCACA,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;sDAEf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;sEAElB,8OAAC;4DAAG,WAAU;sEAAkD;;;;;;sEAChE,8OAAC;4DAAE,WAAU;sEAA6C;;;;;;sEAE1D,8OAAC;4DAAI,WAAU;sEACZ,eAAe,CAAC,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBACzC,8OAAC;oEAAgB,WAAU;;sFACzB,8OAAC;4EAAI,WAAU;;;;;;sFACf,8OAAC;4EAAK,WAAU;sFAAmD;;;;;;;mEAF3D;;;;;;;;;;;;;;;;8DAQhB,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,OAAO;4BAAK,UAAU;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;kCAEV,cAAA,8OAAC,gJAAA,CAAA,gBAAa;4BAAC,WAAU;;8CACvB,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,8OAAC;oCAAK,WAAU;8CAAgD;;;;;;8CAChE,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlC", "debugId": null}}, {"offset": {"line": 2193, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/training/components/LearningModules.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useRef } from 'react';\r\nimport { motion, useScroll, useTransform } from 'framer-motion';\r\nimport { ChevronLeft, ChevronRight, Play, Lock, CheckCircle, Clock, Users } from 'lucide-react';\r\n\r\nconst learningModules = [\r\n  {\r\n    id: 1,\r\n    title: \"Web Application Security\",\r\n    subtitle: \"Fortifying the digital frontline against cyber threats\",\r\n    description: \"Master web app security from OWASP Top 10 to advanced exploitation techniques\",\r\n    progress: 85,\r\n    duration: \"12 hours\",\r\n    students: 234,\r\n    lessons: 24,\r\n    color: \"from-[var(--color-blue)] to-[var(--color-blue-secondary)]\",\r\n    status: \"in-progress\",\r\n    image: \"/images/web-security.jpg\"\r\n  },\r\n  {\r\n    id: 2,\r\n    title: \"API & Network Security\",\r\n    subtitle: \"Safeguarding the backbone of modern interconnected systems\",\r\n    description: \"Learn to identify and exploit API vulnerabilities in modern applications\",\r\n    progress: 60,\r\n    duration: \"8 hours\",\r\n    students: 189,\r\n    lessons: 18,\r\n    color: \"from-[var(--color-dark-blue)] to-[var(--color-dark-blue-hover)]\",\r\n    status: \"in-progress\",\r\n    image: \"/images/api-security.jpg\"\r\n  },\r\n  {\r\n    id: 3,\r\n    title: \"Practical Skills Development\",\r\n    subtitle: \"Honing real-world cybersecurity expertise through hands-on learning\",\r\n    description: \"Comprehensive network security assessment and exploitation techniques\",\r\n    progress: 0,\r\n    duration: \"15 hours\",\r\n    students: 156,\r\n    lessons: 32,\r\n    color: \"from-[var(--color-blue)] to-[var(--color-blue-secondary)]\",\r\n    status: \"locked\",\r\n    image: \"/images/network-security.jpg\"\r\n  },\r\n  {\r\n    id: 4,\r\n    title: \"Soft Skill & Professional Growth\",\r\n    subtitle: \"Cultivating the human element in technical cybersecurity roles\",\r\n    description: \"Security testing for mobile applications and reverse engineering\",\r\n    progress: 0,\r\n    duration: \"10 hours\",\r\n    students: 98,\r\n    lessons: 20,\r\n    color: \"from-[var(--color-blue)] to-[var(--color-blue-secondary)]\",\r\n    status: \"locked\",\r\n    image: \"/images/mobile-security.jpg\"\r\n  },\r\n  {\r\n    id: 5,\r\n    title: \"Real World Environment Navigation\",\r\n    subtitle: \"Mastering the art of securing complex, live digital ecosystems\",\r\n    description: \"Cloud infrastructure security and misconfiguration exploitation\",\r\n    progress: 0,\r\n    duration: \"14 hours\",\r\n    students: 76,\r\n    lessons: 28,\r\n    color: \"from-[var(--color-dark-blue)] to-[var(--color-dark-blue-subtle)]\",\r\n    status: \"locked\",\r\n    image: \"/images/cloud-security.jpg\"\r\n  },\r\n  {\r\n    id: 6,\r\n    title: \"Active Directory & Cloud Security\",\r\n    subtitle: \"Protecting the nerve centers of enterprise and cloud infrastructures\",\r\n    description: \"Psychological manipulation techniques and defense strategies\",\r\n    progress: 0,\r\n    duration: \"6 hours\",\r\n    students: 45,\r\n    lessons: 12,\r\n    color: \"from-[var(--color-blue)] to-[var(--color-blue-secondary)]\",\r\n    status: \"locked\",\r\n    image: \"/images/social-engineering.jpg\"\r\n  },\r\n  {\r\n    id: 7,\r\n    title: \"Continuous Learning & Adaption\",\r\n    subtitle: \"Staying ahead in the ever-evolving cybersecurity landscape\",\r\n    description: \"Advanced techniques for continuous learning and adaptation\",\r\n    progress: 0,\r\n    duration: \"8 hours\",\r\n    students: 67,\r\n    lessons: 16,\r\n    color: \"from-[var(--color-blue)] to-[var(--color-blue-secondary)]\",\r\n    status: \"locked\",\r\n    image: \"/images/continuous-learning.jpg\"\r\n  },\r\n  {\r\n    id: 8,\r\n    title: \"Report Writing Skills\",\r\n    subtitle: \"Crafting clear, concise, and impactful cybersecurity documentation\",\r\n    description: \"Professional documentation and stakeholder communication\",\r\n    progress: 0,\r\n    duration: \"5 hours\",\r\n    students: 89,\r\n    lessons: 10,\r\n    color: \"from-[var(--color-dark-blue)] to-[var(--color-dark-blue-hover)]\",\r\n    status: \"locked\",\r\n    image: \"/images/report-writing.jpg\"\r\n  }\r\n];\r\n\r\nexport default function LearningModules({ content }) {\r\n  // Use content if provided, otherwise use default modules\r\n  const moduleContent = content || {\r\n    title: \"Here's A Short Teaser Of What You Will Learn\",\r\n    modules: learningModules.map(module => ({\r\n      title: module.title,\r\n      description: module.subtitle\r\n    }))\r\n  };\r\n  const containerRef = useRef(null);\r\n  const scrollContainerRef = useRef(null);\r\n\r\n  const scrollLeft = () => {\r\n    if (scrollContainerRef.current) {\r\n      scrollContainerRef.current.scrollBy({ left: -400, behavior: 'smooth' });\r\n    }\r\n  };\r\n\r\n  const scrollRight = () => {\r\n    if (scrollContainerRef.current) {\r\n      scrollContainerRef.current.scrollBy({ left: 400, behavior: 'smooth' });\r\n    }\r\n  };\r\n\r\n  return (\r\n    <section className=\"py-24 bg-gradient-to-br from-[var(--color-dark-blue)] via-[var(--color-dark-blue-hover)] to-[var(--color-dark-blue-bg)] relative overflow-hidden\">\r\n      {/* Background Pattern */}\r\n      <div className=\"absolute inset-0\">\r\n        <div className=\"absolute top-0 left-0 w-full h-full bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.03%22%3E%3Ccircle%20cx%3D%2230%22%20cy%3D%2230%22%20r%3D%221%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-50\"></div>\r\n      </div>\r\n\r\n      <div className=\"relative z-10 container mx-auto px-4 max-w-7xl\">\r\n        {/* Header */}\r\n        <motion.div \r\n          initial={{ opacity: 0, y: 30 }}\r\n          whileInView={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.8 }}\r\n          viewport={{ once: true }}\r\n          className=\"text-center mb-16\"\r\n        >\r\n          <motion.div \r\n            initial={{ opacity: 0, scale: 0.8 }}\r\n            whileInView={{ opacity: 1, scale: 1 }}\r\n            transition={{ delay: 0.2, duration: 0.6 }}\r\n            viewport={{ once: true }}\r\n            className=\"inline-flex items-center bg-[var(--color-blue)]/20 px-4 py-2 rounded-full mb-6\"\r\n          >\r\n            <Play className=\"w-4 h-4 text-[var(--color-blue)] mr-2\" />\r\n            <span className=\"text-sm font-medium text-[var(--color-blue)]\">Here's A Short Teaser</span>\r\n          </motion.div>\r\n          \r\n          <h2 className=\"text-4xl lg:text-5xl font-bold text-white mb-6\">\r\n            {moduleContent.title}\r\n          </h2>\r\n          \r\n          <motion.p \r\n            initial={{ opacity: 0, y: 20 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ delay: 0.6, duration: 0.6 }}\r\n            viewport={{ once: true }}\r\n            className=\"text-xl text-white/80 max-w-4xl mx-auto leading-relaxed\"\r\n          >\r\n            Explore our comprehensive curriculum covering essential cybersecurity areas including web application security, \r\n            network penetration testing, cloud security, API security, and ethical hacking fundamentals.\r\n          </motion.p>\r\n        </motion.div>\r\n\r\n        {/* Navigation Controls */}\r\n        <div className=\"flex justify-between items-center mb-8\">\r\n          <motion.button\r\n            whileHover={{ scale: 1.1 }}\r\n            whileTap={{ scale: 0.9 }}\r\n            onClick={scrollLeft}\r\n            className=\"w-12 h-12 bg-white/10 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-white/20 transition-all duration-300 border border-white/20\"\r\n          >\r\n            <ChevronLeft className=\"w-6 h-6\" />\r\n          </motion.button>\r\n          \r\n          <div className=\"text-center\">\r\n            <p className=\"text-white/70 text-sm\">Scroll to explore modules</p>\r\n          </div>\r\n          \r\n          <motion.button\r\n            whileHover={{ scale: 1.1 }}\r\n            whileTap={{ scale: 0.9 }}\r\n            onClick={scrollRight}\r\n            className=\"w-12 h-12 bg-white/10 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-white/20 transition-all duration-300 border border-white/20\"\r\n          >\r\n            <ChevronRight className=\"w-6 h-6\" />\r\n          </motion.button>\r\n        </div>\r\n\r\n        {/* Scrollable Modules Container */}\r\n        <div \r\n          ref={scrollContainerRef}\r\n          className=\"flex gap-6 overflow-x-auto scrollbar-hide scroll-smooth pb-8\"\r\n          style={{ scrollSnapType: 'x mandatory' }}\r\n        >\r\n          {learningModules.map((module, index) => (\r\n            <motion.div\r\n              key={module.id}\r\n              initial={{ opacity: 0, x: 50 }}\r\n              whileInView={{ opacity: 1, x: 0 }}\r\n              transition={{ delay: index * 0.1, duration: 0.6 }}\r\n              viewport={{ once: true }}\r\n              whileHover={{ \r\n                y: -10,\r\n                scale: 1.02,\r\n                transition: { duration: 0.2 }\r\n              }}\r\n              className=\"group relative flex-shrink-0 w-80 bg-white/10 backdrop-blur-xl rounded-3xl p-6 border border-white/20 cursor-pointer\"\r\n              style={{ scrollSnapAlign: 'start' }}\r\n            >\r\n              {/* Module Image */}\r\n              <div className=\"relative h-40 bg-gradient-to-br from-gray-800 to-gray-900 rounded-2xl mb-6 overflow-hidden\">\r\n                <div className={`absolute inset-0 bg-gradient-to-br ${module.color} opacity-20`} />\r\n                <div className=\"absolute inset-0 flex items-center justify-center\">\r\n                  <div className={`w-16 h-16 bg-gradient-to-br ${module.color} rounded-2xl flex items-center justify-center shadow-xl`}>\r\n                    {module.status === 'locked' ? (\r\n                      <Lock className=\"w-8 h-8 text-white\" />\r\n                    ) : (\r\n                      <Play className=\"w-8 h-8 text-white\" />\r\n                    )}\r\n                  </div>\r\n                </div>\r\n                \r\n                {/* Progress Ring */}\r\n                {module.status === 'in-progress' && (\r\n                  <div className=\"absolute top-4 right-4\">\r\n                    <svg className=\"w-12 h-12 transform -rotate-90\" viewBox=\"0 0 36 36\">\r\n                      <path\r\n                        className=\"text-gray-700\"\r\n                        stroke=\"currentColor\"\r\n                        strokeWidth=\"2\"\r\n                        fill=\"none\"\r\n                        d=\"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831\"\r\n                      />\r\n                      <path\r\n                        className=\"text-[var(--color-blue)]\"\r\n                        stroke=\"currentColor\"\r\n                        strokeWidth=\"2\"\r\n                        strokeDasharray={`${module.progress}, 100`}\r\n                        fill=\"none\"\r\n                        d=\"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831\"\r\n                      />\r\n                    </svg>\r\n                    <div className=\"absolute inset-0 flex items-center justify-center text-xs font-bold text-white\">\r\n                      {module.progress}%\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n\r\n              {/* Module Content */}\r\n              <div className=\"space-y-4\">\r\n                <div>\r\n                  <h3 className=\"text-xl font-bold text-white mb-1 group-hover:text-[var(--color-blue)] transition-colors\">\r\n                    {module.title}\r\n                  </h3>\r\n                  <p className=\"text-[var(--color-blue)] text-sm font-medium\">\r\n                    {module.subtitle}\r\n                  </p>\r\n                </div>\r\n                \r\n                <p className=\"text-white/70 text-sm leading-relaxed\">\r\n                  {module.description}\r\n                </p>\r\n\r\n                {/* Module Stats */}\r\n                <div className=\"flex items-center justify-between text-sm text-white/60\">\r\n                  <div className=\"flex items-center gap-1\">\r\n                    <Clock className=\"w-4 h-4\" />\r\n                    <span>{module.duration}</span>\r\n                  </div>\r\n                  <div className=\"flex items-center gap-1\">\r\n                    <Users className=\"w-4 h-4\" />\r\n                    <span>{module.students}</span>\r\n                  </div>\r\n                  <div className=\"flex items-center gap-1\">\r\n                    <CheckCircle className=\"w-4 h-4\" />\r\n                    <span>{module.lessons} lessons</span>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Status Badge */}\r\n                <div className=\"flex justify-between items-center\">\r\n                  <div className={`px-3 py-1 rounded-full text-xs font-semibold ${\r\n                    module.status === 'in-progress' \r\n                      ? 'bg-[var(--color-blue)]/20 text-[var(--color-blue)]' \r\n                      : 'bg-white/20 text-white/70'\r\n                  }`}>\r\n                    {module.status === 'in-progress' ? 'In Progress' : 'Locked'}\r\n                  </div>\r\n                  <motion.button\r\n                    whileHover={{ scale: 1.05 }}\r\n                    whileTap={{ scale: 0.95 }}\r\n                    className={`px-4 py-2 rounded-xl font-semibold text-sm transition-all duration-300 ${\r\n                      module.status === 'in-progress'\r\n                        ? 'bg-[var(--color-blue)] hover:bg-[var(--color-blue-secondary)] text-white'\r\n                        : 'bg-white/20 hover:bg-white/30 text-white/70'\r\n                    }`}\r\n                  >\r\n                    {module.status === 'in-progress' ? 'Continue' : 'Unlock'}\r\n                  </motion.button>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Hover Effects */}\r\n              <div className=\"absolute inset-0 bg-gradient-to-br from-[var(--color-blue)]/5 to-[var(--color-blue-secondary)]/5 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300\" />\r\n              <div className={`absolute inset-0 bg-gradient-to-br ${module.color} rounded-3xl opacity-0 group-hover:opacity-5 transition-opacity duration-300 blur-xl`} />\r\n            </motion.div>\r\n          ))}\r\n        </div>\r\n\r\n        {/* Scroll Indicator */}\r\n        <div className=\"flex justify-center mt-8\">\r\n          <div className=\"flex gap-2\">\r\n            {learningModules.map((_, index) => (\r\n              <div\r\n                key={index}\r\n                className=\"w-2 h-2 bg-white/30 rounded-full\"\r\n              />\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAMA,MAAM,kBAAkB;IACtB;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,UAAU;QACV,UAAU;QACV,UAAU;QACV,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,UAAU;QACV,UAAU;QACV,UAAU;QACV,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,UAAU;QACV,UAAU;QACV,UAAU;QACV,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,UAAU;QACV,UAAU;QACV,UAAU;QACV,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,UAAU;QACV,UAAU;QACV,UAAU;QACV,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,UAAU;QACV,UAAU;QACV,UAAU;QACV,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,UAAU;QACV,UAAU;QACV,UAAU;QACV,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,UAAU;QACV,UAAU;QACV,UAAU;QACV,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;IACT;CACD;AAEc,SAAS,gBAAgB,EAAE,OAAO,EAAE;IACjD,yDAAyD;IACzD,MAAM,gBAAgB,WAAW;QAC/B,OAAO;QACP,SAAS,gBAAgB,GAAG,CAAC,CAAA,SAAU,CAAC;gBACtC,OAAO,OAAO,KAAK;gBACnB,aAAa,OAAO,QAAQ;YAC9B,CAAC;IACH;IACA,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC5B,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAElC,MAAM,aAAa;QACjB,IAAI,mBAAmB,OAAO,EAAE;YAC9B,mBAAmB,OAAO,CAAC,QAAQ,CAAC;gBAAE,MAAM,CAAC;gBAAK,UAAU;YAAS;QACvE;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,mBAAmB,OAAO,EAAE;YAC9B,mBAAmB,OAAO,CAAC,QAAQ,CAAC;gBAAE,MAAM;gBAAK,UAAU;YAAS;QACtE;IACF;IAEA,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,aAAa;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCACpC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAEV,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,8OAAC;wCAAK,WAAU;kDAA+C;;;;;;;;;;;;0CAGjE,8OAAC;gCAAG,WAAU;0CACX,cAAc,KAAK;;;;;;0CAGtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gCACP,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;0CACX;;;;;;;;;;;;kCAOH,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,YAAY;oCAAE,OAAO;gCAAI;gCACzB,UAAU;oCAAE,OAAO;gCAAI;gCACvB,SAAS;gCACT,WAAU;0CAEV,cAAA,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;0CAGzB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;0CAGvC,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,YAAY;oCAAE,OAAO;gCAAI;gCACzB,UAAU;oCAAE,OAAO;gCAAI;gCACvB,SAAS;gCACT,WAAU;0CAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAK5B,8OAAC;wBACC,KAAK;wBACL,WAAU;wBACV,OAAO;4BAAE,gBAAgB;wBAAc;kCAEtC,gBAAgB,GAAG,CAAC,CAAC,QAAQ,sBAC5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,OAAO,QAAQ;oCAAK,UAAU;gCAAI;gCAChD,UAAU;oCAAE,MAAM;gCAAK;gCACvB,YAAY;oCACV,GAAG,CAAC;oCACJ,OAAO;oCACP,YAAY;wCAAE,UAAU;oCAAI;gCAC9B;gCACA,WAAU;gCACV,OAAO;oCAAE,iBAAiB;gCAAQ;;kDAGlC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAW,CAAC,mCAAmC,EAAE,OAAO,KAAK,CAAC,WAAW,CAAC;;;;;;0DAC/E,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAW,CAAC,4BAA4B,EAAE,OAAO,KAAK,CAAC,uDAAuD,CAAC;8DACjH,OAAO,MAAM,KAAK,yBACjB,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;6EAEhB,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;;;;;;4CAMrB,OAAO,MAAM,KAAK,+BACjB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;wDAAiC,SAAQ;;0EACtD,8OAAC;gEACC,WAAU;gEACV,QAAO;gEACP,aAAY;gEACZ,MAAK;gEACL,GAAE;;;;;;0EAEJ,8OAAC;gEACC,WAAU;gEACV,QAAO;gEACP,aAAY;gEACZ,iBAAiB,GAAG,OAAO,QAAQ,CAAC,KAAK,CAAC;gEAC1C,MAAK;gEACL,GAAE;;;;;;;;;;;;kEAGN,8OAAC;wDAAI,WAAU;;4DACZ,OAAO,QAAQ;4DAAC;;;;;;;;;;;;;;;;;;;kDAOzB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEACX,OAAO,KAAK;;;;;;kEAEf,8OAAC;wDAAE,WAAU;kEACV,OAAO,QAAQ;;;;;;;;;;;;0DAIpB,8OAAC;gDAAE,WAAU;0DACV,OAAO,WAAW;;;;;;0DAIrB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,8OAAC;0EAAM,OAAO,QAAQ;;;;;;;;;;;;kEAExB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,8OAAC;0EAAM,OAAO,QAAQ;;;;;;;;;;;;kEAExB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,2NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;0EACvB,8OAAC;;oEAAM,OAAO,OAAO;oEAAC;;;;;;;;;;;;;;;;;;;0DAK1B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAW,CAAC,6CAA6C,EAC5D,OAAO,MAAM,KAAK,gBACd,uDACA,6BACJ;kEACC,OAAO,MAAM,KAAK,gBAAgB,gBAAgB;;;;;;kEAErD,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wDACZ,YAAY;4DAAE,OAAO;wDAAK;wDAC1B,UAAU;4DAAE,OAAO;wDAAK;wDACxB,WAAW,CAAC,uEAAuE,EACjF,OAAO,MAAM,KAAK,gBACd,6EACA,+CACJ;kEAED,OAAO,MAAM,KAAK,gBAAgB,aAAa;;;;;;;;;;;;;;;;;;kDAMtD,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAW,CAAC,mCAAmC,EAAE,OAAO,KAAK,CAAC,oFAAoF,CAAC;;;;;;;+BA7GnJ,OAAO,EAAE;;;;;;;;;;kCAmHpB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAC,GAAG,sBACvB,8OAAC;oCAEC,WAAU;mCADL;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrB", "debugId": null}}, {"offset": {"line": 2869, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/training/components/CoursePreview.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState } from 'react';\r\nimport { motion, AnimatePresence } from 'framer-motion';\r\nimport { X, Play, Clock, CheckCircle, Lock, ArrowRight, Calendar, Users, Star } from 'lucide-react';\r\nimport { PrimaryButton } from '../../common/buttons/BrandButtons';\r\n\r\nconst courseWeeks = [\r\n  {\r\n    id: 1,\r\n    title: \"Welcome To Security & Training\",\r\n    subtitle: \"Introduction to Cybersecurity\",\r\n    description: \"Introduction to cybersecurity concepts, threat modeling, and basic tools\",\r\n    lessons: [\r\n      { id: 1, title: \"Introduction to Cybersecurity\", duration: \"45 min\", status: \"completed\", type: \"video\" },\r\n      { id: 2, title: \"Threat Modeling Basics\", duration: \"60 min\", status: \"completed\", type: \"video\" },\r\n      { id: 3, title: \"Setting Up Your Lab Environment\", duration: \"30 min\", status: \"completed\", type: \"lab\" },\r\n      { id: 4, title: \"Basic Reconnaissance Techniques\", duration: \"90 min\", status: \"in-progress\", type: \"video\" },\r\n      { id: 5, title: \"Week 1 Assessment\", duration: \"45 min\", status: \"locked\", type: \"quiz\" }\r\n    ],\r\n    color: \"from-[var(--color-blue)] to-[var(--color-blue-secondary)]\",\r\n    progress: 60,\r\n    totalLessons: 5,\r\n    completedLessons: 3\r\n  },\r\n  {\r\n    id: 2,\r\n    title: \"Week 1 : Web Application Security\",\r\n    subtitle: \"OWASP Top 10\",\r\n    description: \"Deep dive into web application vulnerabilities and exploitation techniques\",\r\n    lessons: [\r\n      { id: 1, title: \"OWASP Top 10 Overview\", duration: \"60 min\", status: \"locked\", type: \"video\" },\r\n      { id: 2, title: \"SQL Injection Attacks\", duration: \"90 min\", status: \"locked\", type: \"video\" },\r\n      { id: 3, title: \"XSS and CSRF Vulnerabilities\", duration: \"75 min\", status: \"locked\", type: \"video\" },\r\n      { id: 4, title: \"Web Security Lab\", duration: \"120 min\", status: \"locked\", type: \"lab\" },\r\n      { id: 5, title: \"Authentication Bypass Techniques\", duration: \"60 min\", status: \"locked\", type: \"video\" },\r\n      { id: 6, title: \"Week 2 Assessment\", duration: \"60 min\", status: \"locked\", type: \"quiz\" }\r\n    ],\r\n    color: \"from-[var(--color-dark-blue)] to-[var(--color-dark-blue-hover)]\",\r\n    progress: 0,\r\n    totalLessons: 6,\r\n    completedLessons: 0\r\n  },\r\n  {\r\n    id: 3,\r\n    title: \"Week 7 : Network Pentesting\",\r\n    subtitle: \"Network Security Assessment\",\r\n    description: \"Advanced network penetration testing and infrastructure security\",\r\n    lessons: [\r\n      { id: 1, title: \"Network Reconnaissance\", duration: \"90 min\", status: \"locked\", type: \"video\" },\r\n      { id: 2, title: \"Port Scanning Techniques\", duration: \"120 min\", status: \"locked\", type: \"video\" },\r\n      { id: 3, title: \"Service Enumeration\", duration: \"90 min\", status: \"locked\", type: \"video\" },\r\n      { id: 4, title: \"Network Exploitation Lab\", duration: \"180 min\", status: \"locked\", type: \"lab\" },\r\n      { id: 5, title: \"Network Security Report\", duration: \"60 min\", status: \"locked\", type: \"video\" },\r\n      { id: 6, title: \"Final Assessment\", duration: \"120 min\", status: \"locked\", type: \"quiz\" }\r\n    ],\r\n    color: \"from-[var(--color-yellow)] to-[var(--color-yellow-hover)]\",\r\n    progress: 0,\r\n    totalLessons: 6,\r\n    completedLessons: 0\r\n  },\r\n  {\r\n    id: 4,\r\n    title: \"Week 9 : Active Directory Attacks & HTB Machines Practice\",\r\n    subtitle: \"AD Security & HTB Practice\",\r\n    description: \"Active Directory attack techniques and HackTheBox machine practice\",\r\n    lessons: [\r\n      { id: 1, title: \"Active Directory Basics\", duration: \"90 min\", status: \"locked\", type: \"video\" },\r\n      { id: 2, title: \"AD Enumeration\", duration: \"120 min\", status: \"locked\", type: \"video\" },\r\n      { id: 3, title: \"Kerberoasting & Golden Ticket\", duration: \"90 min\", status: \"locked\", type: \"video\" },\r\n      { id: 4, title: \"HTB Machine Practice\", duration: \"180 min\", status: \"locked\", type: \"lab\" },\r\n      { id: 5, title: \"AD Security Report\", duration: \"60 min\", status: \"locked\", type: \"video\" }\r\n    ],\r\n    color: \"from-[var(--color-blue)] to-[var(--color-blue-secondary)]\",\r\n    progress: 0,\r\n    totalLessons: 5,\r\n    completedLessons: 0\r\n  },\r\n  {\r\n    id: 5,\r\n    title: \"Week 10 : AWS Cloud Pentesting\",\r\n    subtitle: \"Cloud Security Assessment\",\r\n    description: \"AWS cloud penetration testing and security assessment\",\r\n    lessons: [\r\n      { id: 1, title: \"AWS Security Fundamentals\", duration: \"90 min\", status: \"locked\", type: \"video\" },\r\n      { id: 2, title: \"Cloud Reconnaissance\", duration: \"120 min\", status: \"locked\", type: \"video\" },\r\n      { id: 3, title: \"IAM Security Testing\", duration: \"90 min\", status: \"locked\", type: \"video\" },\r\n      { id: 4, title: \"Cloud Security Lab\", duration: \"180 min\", status: \"locked\", type: \"lab\" },\r\n      { id: 5, title: \"Cloud Security Report\", duration: \"60 min\", status: \"locked\", type: \"video\" }\r\n    ],\r\n    color: \"from-[var(--color-dark-blue)] to-[var(--color-dark-blue-hover)]\",\r\n    progress: 0,\r\n    totalLessons: 5,\r\n    completedLessons: 0\r\n  }\r\n];\r\n\r\nexport default function CoursePreview() {\r\n  const [selectedWeek, setSelectedWeek] = useState(null);\r\n  const [isModalOpen, setIsModalOpen] = useState(false);\r\n\r\n  const openModal = (week) => {\r\n    setSelectedWeek(week);\r\n    setIsModalOpen(true);\r\n  };\r\n\r\n  const closeModal = () => {\r\n    setIsModalOpen(false);\r\n    setSelectedWeek(null);\r\n  };\r\n\r\n  return (\r\n    <section className=\"py-16 bg-white relative overflow-hidden\">\r\n      {/* Background Elements */}\r\n      <div className=\"absolute inset-0\">\r\n        <div className=\"absolute top-20 left-10 w-72 h-72 bg-[var(--color-blue)]/10 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob\"></div>\r\n        <div className=\"absolute bottom-20 right-10 w-72 h-72 bg-[var(--color-yellow)]/10 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-2000\"></div>\r\n      </div>\r\n\r\n      <div className=\"relative z-10 container mx-auto px-4 max-w-7xl\">\r\n        {/* Header */}\r\n        <motion.div \r\n          initial={{ opacity: 0, y: 30 }}\r\n          whileInView={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.8 }}\r\n          viewport={{ once: true }}\r\n          className=\"text-center mb-12\"\r\n        >\r\n          <motion.div \r\n            initial={{ opacity: 0, scale: 0.8 }}\r\n            whileInView={{ opacity: 1, scale: 1 }}\r\n            transition={{ delay: 0.2, duration: 0.6 }}\r\n            viewport={{ once: true }}\r\n            className=\"inline-flex items-center bg-[var(--color-blue)]/10 px-4 py-2 rounded-full mb-6\"\r\n          >\r\n            <Calendar className=\"w-4 h-4 text-[var(--color-blue)] mr-2\" />\r\n            <span className=\"text-sm font-medium text-[var(--color-blue)]\">SecurityLit Security Training</span>\r\n          </motion.div>\r\n          \r\n          <motion.h2 \r\n            initial={{ opacity: 0, y: 20 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ delay: 0.4, duration: 0.6 }}\r\n            viewport={{ once: true }}\r\n            className=\"text-4xl lg:text-5xl font-bold text-[var(--color-dark-blue)] mb-6\"\r\n          >\r\n            Free Learning -\r\n            <span className=\"block bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] bg-clip-text text-transparent\">\r\n              Premium Experience\r\n            </span>\r\n          </motion.h2>\r\n          \r\n          <motion.p \r\n            initial={{ opacity: 0, y: 20 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ delay: 0.6, duration: 0.6 }}\r\n            viewport={{ once: true }}\r\n            className=\"text-xl text-[var(--foreground-secondary)] max-w-4xl mx-auto leading-relaxed\"\r\n          >\r\n            Created by SecurityLit Team. Explore the detailed curriculum structure with 11 sections and 83 total lessons. \r\n            Click on any week to view the complete lesson plan.\r\n          </motion.p>\r\n        </motion.div>\r\n\r\n        {/* Course Week Cards */}\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12\">\r\n          {courseWeeks.map((week, index) => (\r\n            <motion.div\r\n              key={week.id}\r\n              initial={{ opacity: 0, y: 30 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ delay: index * 0.1, duration: 0.6 }}\r\n              viewport={{ once: true }}\r\n              whileHover={{ \r\n                y: -8,\r\n                scale: 1.02,\r\n                transition: { duration: 0.2 }\r\n              }}\r\n              onClick={() => openModal(week)}\r\n              className=\"group relative bg-white rounded-3xl p-6 shadow-[0_20px_40px_rgb(0,0,0,0.08)] hover:shadow-[0_30px_60px_rgb(0,0,0,0.12)] transition-all duration-300 border border-gray-100 cursor-pointer\"\r\n            >\r\n              {/* Week Header */}\r\n              <div className=\"flex items-center justify-between mb-4\">\r\n                <div className={`w-10 h-10 bg-gradient-to-br ${week.color} rounded-2xl flex items-center justify-center shadow-lg`}>\r\n                  <span className=\"text-white font-bold text-lg\">{week.id}</span>\r\n                </div>\r\n                <div className=\"text-right\">\r\n                  <div className=\"text-sm text-[var(--foreground-secondary)]\">Progress</div>\r\n                  <div className=\"text-2xl font-bold text-[var(--color-dark-blue)]\">{week.progress}%</div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Week Content */}\r\n              <div className=\"space-y-3\">\r\n                <div>\r\n                  <h3 className=\"text-lg font-bold text-[var(--color-dark-blue)] mb-1 group-hover:text-[var(--color-blue)] transition-colors\">\r\n                    {week.title}\r\n                  </h3>\r\n                  <p className=\"text-[var(--color-blue)] font-medium text-sm\">\r\n                    {week.subtitle}\r\n                  </p>\r\n                </div>\r\n                \r\n                <p className=\"text-[var(--foreground-secondary)] leading-relaxed text-sm\">\r\n                  {week.description}\r\n                </p>\r\n\r\n                {/* Progress Bar */}\r\n                <div className=\"space-y-2\">\r\n                  <div className=\"flex justify-between text-sm text-[var(--foreground-secondary)]\">\r\n                    <span>{week.completedLessons} of {week.totalLessons} lessons</span>\r\n                    <span>{week.progress}%</span>\r\n                  </div>\r\n                  <div className=\"w-full bg-gray-200 rounded-full h-2\">\r\n                    <div \r\n                      className={`h-2 bg-gradient-to-r ${week.color} rounded-full transition-all duration-300`}\r\n                      style={{ width: `${week.progress}%` }}\r\n                    />\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Lesson Types */}\r\n                <div className=\"flex gap-2\">\r\n                  <div className=\"flex items-center gap-1 text-xs text-[var(--foreground-secondary)]\">\r\n                    <Play className=\"w-3 h-3\" />\r\n                    <span>Video</span>\r\n                  </div>\r\n                  <div className=\"flex items-center gap-1 text-xs text-[var(--foreground-secondary)]\">\r\n                    <CheckCircle className=\"w-3 h-3\" />\r\n                    <span>Lab</span>\r\n                  </div>\r\n                  <div className=\"flex items-center gap-1 text-xs text-[var(--foreground-secondary)]\">\r\n                    <Star className=\"w-3 h-3\" />\r\n                    <span>Quiz</span>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* CTA */}\r\n                <div className=\"flex items-center justify-between pt-3\">\r\n                  <span className=\"text-sm text-[var(--foreground-secondary)]\">Click to view details</span>\r\n                  <ArrowRight className=\"w-5 h-5 text-gray-400 group-hover:text-[var(--color-blue)] group-hover:translate-x-1 transition-all\" />\r\n                </div>\r\n              </div>\r\n\r\n              {/* Hover Effects */}\r\n              <div className=\"absolute inset-0 bg-gradient-to-br from-[var(--color-blue)]/5 to-[var(--color-blue-secondary)]/5 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300\" />\r\n              <div className={`absolute inset-0 bg-gradient-to-br ${week.color} rounded-3xl opacity-0 group-hover:opacity-5 transition-opacity duration-300 blur-xl`} />\r\n            </motion.div>\r\n          ))}\r\n        </div>\r\n\r\n        {/* Course Stats */}\r\n        <motion.div \r\n          initial={{ opacity: 0, y: 30 }}\r\n          whileInView={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.8, duration: 0.6 }}\r\n          viewport={{ once: true }}\r\n          className=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\"\r\n        >\r\n          <div className=\"text-center\">\r\n            <div className=\"text-4xl font-bold text-[var(--color-blue)] mb-2\">17</div>\r\n            <div className=\"text-[var(--foreground-secondary)]\">Total Lessons</div>\r\n          </div>\r\n          <div className=\"text-center\">\r\n            <div className=\"text-4xl font-bold text-[var(--color-dark-blue)] mb-2\">24</div>\r\n            <div className=\"text-[var(--foreground-secondary)]\">Hours Content</div>\r\n          </div>\r\n          <div className=\"text-center\">\r\n            <div className=\"text-4xl font-bold text-[var(--color-yellow)] mb-2\">3</div>\r\n            <div className=\"text-[var(--foreground-secondary)]\">Hands-on Labs</div>\r\n          </div>\r\n          <div className=\"text-center\">\r\n            <div className=\"text-4xl font-bold text-[var(--color-blue-secondary)] mb-2\">3</div>\r\n            <div className=\"text-[var(--foreground-secondary)]\">Assessments</div>\r\n          </div>\r\n        </motion.div>\r\n      </div>\r\n\r\n      {/* Modal */}\r\n      <AnimatePresence>\r\n        {isModalOpen && selectedWeek && (\r\n          <motion.div\r\n            initial={{ opacity: 0 }}\r\n            animate={{ opacity: 1 }}\r\n            exit={{ opacity: 0 }}\r\n            className=\"fixed inset-0 z-50 flex items-center justify-center p-4\"\r\n          >\r\n            {/* Backdrop */}\r\n            <motion.div\r\n              initial={{ opacity: 0 }}\r\n              animate={{ opacity: 1 }}\r\n              exit={{ opacity: 0 }}\r\n              onClick={closeModal}\r\n              className=\"absolute inset-0 bg-black/50 backdrop-blur-sm\"\r\n            />\r\n\r\n            {/* Modal Content */}\r\n            <motion.div\r\n              initial={{ opacity: 0, scale: 0.9, y: 20 }}\r\n              animate={{ opacity: 1, scale: 1, y: 0 }}\r\n              exit={{ opacity: 0, scale: 0.9, y: 20 }}\r\n              transition={{ type: \"spring\", damping: 25, stiffness: 300 }}\r\n              className=\"relative bg-white rounded-3xl shadow-2xl max-w-4xl w-full max-h-[80vh] overflow-hidden\"\r\n            >\r\n              {/* Modal Header */}\r\n              <div className={`bg-gradient-to-r ${selectedWeek.color} p-6 text-white`}>\r\n                <div className=\"flex items-center justify-between\">\r\n                  <div>\r\n                    <h2 className=\"text-2xl font-bold mb-1\">{selectedWeek.title}</h2>\r\n                    <p className=\"text-lg opacity-90\">{selectedWeek.subtitle}</p>\r\n                  </div>\r\n                  <motion.button\r\n                    whileHover={{ scale: 1.1 }}\r\n                    whileTap={{ scale: 0.9 }}\r\n                    onClick={closeModal}\r\n                    className=\"w-8 h-8 bg-white/20 rounded-full flex items-center justify-center hover:bg-white/30 transition-colors\"\r\n                  >\r\n                    <X className=\"w-5 h-5\" />\r\n                  </motion.button>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Modal Body */}\r\n              <div className=\"p-6 max-h-[50vh] overflow-y-auto\">\r\n                <div className=\"space-y-4\">\r\n                  <p className=\"text-[var(--foreground-secondary)] text-base leading-relaxed\">\r\n                    {selectedWeek.description}\r\n                  </p>\r\n\r\n                  {/* Progress Summary */}\r\n                  <div className=\"bg-gray-50 rounded-2xl p-4\">\r\n                    <div className=\"flex items-center justify-between mb-3\">\r\n                      <h3 className=\"text-base font-semibold text-[var(--color-dark-blue)]\">Progress</h3>\r\n                      <span className=\"text-xl font-bold text-[var(--color-blue)]\">{selectedWeek.progress}%</span>\r\n                    </div>\r\n                    <div className=\"w-full bg-gray-200 rounded-full h-2\">\r\n                      <div \r\n                        className={`h-2 bg-gradient-to-r ${selectedWeek.color} rounded-full transition-all duration-300`}\r\n                        style={{ width: `${selectedWeek.progress}%` }}\r\n                      />\r\n                    </div>\r\n                    <div className=\"flex justify-between text-sm text-[var(--foreground-secondary)] mt-2\">\r\n                      <span>{selectedWeek.completedLessons} of {selectedWeek.totalLessons} lessons completed</span>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Lessons List */}\r\n                  <div>\r\n                    <h3 className=\"text-lg font-semibold text-[var(--color-dark-blue)] mb-3\">Lessons</h3>\r\n                    <div className=\"space-y-2\">\r\n                      {selectedWeek.lessons.map((lesson, index) => (\r\n                        <motion.div\r\n                          key={lesson.id}\r\n                          initial={{ opacity: 0, x: -20 }}\r\n                          animate={{ opacity: 1, x: 0 }}\r\n                          transition={{ delay: index * 0.1 }}\r\n                          className=\"flex items-center justify-between p-3 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors\"\r\n                        >\r\n                          <div className=\"flex items-center gap-3\">\r\n                            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${\r\n                              lesson.status === 'completed' ? 'bg-green-500' :\r\n                              lesson.status === 'in-progress' ? 'bg-[var(--color-blue)]' : 'bg-gray-400'\r\n                            }`}>\r\n                              {lesson.status === 'completed' ? (\r\n                                <CheckCircle className=\"w-4 h-4 text-white\" />\r\n                              ) : lesson.status === 'in-progress' ? (\r\n                                <Play className=\"w-4 h-4 text-white\" />\r\n                              ) : (\r\n                                <Lock className=\"w-4 h-4 text-white\" />\r\n                              )}\r\n                            </div>\r\n                            <div>\r\n                              <h4 className=\"font-semibold text-[var(--color-dark-blue)] text-sm\">{lesson.title}</h4>\r\n                              <div className=\"flex items-center gap-3 text-xs text-[var(--foreground-secondary)]\">\r\n                                <span className=\"flex items-center gap-1\">\r\n                                  <Clock className=\"w-3 h-3\" />\r\n                                  {lesson.duration}\r\n                                </span>\r\n                                <span className=\"capitalize\">{lesson.type}</span>\r\n                              </div>\r\n                            </div>\r\n                          </div>\r\n                          <div className={`px-2 py-1 rounded-full text-xs font-semibold ${\r\n                            lesson.status === 'completed' ? 'bg-green-100 text-green-700' :\r\n                            lesson.status === 'in-progress' ? 'bg-[var(--color-blue)]/10 text-[var(--color-blue)]' : 'bg-gray-100 text-gray-700'\r\n                          }`}>\r\n                            {lesson.status === 'completed' ? 'Completed' :\r\n                             lesson.status === 'in-progress' ? 'In Progress' : 'Locked'}\r\n                          </div>\r\n                        </motion.div>\r\n                      ))}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Modal Footer */}\r\n              <div className=\"p-6 border-t border-gray-200\">\r\n                <div className=\"flex justify-between items-center\">\r\n                  <div className=\"text-sm text-[var(--foreground-secondary)]\">\r\n                    Total duration: {selectedWeek.lessons.reduce((acc, lesson) => {\r\n                      const minutes = parseInt(lesson.duration.split(' ')[0]);\r\n                      return acc + minutes;\r\n                    }, 0)} minutes\r\n                  </div>\r\n                  <PrimaryButton className=\"px-6 py-2 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300\">\r\n                    Continue Learning\r\n                  </PrimaryButton>\r\n                </div>\r\n              </div>\r\n            </motion.div>\r\n          </motion.div>\r\n        )}\r\n      </AnimatePresence>\r\n    </section>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AALA;;;;;;AAOA,MAAM,cAAc;IAClB;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,SAAS;YACP;gBAAE,IAAI;gBAAG,OAAO;gBAAiC,UAAU;gBAAU,QAAQ;gBAAa,MAAM;YAAQ;YACxG;gBAAE,IAAI;gBAAG,OAAO;gBAA0B,UAAU;gBAAU,QAAQ;gBAAa,MAAM;YAAQ;YACjG;gBAAE,IAAI;gBAAG,OAAO;gBAAmC,UAAU;gBAAU,QAAQ;gBAAa,MAAM;YAAM;YACxG;gBAAE,IAAI;gBAAG,OAAO;gBAAmC,UAAU;gBAAU,QAAQ;gBAAe,MAAM;YAAQ;YAC5G;gBAAE,IAAI;gBAAG,OAAO;gBAAqB,UAAU;gBAAU,QAAQ;gBAAU,MAAM;YAAO;SACzF;QACD,OAAO;QACP,UAAU;QACV,cAAc;QACd,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,SAAS;YACP;gBAAE,IAAI;gBAAG,OAAO;gBAAyB,UAAU;gBAAU,QAAQ;gBAAU,MAAM;YAAQ;YAC7F;gBAAE,IAAI;gBAAG,OAAO;gBAAyB,UAAU;gBAAU,QAAQ;gBAAU,MAAM;YAAQ;YAC7F;gBAAE,IAAI;gBAAG,OAAO;gBAAgC,UAAU;gBAAU,QAAQ;gBAAU,MAAM;YAAQ;YACpG;gBAAE,IAAI;gBAAG,OAAO;gBAAoB,UAAU;gBAAW,QAAQ;gBAAU,MAAM;YAAM;YACvF;gBAAE,IAAI;gBAAG,OAAO;gBAAoC,UAAU;gBAAU,QAAQ;gBAAU,MAAM;YAAQ;YACxG;gBAAE,IAAI;gBAAG,OAAO;gBAAqB,UAAU;gBAAU,QAAQ;gBAAU,MAAM;YAAO;SACzF;QACD,OAAO;QACP,UAAU;QACV,cAAc;QACd,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,SAAS;YACP;gBAAE,IAAI;gBAAG,OAAO;gBAA0B,UAAU;gBAAU,QAAQ;gBAAU,MAAM;YAAQ;YAC9F;gBAAE,IAAI;gBAAG,OAAO;gBAA4B,UAAU;gBAAW,QAAQ;gBAAU,MAAM;YAAQ;YACjG;gBAAE,IAAI;gBAAG,OAAO;gBAAuB,UAAU;gBAAU,QAAQ;gBAAU,MAAM;YAAQ;YAC3F;gBAAE,IAAI;gBAAG,OAAO;gBAA4B,UAAU;gBAAW,QAAQ;gBAAU,MAAM;YAAM;YAC/F;gBAAE,IAAI;gBAAG,OAAO;gBAA2B,UAAU;gBAAU,QAAQ;gBAAU,MAAM;YAAQ;YAC/F;gBAAE,IAAI;gBAAG,OAAO;gBAAoB,UAAU;gBAAW,QAAQ;gBAAU,MAAM;YAAO;SACzF;QACD,OAAO;QACP,UAAU;QACV,cAAc;QACd,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,SAAS;YACP;gBAAE,IAAI;gBAAG,OAAO;gBAA2B,UAAU;gBAAU,QAAQ;gBAAU,MAAM;YAAQ;YAC/F;gBAAE,IAAI;gBAAG,OAAO;gBAAkB,UAAU;gBAAW,QAAQ;gBAAU,MAAM;YAAQ;YACvF;gBAAE,IAAI;gBAAG,OAAO;gBAAiC,UAAU;gBAAU,QAAQ;gBAAU,MAAM;YAAQ;YACrG;gBAAE,IAAI;gBAAG,OAAO;gBAAwB,UAAU;gBAAW,QAAQ;gBAAU,MAAM;YAAM;YAC3F;gBAAE,IAAI;gBAAG,OAAO;gBAAsB,UAAU;gBAAU,QAAQ;gBAAU,MAAM;YAAQ;SAC3F;QACD,OAAO;QACP,UAAU;QACV,cAAc;QACd,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,SAAS;YACP;gBAAE,IAAI;gBAAG,OAAO;gBAA6B,UAAU;gBAAU,QAAQ;gBAAU,MAAM;YAAQ;YACjG;gBAAE,IAAI;gBAAG,OAAO;gBAAwB,UAAU;gBAAW,QAAQ;gBAAU,MAAM;YAAQ;YAC7F;gBAAE,IAAI;gBAAG,OAAO;gBAAwB,UAAU;gBAAU,QAAQ;gBAAU,MAAM;YAAQ;YAC5F;gBAAE,IAAI;gBAAG,OAAO;gBAAsB,UAAU;gBAAW,QAAQ;gBAAU,MAAM;YAAM;YACzF;gBAAE,IAAI;gBAAG,OAAO;gBAAyB,UAAU;gBAAU,QAAQ;gBAAU,MAAM;YAAQ;SAC9F;QACD,OAAO;QACP,UAAU;QACV,cAAc;QACd,kBAAkB;IACpB;CACD;AAEc,SAAS;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,YAAY,CAAC;QACjB,gBAAgB;QAChB,eAAe;IACjB;IAEA,MAAM,aAAa;QACjB,eAAe;QACf,gBAAgB;IAClB;IAEA,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,aAAa;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCACpC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAEV,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;wCAAK,WAAU;kDAA+C;;;;;;;;;;;;0CAGjE,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;gCACR,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;oCACX;kDAEC,8OAAC;wCAAK,WAAU;kDAAiH;;;;;;;;;;;;0CAKnI,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gCACP,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;0CACX;;;;;;;;;;;;kCAOH,8OAAC;wBAAI,WAAU;kCACZ,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,OAAO,QAAQ;oCAAK,UAAU;gCAAI;gCAChD,UAAU;oCAAE,MAAM;gCAAK;gCACvB,YAAY;oCACV,GAAG,CAAC;oCACJ,OAAO;oCACP,YAAY;wCAAE,UAAU;oCAAI;gCAC9B;gCACA,SAAS,IAAM,UAAU;gCACzB,WAAU;;kDAGV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAW,CAAC,4BAA4B,EAAE,KAAK,KAAK,CAAC,uDAAuD,CAAC;0DAChH,cAAA,8OAAC;oDAAK,WAAU;8DAAgC,KAAK,EAAE;;;;;;;;;;;0DAEzD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAA6C;;;;;;kEAC5D,8OAAC;wDAAI,WAAU;;4DAAoD,KAAK,QAAQ;4DAAC;;;;;;;;;;;;;;;;;;;kDAKrF,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEACX,KAAK,KAAK;;;;;;kEAEb,8OAAC;wDAAE,WAAU;kEACV,KAAK,QAAQ;;;;;;;;;;;;0DAIlB,8OAAC;gDAAE,WAAU;0DACV,KAAK,WAAW;;;;;;0DAInB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;oEAAM,KAAK,gBAAgB;oEAAC;oEAAK,KAAK,YAAY;oEAAC;;;;;;;0EACpD,8OAAC;;oEAAM,KAAK,QAAQ;oEAAC;;;;;;;;;;;;;kEAEvB,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DACC,WAAW,CAAC,qBAAqB,EAAE,KAAK,KAAK,CAAC,yCAAyC,CAAC;4DACxF,OAAO;gEAAE,OAAO,GAAG,KAAK,QAAQ,CAAC,CAAC,CAAC;4DAAC;;;;;;;;;;;;;;;;;0DAM1C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,8OAAC;0EAAK;;;;;;;;;;;;kEAER,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,2NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;0EACvB,8OAAC;0EAAK;;;;;;;;;;;;kEAER,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,8OAAC;0EAAK;;;;;;;;;;;;;;;;;;0DAKV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAA6C;;;;;;kEAC7D,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;;;;;;;kDAK1B,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAW,CAAC,mCAAmC,EAAE,KAAK,KAAK,CAAC,oFAAoF,CAAC;;;;;;;+BA9EjJ,KAAK,EAAE;;;;;;;;;;kCAoFlB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,OAAO;4BAAK,UAAU;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAmD;;;;;;kDAClE,8OAAC;wCAAI,WAAU;kDAAqC;;;;;;;;;;;;0CAEtD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAwD;;;;;;kDACvE,8OAAC;wCAAI,WAAU;kDAAqC;;;;;;;;;;;;0CAEtD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAqD;;;;;;kDACpE,8OAAC;wCAAI,WAAU;kDAAqC;;;;;;;;;;;;0CAEtD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAA6D;;;;;;kDAC5E,8OAAC;wCAAI,WAAU;kDAAqC;;;;;;;;;;;;;;;;;;;;;;;;0BAM1D,8OAAC,yLAAA,CAAA,kBAAe;0BACb,eAAe,8BACd,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,WAAU;;sCAGV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;4BAAE;4BACtB,SAAS;gCAAE,SAAS;4BAAE;4BACtB,MAAM;gCAAE,SAAS;4BAAE;4BACnB,SAAS;4BACT,WAAU;;;;;;sCAIZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,OAAO;gCAAK,GAAG;4BAAG;4BACzC,SAAS;gCAAE,SAAS;gCAAG,OAAO;gCAAG,GAAG;4BAAE;4BACtC,MAAM;gCAAE,SAAS;gCAAG,OAAO;gCAAK,GAAG;4BAAG;4BACtC,YAAY;gCAAE,MAAM;gCAAU,SAAS;gCAAI,WAAW;4BAAI;4BAC1D,WAAU;;8CAGV,8OAAC;oCAAI,WAAW,CAAC,iBAAiB,EAAE,aAAa,KAAK,CAAC,eAAe,CAAC;8CACrE,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA2B,aAAa,KAAK;;;;;;kEAC3D,8OAAC;wDAAE,WAAU;kEAAsB,aAAa,QAAQ;;;;;;;;;;;;0DAE1D,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gDACZ,YAAY;oDAAE,OAAO;gDAAI;gDACzB,UAAU;oDAAE,OAAO;gDAAI;gDACvB,SAAS;gDACT,WAAU;0DAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;8CAMnB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DACV,aAAa,WAAW;;;;;;0DAI3B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAwD;;;;;;0EACtE,8OAAC;gEAAK,WAAU;;oEAA8C,aAAa,QAAQ;oEAAC;;;;;;;;;;;;;kEAEtF,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DACC,WAAW,CAAC,qBAAqB,EAAE,aAAa,KAAK,CAAC,yCAAyC,CAAC;4DAChG,OAAO;gEAAE,OAAO,GAAG,aAAa,QAAQ,CAAC,CAAC,CAAC;4DAAC;;;;;;;;;;;kEAGhD,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;;gEAAM,aAAa,gBAAgB;gEAAC;gEAAK,aAAa,YAAY;gEAAC;;;;;;;;;;;;;;;;;;0DAKxE,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA2D;;;;;;kEACzE,8OAAC;wDAAI,WAAU;kEACZ,aAAa,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,sBACjC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gEAET,SAAS;oEAAE,SAAS;oEAAG,GAAG,CAAC;gEAAG;gEAC9B,SAAS;oEAAE,SAAS;oEAAG,GAAG;gEAAE;gEAC5B,YAAY;oEAAE,OAAO,QAAQ;gEAAI;gEACjC,WAAU;;kFAEV,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAW,CAAC,sDAAsD,EACrE,OAAO,MAAM,KAAK,cAAc,iBAChC,OAAO,MAAM,KAAK,gBAAgB,2BAA2B,eAC7D;0FACC,OAAO,MAAM,KAAK,4BACjB,8OAAC,2NAAA,CAAA,cAAW;oFAAC,WAAU;;;;;2FACrB,OAAO,MAAM,KAAK,8BACpB,8OAAC,kMAAA,CAAA,OAAI;oFAAC,WAAU;;;;;yGAEhB,8OAAC,kMAAA,CAAA,OAAI;oFAAC,WAAU;;;;;;;;;;;0FAGpB,8OAAC;;kGACC,8OAAC;wFAAG,WAAU;kGAAuD,OAAO,KAAK;;;;;;kGACjF,8OAAC;wFAAI,WAAU;;0GACb,8OAAC;gGAAK,WAAU;;kHACd,8OAAC,oMAAA,CAAA,QAAK;wGAAC,WAAU;;;;;;oGAChB,OAAO,QAAQ;;;;;;;0GAElB,8OAAC;gGAAK,WAAU;0GAAc,OAAO,IAAI;;;;;;;;;;;;;;;;;;;;;;;;kFAI/C,8OAAC;wEAAI,WAAW,CAAC,6CAA6C,EAC5D,OAAO,MAAM,KAAK,cAAc,gCAChC,OAAO,MAAM,KAAK,gBAAgB,uDAAuD,6BACzF;kFACC,OAAO,MAAM,KAAK,cAAc,cAChC,OAAO,MAAM,KAAK,gBAAgB,gBAAgB;;;;;;;+DAnChD,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;8CA6C1B,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;oDAA6C;oDACzC,aAAa,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK;wDACjD,MAAM,UAAU,SAAS,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;wDACtD,OAAO,MAAM;oDACf,GAAG;oDAAG;;;;;;;0DAER,8OAAC,gJAAA,CAAA,gBAAa;gDAAC,WAAU;0DAA2F;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWtI", "debugId": null}}, {"offset": {"line": 4149, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/training/components/StudentLogos.jsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useEffect, useRef } from 'react';\nimport { motion } from 'framer-motion';\nimport { Building2, GraduationCap, Users, Star } from 'lucide-react';\n\nconst studentLogos = [\n  {\n    id: 1,\n    name: \"AIA NZ\",\n    logo: \"/images/company1.png\",\n    type: \"Insurance\",\n    students: 45,\n    rating: 4.9\n  },\n  {\n    id: 2,\n    name: \"Data Torque\",\n    logo: \"/images/company2.png\",\n    type: \"Data Analytics\",\n    students: 32,\n    rating: 4.8\n  },\n  {\n    id: 3,\n    name: \"CyberDefense Inc\",\n    logo: \"/images/company3.png\",\n    type: \"Security Firm\",\n    students: 28,\n    rating: 4.9\n  },\n  {\n    id: 4,\n    name: \"CloudTech Systems\",\n    logo: \"/images/company4.png\",\n    type: \"Cloud Provider\",\n    students: 38,\n    rating: 4.7\n  },\n  {\n    id: 5,\n    name: \"SecureNet\",\n    logo: \"/images/company5.png\",\n    type: \"Network Security\",\n    students: 25,\n    rating: 4.8\n  },\n  {\n    id: 6,\n    name: \"DataVault\",\n    logo: \"/images/company6.png\",\n    type: \"Data Protection\",\n    students: 30,\n    rating: 4.9\n  },\n  {\n    id: 7,\n    name: \"AppShield\",\n    logo: \"/images/company7.png\",\n    type: \"Application Security\",\n    students: 22,\n    rating: 4.7\n  },\n  {\n    id: 8,\n    name: \"InfraGuard\",\n    logo: \"/images/company8.png\",\n    type: \"Infrastructure\",\n    students: 35,\n    rating: 4.8\n  }\n];\n\nexport default function StudentLogos() {\n  const containerRef = useRef(null);\n\n  useEffect(() => {\n    const container = containerRef.current;\n    if (!container) return;\n\n    let animationId;\n    let scrollPosition = 0;\n    const scrollSpeed = 0.5;\n\n    const animate = () => {\n      scrollPosition += scrollSpeed;\n      if (container) {\n        container.style.transform = `translateX(-${scrollPosition}px)`;\n        \n        // Reset position when scrolled too far\n        if (scrollPosition >= container.scrollWidth / 2) {\n          scrollPosition = 0;\n        }\n      }\n      animationId = requestAnimationFrame(animate);\n    };\n\n    animate();\n\n    return () => {\n      if (animationId) {\n        cancelAnimationFrame(animationId);\n      }\n    };\n  }, []);\n\n  return (\n    <section className=\"py-24 bg-gradient-to-br from-[var(--color-dark-blue)] via-[var(--color-dark-blue-hover)] to-[var(--color-dark-blue-bg)] relative overflow-hidden\">\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0\">\n        <div className=\"absolute top-0 left-0 w-full h-full bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.03%22%3E%3Ccircle%20cx%3D%2230%22%20cy%3D%2230%22%20r%3D%221%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-50\"></div>\n      </div>\n\n      <div className=\"relative z-10 container mx-auto px-4 max-w-7xl\">\n        {/* Header */}\n        <motion.div \n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <motion.div \n            initial={{ opacity: 0, scale: 0.8 }}\n            whileInView={{ opacity: 1, scale: 1 }}\n            transition={{ delay: 0.2, duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"inline-flex items-center bg-[var(--color-blue)]/20 px-4 py-2 rounded-full mb-6\"\n          >\n            <Building2 className=\"w-4 h-4 text-[var(--color-blue)] mr-2\" />\n            <span className=\"text-sm font-medium text-[var(--color-blue)]\">Our Trained Students works at</span>\n          </motion.div>\n          \n          <motion.h2 \n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.4, duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"text-4xl lg:text-5xl font-bold text-white mb-6\"\n          >\n            Companies That\n            <span className=\"block bg-gradient-to-r from-[var(--color-blue)] via-[var(--color-blue-secondary)] to-[var(--color-yellow)] bg-clip-text text-transparent\">\n              Trust Our Training\n            </span>\n          </motion.h2>\n          \n          <motion.p \n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.6, duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"text-xl text-white/80 max-w-4xl mx-auto leading-relaxed\"\n          >\n            Join hundreds of organizations that have upskilled their teams with our \n            comprehensive cybersecurity training programs.\n          </motion.p>\n        </motion.div>\n\n        {/* Floating Logos Carousel */}\n        <div className=\"relative overflow-hidden py-8\">\n          <div \n            ref={containerRef}\n            className=\"flex gap-8 items-center\"\n            style={{ width: 'max-content' }}\n          >\n            {/* Duplicate logos for seamless loop */}\n            {[...studentLogos, ...studentLogos].map((company, index) => (\n              <motion.div\n                key={`${company.id}-${index}`}\n                initial={{ opacity: 0, y: 30 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ delay: index * 0.1, duration: 0.6 }}\n                viewport={{ once: true }}\n                whileHover={{ \n                  y: -5,\n                  scale: 1.02,\n                  transition: { duration: 0.3 }\n                }}\n                className=\"group relative flex-shrink-0\"\n              >\n                {/* 3D Card */}\n                <div className=\"relative bg-white/10 backdrop-blur-xl rounded-3xl p-6 border border-white/20 w-64 h-32 flex items-center justify-center shadow-lg hover:shadow-2xl transition-all duration-300\">\n                  \n                  {/* Company Logo Placeholder */}\n                  <div className=\"text-center space-y-3\">\n                    <div className=\"w-16 h-16 bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-2xl flex items-center justify-center mx-auto shadow-lg group-hover:shadow-xl transition-all duration-300\">\n                      <Building2 className=\"w-8 h-8 text-white\" />\n                    </div>\n                    <div>\n                      <h3 className=\"font-semibold text-white text-lg group-hover:text-[var(--color-blue)] transition-colors\">\n                        {company.name}\n                      </h3>\n                      <p className=\"text-[var(--color-blue)] text-sm\">{company.type}</p>\n                    </div>\n                  </div>\n\n                  {/* Stats Overlay */}\n                  <div className=\"absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300\">\n                    <div className=\"bg-black/50 backdrop-blur-sm rounded-lg p-2 text-white text-xs\">\n                      <div className=\"flex items-center gap-1\">\n                        <Users className=\"w-3 h-3\" />\n                        <span>{company.students}</span>\n                      </div>\n                      <div className=\"flex items-center gap-1\">\n                        <Star className=\"w-3 h-3 text-[var(--color-yellow)]\" />\n                        <span>{company.rating}</span>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Hover Effects */}\n                  <div className=\"absolute inset-0 bg-gradient-to-br from-[var(--color-blue)]/10 to-[var(--color-blue-secondary)]/10 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300\" />\n                  <div className=\"absolute inset-0 bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-3xl opacity-0 group-hover:opacity-5 transition-opacity duration-300 blur-xl\" />\n                </div>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n\n        {/* Stats Section */}\n        <motion.div \n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.8, duration: 0.6 }}\n          viewport={{ once: true }}\n          className=\"mt-20 grid grid-cols-1 md:grid-cols-4 gap-8\"\n        >\n          <div className=\"text-center\">\n            <div className=\"text-4xl font-bold text-[var(--color-blue)] mb-2\">500+</div>\n            <div className=\"text-white/70\">Students Trained</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-4xl font-bold text-[var(--color-blue-secondary)] mb-2\">50+</div>\n            <div className=\"text-white/70\">Companies</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-4xl font-bold text-[var(--color-yellow)] mb-2\">98%</div>\n            <div className=\"text-white/70\">Success Rate</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-4xl font-bold text-[var(--color-blue)] mb-2\">4.9</div>\n            <div className=\"text-white/70\">Average Rating</div>\n          </div>\n        </motion.div>\n\n        {/* CTA Section */}\n        <motion.div \n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ delay: 1, duration: 0.6 }}\n          viewport={{ once: true }}\n          className=\"text-center mt-16\"\n        >\n          <div className=\"inline-flex items-center gap-4 bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] text-white px-8 py-4 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer group\">\n            <GraduationCap className=\"w-5 h-5\" />\n            <span className=\"font-semibold text-lg\">Join These Companies</span>\n            <Star className=\"w-5 h-5 group-hover:scale-110 transition-transform\" />\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAJA;;;;;AAMA,MAAM,eAAe;IACnB;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,MAAM;QACN,UAAU;QACV,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,MAAM;QACN,UAAU;QACV,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,MAAM;QACN,UAAU;QACV,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,MAAM;QACN,UAAU;QACV,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,MAAM;QACN,UAAU;QACV,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,MAAM;QACN,UAAU;QACV,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,MAAM;QACN,UAAU;QACV,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,MAAM;QACN,UAAU;QACV,QAAQ;IACV;CACD;AAEc,SAAS;IACtB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAE5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY,aAAa,OAAO;QACtC,IAAI,CAAC,WAAW;QAEhB,IAAI;QACJ,IAAI,iBAAiB;QACrB,MAAM,cAAc;QAEpB,MAAM,UAAU;YACd,kBAAkB;YAClB,IAAI,WAAW;gBACb,UAAU,KAAK,CAAC,SAAS,GAAG,CAAC,YAAY,EAAE,eAAe,GAAG,CAAC;gBAE9D,uCAAuC;gBACvC,IAAI,kBAAkB,UAAU,WAAW,GAAG,GAAG;oBAC/C,iBAAiB;gBACnB;YACF;YACA,cAAc,sBAAsB;QACtC;QAEA;QAEA,OAAO;YACL,IAAI,aAAa;gBACf,qBAAqB;YACvB;QACF;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,aAAa;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCACpC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAEV,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,8OAAC;wCAAK,WAAU;kDAA+C;;;;;;;;;;;;0CAGjE,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;gCACR,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;oCACX;kDAEC,8OAAC;wCAAK,WAAU;kDAA2I;;;;;;;;;;;;0CAK7J,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gCACP,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;0CACX;;;;;;;;;;;;kCAOH,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,KAAK;4BACL,WAAU;4BACV,OAAO;gCAAE,OAAO;4BAAc;sCAG7B;mCAAI;mCAAiB;6BAAa,CAAC,GAAG,CAAC,CAAC,SAAS,sBAChD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,OAAO,QAAQ;wCAAK,UAAU;oCAAI;oCAChD,UAAU;wCAAE,MAAM;oCAAK;oCACvB,YAAY;wCACV,GAAG,CAAC;wCACJ,OAAO;wCACP,YAAY;4CAAE,UAAU;wCAAI;oCAC9B;oCACA,WAAU;8CAGV,cAAA,8OAAC;wCAAI,WAAU;;0DAGb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,gNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;;;;;;kEAEvB,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EACX,QAAQ,IAAI;;;;;;0EAEf,8OAAC;gEAAE,WAAU;0EAAoC,QAAQ,IAAI;;;;;;;;;;;;;;;;;;0DAKjE,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;8EACjB,8OAAC;8EAAM,QAAQ,QAAQ;;;;;;;;;;;;sEAEzB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,8OAAC;8EAAM,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;;;;0DAM3B,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;;;;;;;mCA5CZ,GAAG,QAAQ,EAAE,CAAC,CAAC,EAAE,OAAO;;;;;;;;;;;;;;;kCAoDrC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,OAAO;4BAAK,UAAU;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAmD;;;;;;kDAClE,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;0CAEjC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAA6D;;;;;;kDAC5E,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;0CAEjC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAqD;;;;;;kDACpE,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;0CAEjC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAmD;;;;;;kDAClE,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;kCAKnC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,OAAO;4BAAG,UAAU;wBAAI;wBACtC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;8CACzB,8OAAC;oCAAK,WAAU;8CAAwB;;;;;;8CACxC,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM5B", "debugId": null}}, {"offset": {"line": 4770, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/training/components/PremiumCTA.jsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { Crown, ArrowRight, CheckCircle, Star, Zap, Shield } from 'lucide-react';\n\nconst premiumFeatures = [\n  \"Access to advance Lab Subscription (TryHackMe, HackTheBox)\",\n  \"Personalized mentorship and guided learning\",\n  \"3 months of hands-on experience with SecurityLit\",\n  \"Professional report writing training\",\n  \"Experience letter upon completion\"\n];\n\nexport default function PremiumCTA() {\n  const [showForm, setShowForm] = useState(false);\n\n  return (\n    <>\n      {/* Main Premium Section */}\n      <section className=\"py-24 bg-gradient-to-br from-[var(--color-dark-blue)] via-[var(--color-dark-blue-hover)] to-[var(--color-dark-blue-bg)] relative overflow-hidden\">\n        {/* Background Elements */}\n        <div className=\"absolute inset-0\">\n          <div className=\"absolute top-0 left-0 w-full h-full bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.03%22%3E%3Ccircle%20cx%3D%2230%22%20cy%3D%2230%22%20r%3D%221%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-50\"></div>\n          <div className=\"absolute top-20 left-10 w-72 h-72 bg-[var(--color-blue)]/20 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob\"></div>\n          <div className=\"absolute bottom-20 right-10 w-72 h-72 bg-[var(--color-blue-secondary)]/20 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000\"></div>\n        </div>\n\n        <div className=\"relative z-10 container mx-auto px-4 max-w-7xl\">\n          {/* Header */}\n          <motion.div \n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n            className=\"text-center mb-16\"\n          >\n            <motion.div \n              initial={{ opacity: 0, scale: 0.8 }}\n              whileInView={{ opacity: 1, scale: 1 }}\n              transition={{ delay: 0.2, duration: 0.6 }}\n              viewport={{ once: true }}\n              className=\"inline-flex items-center bg-[var(--color-yellow)]/20 px-4 py-2 rounded-full mb-6\"\n            >\n              <Crown className=\"w-4 h-4 text-[var(--color-yellow)] mr-2\" />\n              <span className=\"text-sm font-medium text-[var(--color-yellow)]\">Premium Tier Benefits</span>\n            </motion.div>\n            \n            <motion.h2 \n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.4, duration: 0.6 }}\n              viewport={{ once: true }}\n              className=\"text-4xl lg:text-5xl font-bold text-white mb-6\"\n            >\n              As a Premium member, you'll work directly with\n              <span className=\"block bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] bg-clip-text text-transparent\">\n                SecurityLit\n              </span>\n            </motion.h2>\n            \n            <motion.p \n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.6, duration: 0.6 }}\n              viewport={{ once: true }}\n              className=\"text-xl text-white/80 max-w-4xl mx-auto leading-relaxed\"\n            >\n              on live penetration testing projects. This hands-on experience will immerse you in real-time cybersecurity challenges, teaching you how professional pentests are conducted. You'll learn the intricacies of report writing, client communication, and industry best practices. Upon completion, you'll receive an experience letter, validating your practical skills and boosting your career prospects in the cybersecurity field.\n            </motion.p>\n          </motion.div>\n\n          {/* Premium Features Grid */}\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-16\">\n            {/* Features List */}\n            <motion.div \n              initial={{ opacity: 0, x: -50 }}\n              whileInView={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.8 }}\n              viewport={{ once: true }}\n              className=\"space-y-6\"\n            >\n              {premiumFeatures.map((feature, index) => (\n                <motion.div\n                  key={index}\n                  initial={{ opacity: 0, x: -30 }}\n                  whileInView={{ opacity: 1, x: 0 }}\n                  transition={{ delay: index * 0.1, duration: 0.6 }}\n                  viewport={{ once: true }}\n                  className=\"flex items-center gap-4\"\n                >\n                  <div className=\"w-12 h-12 bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-2xl flex items-center justify-center shadow-lg\">\n                    <CheckCircle className=\"w-6 h-6 text-[var(--color-yellow)]\" />\n                  </div>\n                  <div>\n                    <h3 className=\"text-lg font-semibold text-white\">{feature}</h3>\n                  </div>\n                </motion.div>\n              ))}\n            </motion.div>\n\n            {/* Premium Card */}\n            <motion.div \n              initial={{ opacity: 0, x: 50 }}\n              whileInView={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.8 }}\n              viewport={{ once: true }}\n              className=\"relative\"\n            >\n              <div className=\"bg-white/10 backdrop-blur-xl rounded-3xl p-8 border border-white/20 shadow-2xl\">\n                <div className=\"text-center space-y-6\">\n                  {/* Premium Badge */}\n                  <div className=\"inline-flex items-center gap-2 bg-gradient-to-r from-[var(--color-yellow)] to-[var(--color-yellow-hover)] text-white px-6 py-3 rounded-full\">\n                    <Crown className=\"w-5 h-5\" />\n                    <span className=\"font-semibold\">Premium Plan</span>\n                  </div>\n\n                  {/* Pricing */}\n                  <div>\n                    <div className=\"text-4xl font-bold text-white mb-2\">\n                      $299\n                      <span className=\"text-lg text-white/70 font-normal\">/month</span>\n                    </div>\n                    <p className=\"text-white/70\">or $2,999/year (save 17%)</p>\n                  </div>\n\n                  {/* Features */}\n                  <div className=\"space-y-3\">\n                    <div className=\"flex items-center justify-between text-sm\">\n                      <span className=\"text-white/70\">All Basic Features</span>\n                      <CheckCircle className=\"w-4 h-4 text-green-400\" />\n                    </div>\n                    <div className=\"flex items-center justify-between text-sm\">\n                      <span className=\"text-white/70\">Advanced Modules</span>\n                      <CheckCircle className=\"w-4 h-4 text-green-400\" />\n                    </div>\n                    <div className=\"flex items-center justify-between text-sm\">\n                      <span className=\"text-white/70\">1-on-1 Mentoring</span>\n                      <CheckCircle className=\"w-4 h-4 text-green-400\" />\n                    </div>\n                    <div className=\"flex items-center justify-between text-sm\">\n                      <span className=\"text-white/70\">Certification</span>\n                      <CheckCircle className=\"w-4 h-4 text-green-400\" />\n                    </div>\n                    <div className=\"flex items-center justify-between text-sm\">\n                      <span className=\"text-white/70\">Priority Support</span>\n                      <CheckCircle className=\"w-4 h-4 text-green-400\" />\n                    </div>\n                  </div>\n\n                  {/* CTA Button */}\n                  <motion.button\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    onClick={() => setShowForm(true)}\n                    className=\"w-full bg-gradient-to-r from-[var(--color-yellow)] to-[var(--color-yellow-hover)] hover:from-[var(--color-yellow-hover)] hover:to-[var(--color-yellow)] text-white py-4 px-8 rounded-2xl font-bold text-lg shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center gap-2\"\n                  >\n                    <Zap className=\"w-5 h-5\" />\n                    Start Premium Trial\n                    <ArrowRight className=\"w-5 h-5\" />\n                  </motion.button>\n\n                  <p className=\"text-xs text-white/50\">\n                    30-day money-back guarantee • Cancel anytime\n                  </p>\n                </div>\n              </div>\n\n              {/* Floating Elements */}\n              <motion.div\n                animate={{ y: [-10, 10, -10] }}\n                transition={{ duration: 3, repeat: Infinity, ease: \"easeInOut\" }}\n                className=\"absolute -top-4 -right-4 w-8 h-8 bg-[var(--color-blue)] rounded-full opacity-80\"\n              />\n              <motion.div\n                animate={{ y: [10, -10, 10] }}\n                transition={{ duration: 4, repeat: Infinity, ease: \"easeInOut\" }}\n                className=\"absolute -bottom-4 -left-4 w-6 h-6 bg-[var(--color-blue-secondary)] rounded-full opacity-80\"\n              />\n            </motion.div>\n          </div>\n\n          {/* Trust Indicators */}\n          <motion.div \n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.8, duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"text-center\"\n          >\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-[var(--color-yellow)] mb-2\">10,000+</div>\n                <div className=\"text-white/70\">Premium Students</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-green-400 mb-2\">99%</div>\n                <div className=\"text-white/70\">Satisfaction Rate</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-[var(--color-blue)] mb-2\">24/7</div>\n                <div className=\"text-white/70\">Expert Support</div>\n              </div>\n            </div>\n          </motion.div>\n\n          {/* Bottom CTA */}\n          <motion.div \n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ delay: 1, duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"text-center mt-16\"\n          >\n            <motion.button\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              onClick={() => setShowForm(true)}\n              className=\"bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] text-[var(--color-dark-blue)] px-12 py-4 rounded-2xl font-bold text-xl shadow-2xl hover:shadow-3xl transition-all duration-300 flex items-center gap-3 mx-auto\"\n            >\n              <Crown className=\"w-6 h-6\" />\n              Upgrade to the Premium Tier\n              <ArrowRight className=\"w-6 h-6\" />\n            </motion.button>\n            <p className=\"text-white/70 mt-4 text-lg\">\n              Take your cybersecurity training to the next level with our premium tier. Unlock exclusive access to advanced labs, personalized mentorship, and hands-on projects that will transform your skills and career.\n            </p>\n          </motion.div>\n        </div>\n      </section>\n\n      {/* Premium Form Modal */}\n      {showForm && (\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          exit={{ opacity: 0 }}\n          className=\"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm\"\n          onClick={() => setShowForm(false)}\n        >\n          <motion.div\n            initial={{ opacity: 0, scale: 0.9, y: 20 }}\n            animate={{ opacity: 1, scale: 1, y: 0 }}\n            exit={{ opacity: 0, scale: 0.9, y: 20 }}\n            transition={{ type: \"spring\", damping: 25, stiffness: 300 }}\n            className=\"relative bg-white rounded-3xl shadow-2xl max-w-md w-full p-8\"\n            onClick={(e) => e.stopPropagation()}\n          >\n            <div className=\"text-center space-y-6\">\n              <div className=\"w-16 h-16 bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-3xl flex items-center justify-center mx-auto\">\n                <Crown className=\"w-8 h-8 text-[var(--color-yellow)]\" />\n              </div>\n              \n              <div>\n                <h3 className=\"text-2xl font-bold text-[var(--color-dark-blue)] mb-2\">Upgrade to Premium</h3>\n                <p className=\"text-[var(--foreground-secondary)]\">Get started with your premium learning journey</p>\n              </div>\n\n              <form className=\"space-y-4\">\n                <input\n                  type=\"email\"\n                  placeholder=\"Enter your email\"\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:border-[var(--color-blue)]\"\n                />\n                <input\n                  type=\"text\"\n                  placeholder=\"Full name\"\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:border-[var(--color-blue)]\"\n                />\n                <motion.button\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                  className=\"w-full bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] text-white py-3 px-6 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300\"\n                >\n                  Start Premium Trial\n                </motion.button>\n              </form>\n\n              <button\n                onClick={() => setShowForm(false)}\n                className=\"text-[var(--foreground-secondary)] hover:text-[var(--color-dark-blue)] transition-colors\"\n              >\n                Maybe later\n              </button>\n            </div>\n          </motion.div>\n        </motion.div>\n      )}\n    </>\n  );\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAJA;;;;;AAMA,MAAM,kBAAkB;IACtB;IACA;IACA;IACA;IACA;CACD;AAEc,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,qBACE;;0BAEE,8OAAC;gBAAQ,WAAU;;kCAEjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;kCAGjB,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAI;wCAClC,aAAa;4CAAE,SAAS;4CAAG,OAAO;wCAAE;wCACpC,YAAY;4CAAE,OAAO;4CAAK,UAAU;wCAAI;wCACxC,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;;0DAEV,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;gDAAK,WAAU;0DAAiD;;;;;;;;;;;;kDAGnE,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;wCACR,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,OAAO;4CAAK,UAAU;wCAAI;wCACxC,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;;4CACX;0DAEC,8OAAC;gDAAK,WAAU;0DAAiH;;;;;;;;;;;;kDAKnI,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;wCACP,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,OAAO;4CAAK,UAAU;wCAAI;wCACxC,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;kDACX;;;;;;;;;;;;0CAMH,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,UAAU;wCAAI;wCAC5B,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;kDAET,gBAAgB,GAAG,CAAC,CAAC,SAAS,sBAC7B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,SAAS;oDAAG,GAAG,CAAC;gDAAG;gDAC9B,aAAa;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAChC,YAAY;oDAAE,OAAO,QAAQ;oDAAK,UAAU;gDAAI;gDAChD,UAAU;oDAAE,MAAM;gDAAK;gDACvB,WAAU;;kEAEV,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,2NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;;;;;;kEAEzB,8OAAC;kEACC,cAAA,8OAAC;4DAAG,WAAU;sEAAoC;;;;;;;;;;;;+CAX/C;;;;;;;;;;kDAkBX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,UAAU;wCAAI;wCAC5B,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEAEb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;8EACjB,8OAAC;oEAAK,WAAU;8EAAgB;;;;;;;;;;;;sEAIlC,8OAAC;;8EACC,8OAAC;oEAAI,WAAU;;wEAAqC;sFAElD,8OAAC;4EAAK,WAAU;sFAAoC;;;;;;;;;;;;8EAEtD,8OAAC;oEAAE,WAAU;8EAAgB;;;;;;;;;;;;sEAI/B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAAgB;;;;;;sFAChC,8OAAC,2NAAA,CAAA,cAAW;4EAAC,WAAU;;;;;;;;;;;;8EAEzB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAAgB;;;;;;sFAChC,8OAAC,2NAAA,CAAA,cAAW;4EAAC,WAAU;;;;;;;;;;;;8EAEzB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAAgB;;;;;;sFAChC,8OAAC,2NAAA,CAAA,cAAW;4EAAC,WAAU;;;;;;;;;;;;8EAEzB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAAgB;;;;;;sFAChC,8OAAC,2NAAA,CAAA,cAAW;4EAAC,WAAU;;;;;;;;;;;;8EAEzB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAAgB;;;;;;sFAChC,8OAAC,2NAAA,CAAA,cAAW;4EAAC,WAAU;;;;;;;;;;;;;;;;;;sEAK3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4DACZ,YAAY;gEAAE,OAAO;4DAAK;4DAC1B,UAAU;gEAAE,OAAO;4DAAK;4DACxB,SAAS,IAAM,YAAY;4DAC3B,WAAU;;8EAEV,8OAAC,gMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;gEAAY;8EAE3B,8OAAC,kNAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;;;;;;;sEAGxB,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;0DAOzC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,GAAG;wDAAC,CAAC;wDAAI;wDAAI,CAAC;qDAAG;gDAAC;gDAC7B,YAAY;oDAAE,UAAU;oDAAG,QAAQ;oDAAU,MAAM;gDAAY;gDAC/D,WAAU;;;;;;0DAEZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,GAAG;wDAAC;wDAAI,CAAC;wDAAI;qDAAG;gDAAC;gDAC5B,YAAY;oDAAE,UAAU;oDAAG,QAAQ;oDAAU,MAAM;gDAAY;gDAC/D,WAAU;;;;;;;;;;;;;;;;;;0CAMhB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAqD;;;;;;8DACpE,8OAAC;oDAAI,WAAU;8DAAgB;;;;;;;;;;;;sDAEjC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAyC;;;;;;8DACxD,8OAAC;oDAAI,WAAU;8DAAgB;;;;;;;;;;;;sDAEjC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAmD;;;;;;8DAClE,8OAAC;oDAAI,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;;;;;;0CAMrC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,OAAO;oCAAG,UAAU;gCAAI;gCACtC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;wCACxB,SAAS,IAAM,YAAY;wCAC3B,WAAU;;0DAEV,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAY;0DAE7B,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;kDAExB,8OAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;;;;;;;;;;;;;YAQ/C,0BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,MAAM;oBAAE,SAAS;gBAAE;gBACnB,WAAU;gBACV,SAAS,IAAM,YAAY;0BAE3B,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,OAAO;wBAAK,GAAG;oBAAG;oBACzC,SAAS;wBAAE,SAAS;wBAAG,OAAO;wBAAG,GAAG;oBAAE;oBACtC,MAAM;wBAAE,SAAS;wBAAG,OAAO;wBAAK,GAAG;oBAAG;oBACtC,YAAY;wBAAE,MAAM;wBAAU,SAAS;wBAAI,WAAW;oBAAI;oBAC1D,WAAU;oBACV,SAAS,CAAC,IAAM,EAAE,eAAe;8BAEjC,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;0CAGnB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAwD;;;;;;kDACtE,8OAAC;wCAAE,WAAU;kDAAqC;;;;;;;;;;;;0CAGpD,8OAAC;gCAAK,WAAU;;kDACd,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;kDAEZ,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;kDAEZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;wCACxB,WAAU;kDACX;;;;;;;;;;;;0CAKH,8OAAC;gCACC,SAAS,IAAM,YAAY;gCAC3B,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}, {"offset": {"line": 5665, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/training/components/TrainingAccessForm.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState } from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport { Shield, User, Mail, Phone, Building, GraduationCap, ArrowRight, CheckCircle } from 'lucide-react';\r\n\r\nexport default function TrainingAccessForm() {\r\n  const [formData, setFormData] = useState({\r\n    name: '',\r\n    organization: '',\r\n    phone: '',\r\n    email: '',\r\n    disclaimer: false,\r\n    terms: false\r\n  });\r\n\r\n  const handleInputChange = (e) => {\r\n    const { name, value, type, checked } = e.target;\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      [name]: type === 'checkbox' ? checked : value\r\n    }));\r\n  };\r\n\r\n  const handleSubmit = (e) => {\r\n    e.preventDefault();\r\n    // Handle form submission\r\n    console.log('Form submitted:', formData);\r\n  };\r\n\r\n  return (\r\n    <section className=\"py-24 bg-gradient-to-br from-[var(--color-dark-blue)] via-[var(--color-dark-blue-hover)] to-[var(--color-dark-blue-bg)] relative overflow-hidden\">\r\n      {/* Background Pattern */}\r\n      <div className=\"absolute inset-0\">\r\n        <div className=\"absolute top-0 left-0 w-full h-full bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.03%22%3E%3Ccircle%20cx%3D%2230%22%20cy%3D%2230%22%20r%3D%221%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-50\"></div>\r\n      </div>\r\n\r\n      <div className=\"relative z-10 container mx-auto px-4 max-w-7xl\">\r\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center\">\r\n          {/* Content */}\r\n          <motion.div \r\n            initial={{ opacity: 0, x: -50 }}\r\n            whileInView={{ opacity: 1, x: 0 }}\r\n            transition={{ duration: 0.8 }}\r\n            viewport={{ once: true }}\r\n            className=\"space-y-8\"\r\n          >\r\n            <div>\r\n              <motion.div \r\n                initial={{ opacity: 0, scale: 0.8 }}\r\n                whileInView={{ opacity: 1, scale: 1 }}\r\n                transition={{ delay: 0.2, duration: 0.6 }}\r\n                viewport={{ once: true }}\r\n                className=\"inline-flex items-center bg-[var(--color-blue)]/20 px-4 py-2 rounded-full mb-6\"\r\n              >\r\n                <Shield className=\"w-4 h-4 text-[var(--color-blue)] mr-2\" />\r\n                <span className=\"text-sm font-medium text-[var(--color-blue)]\">Free Access</span>\r\n              </motion.div>\r\n              \r\n              <motion.h2 \r\n                initial={{ opacity: 0, y: 20 }}\r\n                whileInView={{ opacity: 1, y: 0 }}\r\n                transition={{ delay: 0.4, duration: 0.6 }}\r\n                viewport={{ once: true }}\r\n                className=\"text-4xl lg:text-5xl font-bold text-white mb-6\"\r\n              >\r\n                Start Your\r\n                <span className=\"block bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] bg-clip-text text-transparent\">\r\n                  Training Now!\r\n                </span>\r\n              </motion.h2>\r\n              \r\n              <motion.p \r\n                initial={{ opacity: 0, y: 20 }}\r\n                whileInView={{ opacity: 1, y: 0 }}\r\n                transition={{ delay: 0.6, duration: 0.6 }}\r\n                viewport={{ once: true }}\r\n                className=\"text-xl text-white/80 leading-relaxed mb-8\"\r\n              >\r\n                Become part of the next generation of cybersecurity professionals with SecurityLit. Our industry-aligned training program is designed to equip you with the skills needed to succeed in today's cybersecurity landscape.\r\n              </motion.p>\r\n            </div>\r\n\r\n            {/* Benefits */}\r\n            <div className=\"space-y-4\">\r\n              <div className=\"flex items-center gap-4\">\r\n                <div className=\"w-12 h-12 bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-xl flex items-center justify-center shadow-lg\">\r\n                  <CheckCircle className=\"w-6 h-6 text-white\" />\r\n                </div>\r\n                <div>\r\n                  <h3 className=\"text-white font-semibold text-lg\">Free Access</h3>\r\n                  <p className=\"text-white/70\">Get started with our comprehensive cybersecurity training program at no cost.</p>\r\n                </div>\r\n              </div>\r\n              \r\n              <div className=\"flex items-center gap-4\">\r\n                <div className=\"w-12 h-12 bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-xl flex items-center justify-center shadow-lg\">\r\n                  <GraduationCap className=\"w-6 h-6 text-white\" />\r\n                </div>\r\n                <div>\r\n                  <h3 className=\"text-white font-semibold text-lg\">Industry-Relevant Skills</h3>\r\n                  <p className=\"text-white/70\">Learn the latest tools and techniques used by cybersecurity professionals.</p>\r\n                </div>\r\n              </div>\r\n              \r\n              <div className=\"flex items-center gap-4\">\r\n                <div className=\"w-12 h-12 bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-xl flex items-center justify-center shadow-lg\">\r\n                  <Shield className=\"w-6 h-6 text-white\" />\r\n                </div>\r\n                <div>\r\n                  <h3 className=\"text-white font-semibold text-lg\">Career Ready</h3>\r\n                  <p className=\"text-white/70\">Develop practical skills that prepare you for real-world cybersecurity challenges.</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </motion.div>\r\n\r\n          {/* Form */}\r\n          <motion.div \r\n            initial={{ opacity: 0, x: 50 }}\r\n            whileInView={{ opacity: 1, x: 0 }}\r\n            transition={{ duration: 0.8 }}\r\n            viewport={{ once: true }}\r\n            className=\"relative\"\r\n          >\r\n            <div className=\"bg-white/10 backdrop-blur-xl rounded-3xl p-8 border border-white/20 shadow-2xl\">\r\n              <div className=\"text-center mb-8\">\r\n                <div className=\"w-16 h-16 bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-3xl flex items-center justify-center mx-auto mb-4\">\r\n                  <Shield className=\"w-8 h-8 text-white\" />\r\n                </div>\r\n                <h3 className=\"text-2xl font-bold text-white mb-2\">Sign Up for Free Access</h3>\r\n                <p className=\"text-white/70\">Join our Cybersecurity Training Program</p>\r\n              </div>\r\n\r\n              <form onSubmit={handleSubmit} className=\"space-y-6\">\r\n                <div>\r\n                  <label className=\"block text-white font-medium mb-2\">Name *</label>\r\n                  <div className=\"relative\">\r\n                    <User className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-white/50\" />\r\n                    <input\r\n                      type=\"text\"\r\n                      name=\"name\"\r\n                      value={formData.name}\r\n                      onChange={handleInputChange}\r\n                      required\r\n                      className=\"w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/50 focus:outline-none focus:border-[var(--color-blue)] transition-colors\"\r\n                      placeholder=\"Enter your full name\"\r\n                    />\r\n                  </div>\r\n                </div>\r\n\r\n                <div>\r\n                  <label className=\"block text-white font-medium mb-2\">Name of Organization *</label>\r\n                  <div className=\"relative\">\r\n                    <Building className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-white/50\" />\r\n                    <input\r\n                      type=\"text\"\r\n                      name=\"organization\"\r\n                      value={formData.organization}\r\n                      onChange={handleInputChange}\r\n                      required\r\n                      className=\"w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/50 focus:outline-none focus:border-[var(--color-blue)] transition-colors\"\r\n                      placeholder=\"Enter your organization name\"\r\n                    />\r\n                  </div>\r\n                </div>\r\n\r\n                <div>\r\n                  <label className=\"block text-white font-medium mb-2\">Contact Number</label>\r\n                  <div className=\"relative\">\r\n                    <Phone className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-white/50\" />\r\n                    <input\r\n                      type=\"tel\"\r\n                      name=\"phone\"\r\n                      value={formData.phone}\r\n                      onChange={handleInputChange}\r\n                      className=\"w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/50 focus:outline-none focus:border-[var(--color-blue)] transition-colors\"\r\n                      placeholder=\"Enter your phone number\"\r\n                    />\r\n                  </div>\r\n                </div>\r\n\r\n                <div>\r\n                  <label className=\"block text-white font-medium mb-2\">Work Email *</label>\r\n                  <div className=\"relative\">\r\n                    <Mail className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-white/50\" />\r\n                    <input\r\n                      type=\"email\"\r\n                      name=\"email\"\r\n                      value={formData.email}\r\n                      onChange={handleInputChange}\r\n                      required\r\n                      className=\"w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/50 focus:outline-none focus:border-[var(--color-blue)] transition-colors\"\r\n                      placeholder=\"Enter your work email\"\r\n                    />\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"space-y-4\">\r\n                  <label className=\"flex items-center gap-3 cursor-pointer\">\r\n                    <input\r\n                      type=\"checkbox\"\r\n                      name=\"disclaimer\"\r\n                      checked={formData.disclaimer}\r\n                      onChange={handleInputChange}\r\n                      required\r\n                      className=\"w-4 h-4 text-[var(--color-blue)] bg-white/10 border-white/20 rounded focus:ring-[var(--color-blue)]\"\r\n                    />\r\n                    <span className=\"text-white/80 text-sm\">\r\n                      I consent to receive communications about the security training program\r\n                    </span>\r\n                  </label>\r\n\r\n                  <label className=\"flex items-center gap-3 cursor-pointer\">\r\n                    <input\r\n                      type=\"checkbox\"\r\n                      name=\"terms\"\r\n                      checked={formData.terms}\r\n                      onChange={handleInputChange}\r\n                      required\r\n                      className=\"w-4 h-4 text-[var(--color-blue)] bg-white/10 border-white/20 rounded focus:ring-[var(--color-blue)]\"\r\n                    />\r\n                    <span className=\"text-white/80 text-sm\">\r\n                      I agree to the terms and conditions\r\n                    </span>\r\n                  </label>\r\n                </div>\r\n\r\n                <motion.button\r\n                  whileHover={{ scale: 1.02 }}\r\n                  whileTap={{ scale: 0.98 }}\r\n                  type=\"submit\"\r\n                  className=\"w-full bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] text-white py-4 px-8 rounded-xl font-bold text-lg shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center gap-2\"\r\n                >\r\n                  <Shield className=\"w-5 h-5\" />\r\n                  Submit Now\r\n                  <ArrowRight className=\"w-5 h-5\" />\r\n                </motion.button>\r\n              </form>\r\n            </div>\r\n          </motion.div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,cAAc;QACd,OAAO;QACP,OAAO;QACP,YAAY;QACZ,OAAO;IACT;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM;QAC/C,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE,SAAS,aAAa,UAAU;YAC1C,CAAC;IACH;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,yBAAyB;QACzB,QAAQ,GAAG,CAAC,mBAAmB;IACjC;IAEA,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,8OAAC;;sDACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,SAAS;gDAAG,OAAO;4CAAI;4CAClC,aAAa;gDAAE,SAAS;gDAAG,OAAO;4CAAE;4CACpC,YAAY;gDAAE,OAAO;gDAAK,UAAU;4CAAI;4CACxC,UAAU;gDAAE,MAAM;4CAAK;4CACvB,WAAU;;8DAEV,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;oDAAK,WAAU;8DAA+C;;;;;;;;;;;;sDAGjE,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;4CACR,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,aAAa;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAChC,YAAY;gDAAE,OAAO;gDAAK,UAAU;4CAAI;4CACxC,UAAU;gDAAE,MAAM;4CAAK;4CACvB,WAAU;;gDACX;8DAEC,8OAAC;oDAAK,WAAU;8DAAiH;;;;;;;;;;;;sDAKnI,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;4CACP,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,aAAa;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAChC,YAAY;gDAAE,OAAO;gDAAK,UAAU;4CAAI;4CACxC,UAAU;gDAAE,MAAM;4CAAK;4CACvB,WAAU;sDACX;;;;;;;;;;;;8CAMH,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,2NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;;;;;;8DAEzB,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAmC;;;;;;sEACjD,8OAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;sDAIjC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;;;;;;8DAE3B,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAmC;;;;;;sEACjD,8OAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;sDAIjC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;8DAEpB,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAmC;;;;;;sEACjD,8OAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOrC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;0DAEpB,8OAAC;gDAAG,WAAU;0DAAqC;;;;;;0DACnD,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;kDAG/B,8OAAC;wCAAK,UAAU;wCAAc,WAAU;;0DACtC,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAoC;;;;;;kEACrD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,8OAAC;gEACC,MAAK;gEACL,MAAK;gEACL,OAAO,SAAS,IAAI;gEACpB,UAAU;gEACV,QAAQ;gEACR,WAAU;gEACV,aAAY;;;;;;;;;;;;;;;;;;0DAKlB,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAoC;;;;;;kEACrD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,8OAAC;gEACC,MAAK;gEACL,MAAK;gEACL,OAAO,SAAS,YAAY;gEAC5B,UAAU;gEACV,QAAQ;gEACR,WAAU;gEACV,aAAY;;;;;;;;;;;;;;;;;;0DAKlB,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAoC;;;;;;kEACrD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,8OAAC;gEACC,MAAK;gEACL,MAAK;gEACL,OAAO,SAAS,KAAK;gEACrB,UAAU;gEACV,WAAU;gEACV,aAAY;;;;;;;;;;;;;;;;;;0DAKlB,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAoC;;;;;;kEACrD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,8OAAC;gEACC,MAAK;gEACL,MAAK;gEACL,OAAO,SAAS,KAAK;gEACrB,UAAU;gEACV,QAAQ;gEACR,WAAU;gEACV,aAAY;;;;;;;;;;;;;;;;;;0DAKlB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAM,WAAU;;0EACf,8OAAC;gEACC,MAAK;gEACL,MAAK;gEACL,SAAS,SAAS,UAAU;gEAC5B,UAAU;gEACV,QAAQ;gEACR,WAAU;;;;;;0EAEZ,8OAAC;gEAAK,WAAU;0EAAwB;;;;;;;;;;;;kEAK1C,8OAAC;wDAAM,WAAU;;0EACf,8OAAC;gEACC,MAAK;gEACL,MAAK;gEACL,SAAS,SAAS,KAAK;gEACvB,UAAU;gEACV,QAAQ;gEACR,WAAU;;;;;;0EAEZ,8OAAC;gEAAK,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;0DAM5C,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gDACZ,YAAY;oDAAE,OAAO;gDAAK;gDAC1B,UAAU;oDAAE,OAAO;gDAAK;gDACxB,MAAK;gDACL,WAAU;;kEAEV,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAY;kEAE9B,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxC", "debugId": null}}]}