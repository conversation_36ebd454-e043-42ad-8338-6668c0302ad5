import React from "react";
import Image from "next/image";
import Link from "next/link";
import { ExternalLink, ArrowRight, Linkedin, Phone, Mail, MapPin } from "lucide-react";
import { FaXTwitter } from 'react-icons/fa6';

const services = [
  { name: "Virtual CISO (vCISO)", href: "/services/vciso" },
  { name: "Red Teaming", href: "/services/red-teaming" },
  { name: "AWS and Azure Configuration", href: "/services/aws-azure" },
  { name: "Web3 Audits (Pentest)", href: "/services/web3-audits" },
  { name: "VDP", href: "/services/vdp" },
  { name: "VAPT", href: "/services/vapt" },
  { name: "Bug Bounty (Through Capture The Bug)", href: "/services/bug-bounty" },
  { name: "Compliance Pre Assessment", href: "/services/compliance" },
  { name: "Office365 Assessment", href: "/services/office365" },
  { name: "Cloud Assessment", href: "/services/cloud-assessment" },
  { name: "Google WorkSpace Assessment", href: "/services/google-workspace" },
  { name: "Incident response", href: "/services/incident-response" },
  { name: "Source code review", href: "/services/source-code-review" },
];

const company = [
  { name: "About Us", href: "/about" },
  { name: "Contact", href: "/contact" },
];

const resources = [
  { name: "Blogs", href: "/blog" },
  { name: "News", href: "/news" },
];

const social = [
  {
    name: "LinkedIn",
    href: "#",
    icon: <Linkedin size={20} />,
  },
  {
    name: "Twitter",
    href: "https://x.com/security_lit",
    icon: <FaXTwitter size={20} />,
  },
];

const Footer = () => {
  return (
    <div className="relative bg-transparent z-10">
      {/* CTA Section - Similar to CTB */}
      <section className="relative z-10 w-[90%] sm:w-4/5 mx-auto overflow-hidden rounded-xl shadow-2xl transform translate-y-1/2 -mt-6 sm:-mt-16 md:-mt-24">
        <div className="bg-[var(--color-blue)] relative">
          <div className="relative py-6 sm:py-8 lg:py-16 px-6 sm:px-8 lg:px-10">
            <div className="flex flex-col sm:flex-row items-center justify-between gap-4 sm:gap-6 text-center sm:text-left">
              <div className="flex-1">
                <h2 className="text-xl sm:text-2xl lg:text-3xl font-bold text-white mb-2 sm:mb-5 leading-tight">
                  Enterprise-grade security that scales with you.
                </h2>
                <p className="text-sm lg:text-base text-white/80 max-w-xl mb-4 sm:mb-0">
                  Comprehensive cybersecurity solutions for modern businesses.
                </p>
              </div>
              <div>
                <Link
                  href="/contact"
                  className="inline-flex items-center justify-center gap-2 bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] hover:from-[var(--color-blue-secondary)] hover:to-[var(--color-blue)] text-white font-medium px-6 py-3 sm:px-7 sm:py-3.5 rounded-lg transition-all duration-300 transform hover:-translate-y-0.5 hover:shadow-lg group relative overflow-hidden"
                >

                  <span className="relative">Get Started</span>
                  <ArrowRight className="w-4 h-4 relative transition-transform group-hover:translate-x-1" />
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Main Footer */}
      <footer className="w-full bg-[var(--color-dark-blue)] text-white pt-32 sm:pt-28 lg:pt-32">
        <div className="max-w-screen-2xl mx-auto px-3 sm:px-4 md:px-6 lg:px-12 xl:ml-10">
          <div className="py-8 sm:py-10 border-b border-white/10">
            <div className="grid grid-cols-1 lg:grid-cols-12 gap-8 sm:gap-10 lg:gap-12">

              {/* Left Side - Brand and Contact */}
              <div className="lg:col-span-4 text-center sm:text-left mx-5">
                <div className="mb-6 sm:mb-8">
                  <Image
                    src="/seclit-logo-white.png"
                    alt="SecurityLit Logo"
                    width={160}
                    height={56}
                    className="h-10 sm:h-12 lg:h-14 w-auto mx-auto sm:mx-0"
                  />
                </div>

                <p className="text-white/70 text-sm leading-6 mb-6 sm:mb-8 max-w-md mx-auto sm:mx-0">
                  Protecting businesses from cyber threats with comprehensive, enterprise-grade security solutions tailored for modern organizations.
                </p>

                {/* Contact Information */}
                <div className="mb-6 sm:mb-8">
                  <div className="space-y-4 sm:space-y-5">
                    <div className="flex items-center gap-3 text-white/70 text-sm">
                      <MapPin size={15} className="text-white/50 flex-shrink-0" />
                      <span>New Zealand - Hamilton, Waikato</span>
                    </div>
                    <div className="flex items-center gap-3 text-white/70 text-sm">
                      <MapPin size={15} className="text-white/50 flex-shrink-0" />
                      <span>India - Gurugram, Haryana</span>
                    </div>
                    <div className="flex items-center gap-3 text-white/70 text-sm">
                      <Mail size={15} className="text-white/50 flex-shrink-0" />
                      <a href="mailto:<EMAIL>" className="hover:text-white transition-colors break-all sm:break-normal"><EMAIL></a>
                    </div>
                    <div className="flex items-center gap-3 text-white/70 text-sm">
                      <Phone size={15} className="text-white/50 flex-shrink-0" />
                      <a href="tel:+6403940821" className="hover:text-white transition-colors">+64 (03) 394 0821</a>
                    </div>
                    <div className="flex items-center gap-3 text-white/70 text-sm">
                      <Phone size={15} className="text-white/50 flex-shrink-0" />
                      <a href="tel:+************" className="hover:text-white transition-colors">+91 8527 800769</a>
                    </div>
                  </div>
                </div>

                <div className="flex justify-center sm:justify-start space-x-4">
                  {social.map((s) => (
                    <a key={s.name} href={s.href} aria-label={s.name} className="text-white/70 hover:text-white transition-colors">
                      {s.icon}
                    </a>
                  ))}
                </div>
              </div>

              {/* Right Side - Navigation Links */}
              <div className="lg:col-span-8">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 sm:gap-10 lg:gap-12 justify-items-start">

                  {/* Services */}
                  <div>
                    <h3 className="text-white font-semibold text-sm mb-4 sm:mb-5">Services</h3>
                    <ul className="space-y-3 sm:space-y-4">
                      {services.slice(0, 8).map((item) => (
                        <li key={item.name}>
                          <a href={item.href} className="text-white/70 hover:text-white text-sm transition-colors">{item.name}</a>
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* More Services */}
                  <div>
                    <h3 className="text-white font-semibold text-sm mb-4 sm:mb-5">More Services</h3>
                    <ul className="space-y-3 sm:space-y-4">
                      {services.slice(8).map((item) => (
                        <li key={item.name}>
                          <a href={item.href} className="text-white/70 hover:text-white text-sm transition-colors">{item.name}</a>
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Company */}
                  <div>
                    <h3 className="text-white font-semibold text-sm mb-4 sm:mb-5">Company</h3>
                    <ul className="space-y-3 sm:space-y-4">
                      {company.map((item) => (
                        <li key={item.name}>
                          <a href={item.href} className="text-white/70 hover:text-white text-sm transition-colors">{item.name}</a>
                        </li>
                      ))}
                    </ul>

                    <h3 className="text-white font-semibold text-sm mb-4 sm:mb-5 mt-8">Resources</h3>
                    <ul className="space-y-3 sm:space-y-4">
                      {resources.map((item) => (
                        <li key={item.name}>
                          <a href={item.href} className="text-white/70 hover:text-white text-sm transition-colors">{item.name}</a>
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Find Us On - Pae Hokohoko */}
                  <div className="w-full lg:col-span-1 flex flex-col items-center">
                    <h3 className="text-white font-semibold text-sm mb-4 sm:mb-5 text-center w-full">Find Us On</h3>
                    <div className="bg-gradient-to-br from-white/8 to-white/3 rounded-xl p-5 border border-white/15 hover:bg-white/10 hover:border-white/20 transition-all duration-300 transform hover:-translate-y-1 hover:shadow-xl backdrop-blur-sm w-full max-w-[280px] -translate-x-1 lg:-translate-x-2">
                      <div className="flex items-center justify-center mb-4">
                        <div className="bg-white/10 rounded-lg p-2">
                          <Image
                            src="/images/pae-hokohoko-logo.jpeg"
                            alt="Pae Hokohoko Marketplace Logo"
                            width={80}
                            height={40}
                            className="object-contain rounded"
                          />
                        </div>
                      </div>
                      <p className="text-xs text-white/80 text-center mb-4 leading-relaxed">
                        Recognized supplier of VAPT services on the Pae Hokohoko marketplace
                      </p>
                      <a
                        href="https://marketplace.govt.nz/"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center justify-center gap-2 w-full bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] hover:from-[var(--color-blue-secondary)] hover:to-[var(--color-blue)] text-white px-4 py-3 rounded-lg text-xs font-semibold transition-all duration-300 transform hover:-translate-y-0.5 hover:shadow-lg group"
                      >
                        <span className="relative">Visit Marketplace</span>
                        <ExternalLink className="w-3 h-3 relative transition-transform group-hover:translate-x-1" />
                      </a>
                    </div>
                  </div>

                </div>
              </div>
            </div>

            <div className="mt-8 sm:mt-10 text-center">
              <p className="text-white/70 text-sm mt-4 sm:mt-5">
                © {new Date().getFullYear()} SecurityLit. All rights reserved. |
                <a href="/privacy" className="hover:text-white transition-colors ml-1">Privacy Policy</a>
              </p>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Footer; 