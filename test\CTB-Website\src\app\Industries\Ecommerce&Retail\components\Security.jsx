import React from "react";
import { Shield, Users, Target } from "lucide-react";
 
export default function  Security() {
  return (
    <> 
  <div className="bg-white py-16 sm:py-24">
  <div className="container mx-auto px-4 md:px-24">
    <div className="mb-16">
      <div className="text-secondary-blue text-xl font-bold mb-5">
        How Capture The Bug Helps
      </div>
      <h2 className="text-4xl sm:text-5xl font-bold text-[#010D2C] leading-tight mb-5">
Ecommerce Cybersecurity for Fast-Moving Stores & Scalable Retail Platforms</h2>
      <p className="text-[#6B7280] text-lg font-medium leading-relaxed  ">
Capture The Bug secures your <strong>ecommerce stack</strong>-including <strong>storefronts</strong>, <strong>APIs</strong>, <strong>checkout flows</strong>, and <strong>third-party plug-ins</strong>-through <strong>continuous penetration testing</strong>, <strong>fraud detection</strong>, and <strong>compliance-ready audits</strong> tailored for <strong>high-growth digital commerce</strong>.
      </p>
    </div>

          <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 lg:gap-8">
            <div className="bg-white border-l-4 border-[#58CC02] pl-4 sm:pl-6 pr-4 sm:pr-6 py-6 sm:py-8 rounded-r-lg shadow-sm hover:shadow-md transition-shadow duration-300">
              <div className="">
                <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-[#58CC02] to-[#4BAF02] rounded-lg flex items-center justify-center mb-4 sm:mb-6">
                  <Shield className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                </div>
          <h3 className="text-xl sm:text-2xl font-bold text-[#010D2C] mb-3 sm:mb-4">
Application & API Security                </h3>
                <p className="text-[#6B7280] text-base sm:text-lg leading-relaxed">
                  We test the entire surface of your <strong>online store</strong>-from <strong>mobile apps</strong> and <strong>custom checkout flows</strong> to <strong>headless APIs</strong> and <strong>payment gateways</strong>. Our security team identifies <strong>misconfigurations</strong>, <strong>broken authentication</strong>, <strong>session risks</strong>, and <strong>access flaws</strong>-ensuring that no exploitable gap goes undetected. Every assessment is designed to safeguard <strong>uptime</strong>, protect <strong>shopper data</strong>, and strengthen <strong>customer trust</strong> during critical transactions.
</p>              </div>
            </div>

            <div className="bg-white border-l-4 border-[#58CC02] pl-4 sm:pl-6 pr-4 sm:pr-6 py-6 sm:py-8 rounded-r-lg shadow-sm hover:shadow-md transition-shadow duration-300">
              <div className="">
                <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-[#58CC02] to-[#4BAF02] rounded-lg flex items-center justify-center mb-4 sm:mb-6">
                  <Users className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                </div>
          <h3 className="text-xl sm:text-2xl font-bold text-[#010D2C] mb-3 sm:mb-4">
Fraud & Account Takeover Prevention                </h3>
                <p className="text-[#6B7280] text-base sm:text-lg leading-relaxed">
                  We simulate <strong>real-world fraud attempts</strong> and <strong>account abuse scenarios</strong> across your platform. From <strong>credential stuffing</strong> and <strong>cart hijacking</strong> to <strong>business logic abuse</strong> and <strong>gift card manipulation</strong>, we uncover vulnerabilities before attackers do. Our skilled <strong>penetration testers</strong> are available on demand to protect <strong>customer sessions</strong>, and preserve your <strong>revenue</strong> from stealthy, high-impact exploits.
                </p>
              </div>
            </div>

            <div className="bg-white border-l-4 border-[#58CC02] pl-4 sm:pl-6 pr-4 sm:pr-6 py-6 sm:py-8 rounded-r-lg shadow-sm hover:shadow-md transition-shadow duration-300 md:col-span-2 xl:col-span-1">
              <div className="">
                <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-[#58CC02] to-[#4BAF02] rounded-lg flex items-center justify-center mb-4 sm:mb-6">
                  <Target className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                </div>
          <h3 className="text-xl sm:text-2xl font-bold text-[#010D2C] mb-3 sm:mb-4">
Customer Trust & Data Protection                </h3>
                <p className="text-[#6B7280] text-base sm:text-lg leading-relaxed">
                  We help you protect sensitive <strong>customer information</strong>-including <strong>payment data</strong>, <strong>PII</strong>, and <strong>loyalty program records</strong>-while aligning with regulations like <strong>PCI-DSS</strong>, <strong>GDPR</strong>, and <strong>local privacy laws</strong>. With Capture The Bug, your <strong>security posture</strong> supports <strong>compliance audits</strong> and delivers <strong>peace of mind</strong> to both regulators and shoppers. You build faster, safer, and with <strong>trust baked in</strong> from the ground up.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div> 
    </>
  );
}