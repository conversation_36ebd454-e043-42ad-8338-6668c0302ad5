import React from 'react';
import CountryHero from './components/CountryHero';
import CountryServices from './components/CountryServices';
import Testimonials from '@/app/Home/components/Testimonials';
import CountryCTA from './components/CountryCTA';
import StructuredData from './components/StructuredData';
import TrustSignals from './components/TrustSignals';
import CaseStudies from './components/CaseStudies';
import CompletePage from '@/app/Company/Contact-Us/components/CompletePage';

// Generate static params for all valid countries
export async function generateStaticParams() {
  return [
    { country: 'nz' },
    { country: 'au' },
    { country: 'us' }
  ];
}

export async function generateMetadata({ params }) {
  // Await params before accessing its properties
  const resolvedParams = await params;
  const country = resolvedParams.country.toLowerCase();

  const metadata = {
    nz: {
      title: "New Zealand Penetration Testing Services | Capture The Bug",
      description: "Expert penetration testing services for New Zealand businesses. Specialized in NZ Privacy Act compliance, critical infrastructure security, and cloud security testing.",
      keywords: "penetration testing New Zealand, NZ cybersecurity services, Auckland security testing, Wellington IT security, Christchurch penetration testing, NZ Privacy Act compliance, cybersecurity audit NZ, vulnerability assessment New Zealand",
      canonical: "https://capturethebug.xyz/Locations/nz",
      alternates: {
        canonical: "https://capturethebug.xyz/Locations/nz"
      }
    },
    au: {
      title: "Australian Penetration Testing Services | Capture The Bug",
      description: "Enterprise-grade penetration testing services across Australia. Compliant with ACSC guidelines, Essential Eight controls, and tailored for Australian businesses.",
      keywords: "penetration testing Australia, Sydney cybersecurity, Melbourne security testing, Australian cyber security, Essential Eight compliance, ACSC security compliance, cybersecurity audit Australia, vulnerability assessment AU",
      canonical: "https://capturethebug.xyz/Locations/au",
      alternates: {
        canonical: "https://capturethebug.xyz/Locations/au"
      }
    },
    us: {
      title: "United States Penetration Testing Services | Capture The Bug",
      description: "Comprehensive penetration testing services for US organizations. Specialized in NIST framework compliance, healthcare security, and financial services protection.",
      keywords: "penetration testing US, American cybersecurity services, NIST compliance testing, SOC 2 security assessment, PCI DSS penetration testing, US security services, cybersecurity audit USA, vulnerability assessment United States",
      canonical: "https://capturethebug.xyz/Locations/us",
      alternates: {
        canonical: "https://capturethebug.xyz/Locations/us"
      }
    },

  };

  const countryMeta = metadata[country] || metadata.nz;

  return {
    title: countryMeta.title,
    description: countryMeta.description,
    keywords: countryMeta.keywords,
    robots: "index, follow",
    canonical: countryMeta.canonical,
    alternates: countryMeta.alternates,
    openGraph: {
      title: countryMeta.title,
      description: countryMeta.description,
      url: countryMeta.canonical,
      type: "website",
      siteName: "Capture The Bug",
      locale: country === 'us' ? 'en_US' : country === 'au' ? 'en_AU' : 'en_NZ',
      images: [
        {
          url: `https://capturethebug.xyz/images/og-${country}.jpg`,
          width: 1200,
          height: 630,
          alt: `${countryMeta.title} - Professional Cybersecurity Services`
        }
      ]
    },
    twitter: {
      card: 'summary_large_image',
      title: countryMeta.title,
      description: countryMeta.description,
      images: [`https://capturethebug.xyz/images/og-${country}.jpg`]
    }
  };
}

export default async function CountryPage({ params }) {
  // Await params before accessing its properties
  const resolvedParams = await params;
  const country = resolvedParams.country.toLowerCase();

  return (
    <>
      {/* SEO Structured Data */}
      <StructuredData country={country} />

      {/* Main Content */}
      <CountryHero country={country} />
      <TrustSignals country={country} />
      <CountryServices country={country} />
      <CaseStudies country={country} />
      <Testimonials />
      <CompletePage />
      <CountryCTA country={country} />
    </>
  );
}