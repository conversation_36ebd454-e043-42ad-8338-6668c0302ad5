'use client';
import React from 'react';
import { usePathname } from 'next/navigation';
import ContentSection from './components/ContentSection';
import HeaderSection from './components/HeaderSection';
import BreadcrumbNavigation from '../../components/BreadcrumbNavigation';

const FullBlogView = ({ headerSection, children, breadcrumbs, blogTitle }) => {
  const pathname = usePathname();

  // Extract blog title from URL path if not provided
  const extractTitleFromPath = (path) => {
    const segments = path.split('/');
    const blogSlug = segments[segments.length - 1];
    // Convert slug to readable title
    return blogSlug
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  // Generate blog title
  const title = blogTitle || extractTitleFromPath(pathname);

  // Generate breadcrumbs if not provided
  const blogBreadcrumbs = breadcrumbs || [
    {
      name: "Home",
      url: "/",
      iconKey: "home",
      description: "Return to homepage"
    },
    {
      name: "Blog",
      url: "/Blogs",
      iconKey: "blogs",
      description: "Cybersecurity insights and penetration testing guidance"
    },
    {
      name: title,
      url: pathname,
      current: true,
      description: `${title} - Expert cybersecurity insights and penetration testing guidance`
    }
  ];

  return (
    <div className="min-h-screen flex flex-col">
      {/* Hero Section with integrated breadcrumbs */}
      <HeaderSection
        {...headerSection}
        title={title}
        breadcrumbs={
          <BreadcrumbNavigation
            items={blogBreadcrumbs}
            className="text-white"
          />
        }
      />

      <ContentSection>
        {children}
      </ContentSection>
    </div>
  );
};

export default FullBlogView;