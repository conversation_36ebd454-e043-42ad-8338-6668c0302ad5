"use client";

import React from "react";
import { LinkButton, OutlinedLinkButton } from "../buttons/BrandButtons";

export default function EmailCallCTA({ 
  title = "Ready to Secure Your Organization?",
  description = "Contact us today for a free consultation and discover how we can protect your business.",
  phoneNumber = "+918527800769",
  emailAddress = "<EMAIL>",
  callButtonText = "Call Now",
  emailButtonText = "Send Email",
  className = ""
}) {
  return (
    <div className={`text-center ${className}`}>
      <div className="bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-2xl p-8 text-white">
        <h3 className="text-2xl font-bold mb-4">{title}</h3>
        <p className="text-lg mb-6 opacity-90">
          {description}
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <LinkButton href={`tel:${phoneNumber}`}>
            {callButtonText}
          </LinkButton>
          <OutlinedLinkButton href={`mailto:${emailAddress}`}>
            {emailButtonText}
          </OutlinedLinkButton>
        </div>
      </div>
    </div>
  );
} 