'use client'
import Image from "next/image";
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";

const logos = [
               { src: "/images/parkable_logo.png", alt: "Parkable logo" },
              { src: "/images/rafay_logo.png", alt: "Ra<PERSON>y logo" },
              { src: "/images/forsite_logo.png", alt: "Forsite logo" },
              { src: "/images/kademi_logo.png", alt: "Kademi Logo" },
              { src: "/images/orbit_logo.png", alt: "Orbit logo" },
              
];

const settings = {
  dots: false,
  infinite: true,
  slidesToShow: 5,
  slidesToScroll: 1,
  autoplay: true,
  speed: 5000,
  autoplaySpeed: 0,
  cssEase: "linear",
  pauseOnHover: true,
  arrows: false,
  rtl: false,
  responsive: [
    {
      breakpoint: 1024,
      settings: {
        slidesToShow: 4,
      }
    },
    {
      breakpoint: 600,
      settings: {
        slidesToShow: 3,
      }
    },
    {
      breakpoint: 480,
      settings: {
        slidesToShow: 2,
      }
    }
  ]
};

export default function PartnersList() {
  return (
    <div className="w-full bg-tertiary-blue py-8 relative overflow-hidden">
      {/* Premium background elements */}
      <div className="absolute inset-0 bg-[url('/images/grid-pattern.svg')] bg-repeat opacity-[0.03]"></div>
      <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>
      <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>
      
      {/* Decorative elements */}
      <div className="absolute -top-40 right-20 w-80 h-80 bg-primary-blue/10 rounded-full blur-3xl"></div>
      <div className="absolute -bottom-40 left-20 w-80 h-80 bg-primary-blue/10 rounded-full blur-3xl"></div>
      
      <div className="container mx-auto relative z-10">
        <div className="text-center mb-12">
         
          <p className="text-white/80 text-base md:text-lg max-w-2xl mx-auto">
Empowering growing teams to scale with confidence          </p>
          <div className="w-20 h-1 bg-gradient-to-r from-white/30 via-ctb-green-50/60 to-white/30 mx-auto rounded-full mt-6"></div>
        </div>
        
        <div className="mx-6 md:mx-12 lg:mx-16 xl:mx-24 relative">
          {/* Left fade effect */}
          <div className="absolute left-0 top-0 bottom-0 w-16 z-10 bg-gradient-to-r from-tertiary-blue to-transparent"></div>
          
          {/* Right fade effect */}
          <div className="absolute right-0 top-0 bottom-0 w-16 z-10 bg-gradient-to-l from-tertiary-blue to-transparent"></div>
          
          <div className="py-6">
            <Slider {...settings}>
              {logos.map((logo, index) => (
                <div key={index} className="px-6 flex items-center justify-center h-24">
                  <div className="bg-white/90 backdrop-blur-sm rounded-xl p-4 h-full w-full flex items-center justify-center hover:bg-white transition-all duration-300 hover:shadow-lg group">
                    <div className="relative w-full h-full flex items-center justify-center">
                      <Image
                        src={logo.src}
                        alt={logo.alt}
                        width={120}
                        height={50}
                        style={{ objectFit: 'contain', maxWidth: '100%', maxHeight: '100%' }}
                        quality={90}
                        loading="eager"
                      />
                    </div>
                  </div>
                </div>
              ))}
            </Slider>
          </div>
        </div>
      </div>
    </div>
  );
}
