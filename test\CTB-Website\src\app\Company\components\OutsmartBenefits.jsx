import React, { memo } from 'react';
import Image from 'next/image';

const Card = memo(({ imageUrl, title, description }) => (
  <div className="bg-blue-50 p-6 rounded-lg">
    <div className="flex items-center mb-4">
      <div className="md:w-16 md:h-16 w-10 h-10 mr-4 overflow-hidden flex items-center justify-center">
        <Image 
          src={imageUrl} 
          alt={title} 
          width={64} 
          height={64} 
          className="object-contain" 
        />
      </div>
      <h3 className="md:text-xl text-lg font-semibold">{title}</h3>
    </div>
    <p className="text-gray-800 md:pl-20">{description}</p>
  </div>
));

Card.displayName = 'Card';

const features = [
  {
    imageUrl: "/images/company_feature1.png",
    title: "Unified Security Platform",
    description: "Streamline your cybersecurity efforts with our all-in-one platform that consolidates multiple testing capabilities. Enhance your VAPT efficiency, reduce overheads, and elevate your security posture seamlessly."
  },
  {
    imageUrl: "/images/company_feature2.png",
    title: "Real-Time Collaboration",
    description: "Work closely with our pentesters to thoroughly understand vulnerabilities and implement effective remediation strategies"
  },
  {
    imageUrl: "/images/company_feature3.png",
    title: "Scalable Security Solutions",
    description: "Capture The Bug provides flexible pentesting options to fit your specific needs. Whether you're a startup or an enterprise, our PTaaS platform delivers security without overextending your resources."
  },
  {
    imageUrl: "/images/company_feature3.png",
    title: "Next-Gen Pentesting",
    description: "No lead time, simple pricing plans. Launch a pentest in less than a week to quickly achieve ISO 27001, SOC, and other security standards compliance."
  }
];

const OutsmartBenefits = () => {
  return (
    <section className="md:px-20 px-8 py-20 bg-gray-50">
      <h1 className="md:text-5xl text-xl font-bold text-center mb-6 py-2">
        Defend Against Cyber Threats with <br />
        Capture The Bug&apos;s Cutting-Edge PTaaS
      </h1>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-16 mt-10">
        {features.map((feature) => (
          <Card key={feature.title} {...feature} />
        ))}
      </div>
    </section>
  );
};

export default OutsmartBenefits;