import React from "react";
import { Shield, Users, Target } from "lucide-react";
 
export default function  Security() {
  return (
    <> 
  <div className="bg-white py-16 sm:py-24">
  <div className="container mx-auto px-4 md:px-24">
    <div className="mb-16">
      <div className="text-secondary-blue text-xl font-bold mb-5">
        How Capture The Bug Helps
      </div>
      <h2 className="text-4xl sm:text-5xl font-bold text-[#010D2C] leading-tight mb-5">
Securing the future of intelligent infrastructure      </h2>
      <p className="text-[#6B7280] text-lg font-medium leading-relaxed  ">
As AI systems rapidly integrate into business-critical operations, their attack surface grows-spanning data, models, APIs, and cloud infrastructure. Capture The Bug delivers robust security strategies that help organizations harden their AI infrastructure before attackers exploit vulnerabilities.</p>    </div>

     <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
       <div className="bg-white border-l-4 border-[#58CC02] pl-6 pr-4 py-6">
        <div className="">
          <div className="w-10 h-10 bg-gradient-to-br from-[#58CC02] to-[#4BAF02] rounded-lg flex items-center justify-center mb-4">
            <Shield className="w-5 h-5 text-white" />
          </div>
            <h3 className="text-xl sm:text-2xl font-bold text-[#010D2C] mb-3 sm:mb-4">
End-to-end protection
          </h3>
          <p className="text-[#6B7280] text-lg leading-relaxed">
            We secure your entire <strong>AI stack</strong>-from <strong>training data</strong> and <strong>fine-tuning pipelines</strong> to <strong>model endpoints</strong> and <strong>inference APIs</strong>. Our assessments harden <strong>cloud storage</strong>, <strong>access controls</strong>, and <strong>compute environments</strong>-ensuring <strong>resilience</strong>, <strong>privacy</strong>, and <strong>uptime</strong> across every stage of your <strong>ML lifecycle</strong>.
          </p>
        </div>
      </div>

      <div className="bg-white border-l-4 border-[#58CC02] pl-6 pr-4 py-6">
        <div className="">
          <div className="w-10 h-10 bg-gradient-to-br from-[#58CC02] to-[#4BAF02] rounded-lg flex items-center justify-center mb-4">
            <Users className="w-5 h-5 text-white" />
          </div>
          <h3 className="text-xl sm:text-2xl font-bold text-[#010D2C] mb-3 sm:mb-4">
            Adversarial attack simulation
          </h3>
          <p className="text-[#6B7280] text-lg leading-relaxed">
            Our <strong>red teamers</strong> and <strong>AI security researchers</strong> simulate real-world <strong>adversarial attacks</strong>-like <strong>prompt injection</strong>, <strong>data poisoning</strong>, and <strong>model extraction</strong>. You&apos;ll see how attackers might <strong>hijack your model&apos;s behavior</strong>, steal <strong>intellectual property</strong>, or exploit <strong>decision logic</strong> in production environments.
          </p>
        </div>
      </div>

      <div className="bg-white border-l-4 border-[#58CC02] pl-6 pr-4 py-6">
        <div className="">
          <div className="w-10 h-10 bg-gradient-to-br from-[#58CC02] to-[#4BAF02] rounded-lg flex items-center justify-center mb-4">
            <Target className="w-5 h-5 text-white" />
          </div>
          <h3 className="text-xl sm:text-2xl font-bold text-[#010D2C] mb-3 sm:mb-4">
            Risk scoring & compliance mapping
          </h3>
          <p className="text-[#6B7280] text-lg leading-relaxed">
            Our pentesting solution align with frameworks like <strong>ISO/IEC 42001</strong> and <strong>NIST</strong>. We deliver actionable <strong>risk scoring</strong>, <strong>model impact prioritization</strong>, and <strong>mitigation guidance</strong>-so your teams can meet emerging <strong>AI governance requirements</strong> while minimizing <strong>business-critical risks</strong>.
          </p>
        </div>
      </div>
    </div>
  </div>
</div>
</>
  );
}