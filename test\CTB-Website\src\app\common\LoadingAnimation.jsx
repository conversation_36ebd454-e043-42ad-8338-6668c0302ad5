"use client";
import React, { useEffect, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";

const LoadingAnimation = ({ isLoading, text = "Processing" }) => {
  const [progressValue, setProgressValue] = useState(0);
  const [progressText, setProgressText] = useState("");
  
  // Simulate progress to make the loading feel more responsive
  useEffect(() => {
    if (!isLoading) {
      setProgressValue(0);
      return;
    }
    
    const messages = [
      "Initializing request...",
      "Validating information...",
      "Securing connection...",
      "Processing data...",
      "Almost there..."
    ];
    
    // Start progress simulation
    let progress = 0;
    const interval = setInterval(() => {
      // Calculate new progress value
      if (progress < 90) {
        // Move faster at the beginning, slower as we approach 90%
        const increment = progress < 30 ? 5 : progress < 60 ? 3 : 1;
        progress = Math.min(90, progress + increment);
        setProgressValue(progress);
        
        // Update message based on progress
        const messageIndex = Math.floor((progress / 90) * (messages.length - 1));
        setProgressText(messages[messageIndex]);
      }
    }, 500);
    
    return () => clearInterval(interval);
  }, [isLoading]);
  
  if (!isLoading) return null;
  
  return (
    <AnimatePresence>
      {isLoading && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50"
        >
          <motion.div 
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            transition={{ duration: 0.3 }}
            className="bg-white rounded-xl shadow-2xl p-6 max-w-sm w-full mx-4"
          >
            <div className="flex flex-col items-center">
              {/* Premium animated spinner */}
              <div className="relative w-20 h-20 mb-6">
                {/* Outer ring */}
                <motion.div 
                  className="absolute inset-0 rounded-full border-4 border-t-transparent border-[#062575]"
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1.5, repeat: Infinity, ease: "linear" }}
                />
                
                {/* Inner ring */}
                <motion.div 
                  className="absolute inset-2 rounded-full border-4 border-t-transparent border-r-transparent border-[#027bfc]"
                  animate={{ rotate: -360 }}
                  transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                />
                
                {/* Center dot with pulse */}
                <motion.div 
                  className="absolute left-1/2 top-1/2 w-4 h-4 -ml-2 -mt-2 bg-[#062575] rounded-full"
                  animate={{ 
                    scale: [1, 1.2, 1],
                    opacity: [0.8, 1, 0.8]
                  }}
                  transition={{ duration: 1.5, repeat: Infinity }}
                />
                
                {/* Shimmer effect */}
                <div className="absolute inset-0 rounded-full overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-shimmer" />
                </div>
              </div>
              
              {/* Text with dots animation */}
              <div className="text-center">
                <h3 className="text-lg font-semibold text-[#062575] mb-2">{text}</h3>
                <div className="flex justify-center items-center space-x-1">
                  <motion.div 
                    className="w-2 h-2 bg-[#062575] rounded-full"
                    animate={{ y: [0, -6, 0] }}
                    transition={{ duration: 0.8, repeat: Infinity, repeatDelay: 0.2 }}
                  />
                  <motion.div 
                    className="w-2 h-2 bg-[#027bfc] rounded-full"
                    animate={{ y: [0, -6, 0] }}
                    transition={{ duration: 0.8, repeat: Infinity, repeatDelay: 0.3, delay: 0.1 }}
                  />
                  <motion.div 
                    className="w-2 h-2 bg-[#062575] rounded-full"
                    animate={{ y: [0, -6, 0] }}
                    transition={{ duration: 0.8, repeat: Infinity, repeatDelay: 0.2, delay: 0.2 }}
                  />
                </div>
                
                {/* Progress indicator */}
                <p className="text-sm text-gray-500 mt-3">{progressText}</p>
                <div className="w-full bg-gray-200 rounded-full h-2.5 mt-3 overflow-hidden">
                  <motion.div 
                    className="bg-gradient-to-r from-[#062575] to-[#027bfc] h-2.5 rounded-full"
                    initial={{ width: "0%" }}
                    animate={{ width: `${progressValue}%` }}
                    transition={{ duration: 0.5 }}
                  />
                </div>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default LoadingAnimation; 