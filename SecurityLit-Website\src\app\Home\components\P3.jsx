import React from 'react';
import { Shield, Check, ArrowRight, Database, Lock, Stethoscope } from 'lucide-react';

const SecuritySolutions = () => {
  const services = [
    {
      icon: Database,
      title: 'Prepare',
      description: 'We offer consulting and training to help you and your team implement a robust cyber security strategy',
      features: [
        'Cybersecurity risk assessment and planning',
        'Employee security awareness training',
        'Security policy development',
        'Compliance framework implementation',
        'Business continuity planning'
      ],
      buttonText: 'Get Prepared'
    },
    {
      icon: Shield,
      title: 'Prevent',
      description: 'We can detect and assess your cyber risk through ethical hacking, and help you remove any system vulnerabilities',
      features: [
        'Penetration testing and vulnerability scanning',
        'Network security monitoring',
        'Firewall and intrusion detection setup',
        'Regular security audits',
        'Proactive threat intelligence'
      ],
      buttonText: 'Start Prevention'
    },
    {
      icon: Stethoscope,
      title: 'Protect',
      description: "If you've suffered a cyber security breach, we can help you respond and recover from any incident",
      features: [
        '24/7 incident response team',
        'Forensic investigation services',
        'Data recovery and restoration',
        'Legal and regulatory compliance support',
        'Post-incident security improvements'
      ],
      buttonText: 'Get Protection'
    }
  ];

  return (
    <div className="min-h-screen">
      
      {/* Services Section */}
      <div className=" bg-[#F8F8F8] py-20 px-4">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="text-center mb-16">
            <h1 className="text-lg  lg:text-xl xl:text-3xl font-bold text-[var(--color-dark-blue)] mb-8 leading-tight">
              NO MATTER WHAT YOUR CYBER SECURITY NEEDS ARE, WE CAN GIVE YOU PEACE OF MIND THROUGH ROBUST CYBER PROTECTION
            </h1>
          </div>

          {/* Service Cards */}
          <div className="grid md:grid-cols-3 gap-8">
            {services.map((service, index) => (
              <div
                key={index}
                className="bg-white rounded-lg shadow-lg border border-gray-200 p-8 relative hover:shadow-xl transition-shadow duration-300"
              >
                {/* Service Icon */}
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-6">
                  <service.icon className="w-8 h-8 text-blue-600" />
                </div>

                {/* Service Title */}
                <h3 className="text-2xl font-semibold text-gray-900 mb-3">
                  {service.title}
                </h3>

                {/* Description */}
                <p className="text-gray-600 text-sm mb-6 leading-relaxed">
                  {service.description}
                </p>

                {/* Features */}
                <div className="space-y-3 mb-8">
                  {service.features.map((feature, featureIndex) => (
                    <div key={featureIndex} className="flex items-start gap-3">
                      <div className="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                        <Check className="w-3 h-3 text-green-600" />
                      </div>
                      <span className="text-sm text-gray-700">
                        {feature}
                      </span>
                    </div>
                  ))}
                </div>

                {/* CTA Button */}
                <button className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center gap-2">
                  {service.buttonText}
                  <ArrowRight className="w-4 h-4" />
                </button>
              </div>
            ))}
          </div>
        </div>
      </div>
 
    </div>
  );
};

export default SecuritySolutions;