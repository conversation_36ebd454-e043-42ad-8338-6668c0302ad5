import React, { Children, cloneElement, useEffect, useRef } from 'react';

const ContentSection = ({ children, toc, onTocGenerated }) => {
  // Function to generate TOC from content
  const generateTocFromContent = (content) => {
    const tocItems = [];
    let currentH1 = null;
    let currentH2 = null;

    Children.forEach(content, (child) => {
      if (child && (child.type === 'h1' || child.type === 'h2' || child.type === 'h3') && child.props && child.props.children) {
        const text = typeof child.props.children === 'string' ? child.props.children : (Array.isArray(child.props.children) ? child.props.children.join(' ') : '');
        const id = text.toLowerCase()
          .replace(/[^a-z0-9\s]+/g, '') // Remove special characters but keep spaces
          .replace(/\s+/g, '-') // Replace spaces with hyphens
          .replace(/(^-|-$)/g, ''); // Remove leading/trailing hyphens

        if (child.type === 'h1') {
          currentH1 = { id: id, text, subItems: [] };
          tocItems.push(currentH1);
          currentH2 = null; // Reset H2 when new H1
        } else if (child.type === 'h2') {
          currentH2 = { id: id, text, subItems: [] };
          if (currentH1) {
            currentH1.subItems.push(currentH2);
          } else {
            tocItems.push(currentH2);
          }
        } else if (child.type === 'h3') {
          const h3Item = { id: id, text };
          if (currentH2) {
            currentH2.subItems.push(h3Item);
          } else if (currentH1) {
            currentH1.subItems.push(h3Item);
          } else {
            tocItems.push(h3Item);
          }
        }
      }
    });

    return tocItems;
  };

  // Generate TOC when component mounts or children change
  const lastTocRef = useRef();
  useEffect(() => {
    if (onTocGenerated) {
      const autoGeneratedToc = generateTocFromContent(children);
      console.log('📋 Generated TOC:', autoGeneratedToc);
      // Only call if TOC actually changed
      if (JSON.stringify(lastTocRef.current) !== JSON.stringify(autoGeneratedToc)) {
        lastTocRef.current = autoGeneratedToc;
        onTocGenerated(autoGeneratedToc);
      }
    }
  }, [children, onTocGenerated]);

  // Add id to all h1, h2, and h3s for anchor navigation
  const enhancedChildren = Children.map(children, (child) => {
    if (child && (child.type === 'h1' || child.type === 'h2' || child.type === 'h3') && child.props && child.props.children) {
      // Generate id from text using the same logic as TOC generation
      const text = typeof child.props.children === 'string' ? child.props.children : (Array.isArray(child.props.children) ? child.props.children.join(' ') : '');
      const id = text.toLowerCase()
        .replace(/[^a-z0-9\s]+/g, '') // Remove special characters but keep spaces
        .replace(/\s+/g, '-') // Replace spaces with hyphens
        .replace(/(^-|-$)/g, ''); // Remove leading/trailing hyphens
      
      console.log('🏷️ Generated ID for heading:', { text: text.slice(0, 50), id });
      
      return cloneElement(child, { 
        id: id,
        className: `${child.props.className || ''} scroll-mt-32` // Add scroll margin for navbar offset
      });
    }
    return child;
  });
  
  return (
    <div className="bg-white sm:mx-8 md:p-14 p-6 flex flex-col gap-4 rounded-2xl shadow-md my-8 border border-[var(--color-yellow)]" style={{ scrollBehavior: 'smooth' }}>
      {enhancedChildren}
    </div>
  );
};

export default ContentSection;