"use client";
import React from "react";
import { ArrowRight, Check } from "lucide-react";
import { motion } from "framer-motion";
import Link from "next/link";

export default function TailorMadeSection() {
  const handleDemoClick = () => {
    window.location.href = '/Request-Demo';
  };

  return (
    <div className="bg-tertiary-blue  py-16 md:py-20 mb-2">
      <div className="container mx-auto px-4 md:px-12">
        {/* Header Section */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-4">
            Tailored for <span className="text-blue-600">modern startups</span>
          </h2>
          <p className="text-white text-base md:text-lg leading-relaxed max-w-3xl mx-auto">
            Everything you need to launch secure, win trust fast, and scale with confidence
          </p>
        </div>

        {/* Pricing Cards */}
        <div className="flex flex-col lg:flex-row gap-8 max-w-5xl mx-auto justify-center">
         {/* Basic Plan */}
          <div className="bg-white rounded-2xl p-6 flex-1 max-w-lg mx-auto lg:mx-0">
            <div className="mb-8">
              <h3 className="text-2xl md:text-3xl font-bold text-ctb-blue-150 mb-4">
                Basic
              </h3>
              <p className="text-ctb-blue-150 text-base leading-relaxed border-l-4 border-blue-500 pl-4 py-1 mb-4 font-semibold">
                For the startups launching with confidence
              </p>
              <p className="text-ctb-blue-150 text-sm mt-2">
                Get your first pentest done right - fast, audit-friendly, and built for agile teams.
              </p>
            </div>

            <div className="space-y-4 mb-8">
              <div className="flex items-start gap-3">
                <Check className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                <span className="text-ctb-blue-150 text-sm">
                  1 manual pentest per year
                </span>
              </div>
              
              <div className="flex items-start gap-3">
                <Check className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                <span className="text-ctb-blue-150 text-sm">
                  Compliance ready reporting
                </span>
              </div>
              
              <div className="flex items-start gap-3">
                <Check className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                <span className="text-ctb-blue-150 text-sm">
                  Real-time vulnerability dashboard
                </span>
              </div>
              
              <div className="flex items-start gap-3">
                <Check className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                <span className="text-ctb-blue-150 text-sm">
                  30-day unlimited retesting
                </span>
              </div>
              
              <div className="flex items-start gap-3">
                <Check className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                <span className="text-ctb-blue-150 text-sm">
                  OWASP Top 10 + business logic coverage
                </span>
              </div>
            </div>

            <div className="text-sm text-ctb-blue-150 mb-6">
              <strong>Best for:</strong> MVPs, early-stage startups, first compliance audit
            </div>

            <Link href="/Request-Demo">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="w-full bg-secondary-blue hover:bg-primary-blue text-white py-4 px-6 rounded-lg font-medium flex items-center justify-center transition-colors group relative overflow-hidden"
              >
                <span className="absolute inset-0 w-full h-full bg-white/10 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out"></span>
                <span className="relative z-10 flex items-center">
                  Request a Demo
                  <motion.span
                    animate={{ x: [0, 4, 0] }}
                    transition={{ duration: 1.5, repeat: Infinity }}
                    className="ml-2"
                  >
                    <ArrowRight className="h-5 w-5" />
                  </motion.span>
                </span>
              </motion.button>
            </Link>
          </div>

  {/* Core Plan */}
            <div className="bg-gradient-to-br from-blue-100 to-blue-50 rounded-2xl p-6 flex-1 max-w-lg mx-auto lg:mx-0 border-4 border-ctb-green-50 relative">
            {/* Popular Badge */}
            <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
              <div className="bg-ctb-green-50 text-black px-4 py-1 rounded-full text-sm font-medium">
                Popular
              </div>
            </div>

            <div className="mb-8">
              <h3 className="text-2xl md:text-3xl font-bold text-ctb-blue-150 mb-4">
                Core
              </h3>
              <p className="text-ctb-blue-150 text-base leading-relaxed border-l-4 border-blue-500 pl-4 py-1 mb-4 font-semibold">
                For growing teams with expanding security needs
              </p>
              <p className="text-ctb-blue-150 text-sm mt-2">
                Run deeper tests, stay audit-ready, and meet rising customer expectations.
              </p>
            </div>

            <div className="space-y-4 mb-8">
              <div className="flex items-start gap-3">
                <Check className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                <span className="text-ctb-blue-150 text-sm">
                  2 pentests/year (web + API)
                </span>
              </div>
              
              <div className="flex items-start gap-3">
                <Check className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                <span className="text-ctb-blue-150 text-sm">
                  Compliance-ready reports for SOC 2, ISO 27001, HIPAA
                </span>
              </div>
              
              <div className="flex items-start gap-3">
                <Check className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                <span className="text-ctb-blue-150 text-sm">
                  Unlimited retesting and evidence-based triage
                </span>
              </div>
              
              <div className="flex items-start gap-3">
                <Check className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                <span className="text-ctb-blue-150 text-sm">
                  Live dashboards and exportable stakeholder reports
                </span>
              </div>
              
              <div className="flex items-start gap-3">
                <Check className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                <span className="text-ctb-blue-150 text-sm">
                  Dev tool integrations (Jira, GitHub, Slack)
                </span>
              </div>

              <div className="flex items-start gap-3">
                <Check className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                <span className="text-ctb-blue-150 text-sm">
                  Direct pentester collaboration via platform
                </span>
              </div>

              <div className="flex items-start gap-3">
                <Check className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                <span className="text-ctb-blue-150 text-sm">
                  SLA-based support and triage
                </span>
              </div>
            </div>

            <div className="text-sm text-ctb-blue-150 mb-6">
              <strong>Best for:</strong> SMEs, scaling SaaS, Series A–C teams
            </div>

            <Link href="/Request-Demo">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="w-full bg-secondary-blue hover:bg-primary-blue text-white py-4 px-6 rounded-lg font-medium flex items-center justify-center transition-colors group relative overflow-hidden"
              >
                <span className="absolute inset-0 w-full h-full bg-white/10 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out"></span>
                <span className="relative z-10 flex items-center">
                  Request a Demo
                  <motion.span
                    animate={{ x: [0, 4, 0] }}
                    transition={{ duration: 1.5, repeat: Infinity }}
                    className="ml-2"
                  >
                    <ArrowRight className="h-5 w-5" />
                  </motion.span>
                </span>
              </motion.button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}