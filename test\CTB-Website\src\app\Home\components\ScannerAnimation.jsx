"use client"
import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

export default function ScannerAnimation() {
  const [scanning, setScanning] = useState(true);
  const [scanProgress, setScanProgress] = useState(0);
  const [scanComplete, setScanComplete] = useState(false);
  const [scannerActive, setScannerActive] = useState(false);
  
  // Animate scanner
  useEffect(() => {
    if (!scanning) return;
    
    const interval = setInterval(() => {
      setScanProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          setScanComplete(true);
          return 100;
        }
        return prev + 0.5;
      });
    }, 50);
    
    // Scanner animation
    const scannerInterval = setInterval(() => {
      setScannerActive(prev => !prev);
    }, 3000);
    
    return () => {
      clearInterval(interval);
      clearInterval(scannerInterval);
    };
  }, [scanning]);

  return (
    <div className="w-full h-full flex items-center justify-center">
      <div className="relative w-full max-w-xs">
        {/* Container */}
        <div className="flex items-center justify-center w-full h-full">
          <div className="relative w-20 h-24">
            {/* Scanner corners - create camera frame effect */}
            <div className="absolute inset-0 z-30 pointer-events-none">
              <motion.div 
                className="absolute top-0 left-0 w-6 h-6 border-t-2 border-l-2 border-primary-blue"
                initial={{ scale: 0, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.3 }}
              />
              <motion.div 
                className="absolute top-0 right-0 w-6 h-6 border-t-2 border-r-2 border-primary-blue"
                initial={{ scale: 0, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.3, delay: 0.1 }}
              />
              <motion.div 
                className="absolute bottom-0 left-0 w-6 h-6 border-b-2 border-l-2 border-primary-blue"
                initial={{ scale: 0, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.3, delay: 0.2 }}
              />
              <motion.div 
                className="absolute bottom-0 right-0 w-6 h-6 border-b-2 border-r-2 border-primary-blue"
                initial={{ scale: 0, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.3, delay: 0.3 }}
              />
            </div>
            
            {/* Scanning line animation */}
            <AnimatePresence>
              {scannerActive && !scanComplete && (
                <motion.div
                  className="absolute inset-x-0 h-[2px] bg-primary-blue z-20 overflow-hidden opacity-60"
                  initial={{ top: 0, opacity: 0 }}
                  animate={{ top: '100%', opacity: 0.6 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 2, ease: "easeInOut" }}
                >
                  <motion.div 
                    className="absolute inset-0 bg-gradient-to-r from-transparent via-blue-300 to-transparent" 
                    animate={{ x: ['-100%', '200%'] }}
                    transition={{ duration: 1, repeat: 2, repeatType: "loop" }}
                  />
                </motion.div>
              )}
            </AnimatePresence>

            {/* Scan grid pattern */}
            <svg 
              className="absolute inset-0 w-full h-full z-10 opacity-10"
              viewBox="0 0 100 100"
              style={{ pointerEvents: 'none' }}
            >
              {/* Vertical lines */}
              {[0, 1, 2, 3, 4].map((i) => (
                <motion.line 
                  key={`v-${i}`}
                  x1={20 * i}
                  y1="0"
                  x2={20 * i}
                  y2="100"
                  stroke="currentColor"
                  strokeWidth="0.5"
                  initial={{ opacity: 0, pathLength: 0 }}
                  animate={{ 
                    opacity: scannerActive ? [0.3, 0.8, 0.3] : 0.3, 
                    pathLength: 1 
                  }}
                  transition={{ 
                    opacity: { duration: 2, repeat: Infinity },
                    pathLength: { duration: 1, delay: i * 0.1 }
                  }}
                />
              ))}
              
              {/* Horizontal lines */}
              {[0, 1, 2, 3, 4].map((i) => (
                <motion.line 
                  key={`h-${i}`}
                  x1="0"
                  y1={20 * i}
                  x2="100"
                  y2={20 * i}
                  stroke="currentColor"
                  strokeWidth="0.5"
                  initial={{ opacity: 0, pathLength: 0 }}
                  animate={{ 
                    opacity: scannerActive ? [0.3, 0.8, 0.3] : 0.3, 
                    pathLength: 1 
                  }}
                  transition={{ 
                    opacity: { duration: 2, repeat: Infinity },
                    pathLength: { duration: 1, delay: i * 0.1 + 0.2 }
                  }}
                />
              ))}
            </svg>

            {/* Shield icon */}
            <div className="absolute inset-0 flex items-center justify-center z-20">
              <motion.div
                className="w-14 h-16 relative"
                animate={{ 
                  y: [0, -2, 0, 2, 0],
                  rotate: [0, -1, 0, 1, 0]
                }}
                transition={{
                  duration: 5,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              >
                <motion.div 
                  className="absolute inset-0 bg-primary-blue opacity-10 rounded-full blur-lg"
                  animate={{
                    scale: [1, 1.15, 1],
                    opacity: [0.1, 0.15, 0.1]
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                />

                <svg className="w-full h-full text-primary-blue" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <motion.path 
                    d="M12 2L4 5V11.09C4 16.14 7.41 20.85 12 22C16.59 20.85 20 16.14 20 11.09V5L12 2Z" 
                    stroke="currentColor" 
                    strokeWidth="1.8" 
                    strokeLinecap="round" 
                    strokeLinejoin="round"
                    initial={{ pathLength: 0 }}
                    animate={{ pathLength: 1 }}
                    transition={{ duration: 1.5 }}
                  />
                  <AnimatePresence>
                    {scanComplete && (
                      <motion.path 
                        d="M16 9L10.5 14.5L8 12" 
                        stroke="currentColor" 
                        strokeWidth="1.8" 
                        strokeLinecap="round" 
                        strokeLinejoin="round"
                        initial={{ pathLength: 0, opacity: 0 }}
                        animate={{ pathLength: 1, opacity: 1 }}
                        transition={{ duration: 0.8, delay: 0.2 }}
                      />
                    )}
                  </AnimatePresence>
                </svg>
                
                {/* Pulsing ring when scan complete */}
                {scanComplete && (
                  <motion.div 
                    className="absolute inset-0 rounded-full border-2 border-primary-blue"
                    initial={{ scale: 0.8, opacity: 0.8 }}
                    animate={{ 
                      scale: [0.8, 1.4, 0.8],
                      opacity: [0.3, 0, 0.3]
                    }}
                    transition={{ 
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  />
                )}
              </motion.div>
            </div>
          </div>
        </div>
        
        {/* Progress bar */}
        <div className="absolute -bottom-8 left-0 right-0 text-center">
          <div className="inline-flex items-center bg-white px-4 py-1 rounded-full shadow-sm border border-gray-100">
            <div className="mr-2 text-xs text-gray-700 font-medium whitespace-nowrap">
              {scanComplete ? 'Scan Complete' : 'Scanning System'}
            </div>
            <div className="w-20 h-1.5 bg-gray-100 rounded-full overflow-hidden">
              <motion.div 
                className="h-full bg-primary-blue"
                style={{ width: `${scanProgress}%` }}
                animate={scanComplete ? { scale: [1, 1.1, 1] } : {}}
                transition={{ duration: 0.5, repeat: scanComplete ? 2 : 0 }}
              />
            </div>
            <motion.div 
              className="ml-2 text-xs text-gray-500"
              animate={scanProgress === 100 ? { 
                scale: [1, 1.2, 1],
                color: ['#6B7280', '#0835A7', '#6B7280'] 
              } : {}}
              transition={{ duration: 1 }}
            >
              {Math.round(scanProgress)}%
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
} 