{"version": 3, "file": "sha256.common.js", "sourceRoot": "", "sources": ["../../../src/util/sha256.common.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,MAAM,oBAAoB,CAAC;AA6C5E,IAAI,YAAsC,CAAC;AAE3C;;;GAGG;AACH,SAAS,SAAS;IAChB,IAAI,YAAY,EAAE,CAAC;QACjB,OAAO,YAAY,CAAC;IACtB,CAAC;IAED,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;QACxC,MAAM,IAAI,KAAK,CAAC,mEAAmE,CAAC,CAAC;IACvF,CAAC;IAED,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;IAClC,OAAO,YAAY,CAAC;AACtB,CAAC;AAED;;;;;GAKG;AACH,MAAM,CAAC,KAAK,UAAU,iBAAiB,CACrC,GAAW,EACX,YAAoB,EACpB,QAA0B;IAE1B,MAAM,MAAM,GAAG,SAAS,EAAE,CAAC;IAC3B,MAAM,QAAQ,GAAG,kBAAkB,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;IACnD,MAAM,iBAAiB,GAAG,kBAAkB,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;IAEpE,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,SAAS,CACtC,KAAK,EACL,QAAQ,EACR;QACE,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;KAC1B,EACD,KAAK,EACL,CAAC,MAAM,CAAC,CACT,CAAC;IACF,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,IAAI,CACjC;QACE,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;KAC1B,EACD,SAAS,EACT,iBAAiB,CAClB,CAAC;IAEF,OAAO,kBAAkB,CAAC,IAAI,UAAU,CAAC,SAAS,CAAC,EAAE,QAAQ,CAAC,CAAC;AACjE,CAAC;AAED;;;;GAIG;AACH,MAAM,CAAC,KAAK,UAAU,iBAAiB,CACrC,OAAe,EACf,QAA0B;IAE1B,MAAM,YAAY,GAAG,kBAAkB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAC1D,MAAM,MAAM,GAAG,MAAM,SAAS,EAAE,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,YAAY,CAAC,CAAC;IAE3E,OAAO,kBAAkB,CAAC,IAAI,UAAU,CAAC,MAAM,CAAC,EAAE,QAAQ,CAAC,CAAC;AAC9D,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { stringToUint8Array, uint8ArrayToString } from \"./bytesEncoding.js\";\n\n// stubs for browser self.crypto\ninterface JsonWebKey {}\ninterface CryptoKey {}\ntype KeyUsage =\n  | \"decrypt\"\n  | \"deriveBits\"\n  | \"deriveKey\"\n  | \"encrypt\"\n  | \"sign\"\n  | \"unwrapKey\"\n  | \"verify\"\n  | \"wrapKey\";\ninterface Algorithm {\n  name: string;\n}\ninterface SubtleCrypto {\n  importKey(\n    format: string,\n    keyData: JsonWeb<PERSON><PERSON>,\n    algorithm: HmacImportParams,\n    extractable: boolean,\n    usage: KeyUsage[],\n  ): Promise<CryptoKey>;\n  sign(\n    algorithm: HmacImportParams,\n    key: Crypto<PERSON><PERSON>,\n    data: ArrayBufferView | ArrayBuffer,\n  ): Promise<ArrayBuffer>;\n  digest(algorithm: Algorithm, data: ArrayBufferView | ArrayBuffer): Promise<ArrayBuffer>;\n}\ninterface Crypto {\n  readonly subtle: SubtleCrypto;\n  getRandomValues<T extends ArrayBufferView | null>(array: T): T;\n}\ndeclare const self: {\n  crypto: Crypto;\n};\ninterface HmacImportParams {\n  name: string;\n  hash: Algorithm;\n  length?: number;\n}\n\nlet subtleCrypto: SubtleCrypto | undefined;\n\n/**\n * Returns a cached reference to the Web API crypto.subtle object.\n * @internal\n */\nfunction getCrypto(): SubtleCrypto {\n  if (subtleCrypto) {\n    return subtleCrypto;\n  }\n\n  if (!self.crypto || !self.crypto.subtle) {\n    throw new Error(\"Your browser environment does not support cryptography functions.\");\n  }\n\n  subtleCrypto = self.crypto.subtle;\n  return subtleCrypto;\n}\n\n/**\n * Generates a SHA-256 HMAC signature.\n * @param key - The HMAC key represented as a base64 string, used to generate the cryptographic HMAC hash.\n * @param stringToSign - The data to be signed.\n * @param encoding - The textual encoding to use for the returned HMAC digest.\n */\nexport async function computeSha256Hmac(\n  key: string,\n  stringToSign: string,\n  encoding: \"base64\" | \"hex\",\n): Promise<string> {\n  const crypto = getCrypto();\n  const keyBytes = stringToUint8Array(key, \"base64\");\n  const stringToSignBytes = stringToUint8Array(stringToSign, \"utf-8\");\n\n  const cryptoKey = await crypto.importKey(\n    \"raw\",\n    keyBytes,\n    {\n      name: \"HMAC\",\n      hash: { name: \"SHA-256\" },\n    },\n    false,\n    [\"sign\"],\n  );\n  const signature = await crypto.sign(\n    {\n      name: \"HMAC\",\n      hash: { name: \"SHA-256\" },\n    },\n    cryptoKey,\n    stringToSignBytes,\n  );\n\n  return uint8ArrayToString(new Uint8Array(signature), encoding);\n}\n\n/**\n * Generates a SHA-256 hash.\n * @param content - The data to be included in the hash.\n * @param encoding - The textual encoding to use for the returned hash.\n */\nexport async function computeSha256Hash(\n  content: string,\n  encoding: \"base64\" | \"hex\",\n): Promise<string> {\n  const contentBytes = stringToUint8Array(content, \"utf-8\");\n  const digest = await getCrypto().digest({ name: \"SHA-256\" }, contentBytes);\n\n  return uint8ArrayToString(new Uint8Array(digest), encoding);\n}\n"]}