"use client";

import React, { useState } from "react";
import { Mail, Phone, CheckCircle, Shield, Users, Clock, Award, Globe } from "lucide-react";
import BreadcrumbNavigation from "../../common/components/BreadcrumbNavigation";
import { SubmitButton } from "../../common/buttons/BrandButtons";

export default function ContactHero() {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    company: '',
    organizationSize: '',
    industry: ''
  });

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    console.log("Form Submitted:", formData);
    // Add your form submission logic here
  };

  const breadcrumbItems = [
    {
      name: "Home",
      url: "/",
      iconKey: "home",
      description: "Return to homepage"
    },
    {
      name: "Contact Us",
      url: "/contact",
      current: true,
      iconKey: "contact-us",
      description: "Get in touch with SecurityLit for cybersecurity consultation and services"
    }
  ];

  const features = [
    { icon: <Shield className="w-5 h-5" />, title: "Free Security Consultation", description: "Expert cybersecurity guidance at no cost" },
    { icon: <Users className="w-5 h-5" />, title: "Certified Security Experts", description: "CISSP, CEH, and OSCP certified professionals" },
    { icon: <Clock className="w-5 h-5" />, title: "24/7 Incident Response", description: "Round-the-clock security support" },
    { icon: <Award className="w-5 h-5" />, title: "200+ Clients Served", description: "Trusted by enterprises globally" },
    { icon: <Globe className="w-5 h-5" />, title: "Global Presence", description: "Services across India, USA, Singapore, Australia" },
    { icon: <CheckCircle className="w-5 h-5" />, title: "Compliance Excellence", description: "ISO 27001 certified solutions" }
  ];
  return (
    <div className="relative min-h-screen bg-white">
      <div className="flex flex-col lg:flex-row min-h-screen">
        {/* Left Section - Dark Hero Panel */}
        <div className="lg:w-1/2 bg-gradient-to-br from-[var(--color-dark-blue)] to-[var(--color-dark-blue-hover)] relative overflow-hidden">
          <div className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-20"
               style={{ backgroundImage: 'url(/images/hexagon.svg)' }}>
          </div>
          <div className="absolute inset-0 bg-[var(--color-dark-blue)]/80"></div>
          
          {/* CHANGED: Added flex and justify-center to center the content block */}
          <div className="relative z-10 flex justify-center px-6 pt-28 pb-16 lg:px-12 lg:pt-28 lg:pb-16">
            <div className="max-w-lg w-full"> {/* Added w-full to ensure max-w-lg is effective */}
              <div className="mb-6">
                <BreadcrumbNavigation items={breadcrumbItems} className="text-white" />
              </div>
              
              <h1 className="text-3xl lg:text-4xl font-bold text-white mb-4">
                Contact SecurityLit
              </h1>
              <p className="text-lg lg:text-xl text-[var(--color-blue)] font-semibold mb-4">
              Your global cybersecurity consulting partner
              </p>
              <p className="text-base lg:text-lg text-white/90 mb-8">
              We partner with organizations worldwide to strengthen their cybersecurity posture through strategic consulting, advanced threat testing, and comprehensive security solutions.
              </p>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-8">
                {features.map((feature, index) => (
                  <div key={index} className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20 hover:bg-white/15 transition-all duration-300 group">
                    <div className="flex items-center gap-3 mb-2">
                      <div className="text-[var(--color-yellow)] group-hover:scale-110 transition-transform">
                        {feature.icon}
                      </div>
                      <h3 className="text-white font-semibold text-sm">{feature.title}</h3>
                    </div>
                    <p className="text-white/70 text-xs leading-relaxed">
                      {feature.description}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Right Section - Contact Form (Unchanged) */}
        <div className="lg:w-1/2 bg-white">
          <div className="max-w-lg mx-auto p-8 pt-28 pb-16 lg:p-12 lg:pt-28 lg:pb-16">
            <div className="mb-6">
              <h2 className="text-3xl lg:text-4xl font-bold text-[var(--color-dark-blue)] mb-4">
                  Complete the form to talk to an expert.
              </h2>
              <p className="text-lg text-gray-600">
                Get in touch for a free consultation and discover how we can protect your organization.
              </p>
            </div>

            <form onSubmit={handleSubmit} className="space-y-5">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                <div>
                  <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-2">First name <span className="text-red-500">*</span></label>
                  <input type="text" id="firstName" name="firstName" value={formData.firstName} onChange={handleInputChange} required className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--color-blue)] transition-all"/>
                </div>
                <div>
                  <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-2">Last name <span className="text-red-500">*</span></label>
                  <input type="text" id="lastName" name="lastName" value={formData.lastName} onChange={handleInputChange} required className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--color-blue)] transition-all"/>
                </div>
              </div>
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">Email <span className="text-red-500">*</span></label>
                <input type="email" id="email" name="email" value={formData.email} onChange={handleInputChange} required className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--color-blue)] transition-all"/>
              </div>
              <div>
                <label htmlFor="company" className="block text-sm font-medium text-gray-700 mb-2">Company <span className="text-red-500">*</span></label>
                <input type="text" id="company" name="company" value={formData.company} onChange={handleInputChange} required className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--color-blue)] transition-all"/>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                <div>
                  <label htmlFor="organizationSize" className="block text-sm font-medium text-gray-700 mb-2">Company size <span className="text-red-500">*</span></label>
                  <select id="organizationSize" name="organizationSize" value={formData.organizationSize} onChange={handleInputChange} required className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--color-blue)] transition-all bg-white">
                    <option value="">Choose size</option>
                    <option value="startup">Startup (1-50)</option>
                    <option value="smb">SMB (51-500)</option>
                    <option value="enterprise">Enterprise (500+)</option>
                    <option value="government">Government</option>
                  </select>
                </div>
                <div>
                  <label htmlFor="industry" className="block text-sm font-medium text-gray-700 mb-2">Company industry <span className="text-red-500">*</span></label>
                  <select id="industry" name="industry" value={formData.industry} onChange={handleInputChange} required className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[var(--color-blue)] transition-all bg-white">
                    <option value="">Choose industry</option>
                    <option value="technology">Technology</option>
                    <option value="finance">Finance</option>
                    <option value="healthcare">Healthcare</option>
                    <option value="manufacturing">Manufacturing</option>
                    <option value="retail">Retail</option>
                    <option value="education">Education</option>
                    <option value="government">Government</option>
                    <option value="other">Other</option>
                  </select>
                </div>
              </div>
              <SubmitButton>
                Submit Inquiry
              </SubmitButton>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
}
