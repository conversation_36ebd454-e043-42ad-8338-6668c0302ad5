{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/Home/components/heroSection.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/Home/components/heroSection.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/Home/components/heroSection.jsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2S,GACxU,yEACA", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/Home/components/heroSection.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/Home/components/heroSection.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/Home/components/heroSection.jsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAuR,GACpT,qDACA", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/Home/components/OurServices.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/Home/components/OurServices.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/Home/components/OurServices.jsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2S,GACxU,yEACA", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/Home/components/OurServices.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/Home/components/OurServices.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/Home/components/OurServices.jsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAuR,GACpT,qDACA", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/Home/components/ComplianceSection.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/Home/components/ComplianceSection.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/Home/components/ComplianceSection.jsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAiT,GAC9U,+EACA", "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/Home/components/ComplianceSection.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/Home/components/ComplianceSection.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/Home/components/ComplianceSection.jsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6R,GAC1T,2DACA", "debugId": null}}, {"offset": {"line": 106, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/Home/components/WhyChooseUsSection.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/Home/components/WhyChooseUsSection.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/Home/components/WhyChooseUsSection.jsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkT,GAC/U,gFACA", "debugId": null}}, {"offset": {"line": 126, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/Home/components/WhyChooseUsSection.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/Home/components/WhyChooseUsSection.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/Home/components/WhyChooseUsSection.jsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8R,GAC3T,4DACA", "debugId": null}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 146, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/Home/components/TrustedTestimonials.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/Home/components/TrustedTestimonials.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/Home/components/TrustedTestimonials.jsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAmT,GAChV,iFACA", "debugId": null}}, {"offset": {"line": 158, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/Home/components/TrustedTestimonials.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/Home/components/TrustedTestimonials.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/Home/components/TrustedTestimonials.jsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+R,GAC5T,6DACA", "debugId": null}}, {"offset": {"line": 170, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 178, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/Home/components/ContactUsSection.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ContactUsSection = registerClientReference(\n    function() { throw new Error(\"Attempted to call ContactUsSection() from the server but ContactUsSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/Home/components/ContactUsSection.jsx <module evaluation>\",\n    \"ContactUsSection\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/Home/components/ContactUsSection.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/Home/components/ContactUsSection.jsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,8EACA;uCAEW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgT,GAC7U,8EACA", "debugId": null}}, {"offset": {"line": 194, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/Home/components/ContactUsSection.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ContactUsSection = registerClientReference(\n    function() { throw new Error(\"Attempted to call ContactUsSection() from the server but ContactUsSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/Home/components/ContactUsSection.jsx\",\n    \"ContactUsSection\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/Home/components/ContactUsSection.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/Home/components/ContactUsSection.jsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,0DACA;uCAEW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA4R,GACzT,0DACA", "debugId": null}}, {"offset": {"line": 210, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 218, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/Home/components/P3.jsx"], "sourcesContent": ["import React from 'react';\r\nimport { Shield, Check, ArrowRight, Database, Lock, Stethoscope } from 'lucide-react';\r\n\r\nconst SecuritySolutions = () => {\r\n  const services = [\r\n    {\r\n      icon: Database,\r\n      title: 'Prepare',\r\n      description: 'We offer consulting and training to help you and your team implement a robust cyber security strategy',\r\n      features: [\r\n        'Cybersecurity risk assessment and planning',\r\n        'Employee security awareness training',\r\n        'Security policy development',\r\n        'Compliance framework implementation',\r\n        'Business continuity planning'\r\n      ],\r\n      buttonText: 'Get Prepared'\r\n    },\r\n    {\r\n      icon: Shield,\r\n      title: 'Prevent',\r\n      description: 'We can detect and assess your cyber risk through ethical hacking, and help you remove any system vulnerabilities',\r\n      features: [\r\n        'Penetration testing and vulnerability scanning',\r\n        'Network security monitoring',\r\n        'Firewall and intrusion detection setup',\r\n        'Regular security audits',\r\n        'Proactive threat intelligence'\r\n      ],\r\n      buttonText: 'Start Prevention'\r\n    },\r\n    {\r\n      icon: Stethoscope,\r\n      title: 'Protect',\r\n      description: \"If you've suffered a cyber security breach, we can help you respond and recover from any incident\",\r\n      features: [\r\n        '24/7 incident response team',\r\n        'Forensic investigation services',\r\n        'Data recovery and restoration',\r\n        'Legal and regulatory compliance support',\r\n        'Post-incident security improvements'\r\n      ],\r\n      buttonText: 'Get Protection'\r\n    }\r\n  ];\r\n\r\n  return (\r\n    <div className=\"min-h-screen\">\r\n      \r\n      {/* Services Section */}\r\n      <div className=\" bg-[#F8F8F8] py-20 px-4\">\r\n        <div className=\"max-w-6xl mx-auto\">\r\n          {/* Header */}\r\n          <div className=\"text-center mb-16\">\r\n            <h1 className=\"text-lg  lg:text-xl xl:text-3xl font-bold text-[var(--color-dark-blue)] mb-8 leading-tight\">\r\n              NO MATTER WHAT YOUR CYBER SECURITY NEEDS ARE, WE CAN GIVE YOU PEACE OF MIND THROUGH ROBUST CYBER PROTECTION\r\n            </h1>\r\n          </div>\r\n\r\n          {/* Service Cards */}\r\n          <div className=\"grid md:grid-cols-3 gap-8\">\r\n            {services.map((service, index) => (\r\n              <div\r\n                key={index}\r\n                className=\"bg-white rounded-lg shadow-lg border border-gray-200 p-8 relative hover:shadow-xl transition-shadow duration-300\"\r\n              >\r\n                {/* Service Icon */}\r\n                <div className=\"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-6\">\r\n                  <service.icon className=\"w-8 h-8 text-blue-600\" />\r\n                </div>\r\n\r\n                {/* Service Title */}\r\n                <h3 className=\"text-2xl font-semibold text-gray-900 mb-3\">\r\n                  {service.title}\r\n                </h3>\r\n\r\n                {/* Description */}\r\n                <p className=\"text-gray-600 text-sm mb-6 leading-relaxed\">\r\n                  {service.description}\r\n                </p>\r\n\r\n                {/* Features */}\r\n                <div className=\"space-y-3 mb-8\">\r\n                  {service.features.map((feature, featureIndex) => (\r\n                    <div key={featureIndex} className=\"flex items-start gap-3\">\r\n                      <div className=\"w-5 h-5 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5\">\r\n                        <Check className=\"w-3 h-3 text-green-600\" />\r\n                      </div>\r\n                      <span className=\"text-sm text-gray-700\">\r\n                        {feature}\r\n                      </span>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n\r\n                {/* CTA Button */}\r\n                <button className=\"w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center gap-2\">\r\n                  {service.buttonText}\r\n                  <ArrowRight className=\"w-4 h-4\" />\r\n                </button>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </div>\r\n \r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SecuritySolutions;"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;;;;AAEA,MAAM,oBAAoB;IACxB,MAAM,WAAW;QACf;YACE,MAAM,0MAAA,CAAA,WAAQ;YACd,OAAO;YACP,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;QACd;QACA;YACE,MAAM,sMAAA,CAAA,SAAM;YACZ,OAAO;YACP,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;QACd;QACA;YACE,MAAM,gNAAA,CAAA,cAAW;YACjB,OAAO;YACP,aAAa;YACb,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;QACd;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;kBAGb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;sCAA6F;;;;;;;;;;;kCAM7G,8OAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC;gCAEC,WAAU;;kDAGV,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,QAAQ,IAAI;4CAAC,WAAU;;;;;;;;;;;kDAI1B,8OAAC;wCAAG,WAAU;kDACX,QAAQ,KAAK;;;;;;kDAIhB,8OAAC;wCAAE,WAAU;kDACV,QAAQ,WAAW;;;;;;kDAItB,8OAAC;wCAAI,WAAU;kDACZ,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC9B,8OAAC;gDAAuB,WAAU;;kEAChC,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;;;;;;kEAEnB,8OAAC;wDAAK,WAAU;kEACb;;;;;;;+CALK;;;;;;;;;;kDAYd,8OAAC;wCAAO,WAAU;;4CACf,QAAQ,UAAU;0DACnB,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;+BAnCnB;;;;;;;;;;;;;;;;;;;;;;;;;;AA6CrB;uCAEe", "debugId": null}}, {"offset": {"line": 418, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/Home/components/LandingSection.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/Home/components/LandingSection.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/Home/components/LandingSection.jsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8S,GAC3U,4EACA", "debugId": null}}, {"offset": {"line": 430, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/Home/components/LandingSection.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/Home/components/LandingSection.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/Home/components/LandingSection.jsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA0R,GACvT,wDACA", "debugId": null}}, {"offset": {"line": 442, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 450, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/page.js"], "sourcesContent": ["import HeroSection from \"@/app/Home/components/heroSection\";\r\nimport OurServices from \"./Home/components/OurServices\";\r\nimport ComplianceSection from \"./Home/components/ComplianceSection\";\r\nimport WhyChooseUsSection from \"./Home/components/WhyChooseUsSection\"; \r\nimport TrustedTestimonials from \"./Home/components/TrustedTestimonials\";\r\nimport ContactUsSection from \"./Home/components/ContactUsSection\";\r\nimport SecuritySolutions from \"./Home/components/P3\";\r\nimport Landing from \"./Home/components/LandingSection\";\r\n\r\nexport const metadata = {\r\n  title: \"SecurityLit | Cybersecurity Services & PTaaS Platform\",\r\n  description:\r\n    \"SecurityLit is a leading cybersecurity service provider offering vCISO as a Service, Red Teaming, VAPT, Compliance Pre-Assessment, and Cybersecurity Training. Explore our PTaaS platform, Capture the Bug, for continuous pentesting and vulnerability management.\",\r\n  openGraph: {\r\n    title: \" Cybersecurity Services & PTaaS Platform | SecurityLit \",\r\n    description:\r\n      \"SecurityLit is a leading cybersecurity service provider offering vCISO as a Service, Red Teaming, VAPT, Compliance Pre-Assessment, and Cybersecurity Training. Explore our PTaaS platform, Capture the Bug, for continuous pentesting and vulnerability management.\",\r\n    url: \"https://securitylit.com/\",\r\n    siteName: \"SecurityLit\",\r\n    images: [\r\n      {\r\n        url: \"/your-hero-bg.jpg\",\r\n        width: 1200,\r\n        height: 630,\r\n        alt: \"SecurityLit Cybersecurity Services\",\r\n      },\r\n    ],\r\n    locale: \"en_GB\",\r\n    type: \"website\",\r\n  },\r\n};\r\n\r\nexport default function Home() {\r\n  return (\r\n    <main className=\"min-h-screen w-full\">\r\n      <Landing /> \r\n      <OurServices/>\r\n      {/* <LogoCarouselSection /> */}\r\n      <WhyChooseUsSection />\r\n      <SecuritySolutions />\r\n      <ComplianceSection /> \r\n   <TrustedTestimonials /> \r\n      <ContactUsSection />\r\n\r\n    </main>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AAEO,MAAM,WAAW;IACtB,OAAO;IACP,aACE;IACF,WAAW;QACT,OAAO;QACP,aACE;QACF,KAAK;QACL,UAAU;QACV,QAAQ;YACN;gBACE,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,KAAK;YACP;SACD;QACD,QAAQ;QACR,MAAM;IACR;AACF;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAK,WAAU;;0BACd,8OAAC,mJAAA,CAAA,UAAO;;;;;0BACR,8OAAC,gJAAA,CAAA,UAAW;;;;;0BAEZ,8OAAC,uJAAA,CAAA,UAAkB;;;;;0BACnB,8OAAC,uIAAA,CAAA,UAAiB;;;;;0BAClB,8OAAC,sJAAA,CAAA,UAAiB;;;;;0BACrB,8OAAC,wJAAA,CAAA,UAAmB;;;;;0BACjB,8OAAC,qJAAA,CAAA,UAAgB;;;;;;;;;;;AAIvB", "debugId": null}}]}