"use client";

import React from "react";

export default function ContactTestimonial() {
  const partners = [
    { name: "Fortune 500", logo: "/seclit-logo-white.png" },
    { name: "Government of India", logo: "/seclit-logo-white.png" },
    { name: "Leading Healthcare Provider", logo: "/seclit-logo-white.png" },
    { name: "Major Financial Institution", logo: "/seclit-logo-white.png" },
  ];

  return (
    // CHANGED: The background is now set to your light brand yellow.
    <section 
      className="py-20 lg:py-28" 
      style={{ backgroundColor: 'var(--color-yellow)' }}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-y-12 items-center">
          
          {/* Left Section - Heading */}
          <div className="lg:col-span-5 text-center lg:text-left">
            <h2 className="text-3xl md:text-4xl font-bold text-[var(--color-white)] leading-snug">
              Trusted cyber security partner to leading Indian organisations.
            </h2>
          </div>

          {/* Divider (visible on large screens only) */}
          <div className="hidden lg:block lg:col-span-1">
             {/* CHANGED: Divider color is slightly darkened for better contrast on the yellow background */}
             <div className="w-px h-24 bg-gray-300 mx-auto"></div>
          </div>

          {/* Right Section - Partner Logos */}
          <div className="lg:col-span-6">
            <div className="grid grid-cols-2 gap-x-8 gap-y-10 sm:gap-12">
              {partners.map((partner) => (
                <div key={partner.name} className="flex justify-center items-center">
                  <img
                    className="max-h-12 w-full object-contain transition-all duration-300 filter grayscale hover:grayscale-0"
                    src={partner.logo}
                    alt={partner.name}
                    onError={(e) => { e.currentTarget.style.display = 'none'; }}
                  />
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
