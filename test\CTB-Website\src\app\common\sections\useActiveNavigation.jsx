// hooks/useActiveNavigation.js
import { useState, useEffect } from 'react';
import { usePathname } from 'next/navigation';

export const useActiveNavigation = () => {
  const pathname = usePathname();
  const [activeNavItem, setActiveNavItem] = useState('');

  useEffect(() => {
    // Define your navigation structure with their corresponding paths
    const navigationPaths = {
      Product: ['/Product', '/Product/Penetration-Testing'],
      Solutions: [
        '/Company-size', '/Company-size/Start-Up', '/Company-size/Growing-Team', '/Company-size/Enterprise',
        '/Services', '/Services/Web-app', '/Services/Mobile-app', '/Services/Network-pentest', '/Services/API-pentest',
        '/Download-Report'
      ],
      Blogs: ['/Blogs'],
      Customer: ['/Customers'],
      Pricing: ['/Pricing'],
      Company: [
        '/Company', '/Company/About-Us', '/Company/Press', '/Company/Contact-Us'
      ]
    };
    const currentPath = pathname;
    
    // Find which navigation item matches the current path
    const activeItem = Object.entries(navigationPaths).find(([key, paths]) => {
      return paths.some(path => {
        // Exact match
        if (currentPath === path) return true;
        // Starts with match (for nested routes)
        if (currentPath.startsWith(path + '/')) return true;
        return false;
      });
    });

    if (activeItem) {
      setActiveNavItem(activeItem[0]);
    } else {
      // If no match found, check if we're on home page
      if (currentPath === '/' || currentPath === '') {
        setActiveNavItem('');
      } else {
        setActiveNavItem('');
      }
    }
  }, [pathname]);

  // Function to check if a specific nav item is active
  const isNavItemActive = (navItem) => {
    return activeNavItem === navItem;
  };

  // Function to get active nav item
  const getActiveNavItem = () => {
    return activeNavItem;
  };

  return {
    activeNavItem,
    isNavItemActive,
    getActiveNavItem
  };
};
