
"use client";
import { motion } from 'framer-motion';
import React from 'react'
import LandingSection from './components/LandingSection'
import CalculatorSection from './components/CalculatorSection'
import PartnersList from './components/PartnersList'
import Testimonial from './components/Testimonial'
import BookACall from '../Home/components/BookACall'
import FAQ from './components/FAQ'
import ComparisonFeatures from './components/ComparisonFeatures'
import AdditionalServices from './components/AdditionalServices'
import WhyPartnerSection from './components/WhyPartnerSection'
import TestingOption from './components/TestingOption'
import ServiceComparison from './components/ServiceComparison'
import PaymentOption from './components/PaymentOption'
import SubscriptionClientJourney from './components/SubscriptionClientJourney'
import CardsTailor from './components/CardsTailor'
import BreadcrumbNavigation from '../common/components/BreadcrumbNavigation'
import { useBreadcrumbs } from '../common/hooks/useBreadcrumbs'


export default function Page() {
  const breadcrumbs = useBreadcrumbs();

  return (
    <div className="relative">
      <title>Capture The Bug | Pricing</title>

      {/* Breadcrumb Navigation - positioned absolutely at the top */}
      <div className="absolute top-0 left-0 right-0 pt-2 sm:pt-2 md:pt-5 xl:pt-14 z-10">
        <div className="max-w-7xl px-2 sm:px-2 md:px-16">
          <BreadcrumbNavigation items={breadcrumbs} />
        </div>
      </div>

      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.1 }}
            >
      <CalculatorSection/>
      </motion.div>
      <CardsTailor/>
      <TestingOption/>
      {/* <ServiceComparison/> */}
      <PaymentOption/>
      <SubscriptionClientJourney/>
      {/* <LandingSection/> */}
      {/* <ComparisonFeatures/> */}
      {/* <AdditionalServices/> */}
      <WhyPartnerSection/>
      <FAQ/>
      {/* <PartnersList/> */}
      {/* <Testimonial/> */}
      {/* <BookACall/> */}
      </motion.div>
    </div>
  )
}
