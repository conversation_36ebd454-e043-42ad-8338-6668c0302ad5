"use client";

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function TrainingPage() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to the new training page
    router.replace('/CybersecurityTraining');
  }, [router]);

  return (
    <div className="min-h-screen bg-white flex items-center justify-center">
      <div className="text-center">
        <h1 className="text-2xl font-bold text-[var(--color-dark-blue)] mb-4">
          Redirecting to Cybersecurity Training...
        </h1>
        <p className="text-[var(--foreground-secondary)]">
          If you are not redirected automatically, <a href="/CybersecurityTraining" className="text-[var(--color-blue)] hover:underline">click here</a>.
        </p>
      </div>
    </div>
  );
}