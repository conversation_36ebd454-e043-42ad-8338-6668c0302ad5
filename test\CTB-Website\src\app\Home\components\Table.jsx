"use client";

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { ChartBarIcon, ClockIcon, BoltIcon, CurrencyDollarIcon } from '@heroicons/react/24/outline';
import Button from '../../common/buttons/Button';

const comparisonData = [
  {
    category: "Pricing Model",
    icon: (
      <svg className="w-full h-full" fill="currentColor" viewBox="0 0 24 24">
        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm.31-8.86c-1.77-.45-2.34-.94-2.34-1.67 0-.84.79-1.43 2.1-1.43 1.38 0 1.9.66 1.94 1.64h1.71c-.05-1.34-.87-2.57-2.49-2.97V5H10.9v1.69c-1.51.32-2.72 1.3-2.72 2.81 0 1.79 1.49 2.69 3.66 3.21 1.95.46 2.34 1.15 2.34 1.87 0 .53-.39 1.39-2.1 1.39-1.6 0-2.23-.72-2.32-1.64H8.04c.1 1.7 1.36 2.66 2.86 2.97V19h2.34v-1.67c1.52-.29 2.72-1.16 2.73-2.77-.01-2.2-1.9-2.96-3.66-3.42z" />
      </svg>
    ),
    ptaas: {
      title: "Annual Subscription",
      description: "One fixed price for unlimited testing, aligned to your business velocity."
    },
    traditional: {
      title: "Per Engagement Cost",
      description: "Every test scoped and billed separately. Scope creep = more $$$."
    },
    ptaasAdvantages: [
      "Unlimited testing",
      "Predictable costs",
      "Aligned to business needs"
    ]
  },
  {
    category: "Testing Cadence",
    icon: (
      <svg className="w-full h-full" fill="currentColor" viewBox="0 0 24 24">
        <path d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8zm.5-13H11v6l5.25 3.15.75-1.23-4.5-2.67z" />
      </svg>
    ),
    ptaas: {
      title: "Flexible, On-Demand Testing",
      description: "You decide the cadence-monthly, quarterly, pre-release, or continuous."
    },
    traditional: {
      title: "Fixed Windows",
      description: "Typically once or twice a year. Difficult to adjust if product timelines shift."
    },
    ptaasAdvantages: [
      "Test when you need",
      "No scheduling bottlenecks",
      "Continuous security"
    ]
  },
  {
    category: "Testing Approach",
    icon: (
      <svg className="w-full h-full" fill="currentColor" viewBox="0 0 24 24">
        <path d="M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5c-1.66 0-3 1.34-3 3s1.34 3 3 3zm-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5C6.34 5 5 6.34 5 8s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5zm8 0c-.29 0-.62.02-.97.05 1.16.84 1.97 1.97 1.97 3.45V19h6v-2.5c0-2.33-4.67-3.5-7-3.5z" />
      </svg>
    ),
    ptaas: {
      title: "Manual-First + Assisted",
      description: "In-depth, contextual testing enhanced by smart tooling-not replaced by it."
    },
    traditional: {
      title: "Heavily Automated",
      description: "Relies on scanners with minimal depth. Manual testing often superficial."
    },
    ptaasAdvantages: [
      "Contextual findings",
      "Human expertise",
      "Smart tooling support"
    ]
  },
  {
    category: "Reporting & Visibility",
    icon: (
      <svg className="w-full h-full" fill="currentColor" viewBox="0 0 24 24">
        <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z" />
      </svg>
    ),
    ptaas: {
      title: "Real-Time Dashboard",
      description: "Track vulnerabilities as they're found. Dev-ready reports with Jira & GitHub sync."
    },
    traditional: {
      title: "Single PDF Report",
      description: "Delivered at the end. No visibility into test progress or partial findings."
    },
    ptaasAdvantages: [
      "Live visibility",
      "Dev tool integration",
      "Actionable insights"
    ]
  },
  {
    category: "Retesting",
    icon: (
      <svg className="w-full h-full" fill="currentColor" viewBox="0 0 24 24">
        <path d="M19 9l1.25-2.75L23 5l-2.75-1.25L19 1l-1.25 2.75L15 5l2.75 1.25L19 9zm-7.5.5L9 4 6.5 9.5 1 12l5.5 2.5L9 20l2.5-5.5L17 12l-5.5-2.5zM19 15l-1.25 2.75L15 19l2.75 1.25L19 23l1.25-2.75L23 19l-2.75-1.25L19 15z" />
      </svg>
    ),
    ptaas: {
      title: "Unlimited Retests",
      description: "Included in your plan. Verify fixes anytime via the platform."
    },
    traditional: {
      title: "Usually Not Included",
      description: "Retests come with additional cost or need separate booking."
    },
    ptaasAdvantages: [
      "No extra cost",
      "On-demand verification",
      "Faster remediation"
    ]
  },
  {
    category: "Dev Enablement",
    icon: (
      <svg className="w-full h-full" fill="currentColor" viewBox="0 0 24 24">
        <path d="M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 1.99 2H18c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z" />
      </svg>
    ),
    ptaas: {
      title: "Developer-Friendly",
      description: "Actionable reports with reproduction steps, severity ratings, and fix guidance."
    },
    traditional: {
      title: "Disconnected from Dev",
      description: "Long-form reports without reproducibility or context."
    },
    ptaasAdvantages: [
      "Reproducible steps",
      "Severity ratings",
      "Fix guidance"
    ]
  }
];

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.07
    }
  }
};

const itemVariants = {
  hidden: { opacity: 0, y: 15 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.4 }
  }
};

const fadeIn = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: { duration: 0.6 }
  }
};

export default function Table() {
  const [expandedDetails, setExpandedDetails] = useState(null);
  
  const toggleDetails = (index) => {
    if (expandedDetails === index) {
      setExpandedDetails(null);
    } else {
      setExpandedDetails(index);
    }
  };
  
  // Arrow icon for button
  const arrowIcon = (
    <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
    </svg>
  );

  return (
    <section className="w-full py-20 sm:py-24 md:py-28 bg-gradient-to-b from-white to-ctb-bg-light/30 relative overflow-hidden">
      {/* Background elements */}
      <div className="absolute inset-0 bg-[url('/images/grid-pattern.svg')] bg-repeat opacity-[0.03]"></div>
      <div className="absolute -left-64 top-1/4 w-96 h-96 rounded-full bg-primary-blue/5 blur-3xl"></div>
      <div className="absolute -right-64 bottom-1/4 w-96 h-96 rounded-full bg-primary-blue/5 blur-3xl"></div>
      
      <div className="max-w-screen-2xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8 relative z-10">
        {/* Header */}
        <motion.div 
          className="mb-16 text-center"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <span className="inline-block px-4 py-1.5 mb-4 rounded-full bg-primary-blue/10 text-primary-blue text-sm font-medium tracking-wide">COMPARISON</span>
          <h2 className="text-3xl sm:text-4xl md:text-5xl font-[500] text-tertiary-blue mb-6">
            Traditional Pentest <span className="text-primary-blue font-[600]">VS.</span> <span className="text-primary-blue">PTaaS</span>
          </h2>
          <p className="text-base sm:text-lg text-secondary-blue/90 max-w-3xl mx-auto leading-relaxed">
            Discover how our Penetration Testing as a Service (PTaaS) approach compares to traditional penetration testing methods.
          </p>
          <div className="w-24 h-1 bg-gradient-to-r from-primary-blue/60 to-secondary-blue/60 mx-auto rounded-full mt-6"></div>
        </motion.div>

        {/* Enterprise-grade comparison table */}
        <div className="md:px-4 lg:px-8 xl:px-16">
          <div className="hidden lg:block relative">
            <motion.div
              variants={containerVariants}
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true, amount: 0.1 }}
              className="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-200/80"
            >
              {/* Table Header */}
              <div className="grid grid-cols-12 bg-gradient-to-r from-tertiary-blue to-primary-blue text-white">
                <div className="col-span-3 p-5 border-r border-white/10 flex items-center justify-center">
                  <h3 className="text-xl font-semibold">Features</h3>
                </div>
                <div className="col-span-4 p-5 border-r border-white/10 text-center">
                  <h3 className="text-xl font-bold">Traditional Pentest</h3>
                </div>
                <div className="col-span-4 p-5 text-center relative overflow-hidden">
                  <div className="absolute inset-0 bg-[url('/images/grid-pattern.svg')] bg-repeat opacity-10"></div>
                  <div className="relative">
                    <h3 className="text-xl font-bold">Capture The Bug PTaaS</h3>
                  </div>
                </div>
              </div>
                
              {/* Table Body */}
              {comparisonData.map((item, index) => (
                <motion.div 
                  key={index}
                  variants={itemVariants} 
                  className={`grid grid-cols-12 border-b border-gray-200 last:border-b-0 ${index % 2 === 0 ? 'bg-gray-50/50' : 'bg-white'}`}
                >
                  {/* Feature Column */}
                  <div className="col-span-3 p-5 border-r border-gray-200">
                    <div className="flex flex-col items-center justify-center text-center gap-3">
                      <div className="w-12 h-12 rounded-lg bg-tertiary-blue/10 flex items-center justify-center text-primary-blue p-2.5 shadow-sm">
                        {item.icon}
                      </div>
                      <span className="font-semibold text-tertiary-blue text-lg">{item.category}</span>
                    </div>
                  </div>
                  
                  {/* Traditional Column */}
                  <div className="col-span-4 p-5 border-r border-gray-200">
                    <h4 className="text-lg font-semibold text-tertiary-blue mb-2">{item.traditional.title}</h4>
                    <p className="text-gray-600 text-base">{item.traditional.description}</p>
                  </div>

                  {/* PTaaS Column */}
                  <div 
                    className="col-span-4 p-5 relative"
                  >
                    <div className="absolute left-0 top-5 bottom-5 w-1 bg-primary-blue/40 rounded-r"></div>
                    <div className="pl-3">
                      <h4 className="text-lg font-semibold text-primary-blue mb-2">{item.ptaas.title}</h4>
                      <p className="text-gray-600 text-base">{item.ptaas.description}</p>
                    </div>
                  </div>
                </motion.div>
              ))}
            </motion.div>
          </div>
          
          {/* Mobile Comparison */}
          <div className="lg:hidden">
            <motion.div
              variants={containerVariants}
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true, amount: 0.1 }}
              className="space-y-6"
            >
              {comparisonData.map((item, index) => (
                <motion.div
                  key={index}
                  variants={itemVariants}
                  className="bg-white rounded-lg shadow-md overflow-hidden border border-gray-200/80"
                >
                  {/* Feature Header */}
                  <div className="bg-gradient-to-r from-tertiary-blue to-primary-blue p-5 text-white">
                    <div className="flex flex-col items-center justify-center gap-3 text-center">
                      <div className="w-12 h-12 rounded-lg bg-white/10 flex items-center justify-center text-white p-2.5">
                        {item.icon}
                      </div>
                      <span className="font-semibold text-lg">{item.category}</span>
                    </div>
                  </div>
                  
                  {/* Comparison Content */}
                  <div className="divide-y divide-gray-200">
                    {/* Traditional Option */}
                    <div className="p-4 relative">
                      <div className="absolute left-0 top-0 h-full w-1 bg-tertiary-blue/60"></div>
                      <div className="flex justify-between items-start mb-2">
                        <h4 className="text-tertiary-blue font-semibold pl-3">Traditional: {item.traditional.title}</h4>
                        <div className="w-6 h-6 rounded-full bg-tertiary-blue/10 flex items-center justify-center">
                          <span className="text-tertiary-blue font-medium text-xs">T</span>
                        </div>
                      </div>
                      <p className="text-gray-600 text-base pl-3">{item.traditional.description}</p>
                    </div>

                    {/* PTaaS Option */}
                    <div 
                      className="p-4 relative"
                    >
                      <div className="absolute left-0 top-0 h-full w-1 bg-primary-blue/60"></div>
                      <div className="flex justify-between items-start mb-2">
                        <h4 className="text-primary-blue font-semibold pl-3">PTaaS: {item.ptaas.title}</h4>
                        <div className="w-6 h-6 rounded-full bg-primary-blue/10 flex items-center justify-center">
                          <span className="text-primary-blue font-medium text-xs">P</span>
                        </div>
                      </div>
                      <p className="text-gray-600 text-base pl-3 mb-3">{item.ptaas.description}</p>
                    </div>
                  </div>
                </motion.div>
              ))}
            </motion.div>
          </div>
        </div>
        
        {/* CTA Section */}
        <motion.div 
          className="mt-12 text-center"
          variants={fadeIn}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.2 }}
        >
          <Button 
            href="/How-it-works" 
            variant="primary"
            size="md"
            rightIcon={arrowIcon}
          >
            Learn More About Our Approach
          </Button>
        </motion.div>
      </div>
    </section>
  );
}
