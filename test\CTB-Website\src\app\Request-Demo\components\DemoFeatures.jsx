"use client";
import React, { useState, useEffect } from 'react';
import { Shield, Zap, Code, CheckCircle, AlertTriangle, Activity, Lock, Eye } from 'lucide-react';
import Image from 'next/image';

export default function VendorSecurityReview() {
  const [scanProgress, setScanProgress] = useState(0);
  const [vulnerabilities, setVulnerabilities] = useState([]);
  const [isScanning, setIsScanning] = useState(false);
  const [hasCompletedFirstScan, setHasCompletedFirstScan] = useState(false);

  useEffect(() => {
    const interval = setInterval(() => {
      setScanProgress(prev => {
        if (prev >= 100) {
          setIsScanning(false);
          setHasCompletedFirstScan(true);
          return 100;
        }
        return prev + 2;
      });
    }, 100);

    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    if (scanProgress > 30 && vulnerabilities.length === 0 && !hasCompletedFirstScan) {
      setVulnerabilities([
        { id: 1, type: 'SQL Injection', severity: 'high', status: 'detected' },
        { id: 2, type: 'XSS Vulnerability', severity: 'medium', status: 'analyzing' },
        { id: 3, type: 'CSRF Token Missing', severity: 'low', status: 'pending' }
      ]);
    }
  }, [scanProgress, vulnerabilities.length, hasCompletedFirstScan]);

  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'high': return 'text-red-600 bg-red-50 border-red-200';
      case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'low': return 'text-blue-600 bg-blue-50 border-blue-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const sections = [
    {
      title: "Continuous Pentesting That Keeps Up with Your Code",
      description: "No more once-a-year security snapshots. Capture The Bug delivers ongoing, real-time pentesting that aligns with your release cycles.",
      features: [
        {
          title: "Real-time scanning",
          description: "Every new deployment gets tested-so you catch critical vulnerabilities before attackers do."
        },
        {
          title: "CI/CD integration", 
          description: "Seamlessly integrate with your existing development pipeline for continuous security testing."
        },
        {
          title: "Automated testing",
          description: "Comprehensive automated vulnerability scanning that runs with every code change."
        }
      ],
      hasInteractiveDashboard: true
    },
    {
      title: "Developer-First Integrations",
      description: "Security shouldn't slow you down. We integrate seamlessly with your CI/CD pipeline, GitHub, and Jira, so your engineers can triage, track, and resolve issues right inside their existing workflows.",
      features: [
        {
          title: "Dev-friendly tooling",
          description: " "
        }, 
      ],
      imageUrl: "/images/Integration.svg"
    },
    {
      title: "Actionable Insights, Audit-Ready Reports", 
      description: "Get clean, executive-ready dashboards, risk scores, and detailed remediation steps that satisfy auditors and win customer trust. Security and ROI, on one screen.",
      features: [
        {
          title: "Executive dashboards",
          description: "Clear, high-level views of your security posture that executives and stakeholders can understand."
        },
        {
          title: "Compliance reporting",
          description: "Audit-ready reports that demonstrate your security practices and compliance status."
        }
      ],
      imageUrl: "/images/APIDash.svg"
    }
  ];

  const renderInteractiveDashboard = () => (
    <div className="w-full flex justify-center lg:justify-start px-2 xs:px-0">
      <div className="relative w-full max-w-[280px] xs:max-w-[320px] sm:max-w-[450px] md:max-w-[500px] lg:max-w-[600px] sm:ml-0 ">
         <div className="absolute -inset-2 xs:-inset-4 bg-gradient-to-r from-blue-100 via-indigo-100 to-purple-100 rounded-2xl xs:rounded-3xl opacity-40 blur-lg xs:blur-xl animate-pulse"></div>
        <div className="absolute -inset-1 xs:-inset-2 bg-gradient-to-r from-blue-200 to-indigo-200 rounded-xl xs:rounded-2xl opacity-20 blur-md xs:blur-lg"></div>
         <div className="relative bg-white rounded-xl xs:rounded-2xl shadow-xl xs:shadow-2xl overflow-hidden border border-slate-200/50 backdrop-blur-sm">

          {/* Dashboard Header */}
          <div className="bg-gradient-to-r from-blue-600 to-indigo-600 px-3 xs:px-4 sm:px-6 py-2.5 xs:py-3 sm:py-4 text-white">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-1.5 xs:gap-2 sm:gap-3">
                <Shield className="w-4 h-4 xs:w-5 xs:h-5 sm:w-6 sm:h-6" />
                <h3 className="font-semibold text-sm xs:text-base sm:text-lg">Security Scanner</h3>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-xs sm:text-sm">{scanProgress > 0 && scanProgress < 100 ? 'Active' : scanProgress === 100 ? 'Complete' : 'Idle'}</span>
              </div>
            </div>
          </div>

          {/* Scanning Progress */}
          <div className="p-3 xs:p-4 sm:p-6 border-b border-gray-100">
            <div className="flex items-center justify-between mb-2">
              <span className="text-xs sm:text-sm font-medium text-gray-700">Scan Progress</span>
              <span className="text-xs sm:text-sm text-gray-500">{scanProgress}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-1.5 xs:h-2 sm:h-2.5">
              <div
                className="bg-gradient-to-r from-blue-500 to-indigo-500 h-1.5 xs:h-2 sm:h-2.5 rounded-full transition-all duration-300 ease-out"
                style={{ width: `${scanProgress}%` }}
              ></div>
            </div>
            {scanProgress > 0 && scanProgress < 100 && (
              <div className="mt-2 flex items-center gap-2 text-xs sm:text-sm text-blue-600">
                <Activity className="w-3 h-3 xs:w-3 xs:h-3 sm:w-4 sm:h-4 animate-pulse" />
                <span>Analyzing endpoints...</span>
              </div>
            )}
            {scanProgress === 100 && (
              <div className="mt-2 flex items-center gap-2 text-xs sm:text-sm text-green-600">
                <CheckCircle className="w-3 h-3 xs:w-3 xs:h-3 sm:w-4 sm:h-4" />
                <span>Scan completed</span>
              </div>
            )}
          </div>

          {/* Vulnerabilities List */}
          <div className="p-3 xs:p-4 sm:p-6">
            <div className="flex items-center justify-between mb-2 xs:mb-3 sm:mb-4">
              <h4 className="font-semibold text-xs xs:text-sm sm:text-base text-gray-800 flex items-center gap-1.5 xs:gap-2">
                <AlertTriangle className="w-3 h-3 xs:w-4 xs:h-4 sm:w-5 sm:h-5 text-orange-500" />
                Detected Issues
              </h4>
              <span className="text-xs bg-red-100 text-red-700 px-2 py-1 rounded-full">
                {vulnerabilities.length} found
              </span>
            </div>

            <div className="space-y-2 sm:space-y-3 min-h-[120px] xs:min-h-[150px] sm:min-h-[200px]">
              {vulnerabilities.length === 0 ? (
                <div className="text-center py-4 xs:py-6 sm:py-8 text-gray-500">
                  <Eye className="w-5 h-5 xs:w-6 xs:h-6 sm:w-8 sm:h-8 mx-auto mb-2 opacity-50" />
                  <p className="text-xs sm:text-sm">No vulnerabilities detected yet</p>
                </div>
              ) : (
                vulnerabilities.map((vuln) => (
                  <div
                    key={vuln.id}
                    className={`p-2 xs:p-2.5 sm:p-3 rounded-lg border ${getSeverityColor(vuln.severity)} shadow-sm`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-1.5 xs:gap-2">
                        <Lock className="w-3 h-3 xs:w-3 xs:h-3 sm:w-4 sm:h-4" />
                        <span className="font-medium text-xs sm:text-sm">{vuln.type}</span>
                      </div>
                      <span className={`text-xs px-1.5 xs:px-2 py-0.5 xs:py-1 rounded-full font-medium capitalize ${getSeverityColor(vuln.severity)}`}>
                        {vuln.severity}
                      </span>
                    </div>
                    <div className="mt-1 text-xs opacity-75">
                      Status: {vuln.status}
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>

          {/* Status indicators */}
          <div className="absolute top-2.5 xs:top-3 sm:top-4 right-2.5 xs:right-3 sm:right-4 flex gap-1 xs:gap-1.5 sm:gap-2">
            <div className="w-1.5 h-1.5 xs:w-2 xs:h-2 sm:w-3 sm:h-3 bg-green-400 rounded-full animate-pulse"></div>
            <div className="w-1.5 h-1.5 xs:w-2 xs:h-2 sm:w-3 sm:h-3 bg-blue-400 rounded-full animate-pulse delay-500"></div>
            <div className="w-1.5 h-1.5 xs:w-2 xs:h-2 sm:w-3 sm:h-3 bg-purple-400 rounded-full animate-pulse delay-1000"></div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderContent = (section, index, isDark) => (
    <div className="space-y-4 xs:space-y-6 sm:space-y-8 lg:space-y-10 order-1 px-2 xs:px-0">
      <div className="space-y-3 xs:space-y-4 sm:space-y-6 md:space-y-10">
        <h2 className="text-lg xs:text-xl sm:text-2xl md:text-3xl lg:text-2xl xl:text-4xl font-bold text-blue-900 leading-tight tracking-tight">
          {section.title}
        </h2>

        <div className="space-y-3 xs:space-y-4 md:space-y-6 text-xs xs:text-sm sm:text-base md:text-lg lg:text-base xl:text-xl text-slate-600 leading-relaxed max-w-2xl">
          <p>
            {section.description}
          </p>
        </div>
      </div>
      
      <div className="flex flex-wrap gap-1.5 xs:gap-2 sm:gap-3 mt-4 xs:mt-6 sm:mt-8">
        {section.features.map((feature, featureIndex) => (
          <span key={featureIndex} className="inline-flex items-center px-2 py-1 xs:px-3 xs:py-1.5 sm:px-4 sm:py-2 rounded-full text-xs sm:text-sm font-medium bg-blue-100 text-blue-800">
            ✓ {feature.title}
          </span>
        ))}
      </div>
    </div>
  );

  const renderImage = (section, index) => {
    if (section.hasInteractiveDashboard) {
      return renderInteractiveDashboard();
    }

    return (
      <div className="w-full flex justify-center order-2 px-2 xs:px-0">
        <div className="relative w-full aspect-[16/9] max-w-[280px] xs:max-w-[320px] sm:max-w-[400px] md:max-w-none">
          <Image
            src={section.imageUrl}
            alt={`Security Integration Dashboard ${index + 1}`}
            className="w-full h-auto object-contain rounded-lg xs:rounded-xl drop-shadow-md xs:drop-shadow-lg"
            width={400}
            height={225}
          />
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen w-full">
      {/* Main Title */}
      <div className="min-h-[calc(100vh-64px)] px-3 xs:px-4 sm:px-6 lg:px-10 bg-gradient-to-br from-slate-50 to-blue-50">
        <div className="container max-w-7xl mx-auto px-2 xs:px-3 sm:px-6 lg:px-8 xl:px-12 py-4 xs:py-6 sm:py-8 md:py-12 lg:py-16">
          <h1 className="text-xl xs:text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-5xl font-bold text-blue-900 text-center leading-tight mb-6 xs:mb-8 sm:mb-12 lg:mb-20 px-2">
            Built for SaaS Teams That Move Fast
          </h1>

          {sections.map((section, index) => {
            const isDark = index % 2 === 1;  
            
            return (
              <div key={index} className="grid grid-cols-1 lg:grid-cols-2 gap-6 xs:gap-8 sm:gap-12 md:gap-16 lg:gap-20 xl:gap-28 items-center mb-12 xs:mb-16 sm:mb-20">
                {/* Content always first on mobile/tablet, alternates on desktop */}
                <div className={`order-1 ${index % 2 === 0 ? 'lg:order-2' : 'lg:order-1'}`}>
                  {renderContent(section, index, isDark)}
                </div>
                {/* Image always second on mobile/tablet, alternates on desktop */}
                <div className={`order-2 ${index % 2 === 0 ? 'lg:order-1' : 'lg:order-2'}`}>
                  {renderImage(section, index)}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      <style jsx>{`
        @keyframes slideInLeft {
          from {
            opacity: 0;
            transform: translateX(-20px);
          }
          to {
            opacity: 1;
            transform: translateX(0);
          }
        }
        
        /* Extra small devices */
        @media (max-width: 374px) {
          .container {
            padding-left: 0.5rem;
            padding-right: 0.5rem;
          }
        }
      `}</style>
    </div>
  );
}