// src/app/common/BrandButtons.jsx

import React from "react";

// Primary blue button
export function PrimaryButton({ children, className = "", ...props }) {
  return (
    <button
      className={`btn-primary text-white px-8 py-3 rounded-lg font-bold text-lg shadow-lg flex items-center justify-center gap-2 cursor-pointer ${className}`}
      {...props}
    >
      {children}
    </button>
  );
}

// Secondary navy blue button
export function SecondaryButton({ children, className = "", ...props }) {
  return (
    <button
      className={`btn-secondary text-white px-8 py-3 rounded-lg font-bold text-lg shadow-md flex items-center justify-center gap-2 cursor-pointer ${className}`}
      {...props}
    >
      {children}
    </button>
  );
}

// Outlined button for secondary actions
export function OutlinedButton({ children, className = "", ...props }) {
  return (
    <button
      className={`btn-outlined text-[var(--color-dark-blue)] px-8 py-3 rounded-lg font-bold text-lg shadow-md flex items-center justify-center gap-2 cursor-pointer ${className}`}
      {...props}
    >
      {children}
    </button>
  );
}

// Accent yellow button
export function AccentButton({ children, className = "", ...props }) {
  return (
    <button
      className={`btn-accent text-[var(--color-gray)] px-8 py-3 rounded-lg font-bold text-lg shadow-md flex items-center justify-center gap-2 cursor-pointer ${className}`}
      {...props}
    >
      {children}
    </button>
  );
}

// Form submit button (full width)
export function SubmitButton({ children, className = "", ...props }) {
  return (
    <button
      type="submit"
      className={`w-full bg-[var(--color-blue)] hover:bg-[var(--color-blue-secondary)] text-white py-4 px-6 rounded-lg font-semibold text-lg transition-all shadow-lg hover:shadow-xl ${className}`}
      {...props}
    >
      {children}
    </button>
  );
}

// Call to action button (white background, blue text)
export function CallToActionButton({ children, className = "", ...props }) {
  return (
    <button
      className={`bg-white text-[var(--color-blue)] px-6 py-3 rounded-lg font-semibold hover:bg-gray-50 transition-colors ${className}`}
      {...props}
    >
      {children}
    </button>
  );
}

// Outlined call to action button (white border, white text)
export function OutlinedCallToActionButton({ children, className = "", ...props }) {
  return (
    <button
      className={`border-2 border-white text-white px-6 py-3 rounded-lg font-semibold hover:bg-white hover:text-[var(--color-blue)] transition-colors ${className}`}
      {...props}
    >
      {children}
    </button>
  );
}

// Link button (for phone/email links)
export function LinkButton({ href, children, className = "", ...props }) {
  return (
    <a
      href={href}
      className={`bg-white text-[var(--color-blue)] px-6 py-3 rounded-lg font-semibold hover:bg-gray-50 transition-colors inline-block text-center ${className}`}
      {...props}
    >
      {children}
    </a>
  );
}

// Outlined link button (for phone/email links)
export function OutlinedLinkButton({ href, children, className = "", ...props }) {
  return (
    <a
      href={href}
      className={`border-2 border-white text-white px-6 py-3 rounded-lg font-semibold hover:bg-white hover:text-[var(--color-blue)] transition-colors inline-block text-center ${className}`}
      {...props}
    >
      {children}
    </a>
  );
}
