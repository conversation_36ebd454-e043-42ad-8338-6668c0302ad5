// src/app/common/Header.jsx
"use client";

import React, { useState, useEffect, useRef } from "react";
import Link from "next/link";
import { PrimaryButton } from "@/app/common/buttons/BrandButtons";
import { FaChevronDown, FaBars, FaTimes } from "react-icons/fa";

export default function Header() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [servicesDropdownOpen, setServicesDropdownOpen] = useState(false);
  const [resourcesDropdownOpen, setResourcesDropdownOpen] = useState(false);
  const [ openMobileDropdown, setOpenMobileDropdown] = useState(null);
  const [isScrolled, setIsScrolled] = useState(false);
  const [isVisible, setIsVisible] = useState(true);
  const [lastScrollY, setLastScrollY] = useState(0);

  // CHANGED: Added a separate ref for the dropdown panel
  const dropdownTriggerRef = useRef(null);
  const dropdownPanelRef = useRef(null);

  // Professional scroll detection for navbar hide/show and glass effect
  useEffect(() => {
    let ticking = false;
    let scrollThreshold = 5; // Minimum scroll distance to trigger hide/show
    
    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          const currentScrollY = window.scrollY;
          const scrollDifference = Math.abs(currentScrollY - lastScrollY);
          
          // Glass effect
          setIsScrolled(currentScrollY > 50);
          
          // Only trigger hide/show if scroll difference is significant
          if (scrollDifference > scrollThreshold) {
            // Navbar hide/show logic with improved threshold
            if (currentScrollY > lastScrollY && currentScrollY > 80) {
              // Scrolling down - hide navbar
              setIsVisible(false);
              console.log('Hiding navbar - scrolling down', currentScrollY);
            } else if (currentScrollY < lastScrollY || currentScrollY <= 80) {
              // Scrolling up or near top - show navbar
              setIsVisible(true);
              console.log('Showing navbar - scrolling up or at top', currentScrollY);
            }
            
            setLastScrollY(currentScrollY);
          }
          
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [lastScrollY]);

    // Navigation links structure (no changes)
  const navLinks = [
    {
      name: "Services",
      dropdown: [
        { name: "Virtual CISO (vCISO)", description: "Expert cybersecurity leadership and strategic guidance", href: "/services/vciso" },
        { name: "Red Teaming", description: "Simulated cyberattacks to test and strengthen defenses", href: "/services/Red-Teaming" },
        { name: "AWS and Azure Configuration", description: "Secure cloud infrastructure setup and management", href: "/services/aws-azure" },
        { name: "Web3 Audits (Pentest)", description: "Comprehensive security audits for blockchain projects", href: "/services/web3-audits" },
        { name: "VDP", description: "Vulnerability Disclosure Program management", href: "/services/vdp" },
        { name: "VAPT", description: "Vulnerability Assessment and Penetration Testing", href: "/services/vapt" },
        { name: "Bug Bounty (Through Capture The Bug)", description: "Crowdsourced security testing platform", href: "/services/bug-bounty" },
        { name: "Compliance Pre Assessment", description: "Get audit-ready with compliance health checks", href: "/services/compliance" },
        { name: "Office365 Assessment", description: "Security evaluation of Microsoft Office 365 environment", href: "/services/office365" },
        { name: "Cloud Assessment", description: "Comprehensive cloud security evaluation", href: "/services/cloud-assessment" },
        { name: "Google WorkSpace Assessment", description: "Security assessment of Google Workspace setup", href: "/services/google-workspace" },
        { name: "Incident response", description: "Rapid response to security incidents and crisis management", href: "/services/incident-response" },
        { name: "Source code review", description: "In-depth analysis of application source code security", href: "/services/source-code-review" },
      ],
    },
    { name: "Security Training", href: "/CybersecurityTraining" },
    { name: "Product", href: "/product" },
    {
      name: "Resources",
      dropdown: [
        { name: "Blogs", description: "Latest cybersecurity insights and industry updates", href: "/blogs" },
        { name: "News", description: "Latest security news and updates", href: "/news" },
      ],
    },
    { name: "About Us", href: "/about" },
  ];

  // CHANGED: Updated the effect to check both refs
  useEffect(() => {
    function handleClickOutside(event) {
      if (
        dropdownTriggerRef.current &&
        !dropdownTriggerRef.current.contains(event.target) &&
        dropdownPanelRef.current &&
        !dropdownPanelRef.current.contains(event.target)
      ) {
        setServicesDropdownOpen(false);
        setResourcesDropdownOpen(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []); // Dependencies are not needed as refs don't trigger re-renders

  // REMOVED: The complex handleDownload function is no longer needed.

  const NavLink = ({ item }) => (
    <Link href={item.href} className="px-4 py-2 hover:text-[var(--color-yellow)] transition-colors font-medium text-white">
      {item.name}
    </Link>
  );

  return (
    <header className={`fixed top-0 left-0 w-full z-[9999] py-2 mb-16 md:mb-0 transition-all duration-300 ease-in-out ${isVisible ? 'translate-y-0 opacity-100' : '-translate-y-full opacity-0'}`}>
      <div className="w-full max-w-7xl mx-auto px-2 sm:px-4 lg:px-0 relative">
        <div className={`navbar-glass ${isScrolled ? 'scrolled' : ''} rounded-4xl shadow-lg border border-gray-700`}>
          <div className="flex items-center justify-between h-16 px-6">
            {/* Logo */}
            <div className="flex-shrink-0">
              <Link href="/" className="flex items-center gap-2">
                <img src="/seclit-logo-white.png" alt="SecurityLit Logo" className="h-12 w-auto" />
              </Link>
            </div>

            {/* Desktop Navigation */}
            <nav className="hidden lg:flex items-center space-x-8">
              {navLinks.map((link) =>
                link.dropdown ? (
                  // CHANGED: Attached the trigger ref here
                                     <div 
                     key={link.name} 
                     className="relative" 
                     ref={link.name === "Services" ? dropdownTriggerRef : null}
                   >
                     <button
                       onClick={() => {
                         if (link.name === "Services") {
                           setServicesDropdownOpen(!servicesDropdownOpen);
                           setResourcesDropdownOpen(false);
                         } else if (link.name === "Resources") {
                           setResourcesDropdownOpen(!resourcesDropdownOpen);
                           setServicesDropdownOpen(false);
                         }
                       }}
                       onMouseEnter={() => {
                         if (link.name === "Services") {
                           setServicesDropdownOpen(true);
                           setResourcesDropdownOpen(false);
                         } else if (link.name === "Resources") {
                           setResourcesDropdownOpen(true);
                           setServicesDropdownOpen(false);
                         }
                       }}
                       className="flex items-center gap-2 px-4 py-2 hover:text-[var(--color-yellow)] transition-colors font-medium text-white"
                     >
                      {link.name}
                      <FaChevronDown className={`h-3 w-3 transition-transform duration-300 ${(link.name === "Services" && servicesDropdownOpen) || (link.name === "Resources" && resourcesDropdownOpen) ? 'transform rotate-180' : ''}`} />
                    </button>
                  </div>
                ) : (
                  <NavLink key={link.name} item={link} />
                )
              )}
            </nav>

            {/* Contact Button */}
            <div className="hidden lg:block">
              <PrimaryButton onClick={() => window.location.href='/contact'} className="px-3 py-1.5 lg:px-4 lg:py-2 text-sm">
                Contact Us
              </PrimaryButton>
            </div>

            {/* Mobile Menu Button */}
            <div className="lg:hidden">
              <button onClick={() => setMobileMenuOpen(!mobileMenuOpen)} className="text-white">
                {mobileMenuOpen ? <FaTimes className="h-6 w-6" /> : <FaBars className="h-6 w-6" />}
              </button>
            </div>
          </div>
        </div>

                 {/* Services Dropdown */}
         {servicesDropdownOpen && (
           // CHANGED: Attached the panel ref here
           <div 
             ref={dropdownPanelRef} 
             className="absolute top-full left-1/2 transform -translate-x-1/2 mt-0 w-11/12 max-w-6xl z-[10000]"
             onMouseEnter={() => setServicesDropdownOpen(true)}
             onMouseLeave={() => setServicesDropdownOpen(false)}
           >
             {/* Invisible bridge to prevent gap */}
             <div className="h-2 bg-transparent"></div>
             <div className="bg-white rounded-2xl shadow-xl py-6 border border-gray-200 fade-in-up">
               <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 px-8">
                 <div className="lg:col-span-3">
                   <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                     {navLinks.find(link => link.dropdown)?.dropdown.map((item, index) => (
                       <Link
                         key={item.name}
                         href={item.href}
                         onClick={() => setServicesDropdownOpen(false)}
                         className="block w-full text-left p-3 hover:bg-gray-50 transition-colors rounded-lg group"
                         style={{ animationDelay: `${index * 0.05}s` }}
                       >
                         <h4 className="font-semibold text-[var(--color-dark-blue)] mb-1 group-hover:text-[var(--color-blue)] transition-colors text-sm">
                           {item.name}
                         </h4>
                         <p className="text-xs text-gray-600 leading-relaxed">
                           {item.description}
                         </p>
                       </Link>
                     ))}
                   </div>
                 </div>
                 <div className="lg:col-span-1 lg:border-l lg:border-gray-200 lg:pl-6">
                   <div className="bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-lg p-4 text-white">
                     <div className="mb-4">
                       {/* Brochure cover image */}
                       <img 
                         src="/brochure-cover.png" 
                         alt="SecurityLit Brochure Cover" 
                         className="w-full h-32 object-cover rounded-lg shadow-lg"
                       />
                     </div>
                     <h4 className="font-bold text-lg mb-2">Download Brochure</h4>
                     <p className="text-sm text-white/90 mb-4">Get our comprehensive service guide</p>
                     {/* CHANGED: Reverted to a simple and reliable <a> tag */}
                     <a
                       href="/seclit-brochure.pdf"
                       download="seclit-brochure.pdf"
                       onClick={() => setServicesDropdownOpen(false)}
                       className="bg-white text-[var(--color-blue)] px-4 py-2 rounded-lg font-semibold text-sm hover:bg-gray-50 transition-colors block text-center w-full"
                     >
                       Download →
                     </a>
                   </div>
                 </div>
               </div>
             </div>
           </div>
         )}

                 {/* Resources Dropdown */}
         {resourcesDropdownOpen && (
           <div 
             className="absolute top-full left-1/2 transform -translate-x-1/2 mt-0 w-11/12 max-w-4xl z-[10000]"
             onMouseEnter={() => setResourcesDropdownOpen(true)}
             onMouseLeave={() => setResourcesDropdownOpen(false)}
           >
             {/* Invisible bridge to prevent gap */}
             <div className="h-2 bg-transparent"></div>
             <div className="bg-white rounded-2xl shadow-xl py-6 border border-gray-200 fade-in-up">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 px-8">
              <div className="lg:col-span-1">
                <div className="grid grid-cols-1 gap-4">
                  {navLinks.find(link => link.name === "Resources")?.dropdown.map((item, index) => (
                    <Link
                      key={item.name}
                      href={item.href}
                      onClick={() => setResourcesDropdownOpen(false)}
                      className="block w-full text-left p-3 hover:bg-gray-50 transition-colors rounded-lg group"
                      style={{ animationDelay: `${index * 0.1}s` }}
                    >
                      <h4 className="font-semibold text-[var(--color-dark-blue)] mb-1 group-hover:text-[var(--color-blue)] transition-colors text-sm">
                        {item.name}
                      </h4>
                      <p className="text-xs text-gray-600 leading-relaxed">
                        {item.description}
                      </p>
                    </Link>
                  ))}
                </div>
              </div>
              <div className="lg:col-span-1 lg:border-l lg:border-gray-200 lg:pl-6">
                <div className="bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-lg p-4 text-white">
                  <h4 className="font-bold text-lg mb-2">Stay Updated</h4>
                  <p className="text-sm text-white/90 mb-4">Get the latest cybersecurity insights and news</p>
                  <a
                    href="/subscribe"
                    className="bg-white text-[var(--color-blue)] px-4 py-2 rounded-lg font-semibold text-sm hover:bg-gray-50 transition-colors block text-center w-full"
                  >
                    Subscribe →
                  </a>
                </div>
              </div>
            </div>
             </div>
           </div>
         )}
      </div>

      {/* Mobile Menu */}
      {mobileMenuOpen && (
        <div className="lg:hidden absolute top-24 left-4 right-4 bg-gray-800 rounded-2xl shadow-lg z-[10000] border border-gray-700 max-h-[80vh] overflow-y-auto fade-in-up">
          <nav className="flex flex-col items-center space-y-2 px-4 py-6">
                        {navLinks.map((link, idx) =>
              link.dropdown ? (
                <div key={link.name} className="w-full text-center">
                   <button
                    onClick={() => {
                      if (openMobileDropdown === idx) {
                        setOpenMobileDropdown(null);
                      } else {
                        setOpenMobileDropdown(idx);
                      }
                    }}
                    className="w-full flex items-center justify-center gap-2 px-4 py-3 hover:text-[var(--color-dark-blue)] transition-colors font-medium text-white"
                  >
                    {link.name}
                    <FaChevronDown className={`h-3 w-3 transition-transform duration-300 ${openMobileDropdown === idx ? 'transform rotate-180' : ''}`} />
                  </button>
                  {openMobileDropdown === idx && (
                    <div className="w-full bg-white rounded-lg py-4 mt-2 border border-gray-200 fade-in-up">
                      <div className="grid grid-cols-1 gap-3 px-4 mb-4 max-h-[60vh] overflow-y-auto">
                        {link.dropdown.map((item, index) => (
                          <Link
                            key={item.name}
                            href={item.href}
                            onClick={() => {
                              setOpenMobileDropdown(null);
                              setMobileMenuOpen(false);
                            }}
                            className="block w-full text-left p-3 hover:bg-gray-50 transition-colors rounded-lg"
                            style={{ animationDelay: `${index * 0.05}s` }}
                          >
                            <h4 className="font-semibold text-[var(--color-dark-blue)] mb-1 text-sm">{item.name}</h4>
                            <p className="text-xs text-gray-600">{item.description}</p>
                          </Link>
                        ))}
                      </div>
                      <div className="border-t border-gray-200 pt-4 px-4">
                        <div className="bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-lg p-4 text-white">
                                                     <div className="mb-4">
                             <img 
                               src="/brochure-cover.png"
                               alt="SecurityLit Brochure Cover"
                               className="w-full h-32 object-cover rounded-lg shadow-lg"
                             />
                           </div>
                          <h4 className="font-bold text-lg mb-2">Download Brochure</h4>
                          <p className="text-sm text-white/90 mb-4">Get our comprehensive service guide</p>
                          {/* CHANGED: Reverted to a simple and reliable <a> tag */}
                          <a
                            href="/seclit-brochure.pdf"
                            download="seclit-brochure.pdf"
                            onClick={() => {
                              setOpenMobileDropdown(null);
                              setMobileMenuOpen(false);
                            }}
                            className="bg-white text-[var(--color-blue)] px-4 py-2 rounded-lg font-semibold text-sm hover:bg-gray-50 transition-colors block text-center w-full"
                          >
                            Download →
                          </a>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <Link key={link.name} href={link.href} onClick={() => setMobileMenuOpen(false)} className="block w-full text-center px-4 py-3 hover:bg-gray-700 rounded-lg font-medium text-white">
                  {link.name}
                </Link>
              )
            )}
            <div className="pt-4 w-full">
              <PrimaryButton className="w-full px-4 py-2 text-sm" onClick={() => window.location.href='/contact'}>
                Contact Us
              </PrimaryButton>
            </div>
          </nav>
        </div>
      )}
    </header>
  );
}
