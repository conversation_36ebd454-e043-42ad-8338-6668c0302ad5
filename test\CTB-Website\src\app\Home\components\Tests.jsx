"use client";
import { useState } from "react";
import Button from "../../common/buttons/Button";
import Image from "next/image";
import React from "react";

const navItems = [
  "Pentesting",
  "Remediation",
  "Intelligent Reporting",
  "Bug Bounty",
];

const FeatureContent = React.memo(function FeatureContent({ title, image, description, bulletPoints }) {
  return (
    <div className="flex flex-col lg:flex-row items-start justify-between px-4 lg:px-20">
      <div className="w-full lg:w-1/2 lg:pr-10 text-left mb-4 lg:mb-0">
        <h2 className="text-3xl font-bold mb-4 bg-gradient-to-r from-[#062575] via-[#0B45DB] to-[#0B45DB] bg-clip-text text-transparent">
          {title}
        </h2>
        <p className="mb-4 text-lg text-slate-600 leading-8">{description}</p>
        <ul className="list-disc pl-5 mb-4 text-slate-600 text-lg">
          {bulletPoints.map((point, index) => (
            <li key={index}>{point}</li>
          ))}
        </ul>
        {/* <DarkButton className="px-6 rounded-3xl mt-10">Learn more</DarkButton> */}
      </div>
      <div className="w-full lg:w-1/2">
        <Image src={image} alt={title} width={600} height={400} priority
          quality={80} />
      </div>
    </div>
  );
});

export default function Tests() {
  const [activeTab, setActiveTab] = useState("Pentesting");
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => setIsMenuOpen(prev => !prev);

  const tabContent = {
    Pentesting: {
      title: "Expert-Driven Manual Pentests",
      image: "/images/Dashboard.jpg",
      description: "Our expert-driven manual pentests involve skilled cybersecurity professionals simulating real-world attacks on your systems and networks. Unlike automated tools, manual testing provides deeper analysis, uncovering complex vulnerabilities that scans might miss. This approach offers valuable insights to enhance your defences against real-world threats.",
      bulletPoints: [
        "Certified penetration testers delivering compliant testing for SOC2, ISO27001, HIPAA, and more",
        "Adherence to top industry standards: OWASP, SANS, CREST",
      ]
    },
    Remediation: {
      title: "AI Powered Patch Assistance",
      image: "/images/AI_Patch.jpg",
      description: "Our AI-powered patch assistance focuses on helping developers with remediation support. Our platform offers actionable guidance to address vulnerabilities and features real-time collaboration tools for direct interaction between pentesters and developers. This ensures that vulnerabilities are not only identified but rectified swiftly and effectively, enhancing overall cybersecurity resilience.",
      bulletPoints: [
        "Empower developers with clear, actionable steps for remediation.",
        "Accelerate vulnerability resolution with AI-driven insights.",
        "Facilitate seamless interaction between pentesters and development teams for effective remediation."
      ]
    },
    "Intelligent Reporting": {
      title: "Real-Time Security Insights",
      image: "/images/Intelligent_Report.jpg",
      description: "Our platform offers real-time tracking of both penetration tests and bug bounty programs, providing immediate alerts for any vulnerabilities detected. The intuitive dashboard delivers comprehensive analytics and detailed reports, empowering proactive and informed decision-making.",
      bulletPoints: [
        "Keep track of your penetration tests as they happen, ensuring continuous visibility.",
        "Get immediate alerts for any new vulnerabilities, enabling quick remediation.",
        "Leverage in-depth reports for effective security management and decision-making.",
      ]
    },
    "Bug Bounty": {
      title: "Bug Bounty Programs",
      image: "/images/Bug_Bounty.jpg",
      description: "Our bug bounty programs engage a global community of ethical hackers to identify vulnerabilities before they can be exploited. By leveraging the expertise of top security researchers, we ensure your systems are continually tested and secured.",
      bulletPoints: [
        "Access a vast network of skilled security researchers globally.",
        "Benefit from continuous testing across different time zones for comprehensive coverage.",
        "Incentivize researchers to find critical vulnerabilities, ensuring robust protection.",
      ]
    }
  };

  return (
    <div className="bg-white">
      <div className="container py-16 flex flex-col items-center mx-auto text-center lg:text-center">
        <h1 className="md:text-4xl text-2xl font-semibold px-4 lg:px-40 mb-12">
          Why Leading SaaS Companies Trust Our Advanced Penetration Testing
        </h1>

        {/* Mobile Dropdown Navbar */}
        <div className="lg:hidden w-full mb-8 relative px-10">
          <button
            onClick={toggleMenu}
            className="w-full py-2 px-4 text-left bg-white border border-gray-300 rounded-md shadow-sm"
          >
            {activeTab} ▼
          </button>
          {isMenuOpen && (
            <ul className="w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg z-10">
              {navItems.map((item) => (
                <li key={item}>
                  <button
                    className={`w-full py-2 px-4 text-left ${
                      activeTab === item
                        ? "bg-blue-100 text-blue-700"
                        : "text-gray-700 hover:bg-gray-100"
                    }`}
                    onClick={() => {
                      setActiveTab(item);
                      setIsMenuOpen(false);
                    }}
                  >
                    {item}
                  </button>
                </li>
              ))}
            </ul>
          )}
        </div>

        {/* Desktop Navigation */}
        <div className="hidden lg:flex mb-8 justify-center space-x-40 border-b border-gray-200">
          {navItems.map((item) => (
            <button
              key={item}
              className={`py-2 px-1 font-medium text-[23px] transition-colors duration-300 relative ${
                activeTab === item
                  ? "text-blue-700"
                  : "text-gray-500 hover:text-blue-700"
              }`}
              onClick={() => setActiveTab(item)}
            >
              {item}
              {activeTab === item && (
                <span className="absolute bottom-0 left-0 w-full h-0.5 bg-blue-700"></span>
              )}
            </button>
          ))}
        </div>

        <main className="w-full">
          <FeatureContent {...tabContent[activeTab]} />
        </main>
      </div>
    </div>
  );
}
