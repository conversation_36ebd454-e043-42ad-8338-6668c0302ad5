"use client";

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Play, Clock, CheckCircle, Lock, ArrowRight, Calendar, Users, Star } from 'lucide-react';
import { PrimaryButton } from '../../common/buttons/BrandButtons';

const courseWeeks = [
  {
    id: 1,
    title: "Welcome To Security & Training",
    subtitle: "Introduction to Cybersecurity",
    description: "Introduction to cybersecurity concepts, threat modeling, and basic tools",
    lessons: [
      { id: 1, title: "Introduction to Cybersecurity", duration: "45 min", status: "completed", type: "video" },
      { id: 2, title: "Threat Modeling Basics", duration: "60 min", status: "completed", type: "video" },
      { id: 3, title: "Setting Up Your Lab Environment", duration: "30 min", status: "completed", type: "lab" },
      { id: 4, title: "Basic Reconnaissance Techniques", duration: "90 min", status: "in-progress", type: "video" },
      { id: 5, title: "Week 1 Assessment", duration: "45 min", status: "locked", type: "quiz" }
    ],
    color: "from-[var(--color-blue)] to-[var(--color-blue-secondary)]",
    progress: 60,
    totalLessons: 5,
    completedLessons: 3
  },
  {
    id: 2,
    title: "Week 1 : Web Application Security",
    subtitle: "OWASP Top 10",
    description: "Deep dive into web application vulnerabilities and exploitation techniques",
    lessons: [
      { id: 1, title: "OWASP Top 10 Overview", duration: "60 min", status: "locked", type: "video" },
      { id: 2, title: "SQL Injection Attacks", duration: "90 min", status: "locked", type: "video" },
      { id: 3, title: "XSS and CSRF Vulnerabilities", duration: "75 min", status: "locked", type: "video" },
      { id: 4, title: "Web Security Lab", duration: "120 min", status: "locked", type: "lab" },
      { id: 5, title: "Authentication Bypass Techniques", duration: "60 min", status: "locked", type: "video" },
      { id: 6, title: "Week 2 Assessment", duration: "60 min", status: "locked", type: "quiz" }
    ],
    color: "from-[var(--color-dark-blue)] to-[var(--color-dark-blue-hover)]",
    progress: 0,
    totalLessons: 6,
    completedLessons: 0
  },
  {
    id: 3,
    title: "Week 7 : Network Pentesting",
    subtitle: "Network Security Assessment",
    description: "Advanced network penetration testing and infrastructure security",
    lessons: [
      { id: 1, title: "Network Reconnaissance", duration: "90 min", status: "locked", type: "video" },
      { id: 2, title: "Port Scanning Techniques", duration: "120 min", status: "locked", type: "video" },
      { id: 3, title: "Service Enumeration", duration: "90 min", status: "locked", type: "video" },
      { id: 4, title: "Network Exploitation Lab", duration: "180 min", status: "locked", type: "lab" },
      { id: 5, title: "Network Security Report", duration: "60 min", status: "locked", type: "video" },
      { id: 6, title: "Final Assessment", duration: "120 min", status: "locked", type: "quiz" }
    ],
    color: "from-[var(--color-yellow)] to-[var(--color-yellow-hover)]",
    progress: 0,
    totalLessons: 6,
    completedLessons: 0
  },
  {
    id: 4,
    title: "Week 9 : Active Directory Attacks & HTB Machines Practice",
    subtitle: "AD Security & HTB Practice",
    description: "Active Directory attack techniques and HackTheBox machine practice",
    lessons: [
      { id: 1, title: "Active Directory Basics", duration: "90 min", status: "locked", type: "video" },
      { id: 2, title: "AD Enumeration", duration: "120 min", status: "locked", type: "video" },
      { id: 3, title: "Kerberoasting & Golden Ticket", duration: "90 min", status: "locked", type: "video" },
      { id: 4, title: "HTB Machine Practice", duration: "180 min", status: "locked", type: "lab" },
      { id: 5, title: "AD Security Report", duration: "60 min", status: "locked", type: "video" }
    ],
    color: "from-[var(--color-blue)] to-[var(--color-blue-secondary)]",
    progress: 0,
    totalLessons: 5,
    completedLessons: 0
  },
  {
    id: 5,
    title: "Week 10 : AWS Cloud Pentesting",
    subtitle: "Cloud Security Assessment",
    description: "AWS cloud penetration testing and security assessment",
    lessons: [
      { id: 1, title: "AWS Security Fundamentals", duration: "90 min", status: "locked", type: "video" },
      { id: 2, title: "Cloud Reconnaissance", duration: "120 min", status: "locked", type: "video" },
      { id: 3, title: "IAM Security Testing", duration: "90 min", status: "locked", type: "video" },
      { id: 4, title: "Cloud Security Lab", duration: "180 min", status: "locked", type: "lab" },
      { id: 5, title: "Cloud Security Report", duration: "60 min", status: "locked", type: "video" }
    ],
    color: "from-[var(--color-dark-blue)] to-[var(--color-dark-blue-hover)]",
    progress: 0,
    totalLessons: 5,
    completedLessons: 0
  }
];

export default function CoursePreview() {
  const [selectedWeek, setSelectedWeek] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const openModal = (week) => {
    setSelectedWeek(week);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedWeek(null);
  };

  return (
    <section className="py-16 bg-white relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-72 h-72 bg-[var(--color-blue)]/10 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob"></div>
        <div className="absolute bottom-20 right-10 w-72 h-72 bg-[var(--color-yellow)]/10 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-2000"></div>
      </div>

      <div className="relative z-10 container mx-auto px-4 max-w-7xl">
        {/* Header */}
        <motion.div 
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <motion.div 
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.2, duration: 0.6 }}
            viewport={{ once: true }}
            className="inline-flex items-center bg-[var(--color-blue)]/10 px-4 py-2 rounded-full mb-6"
          >
            <Calendar className="w-4 h-4 text-[var(--color-blue)] mr-2" />
            <span className="text-sm font-medium text-[var(--color-blue)]">SecurityLit Security Training</span>
          </motion.div>
          
          <motion.h2 
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, duration: 0.6 }}
            viewport={{ once: true }}
            className="text-4xl lg:text-5xl font-bold text-[var(--color-dark-blue)] mb-6"
          >
            Free Learning -
            <span className="block bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] bg-clip-text text-transparent">
              Premium Experience
            </span>
          </motion.h2>
          
          <motion.p 
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6, duration: 0.6 }}
            viewport={{ once: true }}
            className="text-xl text-[var(--foreground-secondary)] max-w-4xl mx-auto leading-relaxed"
          >
            Created by SecurityLit Team. Explore the detailed curriculum structure with 11 sections and 83 total lessons. 
            Click on any week to view the complete lesson plan.
          </motion.p>
        </motion.div>

        {/* Course Week Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          {courseWeeks.map((week, index) => (
            <motion.div
              key={week.id}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1, duration: 0.6 }}
              viewport={{ once: true }}
              whileHover={{ 
                y: -8,
                scale: 1.02,
                transition: { duration: 0.2 }
              }}
              onClick={() => openModal(week)}
              className="group relative bg-white rounded-3xl p-6 shadow-[0_20px_40px_rgb(0,0,0,0.08)] hover:shadow-[0_30px_60px_rgb(0,0,0,0.12)] transition-all duration-300 border border-gray-100 cursor-pointer"
            >
              {/* Week Header */}
              <div className="flex items-center justify-between mb-4">
                <div className={`w-10 h-10 bg-gradient-to-br ${week.color} rounded-2xl flex items-center justify-center shadow-lg`}>
                  <span className="text-white font-bold text-lg">{week.id}</span>
                </div>
                <div className="text-right">
                  <div className="text-sm text-[var(--foreground-secondary)]">Progress</div>
                  <div className="text-2xl font-bold text-[var(--color-dark-blue)]">{week.progress}%</div>
                </div>
              </div>

              {/* Week Content */}
              <div className="space-y-3">
                <div>
                  <h3 className="text-lg font-bold text-[var(--color-dark-blue)] mb-1 group-hover:text-[var(--color-blue)] transition-colors">
                    {week.title}
                  </h3>
                  <p className="text-[var(--color-blue)] font-medium text-sm">
                    {week.subtitle}
                  </p>
                </div>
                
                <p className="text-[var(--foreground-secondary)] leading-relaxed text-sm">
                  {week.description}
                </p>

                {/* Progress Bar */}
                <div className="space-y-2">
                  <div className="flex justify-between text-sm text-[var(--foreground-secondary)]">
                    <span>{week.completedLessons} of {week.totalLessons} lessons</span>
                    <span>{week.progress}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className={`h-2 bg-gradient-to-r ${week.color} rounded-full transition-all duration-300`}
                      style={{ width: `${week.progress}%` }}
                    />
                  </div>
                </div>

                {/* Lesson Types */}
                <div className="flex gap-2">
                  <div className="flex items-center gap-1 text-xs text-[var(--foreground-secondary)]">
                    <Play className="w-3 h-3" />
                    <span>Video</span>
                  </div>
                  <div className="flex items-center gap-1 text-xs text-[var(--foreground-secondary)]">
                    <CheckCircle className="w-3 h-3" />
                    <span>Lab</span>
                  </div>
                  <div className="flex items-center gap-1 text-xs text-[var(--foreground-secondary)]">
                    <Star className="w-3 h-3" />
                    <span>Quiz</span>
                  </div>
                </div>

                {/* CTA */}
                <div className="flex items-center justify-between pt-3">
                  <span className="text-sm text-[var(--foreground-secondary)]">Click to view details</span>
                  <ArrowRight className="w-5 h-5 text-gray-400 group-hover:text-[var(--color-blue)] group-hover:translate-x-1 transition-all" />
                </div>
              </div>

              {/* Hover Effects */}
              <div className="absolute inset-0 bg-gradient-to-br from-[var(--color-blue)]/5 to-[var(--color-blue-secondary)]/5 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              <div className={`absolute inset-0 bg-gradient-to-br ${week.color} rounded-3xl opacity-0 group-hover:opacity-5 transition-opacity duration-300 blur-xl`} />
            </motion.div>
          ))}
        </div>

        {/* Course Stats */}
        <motion.div 
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8, duration: 0.6 }}
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8"
        >
          <div className="text-center">
            <div className="text-4xl font-bold text-[var(--color-blue)] mb-2">17</div>
            <div className="text-[var(--foreground-secondary)]">Total Lessons</div>
          </div>
          <div className="text-center">
            <div className="text-4xl font-bold text-[var(--color-dark-blue)] mb-2">24</div>
            <div className="text-[var(--foreground-secondary)]">Hours Content</div>
          </div>
          <div className="text-center">
            <div className="text-4xl font-bold text-[var(--color-yellow)] mb-2">3</div>
            <div className="text-[var(--foreground-secondary)]">Hands-on Labs</div>
          </div>
          <div className="text-center">
            <div className="text-4xl font-bold text-[var(--color-blue-secondary)] mb-2">3</div>
            <div className="text-[var(--foreground-secondary)]">Assessments</div>
          </div>
        </motion.div>
      </div>

      {/* Modal */}
      <AnimatePresence>
        {isModalOpen && selectedWeek && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 flex items-center justify-center p-4"
          >
            {/* Backdrop */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={closeModal}
              className="absolute inset-0 bg-black/50 backdrop-blur-sm"
            />

            {/* Modal Content */}
            <motion.div
              initial={{ opacity: 0, scale: 0.9, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.9, y: 20 }}
              transition={{ type: "spring", damping: 25, stiffness: 300 }}
              className="relative bg-white rounded-3xl shadow-2xl max-w-4xl w-full max-h-[80vh] overflow-hidden"
            >
              {/* Modal Header */}
              <div className={`bg-gradient-to-r ${selectedWeek.color} p-6 text-white`}>
                <div className="flex items-center justify-between">
                  <div>
                    <h2 className="text-2xl font-bold mb-1">{selectedWeek.title}</h2>
                    <p className="text-lg opacity-90">{selectedWeek.subtitle}</p>
                  </div>
                  <motion.button
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    onClick={closeModal}
                    className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center hover:bg-white/30 transition-colors"
                  >
                    <X className="w-5 h-5" />
                  </motion.button>
                </div>
              </div>

              {/* Modal Body */}
              <div className="p-6 max-h-[50vh] overflow-y-auto">
                <div className="space-y-4">
                  <p className="text-[var(--foreground-secondary)] text-base leading-relaxed">
                    {selectedWeek.description}
                  </p>

                  {/* Progress Summary */}
                  <div className="bg-gray-50 rounded-2xl p-4">
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="text-base font-semibold text-[var(--color-dark-blue)]">Progress</h3>
                      <span className="text-xl font-bold text-[var(--color-blue)]">{selectedWeek.progress}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className={`h-2 bg-gradient-to-r ${selectedWeek.color} rounded-full transition-all duration-300`}
                        style={{ width: `${selectedWeek.progress}%` }}
                      />
                    </div>
                    <div className="flex justify-between text-sm text-[var(--foreground-secondary)] mt-2">
                      <span>{selectedWeek.completedLessons} of {selectedWeek.totalLessons} lessons completed</span>
                    </div>
                  </div>

                  {/* Lessons List */}
                  <div>
                    <h3 className="text-lg font-semibold text-[var(--color-dark-blue)] mb-3">Lessons</h3>
                    <div className="space-y-2">
                      {selectedWeek.lessons.map((lesson, index) => (
                        <motion.div
                          key={lesson.id}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: index * 0.1 }}
                          className="flex items-center justify-between p-3 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors"
                        >
                          <div className="flex items-center gap-3">
                            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                              lesson.status === 'completed' ? 'bg-green-500' :
                              lesson.status === 'in-progress' ? 'bg-[var(--color-blue)]' : 'bg-gray-400'
                            }`}>
                              {lesson.status === 'completed' ? (
                                <CheckCircle className="w-4 h-4 text-white" />
                              ) : lesson.status === 'in-progress' ? (
                                <Play className="w-4 h-4 text-white" />
                              ) : (
                                <Lock className="w-4 h-4 text-white" />
                              )}
                            </div>
                            <div>
                              <h4 className="font-semibold text-[var(--color-dark-blue)] text-sm">{lesson.title}</h4>
                              <div className="flex items-center gap-3 text-xs text-[var(--foreground-secondary)]">
                                <span className="flex items-center gap-1">
                                  <Clock className="w-3 h-3" />
                                  {lesson.duration}
                                </span>
                                <span className="capitalize">{lesson.type}</span>
                              </div>
                            </div>
                          </div>
                          <div className={`px-2 py-1 rounded-full text-xs font-semibold ${
                            lesson.status === 'completed' ? 'bg-green-100 text-green-700' :
                            lesson.status === 'in-progress' ? 'bg-[var(--color-blue)]/10 text-[var(--color-blue)]' : 'bg-gray-100 text-gray-700'
                          }`}>
                            {lesson.status === 'completed' ? 'Completed' :
                             lesson.status === 'in-progress' ? 'In Progress' : 'Locked'}
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              {/* Modal Footer */}
              <div className="p-6 border-t border-gray-200">
                <div className="flex justify-between items-center">
                  <div className="text-sm text-[var(--foreground-secondary)]">
                    Total duration: {selectedWeek.lessons.reduce((acc, lesson) => {
                      const minutes = parseInt(lesson.duration.split(' ')[0]);
                      return acc + minutes;
                    }, 0)} minutes
                  </div>
                  <PrimaryButton className="px-6 py-2 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300">
                    Continue Learning
                  </PrimaryButton>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </section>
  );
} 