import DarkButton from "@/app/common/buttons/DarkButton";
import LightButton from "@/app/common/buttons/LightButton";
import Image from "next/image";
import React from "react";

export default function TheChallenge() {
  return (
    <div>
      <div className="md:p-12 p-8 flex md:flex-row flex-col justify-between">
        <div className="Content md:w-[50%] gap-6 flex flex-col">
          <div className="Title md:text-5xl text-2xl font-bold">
          Cybersecurity Challenges
          </div>

          <div className="subTitle2 leading-8 pr-10 text-slate-600 text-sm md:text-lg">
          Organizations today face escalating security challenges due to increasingly sophisticated cyber attacks. These threats can lead to severe reputational and financial damage if not addressed promptly. <br /> <br />
To stay secure, businesses need a robust cybersecurity strategy that includes Vulnerability Assessment and Penetration Testing (VAPT). VAPT helps identify security gaps and provides actionable insights to strengthen defenses, ensuring businesses can effectively protect their assets and maintain trust with clients.

          </div>

        </div>
        <div className="Image md:mt-0 mt-10">
            <Image src="/images/challenge_img.png" width={400} height={350} alt=""/>
        </div>
      </div>
    </div>
  );
}
