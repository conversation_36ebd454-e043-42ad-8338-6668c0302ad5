"use client";

import React from 'react';

const journeyPhases = [
  {
    id: "initial",
    title: "Initial Phase (Months 1-2)",
    description: "Comprehensive penetration test of your selected applications, delivery of detailed report with vulnerabilities and recommendations, plus platform access for managing findings and remediation tracking."
  },
  {
    id: "monthly",
    title: "Monthly Testing (Months 3-12)",
    description: "Focused 3-day penetration test each month on selected features, modules, or applications. Real-time vulnerability reporting through the platform with unlimited retesting to validate remediated vulnerabilities."
  }
];

export default function SubscriptionClientJourney() {
  return (
    <section className="w-full py-20 sm:py-24 md:py-28 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16 sm:mb-14">
          <h2 className="text-4xl sm:text-5xl font-bold text-ctb-light-blue">
            Subscription Client Journey
          </h2>
        </div>

        {/* Vertical Timeline */}
        <div className="relative max-w-3xl mx-auto">
          {/* The vertical line */}
          <div className="absolute left-6 top-0 h-full w-0.5 bg-ctb-blue-0" aria-hidden="true"></div>

          <div className="space-y-16">
            {journeyPhases.map((phase) => (
              <div key={phase.id} className="group relative pl-20">
                {/* Numbered Marker on the timeline */}
                <div className="absolute left-0 top-0 flex items-center justify-center w-12 h-12 rounded-full bg-white border-4 border-ctb-green-50 shadow-sm transition-colors duration-300 group-hover:border-ctb-green-50/80">
                  <span className="text-xl font-bold text-ctb-green-50">
                    {phase.id === 'initial' ? '1' : '2'}
                  </span>
                </div>
                
                {/* Card Content */}
                <div className="bg-ctb-blue-350 rounded-xl border border-ctb-green-50/30 p-8 shadow-md hover:shadow-xl hover:border-ctb-green-50 transition-all duration-300">
                  <h3 className="text-2xl font-semibold text-white mb-3 transition-colors duration-300 group-hover:text-ctb-green-50">
                    {phase.title}
                  </h3>
                  <p className="text-ctb-blue-0 leading-relaxed">
                    {phase.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
