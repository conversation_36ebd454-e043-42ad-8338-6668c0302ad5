"use client";

import React from 'react';

export default function LearningModules({ content }) {
  // Use content if provided, otherwise use default modules
  const moduleContent = content || {
    title: "Here's A Short Teaser Of What You Will Learn",
    modules: [
      {
        title: "Web Application Security",
        description: "Fortifying the digital frontline against cyber threats."
      },
      {
        title: "API & Network Security", 
        description: "Safeguarding the backbone of modern interconnected systems."
      },
      {
        title: "Practical Skills Development",
        description: "Honing real-world cybersecurity expertise through hands-on learning."
      },
      {
        title: "Soft Skill & Professional Growth",
        description: "Cultivating the human element in technical cybersecurity roles."
      },
      {
        title: "Real World Environment Navigation",
        description: "Mastering the art of securing complex, live digital ecosystems."
      },
      {
        title: "Active Directory & Cloud Security",
        description: "Protecting the nerve centers of enterprise and cloud infrastructures."
      },
      {
        title: "Continuous Learning & Adaption",
        description: "Staying ahead in the ever-evolving cybersecurity landscape."
      },
      {
        title: "Report Writing Skills",
        description: "Crafting clear, concise, and impactful cybersecurity documentation for stakeholders at all levels."
      }
    ]
  };

  return (
    <section className="py-16 lg:py-24 bg-gradient-to-br from-gray-50 to-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-[var(--color-dark-blue)] mb-6 font-poppins">
            {moduleContent.title}
          </h2>
        </div>

        {/* Learning Areas Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 lg:gap-8">
          {moduleContent.modules.map((module, index) => (
            <div key={index} className="group">
              <div className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 group-hover:border-[var(--color-blue)]/20 h-full flex flex-col">
                
                {/* Icon */}
                <div className="w-12 h-12 bg-gradient-to-br from-[var(--color-blue)]/10 to-[var(--color-blue-secondary)]/10 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform">
                  <img 
                    src="/images/web.png" 
                    alt="Web Security Icon" 
                    className="w-6 h-6"
                  />
                </div>

                {/* Content */}
                <h5 className="text-lg font-bold text-[var(--color-dark-blue)] mb-3 font-poppins">
                  {module.title}
                </h5>
                
                <p className="text-[var(--foreground-secondary)] text-sm leading-relaxed flex-grow font-roboto">
                  {module.description}
                </p>
              </div>
            </div>
          ))}
        </div>

      </div>
    </section>
  );
}
