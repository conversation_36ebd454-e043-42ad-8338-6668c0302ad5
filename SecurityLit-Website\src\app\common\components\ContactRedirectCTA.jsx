"use client";

import React from "react";
import Link from "next/link";
import { ArrowRight } from "lucide-react";
import { PrimaryButton } from "../buttons/BrandButtons";

export default function ContactRedirectCTA({ 
  title = "Ready to Secure Your Organization?",
  description = "Let's discuss how our expert team can help you achieve your security goals and protect your valuable assets.",
  buttonText = "Contact Our Experts",
  buttonLink = "/contact",
  showArrow = true,
  className = ""
}) {
  return (
    <section className={`bg-white py-20 lg:py-28 ${className}`}>
      <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
        <h2 className="text-3xl md:text-4xl font-bold text-[var(--color-dark-blue)]">
          {title}
        </h2>
        <p className="mt-4 text-lg text-gray-600 max-w-2xl mx-auto">
          {description}
        </p>
        <div className="mt-8">
          <Link href={buttonLink}>
            <PrimaryButton className="inline-flex items-center gap-2 transform hover:-translate-y-1">
              {buttonText}
              {showArrow && <ArrowRight className="w-5 h-5" />}
            </PrimaryButton>
          </Link>
        </div>
      </div>
    </section>
  );
} 