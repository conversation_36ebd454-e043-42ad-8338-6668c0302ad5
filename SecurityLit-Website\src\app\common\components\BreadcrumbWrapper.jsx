"use client";
import React from 'react';
import BreadcrumbNavigation, { generateBreadcrumbs } from './BreadcrumbNavigation';

/**
 * Client-side wrapper component for breadcrumb navigation
 * Use this when you need to use the generateBreadcrumbs helper function
 */
const BreadcrumbWrapper = ({ pageType, params = {}, className = "" }) => {
  const breadcrumbItems = generateBreadcrumbs(pageType, params);

  return (
    <BreadcrumbNavigation 
      items={breadcrumbItems} 
      className={className}
    />
  );
};

export default BreadcrumbWrapper; 