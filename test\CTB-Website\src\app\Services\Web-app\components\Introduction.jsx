import React from "react";

export default function Introduction() {
  return (
    <div>
      <div className="Container flex flex-col gap-4 md:p-12 p-8 bg-gray-50 md:py-20">
        <div className="Title md:text-3xl text-2xl bg-gradient-to-r from-[#062575] via-[#0B45DB] to-[#0B45DB] bg-clip-text text-transparent font-bold">
        Agile Security for Agile SaaS
        </div>
        <div className="text-slate-700 md:text-lg">
        Whether your web apps are cloud-based or on-premises, they&apos;re often your most vulnerable points of entry. Relying on outdated, consulting-heavy pen tests just isn&apos;t enough anymore. With Capture The Bug&apos;s VAPT services, you can boost your security posture through highly customizable, high-impact tests that scale with your needs. Get prioritized results, monitor pentester progress 24/7, and keep those attack vectors under control with our comprehensive Pen Test Dashboard.
        </div>
      </div>
    </div>
  );
}
