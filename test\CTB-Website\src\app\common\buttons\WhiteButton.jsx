import PropTypes from 'prop-types';
import clsx from 'clsx';

const WhiteButton = ({ children, onClick, icon, className }) => {
  return (
    <button 
      className={clsx(
        "bg-white text-black py-1 px-4 rounded-lg text-md flex items-center justify-center transition-all duration-300",
        className
      )}
      onClick={onClick}
      aria-label="white button"
    >
      {children}
      {icon && <span className="ml-2">{icon}</span>}
    </button>
  );
};

WhiteButton.propTypes = {
  children: PropTypes.node.isRequired,
  onClick: PropTypes.func,
  icon: PropTypes.node,
  className: PropTypes.string,
};

export default WhiteButton;
