import React from "react";
import { ArrowRight } from "lucide-react";
 

const Button = ({ href, variant = "primary", size = "md", rightIcon, className = "", children, ...props }) => {
  const baseClasses = "inline-flex items-center justify-center gap-2 font-medium transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2";
  
  const variants = {
    primary: "bg-gradient-to-r from-[#58CC02] to-[#4BAF02] hover:from-[#4BAF02] hover:to-[#3A8C02] text-black",
    outline: "border-2 border-[#027bfb] text-[#027bfb] hover:bg-[#027bfb] hover:text-white"
  };
  
  const sizes = {
    md: "px-6 py-3 text-base",
    lg: "px-8 py-4 text-lg"
  };
  
  return (
    <a
      href={href}
      className={`${baseClasses} ${variants[variant]} ${sizes[size]} ${className}`}
      {...props}
    >
      {children}
      {rightIcon}
    </a>
  );
};
 
export default function Landing() {
  return (
    <>
     <div className="bg-gradient-to-b from-[#0A0A0A] via-[#010D2C] to-[#000000] min-h-screen">
      <div className="relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0" style={{
            backgroundImage: `
              linear-gradient(90deg, #58CC02 1px, transparent 1px),
              linear-gradient(0deg, #58CC02 1px, transparent 1px)
            `,
            backgroundSize: '60px 60px'
          }}></div>
        </div>

        <div className="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none">
          <div className="absolute h-px bg-gradient-to-r from-transparent via-[#027bfb] to-transparent opacity-30 animate-pulse" style={{ top: '20%', left: '-100%', width: '200%' }} />
          <div className="absolute h-px bg-gradient-to-r from-transparent via-[#027bfb] to-transparent opacity-20 animate-pulse" style={{ top: '35%', left: '-100%', width: '200%', animationDelay: '0.5s' }} />
          <div className="absolute h-px bg-gradient-to-r from-transparent via-[#027bfb] to-transparent opacity-25 animate-pulse" style={{ top: '50%', left: '-100%', width: '200%', animationDelay: '1s' }} />
          <div className="absolute h-px bg-gradient-to-r from-transparent via-[#027bfb] to-transparent opacity-20 animate-pulse" style={{ top: '65%', left: '-100%', width: '200%', animationDelay: '1.5s' }} />
          <div className="absolute h-px bg-gradient-to-r from-transparent via-[#027bfb] to-transparent opacity-30 animate-pulse" style={{ top: '80%', left: '-100%', width: '200%', animationDelay: '2s' }} />
        </div>

        <div className="container mx-auto px-4 sm:px-6 md:px-12 py-10 sm:py-16 lg:py-32 relative">

          <div className="max-w-5xl mx-auto text-center">
            <div className="inline-flex items-center gap-3 bg-gradient-to-r from-[#062575] to-[#0835A7] text-[#58CC02] px-4 sm:px-6 py-2 sm:py-3 rounded-full text-xs sm:text-sm font-medium mb-6 sm:mb-8 border border-[#58CC02] border-opacity-30">
              <span>Ecommerce & Retail Security</span>
            </div>

            <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-black text-white leading-relaxed mb-6 sm:mb-8">
              <span className="block font-bold">Ecommerce Cybersecurity for  </span>
                <span className="block text-[#58CC02] bg-clip-text mt-2 sm:mt-4   font-bold">ONLINE STORE AND CUSTOMER DATA </span>
              <span className="block text-xl sm:text-3xl md:text-4xl lg:text-5xl mt-2 sm:mt-4 text-[#027bfb] font-medium  ">
                 Retail Platforms
              </span>
            </h1>
{/* Description */}
            <p className="text-gray-300 text-lg md:text-xl leading-relaxed mb-16 max-w-3xl mx-auto font-light">
 <span className="text-[#027bfb] "> Capture The Bug</span> secures your ecommerce stack-including storefronts, APIs, checkout flows, and third-party plug-ins-through continuous penetration testing, fraud detection, and compliance-ready audits tailored for high-growth digital commerce.
</p>
{/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center sm:items-stretch w-full">
              <Button
                href="/Request-Demo"
                variant="primary"
                size="lg"
                rightIcon={<ArrowRight className="h-5 w-5" />}
                className="bg-gradient-to-r from-[#58CC02] to-[#4BAF02] hover:from-[#4BAF02] hover:to-[#3A8C02] text-black px-8 py-4 text-base sm:text-lg font-bold rounded-lg transition-all duration-300 shadow-lg hover:shadow-[#58CC02]/25 w-full sm:w-auto"
              >
Launch E-commerce Security Audit
              </Button>

              <Button
                href="/Download-Report"
                variant="outline"
                size="lg"
                rightIcon={<ArrowRight className="h-5 w-5" />}
                className="border-2 border-[#027bfb] text-[#027bfb] hover:bg-[#027bfb] hover:text-white px-8 py-4 text-base sm:text-lg font-semibold rounded-lg transition-all duration-300 w-full sm:w-auto"
              >
                Download Offensive Security Report 2025
              </Button>
            </div>

          </div>
        </div>
      </div>
    </div>  
</>
  );
}