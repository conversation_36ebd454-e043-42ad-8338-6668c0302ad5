import React from "react";
import { Shield, Users, Target } from "lucide-react";
 
export default function Security() {
  return (
    <> 
  <div className="bg-white py-16 sm:py-24">
  <div className="container mx-auto px-4 md:px-24">
    <div className="mb-16">
      <div className="text-secondary-blue text-xl font-bold mb-5">
        How Capture The Bug Helps
      </div>
      <h2 className="text-4xl sm:text-5xl font-bold text-[#010D2C] leading-tight mb-5">
Securing the Future of Digital Legal Operations</h2>
      <p className="text-[#6B7280] text-lg font-medium leading-relaxed  ">
        With legal teams increasingly reliant on <strong>SaaS platforms</strong> for contracts, e-discovery, and case management, securing <strong>client data</strong> and <strong>privileged workflows</strong> is non-negotiable. Capture The Bug helps LegalTech vendors identify risks in <strong>live environments</strong>-protecting <strong>confidentiality</strong>, <strong>compliance</strong>, and <strong>trust</strong>.
</p>
    </div>

   <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 lg:gap-8">
              <div className="bg-white border-l-4 border-[#58CC02] pl-4 sm:pl-6 pr-4 sm:pr-6 py-6 sm:py-8 rounded-r-lg shadow-sm hover:shadow-md transition-shadow duration-300">
                <div className="">
                  <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-[#58CC02] to-[#4BAF02] rounded-lg flex items-center justify-center mb-4 sm:mb-6">
                    <Shield className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                  </div>
                  <h3 className="text-xl sm:text-2xl font-bold text-[#010D2C] mb-3 sm:mb-4">
End-to-End Protection for Legal Workflows
                  </h3>
                   <p className="text-[#6B7280] text-base sm:text-lg leading-relaxed">
            We test every layer of your <strong>legal tech platform</strong>-from contract repositories and user roles to access logs and digital signatures. Our assessments uncover <strong>cloud misconfigurations</strong>, insecure file sharing, and <strong>data exposure risks</strong> across e-signature flows and permission-based access control. We help ensure <strong>confidentiality</strong>, <strong>integrity</strong>, and <strong>visibility</strong> across the tools legal teams rely on daily.
          </p>
                </div>
              </div>
  
              <div className="bg-white border-l-4 border-[#58CC02] pl-4 sm:pl-6 pr-4 sm:pr-6 py-6 sm:py-8 rounded-r-lg shadow-sm hover:shadow-md transition-shadow duration-300">
                <div className="">
                  <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-[#58CC02] to-[#4BAF02] rounded-lg flex items-center justify-center mb-4 sm:mb-6">
                    <Users className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                  </div>
                <h3 className="text-xl sm:text-2xl font-bold text-[#010D2C] mb-3 sm:mb-4">
Compliance-Focused Testing for Legal Standards                  </h3>
<p className="text-[#6B7280] text-base sm:text-lg leading-relaxed">
            We align your platform with legal-specific compliance frameworks like <strong>GDPR</strong>, <strong>HIPAA</strong> (for legal-health overlap), <strong>SOC 2</strong>, and <strong>ISO 27001</strong>. Our audits help ensure your app is <strong>audit-ready</strong>, <strong>privacy-aligned</strong>, and trusted by firms and regulators alike. Whether you&apos;re serving law firms, legal ops teams, or cross-border clients-Capture The Bug keeps your data <strong>secure</strong> and <strong>defensible</strong>.
          </p>
                </div>
              </div>
  
              <div className="bg-white border-l-4 border-[#58CC02] pl-4 sm:pl-6 pr-4 sm:pr-6 py-6 sm:py-8 rounded-r-lg shadow-sm hover:shadow-md transition-shadow duration-300 md:col-span-2 xl:col-span-1">
                <div className="">
                  <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-[#58CC02] to-[#4BAF02] rounded-lg flex items-center justify-center mb-4 sm:mb-6">
                    <Target className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                  </div>
                  <h3 className="text-xl sm:text-2xl font-bold text-[#010D2C] mb-3 sm:mb-4">
Simulated Attacks, Real-World Threat Discovery                  </h3>
                  <p className="text-[#6B7280] text-base sm:text-lg leading-relaxed">
            Our <strong>red team</strong> simulates how attackers breach legal systems-through <strong>session hijacking</strong>, hardcoded credentials, token misuse, or broken role-based access. We also identify <strong>SSO/API weaknesses</strong> and unauthorized access points that could expose privileged legal content. These simulations mirror <strong>real-world threats</strong> to help legal SaaS teams fix what matters most-before a <strong>breach occurs</strong>.
          </p>          
                </div>
              </div>
            </div>
  </div>
</div> 
</>
  );
}
