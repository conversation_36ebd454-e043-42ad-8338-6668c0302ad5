import React, { Children, cloneElement } from 'react';

const ContentSection = ({ children, toc }) => {
  // Add id to all h1, h2, and h3s for anchor navigation
  const enhancedChildren = Children.map(children, (child) => {
    if (child && (child.type === 'h1' || child.type === 'h2' || child.type === 'h3') && child.props && child.props.children) {
      // Generate id from text
      const text = typeof child.props.children === 'string' ? child.props.children : (Array.isArray(child.props.children) ? child.props.children.join(' ') : '');
      const id = text.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '');
      return cloneElement(child, { 
        id,
        className: `${child.props.className || ''} scroll-mt-32` // Add scroll margin for navbar offset
      });
    }
    return child;
  });
  
  return (
    <div className="bg-white sm:mx-8 md:p-14 p-6 flex flex-col gap-4 rounded-2xl shadow-md my-8 border border-[var(--color-yellow)]" style={{ scrollBehavior: 'smooth' }}>
      {enhancedChildren}
    </div>
  );
};

export default ContentSection;