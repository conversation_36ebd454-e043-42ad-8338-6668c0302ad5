import { cn } from "@/lib/utils";

export const BentoGrid = ({
  className,
  children
}) => {
  return (
    <div
      className={cn(
        "mx-auto grid max-w-7xl grid-cols-1 gap-4 sm:gap-6 md:auto-rows-[18rem] md:grid-cols-3 px-0 sm:px-2 lg:px-0",
        className
      )}>
      {children}
    </div>
  );
};

export const BentoGridItem = ({
  className,
  title,
  description,
  header,
  icon
}) => {
  return (
    <div
    className={cn(
      "row-span-1 rounded-2xl group/bento transition duration-200 p-6 justify-between flex flex-col space-y-4",
      "bg-[var(--color-brand-blue)] border border-white/10 shadow-lg", // Changed to blue
      className
    )}
  >
      {header}
      <div className="transition duration-200 group-hover/bento:translate-x-2">
        {icon}
        <div
          className="mt-2 mb-2 font-sans font-bold text-neutral-600 dark:text-neutral-200">
          {title}
        </div>
        <div
          className="font-sans text-xs font-normal text-neutral-600 dark:text-neutral-300">
          {description}
        </div>
      </div>
    </div>
  );
};
