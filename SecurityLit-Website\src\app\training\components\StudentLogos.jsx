"use client";

import React from 'react';
import { CheckCircle } from 'lucide-react';

const premiumBenefits = [
  "Access to advance Lab Subscription (TryHackMe, HackTheBox)",
  "Personalized mentorship and guided learning",
  "3 months of hands-on experience with SecurityLit",
  "Professional report writing training",
  "Experience letter upon completion"
];

export default function StudentLogos() {
  return (
    <section className="py-24 bg-white">
      <div className="container mx-auto px-4 max-w-7xl">
        {/* Header Section */}
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-[var(--color-dark-blue)] mb-8">
            Our Trained Students works at
          </h2>
          
          {/* Company Logos */}
          <div className="flex justify-center items-center gap-8 mb-16">
            <div className="flex items-center justify-center">
              <img 
                src="https://securitylit.com/images/aia_nz_logo.jpg" 
                alt="AIA NZ" 
                className="h-16 w-auto object-contain"
              />
            </div>
            <div className="flex items-center justify-center">
              <img 
                src="https://securitylit.com/images/data_torque_ltd_logo.jpg" 
                alt="Data Torque" 
                className="h-16 w-auto object-contain"
              />
            </div>
          </div>
        </div>

        {/* Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start">
          {/* Left Section - Premium Tier Benefits */}
          <div className="bg-gradient-to-br from-[var(--color-dark-blue)] to-[var(--color-dark-blue-hover)] rounded-3xl p-8 lg:p-12 text-white">
            <h3 className="text-2xl lg:text-3xl font-bold text-white mb-6">
              Premium Tier Benefits
            </h3>
            
            <p className="text-lg text-white/90 leading-relaxed mb-8">
              As a Premium member, you'll work directly with SecurityLit on live penetration testing projects. This hands-on experience will immerse you in real-time cybersecurity challenges, teaching you how professional pentests are conducted. You'll learn the intricacies of report writing, client communication, and industry best practices. Upon completion, you'll receive an experience letter, validating your practical skills and boosting your career prospects in the cybersecurity field.
            </p>
          </div>

          {/* Right Section - Benefits List */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {premiumBenefits.map((benefit, index) => (
              <div 
                key={index} 
                className="bg-gradient-to-br from-[var(--color-blue)]/10 to-[var(--color-blue-secondary)]/10 rounded-2xl p-6 border border-[var(--color-blue)]/20 hover:border-[var(--color-blue)]/30 transition-all duration-300"
              >
                <div className="flex items-start gap-4">
                  <div className="w-8 h-8 bg-[var(--color-blue)] rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                    <img 
                      src="https://securitylit.com/images/icon.png" 
                      alt="Benefit Icon" 
                      className="w-5 h-5 object-contain"
                    />
                  </div>
                  <p className="text-[var(--color-dark-blue)] font-medium leading-relaxed">
                    {benefit}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
} 