import DownloadReport from './downloadReport';
 
export const metadata = {
  title: "Capture The Bug | 2025 Offensive Security Report",
  description: "Download the 2025 State of Offensive Security Report. Get insights on modern pentesting, threat trends, and PTaaS adoption from industry leaders.",
keywords: "offensive security report, 2025 cybersecurity trends, penetration testing insights, PTaaS adoption, threat landscape 2025, CISO security strategy, Capture The Bug report",  robots: "index, follow",
  icons: {
    icon: '/favicon.ico',
  },
  openGraph: {
    title: "Capture The Bug | 2025 Offensive Security Report",
    type: "website",
    url: "https://capturethebug.xyz/Download-Report",
    description: "Explore the 2025 Offensive Security Report by Capture The Bug. Industry trends, PTaaS insights, and expert takeaways for security leaders.",
    images: "https://postimg.cc/MfbtB2cY",
  },
  twitter: {
    card: 'summary_large_image',
    title: "Capture The Bug | 2025 Offensive Security Report",
    description: "Get the 2025 State of Offensive Security Report. Expert analysis on pentesting trends, PTaaS adoption, and cybersecurity strategy.",
    images: "https://postimg.cc/MfbtB2cY",
  }
};

export default function Page() {
  return <DownloadReport />;
}