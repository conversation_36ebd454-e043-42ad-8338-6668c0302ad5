import Image from 'next/image';

export default function Steps() {
  return (
    <div className="bg-gray-50 py-16">
      <div className="container mx-auto px-4 md:px-28">
        <h2 className="text-3xl md:text-4xl text-center font-bold mb-4 md:px-[150px] text-transparent bg-gradient-to-r from-[#062575] via-[#0B45DB] to-[#0B45DB] bg-clip-text">
          Tailor-Made for the Agile Needs of Modern Tech Enterprises
        </h2>
        <div className="flex flex-col md:flex-row items-center md:gap-4 gap-6 pt-6">
          <div className="md:w-1/2 w-full md:pr-6 text-lg">
            <ul className="list-disc pl-5 mb-4 text-slate-600 leading-8">
              <li>
                Foster seamless integration between security and dev teams with
                our platform&apos;s collaborative tools.
              </li>
              <li>
                Initiate and execute penetration tests swiftly, leveraging
                real-time insights and remediation support.
              </li>
              <li>
                Utilize our intuitive dashboard for a full-spectrum analysis of
                vulnerabilities at a glance.
              </li>
              <li>
                Empower your operations by engaging with top-tier security
                researchers through our bug bounty initiatives.
              </li>
              <li>
                Enjoy a scalable penetration testing service that&apos;s
                cost-effective, avoiding fees for retests or additional
                remediation guidance.
              </li>
            </ul>
          </div>
          <div className="md:w-1/2">
            <Image
              src="/images/Steps.png"
              alt="Illustration of steps"
              width={800} // Adjust width and height as needed
              height={600} // Adjust width and height as needed
              className="w-full"
            />
          </div>
        </div>
      </div>
    </div>
  );
}
