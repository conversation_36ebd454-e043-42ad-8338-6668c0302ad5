'use client';
import React from 'react';
import { motion } from 'framer-motion';
import { Star, Quote, User, Building } from 'lucide-react';
import Image from 'next/image';
import { designSystem, getSectionWrapper, getCardStyles, getIconStyles, getGridStyles } from '../styles/designSystem';

const CountryTestimonials = ({ country }) => {
  const countryData = {
    nz: {
      title: "What Our New Zealand Clients Say",
      description: "Hear from leading organizations across New Zealand who trust our penetration testing services to protect their critical systems and data.",
      testimonials: [
        {
          quote: "Capture The Bug's penetration testing team provided exceptional service for our banking platform. Their deep understanding of NZ financial regulations and cybersecurity best practices helped us identify and remediate critical vulnerabilities before our product launch.",
          author: "<PERSON>",
          position: "CISO",
          company: "NZ Financial Services Group",
          industry: "Banking",
          rating: 5,
          image: "/images/testimonials/nz-testimonial-1.jpg",
          color: "from-blue-500/10 to-blue-600/5"
        },
        {
          quote: "We engaged Capture The Bug for our annual security assessment and were impressed with their thorough approach and practical recommendations. Their team's knowledge of NZ Privacy Act requirements was invaluable for our compliance program.",
          author: "<PERSON>",
          position: "IT Director",
          company: "Auckland Healthcare Systems",
          industry: "Healthcare",
          rating: 5,
          image: "/images/testimonials/nz-testimonial-2.jpg",
          color: "from-emerald-500/10 to-emerald-600/5"
        },
        {
          quote: "The team at Capture The Bug delivered a comprehensive penetration test of our government portal that exceeded our expectations. Their detailed reporting and remediation guidance significantly improved our security posture.",
          author: "Michael Thompson",
          position: "Security Manager",
          company: "NZ Government Agency",
          industry: "Government",
          rating: 5,
          image: "/images/testimonials/nz-testimonial-3.jpg",
          color: "from-purple-500/10 to-purple-600/5"
        }
      ]
    },
    au: {
      title: "What Our Australian Clients Say",
      description: "Trusted by leading Australian enterprises and government agencies to deliver world-class security testing services.",
      testimonials: [
        {
          quote: "Capture The Bug's penetration testing services helped us achieve compliance with the Essential Eight framework. Their team's expertise in Australian security requirements was evident throughout the engagement.",
          author: "Emma Roberts",
          position: "Head of Cybersecurity",
          company: "Australian Energy Corporation",
          industry: "Energy",
          rating: 5,
          image: "/images/testimonials/au-testimonial-1.jpg",
          color: "from-orange-500/10 to-orange-600/5"
        },
        {
          quote: "We've partnered with Capture The Bug for three years running for our security testing needs. Their in-depth knowledge of APRA CPS 234 requirements has been crucial for our financial services compliance program.",
          author: "James Wilson",
          position: "CTO",
          company: "Sydney Banking Solutions",
          industry: "FinTech",
          rating: 5,
          image: "/images/testimonials/au-testimonial-2.jpg",
          color: "from-blue-500/10 to-blue-600/5"
        },
        {
          quote: "The penetration testing conducted by Capture The Bug uncovered several critical vulnerabilities in our healthcare system that other vendors had missed. Their attention to detail and expertise is unmatched in the Australian market.",
          author: "Sophia Lee",
          position: "Information Security Officer",
          company: "Melbourne Health Network",
          industry: "Healthcare",
          rating: 5,
          image: "/images/testimonials/au-testimonial-3.jpg",
          color: "from-emerald-500/10 to-emerald-600/5"
        }
      ]
    },
    us: {
      title: "What Our US Clients Say",
      description: "Trusted by American enterprises across industries to deliver enterprise-grade penetration testing services.",
      testimonials: [
        {
          quote: "Capture The Bug provided exceptional penetration testing services for our financial platform. Their team's understanding of PCI DSS and SOC 2 requirements helped us achieve compliance with minimal disruption to our operations.",
          author: "Robert Anderson",
          position: "VP of Security",
          company: "US Financial Technologies",
          industry: "FinTech",
          rating: 5,
          image: "/images/testimonials/us-testimonial-1.jpg",
          color: "from-purple-500/10 to-purple-600/5"
        },
        {
          quote: "We engaged Capture The Bug for a comprehensive security assessment of our healthcare systems. Their expertise in HIPAA compliance and medical device security was invaluable for our organization.",
          author: "Jennifer Martinez",
          position: "CISO",
          company: "American Healthcare Systems",
          industry: "Healthcare",
          rating: 5,
          image: "/images/testimonials/us-testimonial-2.jpg",
          color: "from-emerald-500/10 to-emerald-600/5"
        },
        {
          quote: "The team at Capture The Bug delivered a thorough security assessment of our cloud infrastructure. Their detailed findings and remediation recommendations significantly enhanced our AWS security posture.",
          author: "Daniel Thompson",
          position: "Cloud Security Architect",
          company: "US Technology Corporation",
          industry: "Technology",
          rating: 5,
          image: "/images/testimonials/us-testimonial-3.jpg",
          color: "from-blue-500/10 to-blue-600/5"
        }
      ]
    }
  };

  const data = countryData[country.toLowerCase()];

  return (
    <section className={getSectionWrapper('light')}>
      <div className={designSystem.section.maxWidth}>
        <motion.div
          {...designSystem.animation.fadeInUp}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <h2 className={designSystem.typography.sectionTitle}>{data.title}</h2>
          <p className={designSystem.typography.sectionSubtitle}>
            {data.description}
          </p>
        </motion.div>

        <motion.div
          variants={designSystem.animation.staggerContainer}
          initial="hidden"
          whileInView="show"
          viewport={{ once: true }}
          className={getGridStyles('three')}
        >
          {data.testimonials.map((testimonial, index) => (
            <motion.div
              key={index}
              variants={designSystem.animation.staggerItem}
              className={`${getCardStyles()}`}
            >
              {/* Quote icon and industry badge */}
              <div className="flex items-center justify-between mb-6">
                <div className="w-12 h-12 bg-primary-blue/10 rounded-2xl flex items-center justify-center">
                  <Quote className="w-6 h-6 text-primary-blue" />
                </div>
                <span className="text-sm font-semibold text-primary-blue bg-primary-blue/10 px-3 py-1 rounded-full">
                  {testimonial.industry}
                </span>
              </div>

              {/* Quote text */}
              <blockquote className="text-gray-700 mb-8 leading-relaxed text-lg italic">
                &ldquo;{testimonial.quote}&rdquo;
              </blockquote>

                {/* Star rating */}
                <div className="flex items-center mb-6">
                  <div className="flex gap-1">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <motion.div
                        key={i}
                        initial={{ opacity: 0, scale: 0 }}
                        whileInView={{ opacity: 1, scale: 1 }}
                        viewport={{ once: true }}
                        transition={{ duration: 0.3, delay: 0.1 * i }}
                      >
                        <Star className="w-5 h-5 text-ctb-green-50 fill-ctb-green-50" />
                      </motion.div>
                    ))}
                  </div>
                </div>

                {/* Author information */}
                <div className="flex items-center pt-6 border-t border-gray-200/50">
                  <div className="w-14 h-14 rounded-2xl overflow-hidden mr-4 relative bg-gray-100 flex items-center justify-center">
                    <User className="w-6 h-6 text-gray-400" />
                    {/* Placeholder for actual image */}
                    {/* <Image
                      src={testimonial.image}
                      alt={testimonial.author}
                      width={56}
                      height={56}
                      className="object-cover"
                    /> */}
                  </div>
                  <div className="flex-1">
                    <h4 className="font-bold text-gray-900 mb-1">
                      {testimonial.author}
                    </h4>
                    <p className="text-sm text-gray-600 mb-1">{testimonial.position}</p>
                    <div className="flex items-center gap-2">
                      <Building className="w-3 h-3 text-primary-blue" />
                      <p className="text-sm font-medium text-primary-blue">{testimonial.company}</p>
                    </div>
                  </div>
                </div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
};

export default CountryTestimonials; 