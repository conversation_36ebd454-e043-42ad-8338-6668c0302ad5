{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/common/components/BreadcrumbNavigation.jsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState } from 'react';\r\nimport Link from 'next/link';\r\nimport { ChevronRight, Home, FileText, Users, Settings, MapPin, Shield, Code, Smartphone, Globe, DollarSign, Building, Phone, Download } from 'lucide-react';\r\nimport { motion } from 'framer-motion';\r\n\r\n/**\r\n * Truncated Text Component for Mobile Breadcrumbs\r\n * Shows truncated text on mobile with click to expand functionality\r\n */\r\nconst TruncatedText = ({ text, maxLength = 25, onExpansionChange }) => {\r\n  const [isExpanded, setIsExpanded] = useState(false);\r\n\r\n  const handleToggle = () => {\r\n    const newExpanded = !isExpanded;\r\n    setIsExpanded(newExpanded);\r\n    if (onExpansionChange) {\r\n      onExpansionChange(newExpanded);\r\n    }\r\n  };\r\n\r\n  if (text.length <= maxLength) {\r\n    return <span>{text}</span>;\r\n  }\r\n\r\n  return (\r\n    <span\r\n      className=\"cursor-pointer\"\r\n      onClick={handleToggle}\r\n      title={isExpanded ? \"Click to collapse\" : \"Click to expand\"}\r\n    >\r\n      <span className=\"sm:hidden\">\r\n        {isExpanded ? text : `${text.substring(0, maxLength)}...`}\r\n      </span>\r\n      <span className=\"hidden sm:inline\">\r\n        {text}\r\n      </span>\r\n    </span>\r\n  );\r\n};\r\n\r\n/**\r\n * Structured Data Component for SEO\r\n */\r\nconst BreadcrumbStructuredData = ({ items }) => {\r\n  const structuredData = {\r\n    \"@context\": \"https://schema.org\",\r\n    \"@type\": \"BreadcrumbList\",\r\n    \"itemListElement\": items.map((item, index) => ({\r\n      \"@type\": \"ListItem\",\r\n      \"position\": index + 1,\r\n      \"name\": item.name,\r\n      \"item\": item.current ? undefined : `https://securitylit.com${item.url}`\r\n    }))\r\n  };\r\n\r\n  return (\r\n    <script\r\n      type=\"application/ld+json\"\r\n      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}\r\n    />\r\n  );\r\n};\r\n\r\n/**\r\n * Universal Breadcrumb Navigation Component with SEO structured data\r\n * Provides clear navigation hierarchy and improves SEO across all pages\r\n */\r\nconst BreadcrumbNavigation = ({ items, className = \"\" }) => {\r\n  const [hasExpandedText, setHasExpandedText] = useState(false);\r\n\r\n  // Check if this is a blog page for conditional padding\r\n  const isBlogPage = items?.some(item =>\r\n    item.url?.includes('/Blogs') ||\r\n    item.iconKey === 'blogs' ||\r\n    className?.includes('blog')\r\n  );\r\n\r\n  return (\r\n    <>\r\n      {/* Structured Data for SEO */}\r\n      <BreadcrumbStructuredData items={items} />\r\n      \r\n      {/* Simple Breadcrumb Navigation */}\r\n      <nav\r\n        className={`bg-transparent py-2 sm:py-3 ${\r\n          isBlogPage\r\n            ? hasExpandedText\r\n              ? 'pb-6 sm:pb-4'\r\n              : 'pb-3 sm:pb-4'\r\n            : 'pb-2 sm:pb-3'\r\n        } ${className}`}\r\n        aria-label=\"Breadcrumb navigation\"\r\n      >\r\n        <div className=\"max-w-7xl mx-auto px-4\">\r\n          <motion.ol\r\n            className={`flex items-center space-x-1 text-xs sm:text-sm ${\r\n              className?.includes('text-white')\r\n                ? 'text-white'\r\n                : className?.includes('text-blue-600')\r\n                ? 'text-blue-600'\r\n                : 'text-gray-600'\r\n            }`}\r\n            initial={{ opacity: 0 }}\r\n            animate={{ opacity: 1 }}\r\n            transition={{ duration: 0.4 }}\r\n          >\r\n            {items.map((item, index) => (\r\n              <motion.li\r\n                key={index}\r\n                className=\"flex items-center\"\r\n                initial={{ opacity: 0 }}\r\n                animate={{ opacity: 1 }}\r\n                transition={{ duration: 0.3, delay: index * 0.1 }}\r\n              >\r\n                {index > 0 && (\r\n                  <ChevronRight\r\n                    className={`w-3 h-3 mx-1 ${\r\n                      className?.includes('text-white')\r\n                        ? 'text-white/70'\r\n                        : className?.includes('text-blue-600')\r\n                        ? 'text-blue-400'\r\n                        : 'text-gray-400'\r\n                    }`}\r\n                    aria-hidden=\"true\"\r\n                  />\r\n                )}\r\n\r\n                {item.current ? (\r\n                  <span\r\n                    className={`font-medium flex items-center gap-1 ${\r\n                      className?.includes('text-white')\r\n                        ? 'text-white'\r\n                        : className?.includes('text-blue-600')\r\n                        ? 'text-blue-600'\r\n                        : 'text-gray-900'\r\n                    }`}\r\n                    aria-current=\"page\"\r\n                    title={item.description}\r\n                  >\r\n                    {item.iconKey === 'home' && (\r\n                      <Home className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-blue-600'\r\n                      }`} />\r\n                    )}\r\n                    {item.iconKey === 'blogs' && (\r\n                      <FileText className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-blue-600'\r\n                      }`} />\r\n                    )}\r\n                    {item.iconKey === 'contact-us' && (\r\n                      <Phone className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-blue-600'\r\n                      }`} />\r\n                    )}\r\n                    <TruncatedText\r\n                      text={item.name}\r\n                      onExpansionChange={setHasExpandedText}\r\n                    />\r\n                  </span>\r\n                ) : (\r\n                  <Link\r\n                    href={item.url}\r\n                    className={`transition-colors duration-200 flex items-center gap-1 hover:underline ${\r\n                      className?.includes('text-white')\r\n                        ? 'text-white hover:text-white/80'\r\n                        : className?.includes('text-blue-600')\r\n                        ? 'text-blue-600 hover:text-blue-800'\r\n                        : 'text-blue-600 hover:text-blue-800'\r\n                    }`}\r\n                    title={item.description}\r\n                  >\r\n                    {item.iconKey === 'home' && (\r\n                      <Home className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-current'\r\n                      }`} />\r\n                    )}\r\n                    {item.iconKey === 'blogs' && (\r\n                      <FileText className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-current'\r\n                      }`} />\r\n                    )}\r\n                    {item.iconKey === 'contact-us' && (\r\n                      <Phone className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-current'\r\n                      }`} />\r\n                    )}\r\n                    <TruncatedText\r\n                      text={item.name}\r\n                      onExpansionChange={setHasExpandedText}\r\n                    />\r\n                  </Link>\r\n                )}\r\n              </motion.li>\r\n            ))}\r\n          </motion.ol>\r\n        </div>\r\n      </nav>\r\n    </>\r\n  );\r\n};\r\n\r\n// Client-side helper function for generating breadcrumb items\r\nexport const generateBreadcrumbs = (pageType, params = {}) => {\r\n  const baseBreadcrumb = {\r\n    name: \"Home\",\r\n    url: \"/\",\r\n    iconKey: \"home\",\r\n    description: \"Return to homepage\"\r\n  };\r\n\r\n  const breadcrumbConfigs = {\r\n    blog: (slug) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Blog\",\r\n        url: \"/Blogs\",\r\n        iconKey: \"blogs\",\r\n        description: \"View all blog posts\"\r\n      },\r\n      {\r\n        name: params.title || \"Blog Post\",\r\n        url: `/Blogs/${slug}`,\r\n        current: true,\r\n        description: `${params.title || 'Blog post'} - Cybersecurity insights and penetration testing guidance`\r\n      }\r\n    ],\r\n    \r\n    service: (service) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Services\",\r\n        url: \"/Services\",\r\n        iconKey: \"services\",\r\n        description: \"View all cybersecurity services\"\r\n      },\r\n      {\r\n        name: params.title || service,\r\n        url: `/Services/${service}`,\r\n        current: true,\r\n        iconKey: service,\r\n        description: `${params.title || service} penetration testing services`\r\n      }\r\n    ],\r\n\r\n    location: (country) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Locations\",\r\n        url: \"/Locations\",\r\n        iconKey: \"locations\",\r\n        description: \"View all locations\"\r\n      },\r\n      {\r\n        name: params.countryName || country,\r\n        url: `/Locations/${country}`,\r\n        current: true,\r\n        description: `${params.countryName || country} cybersecurity services`\r\n      }\r\n    ],\r\n\r\n    company: (page) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Company\",\r\n        url: \"/Company\",\r\n        iconKey: \"company\",\r\n        description: \"Learn about SecurityLit\"\r\n      },\r\n      {\r\n        name: params.title || page,\r\n        url: `/Company/${page}`,\r\n        current: true,\r\n        description: params.description || `${page} information`\r\n      }\r\n    ],\r\n\r\n    'company-size': (size) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Company Size\",\r\n        url: \"/Company-size\",\r\n        iconKey: \"company\",\r\n        description: \"Solutions for different company sizes\"\r\n      },\r\n      {\r\n        name: params.title || size,\r\n        url: `/Company-size/${size}`,\r\n        current: true,\r\n        description: params.description || `${size} cybersecurity solutions`\r\n      }\r\n    ],\r\n\r\n    industry: (industry) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Industries\",\r\n        url: \"/Industries\",\r\n        iconKey: \"company\",\r\n        description: \"Industry-specific cybersecurity solutions\"\r\n      },\r\n      {\r\n        name: params.title || industry,\r\n        url: `/Industries/${industry}`,\r\n        current: true,\r\n        description: params.description || `${industry} cybersecurity solutions`\r\n      }\r\n    ],\r\n\r\n    product: (product) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Product\",\r\n        url: \"/Product\",\r\n        iconKey: \"product\",\r\n        description: \"Explore our cybersecurity products\"\r\n      },\r\n      {\r\n        name: params.title || product,\r\n        url: `/Product/${product}`,\r\n        current: true,\r\n        description: params.description || `${product} product information`\r\n      }\r\n    ],\r\n\r\n    simple: (pageName, url) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: pageName,\r\n        url: url,\r\n        current: true,\r\n        iconKey: params.iconKey,\r\n        description: params.description || `${pageName} page`\r\n      }\r\n    ]\r\n  };\r\n\r\n  return breadcrumbConfigs[pageType] || (() => [baseBreadcrumb]);\r\n};\r\n\r\nexport default BreadcrumbNavigation; "], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;;AAMA;;;CAGC,GACD,MAAM,gBAAgB,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,EAAE,iBAAiB,EAAE;IAChE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,eAAe;QACnB,MAAM,cAAc,CAAC;QACrB,cAAc;QACd,IAAI,mBAAmB;YACrB,kBAAkB;QACpB;IACF;IAEA,IAAI,KAAK,MAAM,IAAI,WAAW;QAC5B,qBAAO,8OAAC;sBAAM;;;;;;IAChB;IAEA,qBACE,8OAAC;QACC,WAAU;QACV,SAAS;QACT,OAAO,aAAa,sBAAsB;;0BAE1C,8OAAC;gBAAK,WAAU;0BACb,aAAa,OAAO,GAAG,KAAK,SAAS,CAAC,GAAG,WAAW,GAAG,CAAC;;;;;;0BAE3D,8OAAC;gBAAK,WAAU;0BACb;;;;;;;;;;;;AAIT;AAEA;;CAEC,GACD,MAAM,2BAA2B,CAAC,EAAE,KAAK,EAAE;IACzC,MAAM,iBAAiB;QACrB,YAAY;QACZ,SAAS;QACT,mBAAmB,MAAM,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;gBAC7C,SAAS;gBACT,YAAY,QAAQ;gBACpB,QAAQ,KAAK,IAAI;gBACjB,QAAQ,KAAK,OAAO,GAAG,YAAY,CAAC,uBAAuB,EAAE,KAAK,GAAG,EAAE;YACzE,CAAC;IACH;IAEA,qBACE,8OAAC;QACC,MAAK;QACL,yBAAyB;YAAE,QAAQ,KAAK,SAAS,CAAC;QAAgB;;;;;;AAGxE;AAEA;;;CAGC,GACD,MAAM,uBAAuB,CAAC,EAAE,KAAK,EAAE,YAAY,EAAE,EAAE;IACrD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,uDAAuD;IACvD,MAAM,aAAa,OAAO,KAAK,CAAA,OAC7B,KAAK,GAAG,EAAE,SAAS,aACnB,KAAK,OAAO,KAAK,WACjB,WAAW,SAAS;IAGtB,qBACE;;0BAEE,8OAAC;gBAAyB,OAAO;;;;;;0BAGjC,8OAAC;gBACC,WAAW,CAAC,4BAA4B,EACtC,aACI,kBACE,iBACA,iBACF,eACL,CAAC,EAAE,WAAW;gBACf,cAAW;0BAEX,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;wBACR,WAAW,CAAC,+CAA+C,EACzD,WAAW,SAAS,gBAChB,eACA,WAAW,SAAS,mBACpB,kBACA,iBACJ;wBACF,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,YAAY;4BAAE,UAAU;wBAAI;kCAE3B,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;gCAER,WAAU;gCACV,SAAS;oCAAE,SAAS;gCAAE;gCACtB,SAAS;oCAAE,SAAS;gCAAE;gCACtB,YAAY;oCAAE,UAAU;oCAAK,OAAO,QAAQ;gCAAI;;oCAE/C,QAAQ,mBACP,8OAAC,sNAAA,CAAA,eAAY;wCACX,WAAW,CAAC,aAAa,EACvB,WAAW,SAAS,gBAChB,kBACA,WAAW,SAAS,mBACpB,kBACA,iBACJ;wCACF,eAAY;;;;;;oCAIf,KAAK,OAAO,iBACX,8OAAC;wCACC,WAAW,CAAC,oCAAoC,EAC9C,WAAW,SAAS,gBAChB,eACA,WAAW,SAAS,mBACpB,kBACA,iBACJ;wCACF,gBAAa;wCACb,OAAO,KAAK,WAAW;;4CAEtB,KAAK,OAAO,KAAK,wBAChB,8OAAC,mMAAA,CAAA,OAAI;gDAAC,WAAW,CAAC,QAAQ,EACxB,WAAW,SAAS,gBAChB,eACA,WAAW,SAAS,mBACpB,kBACA,iBACJ;;;;;;4CAEH,KAAK,OAAO,KAAK,yBAChB,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAW,CAAC,QAAQ,EAC5B,WAAW,SAAS,gBAChB,eACA,WAAW,SAAS,mBACpB,kBACA,iBACJ;;;;;;4CAEH,KAAK,OAAO,KAAK,8BAChB,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAW,CAAC,QAAQ,EACzB,WAAW,SAAS,gBAChB,eACA,WAAW,SAAS,mBACpB,kBACA,iBACJ;;;;;;0DAEJ,8OAAC;gDACC,MAAM,KAAK,IAAI;gDACf,mBAAmB;;;;;;;;;;;iGAIvB,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAM,KAAK,GAAG;wCACd,WAAW,CAAC,uEAAuE,EACjF,WAAW,SAAS,gBAChB,mCACA,WAAW,SAAS,mBACpB,sCACA,qCACJ;wCACF,OAAO,KAAK,WAAW;;4CAEtB,KAAK,OAAO,KAAK,wBAChB,8OAAC,mMAAA,CAAA,OAAI;gDAAC,WAAW,CAAC,QAAQ,EACxB,WAAW,SAAS,gBAChB,eACA,WAAW,SAAS,mBACpB,kBACA,gBACJ;;;;;;4CAEH,KAAK,OAAO,KAAK,yBAChB,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAW,CAAC,QAAQ,EAC5B,WAAW,SAAS,gBAChB,eACA,WAAW,SAAS,mBACpB,kBACA,gBACJ;;;;;;4CAEH,KAAK,OAAO,KAAK,8BAChB,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAW,CAAC,QAAQ,EACzB,WAAW,SAAS,gBAChB,eACA,WAAW,SAAS,mBACpB,kBACA,gBACJ;;;;;;0DAEJ,8OAAC;gDACC,MAAM,KAAK,IAAI;gDACf,mBAAmB;;;;;;;;;;;;;+BAxGpB;;;;;;;;;;;;;;;;;;;;;;AAmHrB;AAGO,MAAM,sBAAsB,CAAC,UAAU,SAAS,CAAC,CAAC;IACvD,MAAM,iBAAiB;QACrB,MAAM;QACN,KAAK;QACL,SAAS;QACT,aAAa;IACf;IAEA,MAAM,oBAAoB;QACxB,MAAM,CAAC,OAAS;gBACd;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,CAAC,OAAO,EAAE,MAAM;oBACrB,SAAS;oBACT,aAAa,GAAG,OAAO,KAAK,IAAI,YAAY,0DAA0D,CAAC;gBACzG;aACD;QAED,SAAS,CAAC,UAAY;gBACpB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,CAAC,UAAU,EAAE,SAAS;oBAC3B,SAAS;oBACT,SAAS;oBACT,aAAa,GAAG,OAAO,KAAK,IAAI,QAAQ,6BAA6B,CAAC;gBACxE;aACD;QAED,UAAU,CAAC,UAAY;gBACrB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,WAAW,IAAI;oBAC5B,KAAK,CAAC,WAAW,EAAE,SAAS;oBAC5B,SAAS;oBACT,aAAa,GAAG,OAAO,WAAW,IAAI,QAAQ,uBAAuB,CAAC;gBACxE;aACD;QAED,SAAS,CAAC,OAAS;gBACjB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,CAAC,SAAS,EAAE,MAAM;oBACvB,SAAS;oBACT,aAAa,OAAO,WAAW,IAAI,GAAG,KAAK,YAAY,CAAC;gBAC1D;aACD;QAED,gBAAgB,CAAC,OAAS;gBACxB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,CAAC,cAAc,EAAE,MAAM;oBAC5B,SAAS;oBACT,aAAa,OAAO,WAAW,IAAI,GAAG,KAAK,wBAAwB,CAAC;gBACtE;aACD;QAED,UAAU,CAAC,WAAa;gBACtB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,CAAC,YAAY,EAAE,UAAU;oBAC9B,SAAS;oBACT,aAAa,OAAO,WAAW,IAAI,GAAG,SAAS,wBAAwB,CAAC;gBAC1E;aACD;QAED,SAAS,CAAC,UAAY;gBACpB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,CAAC,SAAS,EAAE,SAAS;oBAC1B,SAAS;oBACT,aAAa,OAAO,WAAW,IAAI,GAAG,QAAQ,oBAAoB,CAAC;gBACrE;aACD;QAED,QAAQ,CAAC,UAAU,MAAQ;gBACzB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,SAAS,OAAO,OAAO;oBACvB,aAAa,OAAO,WAAW,IAAI,GAAG,SAAS,KAAK,CAAC;gBACvD;aACD;IACH;IAEA,OAAO,iBAAiB,CAAC,SAAS,IAAI,CAAC,IAAM;YAAC;SAAe;AAC/D;uCAEe", "debugId": null}}, {"offset": {"line": 386, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/CyberSecTraining/page.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport { Shield, Play, ArrowRight, GraduationCap, CheckCircle, Users, Star, Clock, Award, Zap, BookOpen, Brain, Code, Compass, Target, Lock, Mail } from 'lucide-react';\r\nimport BreadcrumbNavigation from '../common/components/BreadcrumbNavigation';\r\nimport { PrimaryButton } from '../common/buttons/BrandButtons';\r\n\r\nexport default function CyberSecTraining() {\r\n  const breadcrumbItems = [\r\n    {\r\n      name: \"Home\",\r\n      url: \"/\",\r\n      iconKey: \"home\",\r\n      description: \"Return to homepage\"\r\n    },\r\n    {\r\n      name: \"CyberSec Training\",\r\n      url: \"/CyberSecTraining\",\r\n      current: true,\r\n      iconKey: \"graduation-cap\",\r\n      description: \"Advanced Cybersecurity Training Program\"\r\n    }\r\n  ];\r\n\r\n  const programPhases = [\r\n    {\r\n      phase: \"Phase 1\",\r\n      title: \"Web And API Penetration Testing\",\r\n      description: \"Our program introduces participants to VAPT (Vulnerability Assessment and Penetration Testing) for mobile and web applications, focusing on identifying and mitigating security flaws. With the growing reliance on mobile apps and APIs, security is critical for organizations.\",\r\n      features: [\"Assignments\", \"Labs\", \"Mentoring (Premium Only)\", \"Hands-On Real Project (Premium Only)\"]\r\n    },\r\n    {\r\n      phase: \"Phase 2\",\r\n      title: \"Network and Cloud Penetration Testing\",\r\n      description: \"This phase focuses on network security, equipping participants with the skills to assess and secure organizational networks and endpoints. The emphasis is on identifying vulnerabilities in network infrastructure and developing robust defenses.\",\r\n      features: [\"Assignments\", \"Labs\", \"Mentoring (Premium Only)\", \"Hands-On Real Project (Premium Only)\"]\r\n    },\r\n    {\r\n      phase: \"Phase 3\",\r\n      title: \"Cloud Security\",\r\n      description: \"This phase introduces participants to essential cloud security practices, focusing on securing AWS environments. With more organizations moving to the cloud, understanding foundational cloud security principles is crucial for safeguarding cloud-based infrastructure.\",\r\n      features: [\"Assignments\", \"Labs\", \"Mentoring (Premium Only)\", \"Hands-On Real Project (Premium Only)\"]\r\n    }\r\n  ];\r\n\r\n  const testimonials = [\r\n    {\r\n      quote: \"SecurityLit transformed my perspective on IT security. Through identifying risks for organizations, I've grown in my role and feel more capable now. The supportive work environment and knowledgeable colleagues at SecurityLit have been invaluable.\",\r\n      author: \"Keziah Achshah Guha\",\r\n      position: \"Information Security Analyst at DataTorque Ltd\"\r\n    },\r\n    {\r\n      quote: \"The training boosted my expertise and confidence, helping me excel as a pentester. Hands-on projects deepened my skills in web security and advanced exploitation, which I now apply regularly, along with improved communication skills.\",\r\n      author: \"Anindya Roy\",\r\n      position: \"Associate Penetration Tester at SecurityLit\"\r\n    },\r\n    {\r\n      quote: \"This training improved my approach to pentesting, enhancing my process, documentation, and teamwork. Collaborating on tests and following a structured process was far more effective than my previous ad-hoc methods in bug hunting.\",\r\n      author: \"Krishna Dinkar Biradar\",\r\n      position: \"Associate Penetration Tester at SecurityLit\"\r\n    },\r\n    {\r\n      quote: \"I believe I got the job thanks to the training, support, and feedback from Ankita and the SecurityLit team, which helped me excel in the interviews. Thank you for all the guidance that led me to this role.\",\r\n      author: \"Rini Sebastian\",\r\n      position: \"Information Security Analyst at AIA NZ\"\r\n    }\r\n  ];\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-white\">\r\n      {/* Hero Section */}\r\n      <section className=\"py-12 sm:py-16 lg:py-24 bg-gradient-to-br from-[var(--color-dark-blue)] to-[var(--color-dark-blue-hover)] relative overflow-hidden\">\r\n        <div className=\"absolute inset-0 bg-cover bg-center bg-no-repeat opacity-10\"\r\n             style={{ backgroundImage: 'url(/images/hexagon.svg)' }}>\r\n        </div>\r\n        <div className=\"absolute inset-0 bg-[var(--color-dark-blue)]/90\"></div>\r\n        \r\n        <div className=\"relative z-10 container mx-auto px-4 max-w-7xl\">\r\n          <div className=\"flex flex-col lg:flex-row items-center gap-12\">\r\n            <div className=\"flex-1 text-center lg:text-left\">\r\n              {/* Breadcrumb */}\r\n              <div className=\"mb-6\">\r\n                <BreadcrumbNavigation items={breadcrumbItems} className=\"text-white\" />\r\n              </div>\r\n\r\n              <div className=\"inline-flex items-center bg-white/10 backdrop-blur-sm px-6 py-3 rounded-full mb-8 border border-white/20\">\r\n                <Shield className=\"w-5 h-5 text-white mr-3\" />\r\n                <span className=\"text-sm font-semibold text-white\">Security Lit Presents</span>\r\n              </div>\r\n\r\n              <h1 className=\"text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-bold text-white mb-6 leading-tight\">\r\n                Are you searching for training in\r\n                <span className=\"block bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] bg-clip-text text-transparent\">\r\n                  Cyber Security field?\r\n                </span>\r\n              </h1>\r\n              \r\n              <p className=\"text-lg sm:text-xl text-white/90 mb-8 leading-relaxed max-w-3xl\">\r\n                We are among the few companies in India offering internships across different sectors of Cyber Security. Check out real-life Cyber Security projects, get awesome experience to kickstart your career in cyber security and totally change your life!\r\n              </p>\r\n\r\n              <div className=\"flex flex-col sm:flex-row gap-4 justify-center lg:justify-start\">\r\n                <button className=\"group bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] text-white px-6 py-3 rounded-lg font-semibold text-base transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center gap-2\">\r\n                  <Play className=\"w-5 h-5\" />\r\n                  Enroll Now\r\n                  <ArrowRight className=\"w-5 h-5 group-hover:translate-x-1 transition-transform\" />\r\n                </button>\r\n\r\n                <button className=\"group bg-white/10 backdrop-blur-sm text-white px-6 py-3 rounded-lg font-semibold text-base transition-all duration-300 border-2 border-white/20 hover:bg-white/20 flex items-center justify-center gap-2\">\r\n                  <Mail className=\"w-5 h-5\" />\r\n                  Contact Us\r\n                </button>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"flex-1\">\r\n              <div className=\"relative\">\r\n                <img \r\n                  src=\"/images/p2s1.png\" \r\n                  alt=\"Cybersecurity Training\" \r\n                  className=\"w-full h-auto rounded-2xl shadow-2xl\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Mission Vision Section */}\r\n      <section className=\"py-12 sm:py-16 lg:py-24 bg-white\">\r\n        <div className=\"container mx-auto px-4 max-w-7xl\">\r\n          <div className=\"text-center mb-16\">\r\n            <h2 className=\"text-3xl lg:text-4xl xl:text-5xl font-bold text-[var(--color-dark-blue)] mb-8\">\r\n              Become Cyber Security Expert in just 6 months\r\n            </h2>\r\n            <p className=\"text-lg text-[var(--foreground-secondary)] max-w-4xl mx-auto\">\r\n              At Security Lit Pvt Ltd, we train beginners to become cyber security experts and job-ready individuals.\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8\">\r\n            <div className=\"bg-gradient-to-br from-[var(--color-blue)]/10 to-[var(--color-blue-secondary)]/10 rounded-3xl p-8\">\r\n              <div className=\"w-16 h-16 bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-2xl flex items-center justify-center mb-6\">\r\n                <Target className=\"w-8 h-8 text-white\" />\r\n              </div>\r\n              <h3 className=\"text-2xl font-bold text-[var(--color-dark-blue)] mb-4\">Our Mission</h3>\r\n              <p className=\"text-[var(--foreground-secondary)] leading-relaxed\">\r\n                We're on a mission to secure the digital world. Through robust partnerships, cybersecurity excellence, and raising awareness, we're making it safe for all.\r\n              </p>\r\n            </div>\r\n\r\n            <div className=\"bg-gradient-to-br from-[var(--color-dark-blue)]/10 to-[var(--color-dark-blue-hover)]/10 rounded-3xl p-8\">\r\n              <div className=\"w-16 h-16 bg-gradient-to-br from-[var(--color-dark-blue)] to-[var(--color-dark-blue-hover)] rounded-2xl flex items-center justify-center mb-6\">\r\n                <Compass className=\"w-8 h-8 text-white\" />\r\n              </div>\r\n              <h3 className=\"text-2xl font-bold text-[var(--color-dark-blue)] mb-4\">Our Vision</h3>\r\n              <p className=\"text-[var(--foreground-secondary)] leading-relaxed\">\r\n                At Security Lit Pvt Ltd, we envision a world where every individual and organization, regardless of their size or complexity, finds a single, trusted solution for all their cybersecurity needs.\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Program Structure Section */}\r\n      <section className=\"py-12 sm:py-16 lg:py-24 bg-gray-50\">\r\n        <div className=\"container mx-auto px-4 max-w-7xl\">\r\n          <div className=\"text-center mb-16\">\r\n            <h2 className=\"text-3xl lg:text-4xl xl:text-5xl font-bold text-[var(--color-dark-blue)] mb-8\">\r\n              Program Structure\r\n            </h2>\r\n            <p className=\"text-lg text-[var(--foreground-secondary)] max-w-4xl mx-auto\">\r\n              We provide a job-focused, hands-on curriculum designed to take participants from foundational to advanced cybersecurity skills across three comprehensive phases.\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"space-y-12\">\r\n            {programPhases.map((phase, index) => (\r\n              <div key={index} className=\"bg-white rounded-3xl p-8 lg:p-12 shadow-lg\">\r\n                <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8 items-center\">\r\n                  <div>\r\n                    <div className=\"inline-flex items-center bg-gradient-to-r from-[var(--color-blue)]/10 to-[var(--color-blue-secondary)]/10 px-4 py-2 rounded-full mb-6 border border-[var(--color-blue)]/20\">\r\n                      <span className=\"text-sm font-semibold text-[var(--color-blue)]\">{phase.phase}</span>\r\n                    </div>\r\n                    \r\n                    <h3 className=\"text-2xl lg:text-3xl font-bold text-[var(--color-dark-blue)] mb-6\">\r\n                      {phase.title}\r\n                    </h3>\r\n                    \r\n                    <p className=\"text-[var(--foreground-secondary)] leading-relaxed mb-8\">\r\n                      {phase.description}\r\n                    </p>\r\n\r\n                    <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4\">\r\n                      {phase.features.map((feature, featureIndex) => (\r\n                        <div key={featureIndex} className=\"flex items-center gap-3\">\r\n                          <div className=\"w-6 h-6 bg-[var(--color-yellow)] rounded-full flex items-center justify-center flex-shrink-0\">\r\n                            <CheckCircle className=\"w-4 h-4 text-[var(--color-dark-blue)]\" />\r\n                          </div>\r\n                          <span className=\"text-[var(--color-dark-blue)] font-medium\">{feature}</span>\r\n                        </div>\r\n                      ))}\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div className=\"relative\">\r\n                    <img \r\n                      src={`/images/p2s${index + 3}.png`} \r\n                      alt={phase.title}\r\n                      className=\"w-full h-auto rounded-2xl shadow-lg\"\r\n                    />\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Testimonials Section */}\r\n      <section className=\"py-12 sm:py-16 lg:py-24 bg-white\">\r\n        <div className=\"container mx-auto px-4 max-w-7xl\">\r\n          <div className=\"text-center mb-16\">\r\n            <h2 className=\"text-3xl lg:text-4xl xl:text-5xl font-bold text-[var(--color-dark-blue)] mb-8\">\r\n              Real Feedback from Professionals Who Completed Our Security Training\r\n            </h2>\r\n          </div>\r\n\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8\">\r\n            {testimonials.map((testimonial, index) => (\r\n              <div key={index} className=\"bg-gradient-to-br from-[var(--color-blue)]/10 to-[var(--color-blue-secondary)]/10 rounded-3xl p-8 border border-[var(--color-blue)]/20\">\r\n                <div className=\"flex items-start gap-4 mb-6\">\r\n                  <div className=\"w-12 h-12 bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-full flex items-center justify-center flex-shrink-0\">\r\n                    <Users className=\"w-6 h-6 text-white\" />\r\n                  </div>\r\n                  <div className=\"flex-1\">\r\n                    <p className=\"text-[var(--color-dark-blue)] leading-relaxed mb-4 italic\">\r\n                      \"{testimonial.quote}\"\r\n                    </p>\r\n                    <div>\r\n                      <div className=\"font-bold text-[var(--color-dark-blue)]\">{testimonial.author}</div>\r\n                      <div className=\"text-[var(--foreground-secondary)] text-sm\">{testimonial.position}</div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* CTA Section */}\r\n      <section className=\"py-12 sm:py-16 lg:py-24 bg-gradient-to-br from-[var(--color-dark-blue)] to-[var(--color-dark-blue-hover)]\">\r\n        <div className=\"container mx-auto px-4 max-w-4xl text-center\">\r\n          <h2 className=\"text-3xl lg:text-4xl xl:text-5xl font-bold text-white mb-8\">\r\n            Start Your Training Now!\r\n          </h2>\r\n          <p className=\"text-lg text-white/90 mb-8\">\r\n            Become part of the next generation of cybersecurity professionals with SecurityLit. SecurityLit offers an industry-aligned training program designed to equip you with the skills needed to succeed in today's cybersecurity landscape.\r\n          </p>\r\n          \r\n          <div className=\"bg-white/10 backdrop-blur-sm rounded-3xl p-8 mb-8\">\r\n            <form className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n              <div>\r\n                <label className=\"block text-white font-medium mb-2\">Name *</label>\r\n                <input type=\"text\" className=\"w-full px-4 py-3 rounded-lg bg-white/20 border border-white/30 text-white placeholder-white/70 focus:outline-none focus:border-white/50\" placeholder=\"Enter your name\" />\r\n              </div>\r\n              <div>\r\n                <label className=\"block text-white font-medium mb-2\">Name Of Organization *</label>\r\n                <input type=\"text\" className=\"w-full px-4 py-3 rounded-lg bg-white/20 border border-white/30 text-white placeholder-white/70 focus:outline-none focus:border-white/50\" placeholder=\"Enter organization name\" />\r\n              </div>\r\n              <div>\r\n                <label className=\"block text-white font-medium mb-2\">Contact Number</label>\r\n                <input type=\"tel\" className=\"w-full px-4 py-3 rounded-lg bg-white/20 border border-white/30 text-white placeholder-white/70 focus:outline-none focus:border-white/50\" placeholder=\"Enter contact number\" />\r\n              </div>\r\n              <div>\r\n                <label className=\"block text-white font-medium mb-2\">Work Email *</label>\r\n                <input type=\"email\" className=\"w-full px-4 py-3 rounded-lg bg-white/20 border border-white/30 text-white placeholder-white/70 focus:outline-none focus:border-white/50\" placeholder=\"Enter work email\" />\r\n              </div>\r\n            </form>\r\n          </div>\r\n\r\n          <PrimaryButton className=\"inline-flex items-center gap-3 px-8 py-4 rounded-2xl text-lg font-semibold\">\r\n            <Shield className=\"w-6 h-6\" />\r\n            Submit Now\r\n            <ArrowRight className=\"w-6 h-6\" />\r\n          </PrimaryButton>\r\n        </div>\r\n      </section>\r\n    </div>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,kBAAkB;QACtB;YACE,MAAM;YACN,KAAK;YACL,SAAS;YACT,aAAa;QACf;QACA;YACE,MAAM;YACN,KAAK;YACL,SAAS;YACT,SAAS;YACT,aAAa;QACf;KACD;IAED,MAAM,gBAAgB;QACpB;YACE,OAAO;YACP,OAAO;YACP,aAAa;YACb,UAAU;gBAAC;gBAAe;gBAAQ;gBAA4B;aAAuC;QACvG;QACA;YACE,OAAO;YACP,OAAO;YACP,aAAa;YACb,UAAU;gBAAC;gBAAe;gBAAQ;gBAA4B;aAAuC;QACvG;QACA;YACE,OAAO;YACP,OAAO;YACP,aAAa;YACb,UAAU;gBAAC;gBAAe;gBAAQ;gBAA4B;aAAuC;QACvG;KACD;IAED,MAAM,eAAe;QACnB;YACE,OAAO;YACP,QAAQ;YACR,UAAU;QACZ;QACA;YACE,OAAO;YACP,QAAQ;YACR,UAAU;QACZ;QACA;YACE,OAAO;YACP,QAAQ;YACR,UAAU;QACZ;QACA;YACE,OAAO;YACP,QAAQ;YACR,UAAU;QACZ;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAQ,WAAU;;kCACjB,8OAAC;wBAAI,WAAU;wBACV,OAAO;4BAAE,iBAAiB;wBAA2B;;;;;;kCAE1D,8OAAC;wBAAI,WAAU;;;;;;kCAEf,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,2JAAA,CAAA,UAAoB;gDAAC,OAAO;gDAAiB,WAAU;;;;;;;;;;;sDAG1D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;oDAAK,WAAU;8DAAmC;;;;;;;;;;;;sDAGrD,8OAAC;4CAAG,WAAU;;gDAAuF;8DAEnG,8OAAC;oDAAK,WAAU;8DAAiH;;;;;;;;;;;;sDAKnI,8OAAC;4CAAE,WAAU;sDAAkE;;;;;;sDAI/E,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAO,WAAU;;sEAChB,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAY;sEAE5B,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;;8DAGxB,8OAAC;oDAAO,WAAU;;sEAChB,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAY;;;;;;;;;;;;;;;;;;;8CAMlC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,KAAI;4CACJ,KAAI;4CACJ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAStB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAgF;;;;;;8CAG9F,8OAAC;oCAAE,WAAU;8CAA+D;;;;;;;;;;;;sCAK9E,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;sDAEpB,8OAAC;4CAAG,WAAU;sDAAwD;;;;;;sDACtE,8OAAC;4CAAE,WAAU;sDAAqD;;;;;;;;;;;;8CAKpE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,wMAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;sDAErB,8OAAC;4CAAG,WAAU;sDAAwD;;;;;;sDACtE,8OAAC;4CAAE,WAAU;sDAAqD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS1E,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAgF;;;;;;8CAG9F,8OAAC;oCAAE,WAAU;8CAA+D;;;;;;;;;;;;sCAK9E,8OAAC;4BAAI,WAAU;sCACZ,cAAc,GAAG,CAAC,CAAC,OAAO,sBACzB,8OAAC;oCAAgB,WAAU;8CACzB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAK,WAAU;sEAAkD,MAAM,KAAK;;;;;;;;;;;kEAG/E,8OAAC;wDAAG,WAAU;kEACX,MAAM,KAAK;;;;;;kEAGd,8OAAC;wDAAE,WAAU;kEACV,MAAM,WAAW;;;;;;kEAGpB,8OAAC;wDAAI,WAAU;kEACZ,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC5B,8OAAC;gEAAuB,WAAU;;kFAChC,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC,2NAAA,CAAA,cAAW;4EAAC,WAAU;;;;;;;;;;;kFAEzB,8OAAC;wEAAK,WAAU;kFAA6C;;;;;;;+DAJrD;;;;;;;;;;;;;;;;0DAUhB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDACC,KAAK,CAAC,WAAW,EAAE,QAAQ,EAAE,IAAI,CAAC;oDAClC,KAAK,MAAM,KAAK;oDAChB,WAAU;;;;;;;;;;;;;;;;;mCA/BR;;;;;;;;;;;;;;;;;;;;;0BA0ClB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;0CAAgF;;;;;;;;;;;sCAKhG,8OAAC;4BAAI,WAAU;sCACZ,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,8OAAC;oCAAgB,WAAU;8CACzB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;0DAEnB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;;4DAA4D;4DACrE,YAAY,KAAK;4DAAC;;;;;;;kEAEtB,8OAAC;;0EACC,8OAAC;gEAAI,WAAU;0EAA2C,YAAY,MAAM;;;;;;0EAC5E,8OAAC;gEAAI,WAAU;0EAA8C,YAAY,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;mCAX/E;;;;;;;;;;;;;;;;;;;;;0BAsBlB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA6D;;;;;;sCAG3E,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAI1C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,WAAU;;kDACd,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAAoC;;;;;;0DACrD,8OAAC;gDAAM,MAAK;gDAAO,WAAU;gDAA0I,aAAY;;;;;;;;;;;;kDAErL,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAAoC;;;;;;0DACrD,8OAAC;gDAAM,MAAK;gDAAO,WAAU;gDAA0I,aAAY;;;;;;;;;;;;kDAErL,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAAoC;;;;;;0DACrD,8OAAC;gDAAM,MAAK;gDAAM,WAAU;gDAA0I,aAAY;;;;;;;;;;;;kDAEpL,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAAoC;;;;;;0DACrD,8OAAC;gDAAM,MAAK;gDAAQ,WAAU;gDAA0I,aAAY;;;;;;;;;;;;;;;;;;;;;;;sCAK1L,8OAAC,gJAAA,CAAA,gBAAa;4BAAC,WAAU;;8CACvB,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAY;8CAE9B,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlC", "debugId": null}}]}