"use client";

import React from "react";
import { motion } from "framer-motion";
import { Newspaper, Award, ArrowRight } from "lucide-react";
import BreadcrumbNavigation from "../../common/components/BreadcrumbNavigation";

export default function NewsHero() {
  const breadcrumbItems = [
    {
      name: "Home",
      url: "/",
      iconKey: "home",
      description: "Return to homepage",
    },
    {
      name: "News",
      url: "/news",
      current: true,
      iconKey: "newspaper",
      description: "SecurityLit in the News",
    },
  ];

  return (
    <section className="relative bg-gradient-to-br from-[var(--color-dark-blue)] to-[var(--color-blue)] min-h-[60vh] flex items-center overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-[url('/images/hexagon.svg')] bg-cover opacity-10" />
      {/* Remove blue overlay, keep dark blue as dominant */}
      {/* <div className="absolute inset-0 bg-[var(--color-blue)]/80" /> */}
      <div className="absolute right-0 top-0 w-1/3 h-full bg-gradient-to-l from-white/10 to-transparent" />

      <div className="relative z-10 container mx-auto px-4 max-w-7xl w-full">
        <div className="pt-24 pb-16 flex flex-col items-center text-center">
          {/* Breadcrumb */}
          <div className="mb-4 w-full flex justify-center">
            <BreadcrumbNavigation items={breadcrumbItems} className="text-white" />
          </div>

          {/* Main Headline */}
          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-4xl lg:text-5xl font-bold text-white mb-4 leading-tight drop-shadow-lg"
          >
            Security Lit in News
          </motion.h1>

          {/* Subheadline */}
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-xl lg:text-2xl font-semibold text-white/90 mb-6"
          >
            Recognition for our efforts to make the internet a safer place
          </motion.h2>

          {/* News Icon */}
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="flex items-center justify-center mb-6"
          >
            <div className="w-20 h-20 bg-white/10 rounded-2xl flex items-center justify-center shadow-lg">
              <Newspaper className="w-12 h-12 text-white" aria-label="News" />
            </div>
          </motion.div>

          {/* Optional: Featured In/As Seen On logos */}
          {/* <div className="flex flex-wrap justify-center gap-6 mt-4">
            <img src="/images/news/csc_sponsor.png" alt="CSC Sponsor" className="h-10" />
            <img src="/images/news/ankita-feature-nzbusiness.png" alt="NZ Business" className="h-10" />
            ...
          </div> */}
        </div>
      </div>
    </section>
  );
} 