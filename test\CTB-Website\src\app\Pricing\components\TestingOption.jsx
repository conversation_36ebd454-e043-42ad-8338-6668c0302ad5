"use client";

import React from 'react';
import { CheckIcon, ArrowRightIcon } from '@heroicons/react/24/solid';

const testingOptions = [
  {
    id: "one-off",
    title: "One-Off Comprehensive Penetration Test",
    subtitle: "Complete security assessment at a specific point in time",
    description: "A standalone security assessment designed to give you a snapshot of your security posture. Think of it as a thorough security checkup for your digital assets.",
    features: [
      "Full Scope Assessment: Web applications, mobile apps, APIs, and network infrastructure",
      "Comprehensive Report: Technical details and executive summary",
      "One Round of Retesting: Validation of vulnerability fixes",
      "Remediation Support: Guidance for implementing fixes"
    ],
    specialSection: {
      title: "Ideal For:",
      items: [
        "Organizations needing comprehensive security assessment",
        "Pre-release security validation",
        "Regulatory compliance requirements",
        "Understanding immediate security risks"
      ]
    }
  },
  {
    id: "subscription",
    title: "Subscription Penetration Testing",
    subtitle: "Continuous security program with ongoing protection",
    description: "A continuous security program that combines comprehensive annual testing with monthly focused assessments, providing constant protection against emerging threats.",
    features: [
      "Initial Comprehensive Test: Complete security baseline assessment",
      "Live Vulnerability Dashboard: Real-time reporting and tracking",
      "Annual Comprehensive Report: Detailed yearly security overview",
      "Unlimited Retests: Continuous validation of fixes",
      "Ongoing Remediation Support: Year-round security guidance"
    ],
    specialSection: {
      title: "Benefits:",
      items: [
        "Ongoing security and proactive threat management",
        "Cost-effective compared to multiple one-off tests",
        "Flexible monthly testing focus",
        "Peace of mind with continuous monitoring"
      ]
    }
  }
];

export default function PenetrationTestingOptions() {
  return (
    <section className="w-full py-20 sm:py-24 md:py-24 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16 sm:mb-16">
          <h2 className="text-4xl sm:text-5xl font-bold text-blue-900 mb-4">
            Our Penetration Testing Options
          </h2>
          <p className="text-lg text-black  font-semibold max-w-3xl mx-auto">
            Choose the security testing solution that best fits your organization&apos;s needs.
          </p>
        </div>

        {/* Pricing Cards */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-10">
          {testingOptions.map((option) => (
            <div
              key={option.id}
              className="bg-white rounded-2xl p-8 border border-gray-200 shadow-lg flex flex-col h-full"
            >
              <div className="flex-grow">
                {/* Titles and Description */}
                <div className="mb-8">
                  <h3 className="text-2xl font-bold text-secondary-blue mb-4">
                    {option.title}
                  </h3>
                  
                  {/* --- MODIFIED SUBTITLE --- */}
                  <div className="mb-6">
                    <span className="inline-block bg-indigo-100 text-blue-900 text-sm font-semibold px-4 py-1.5 rounded-full">
                      {option.subtitle}
                    </span>
                  </div>
                  
                  <p className="text-gray-700 leading-relaxed">
                    {option.description}
                  </p>
                </div>

                {/* Features List */}
                <ul className="space-y-4 mb-8">
                  {option.features.map((feature, index) => (
                    <li key={index} className="flex items-start">
                      <div className="flex-shrink-0 h-6 w-6 rounded-full bg-green-100 flex items-center justify-center mr-4 mt-0.5">
                        <CheckIcon className="h-4 w-4 text-green-600" />
                      </div>
                      <span className="text-gray-700">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Special Section (Ideal For / Benefits) */}
              <div className="mt-auto">
                <div className="bg-indigo-50 rounded-xl p-6">
                  <h4 className="text-lg font-semibold text-secondary-blue mb-4">
                    {option.specialSection.title}
                  </h4>
                  <ul className="space-y-3">
                    {option.specialSection.items.map((item, index) => (
                      <li key={index} className="flex items-start">
                        <ArrowRightIcon className="h-5 w-5 text-indigo-400 mr-3 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-600">{item}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
