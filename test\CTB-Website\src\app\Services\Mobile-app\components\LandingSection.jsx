import Button from "@/app/common/buttons/Button";
import Image from "next/image";
import Link from "next/link";
import React from "react";
import { <PERSON>R<PERSON>, Shield, Zap, Target } from "lucide-react";

const logos = [ 
  { src: '/images/cronberry-removebg.png', alt: "cronberry Logo" },
  { src: '/images/lawvu-removebg-preview.png', alt: "lawvu Logo" },
  { src: '/images/Bonnet_logo.jpeg', alt: "Bonnet Logo" },  
  { src: '/images/forsite_logo.png', alt: "forsite Logo" },
  { src: '/images/cronberry_logo.png', alt: "cronberry Logo" },
];

export default function LandingSection() {
  return (
    <div className="bg-[#F8F5F7]">
      <div className="container mx-auto px-4 md:px-12 flex flex-col items-center text-center">
  
        <div className="flex flex-col gap-3 md:gap-4 max-w-3xl py-16 sm:py-24 lg:py-26 text-center">
          <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold text-ctb-blue-150 leading-tight md:leading-relaxed px-2 sm:px-0">
            A Modern Way to Do <br className="block sm:hidden" />
            <span className="text-blue-700 block sm:inline">Mobile Application Penetration Testing</span>
          </h1>

          <p className="text-[#7B6B9C] text-sm sm:text-base md:text-lg leading-relaxed px-2 sm:px-0 mt-2 sm:mt-3">
            We deliver <span className="font-semibold text-slate-800 bg-yellow-100 px-2 py-1 rounded-md">premium pentesting solution</span> for iOS and Android apps-targeting vulnerabilities in mobile APIs, local storage, authentication, and more. Our approach combines deep static and dynamic analysis, reverse engineering, and business logic testing-all tracked in your Capture The Bug PTaaS platform, with real-time updates and remediation support.
          </p>

          <div className="flex justify-center gap-4 mt-4 sm:mt-6 md:mt-6">
<Button
              href="/Request-Demo"
              variant="success"
              size="lg"
              rightIcon={<ArrowRight className="h-4 w-4 sm:h-5 sm:w-5" />}
              className="text-base sm:text-lg px-6 sm:px-8"
            >
              Get Started Today
            </Button>
          </div>
        </div>
      </div>

        <div className="relative w-full overflow-hidden min-h-[100px] sm:min-h-[400px]">
        <div
          className="absolute inset-0 w-full mt-[100px] sm:mt-[150px] md:mt-[110px] h-full bg-no-repeat"
          style={{
            backgroundImage: 'url(/images/cybersecurity-services-background.png)',
            backgroundPosition: "top left",
            backgroundSize: 'cover'
          }}
        />

          <div className="relative z-10 flex justify-center pt-0 sm:pt-12 md:pt-0 pb-8 sm:pb-12 md:pb-10">
                 <div className="container mx-auto px-4 md:px-12">
                   <Image
              src="/images/mobile-app-security-dashboard.svg"
                     alt="Mobile application security testing dashboard showing comprehensive vulnerability assessment and penetration testing results for iOS and Android applications"
                     width={1120}
                     height={980}
                     className="w-full max-w-[280px] sm:max-w-[400px] md:max-w-[600px] rounded-3xl lg:max-w-[800px] xl:max-w-[1200px] mx-auto"
                     priority
                     quality={90}
                   />
                 </div>
               </div>
             </div>
 
      <div className="w-full bg-ctb-blue-350 pb-6 sm:pb-8 md:pb-10">
        <div className="container mx-auto px-4 md:px-12 flex flex-col items-center text-center">
          <div className="max-w-3xl px-4 sm:px-0">
            <h2 className="text-ctb-green-50 text-lg sm:text-lg md:text-xl lg:text-2xl xl:text-3xl mb-2 sm:mb-3 md:mb-4 
            leading-tight sm:leading-relaxed font-semibold">
              <span className="block sm:inline">Reduce Mobile Vulnerabilities</span>
              <span className="block sm:inline"> by Up to 70%</span>
            </h2>
            <p className="text-white text-xs sm:text-sm md:text-base leading-relaxed px-2 sm:px-0">
              Continuous assessment. Actionable reports. Enterprise-grade security.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}