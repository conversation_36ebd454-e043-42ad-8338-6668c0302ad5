import { NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';

// In-memory token store (for demo; use Redis or DB for production)
const tokenStore = global._downloadTokenStore || (global._downloadTokenStore = new Map());

const PDF_PATH = path.join(process.cwd(), 'data', 'report.pdf');

export async function GET(request) {
  const { searchParams } = new URL(request.url);
  const token = searchParams.get('token');

  if (!token || !tokenStore.has(token)) {
    return new NextResponse('Forbidden', { status: 403 });
  }

  // Check if token is expired or used
  const tokenData = tokenStore.get(token);
  if (!tokenData || tokenData.used || Date.now() > tokenData.expiry) {
    return new NextResponse('Forbidden', { status: 403 });
  }

  // Mark token as used
  tokenData.used = true;
  tokenStore.set(token, tokenData);

  // Stream the PDF
  try {
    const pdfBuffer = await fs.readFile(PDF_PATH);
    return new NextResponse(pdfBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': 'attachment; filename="report.pdf"',
      },
    });
  } catch (err) {
    return new NextResponse('File not found', { status: 404 });
  }
}

// Helper for other routes to generate a token
export function generateDownloadToken() {
  const token = Math.random().toString(36).slice(2) + Date.now().toString(36);
  // Token valid for 10 minutes
  tokenStore.set(token, { used: false, expiry: Date.now() + 10 * 60 * 1000 });
  return token;
} 