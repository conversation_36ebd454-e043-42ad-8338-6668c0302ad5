export const metadata = {
  title: "Contact SecurityLit | Get Expert Cybersecurity Consultation",
  description: "Connect with SecurityLit's cybersecurity experts for a free consultation. Get in touch for VAPT, Red Team, VCISO, and compliance services. Expert team ready to secure your business.",
  keywords: "contact SecurityLit, cybersecurity consultation, VAPT services, red team services, VCISO services, security audit, penetration testing, compliance services, cybersecurity experts, security consultation",
  robots: "index, follow",
  icons: {
    icon: '/favicon.ico',
  },
  openGraph: {
    title: "Contact SecurityLit | Get Expert Cybersecurity Consultation",
    type: "website",
    url: "https://securitylit.com/contact",
    description: "Connect with SecurityLit's cybersecurity experts for a free consultation. Get in touch for VAPT, Red Team, VCISO, and compliance services. Expert team ready to secure your business.",
    images: "/seclit-logo-1.png",
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Contact SecurityLit | Get Expert Cybersecurity Consultation',
    description: 'Connect with SecurityLit\'s cybersecurity experts for a free consultation. Get in touch for VAPT, Red Team, VCISO, and compliance services.',
    images: "/seclit-logo-1.png",
  }
};

import ContactHero from './components/contactHero';
import ContactOfficeInfo from './components/contactOfficeInfo';
import ContactTestimonial from './components/contactTestimonial';
import TrustedTestimonials from '../Home/components/TrustedTestimonials';

export default function ContactPage() {
  return (
    <div className="min-h-screen">
      <ContactHero />
      {/* <ContactTestimonial /> */}
      <ContactOfficeInfo />
      <TrustedTestimonials />
    </div>
  );
}
