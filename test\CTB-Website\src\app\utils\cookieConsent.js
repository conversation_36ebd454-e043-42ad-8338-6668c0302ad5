/**
 * Cookie consent utility functions for managing user consent preferences
 */

// Check if code is running on client side
const isClient = typeof window !== 'undefined';

// Check if the site is running in production environment (not localhost)
const isProduction = isClient && 
  !window.location.hostname.includes('localhost') && 
  !window.location.hostname.includes('127.0.0.1');

// GTM ID
const GTM_ID = 'GTM-MMMXC8Z';

/**
 * Get the user's current cookie consent preference
 * @returns {string} 'all', 'essential', or 'none' if no preference is set
 */
export const getCookieConsent = () => {
  if (!isClient) return 'none';
  return localStorage.getItem('cookieConsent') || 'none';
};

/**
 * Save the user's cookie consent preference
 * @param {string} consentType - 'all' or 'essential'
 */
export const saveCookieConsent = (consentType) => {
  if (!isClient) return;
  localStorage.setItem('cookieConsent', consentType);
};

/**
 * Initialize Google Tag Manager based on user consent
 * Only loads GTM if user has consented to 'all' cookies AND is in production
 */
export const initializeGTM = () => {
  if (!isClient) return;
  
  const consent = getCookieConsent();
  
  // Skip analytics in development/localhost environment
  if (!isProduction) {
    console.log('Analytics disabled in development environment');
    return;
  }
  
  if (consent === 'all') {
    // Initialize Google Tag Manager
    window.dataLayer = window.dataLayer || [];
    window.dataLayer.push({
      event: 'consent_given',
      consent_type: 'all'
    });
    
    console.log('Google Tag Manager initialized with full consent');
  } else {
    // If GTM is already loaded but consent is withdrawn, notify GTM
    if (window.dataLayer) {
      window.dataLayer.push({
        event: 'consent_updated',
        consent_type: 'essential'
      });
      console.log('Google Tag Manager notified of essential-only consent');
    }
  }
};

/**
 * Initialize analytics based on user consent
 * Only loads analytics if user has consented to 'all' cookies
 * (In this case, analytics will be handled through GTM)
 */
export const initializeAnalytics = () => {
  if (!isClient) return;
  
  // Skip analytics in development/localhost environment
  if (!isProduction) {
    console.log('Analytics disabled in development environment');
    return;
  }
  
  // Analytics are loaded through GTM, so we just need to call initializeGTM
  initializeGTM();
};

/**
 * Initialize marketing tools based on user consent
 * Only loads marketing pixels if user has consented to 'all' cookies
 * (In this case, marketing pixels will be handled through GTM)
 */
export const initializeMarketingTools = () => {
  if (!isClient) return;
  
  // Skip marketing tools in development/localhost environment
  if (!isProduction) {
    console.log('Marketing tools disabled in development environment');
    return;
  }
  
  // Marketing tools are loaded through GTM, so nothing extra needed here
  // initializeGTM() is already called in initializeAnalytics()
};

/**
 * Specifically disable Microsoft Clarity in development
 * This function should be called early in the application lifecycle
 */
export const disableMicrosoftClarity = () => {
  if (!isClient) return;
  
  // If we're in development, block Microsoft Clarity
  if (!isProduction) {
    // Create a disabled version of the clarity object
    window.clarity = window.clarity || function() {
      console.log('Microsoft Clarity tracking disabled in development environment');
    };
    
    // Block any attempts to load the Clarity script
    const originalAppendChild = Document.prototype.appendChild;
    Document.prototype.appendChild = function(node) {
      if (node.src && (
        node.src.includes('clarity.ms') || 
        node.src.includes('clarity-js') ||
        node.src.includes('c.clarity')
      )) {
        console.log('Blocked Microsoft Clarity script from loading in development');
        return node; // Return node but don't actually append it
      }
      return originalAppendChild.call(this, node);
    };
    
    console.log('Microsoft Clarity tracking disabled in development environment');
  }
};

/**
 * Comprehensive function to block all common analytics and tracking services in development
 * This blocks most popular analytics platforms that might be loaded via scripts
 */
export const disableAllTracking = () => {
  if (!isClient) return;
  
  if (!isProduction) {
    console.log('Disabling all analytics and tracking services in development environment');
    
    // List of tracking services to block (domains and script identifiers)
    const trackingServices = [
      // Google services
      'google-analytics.com',
      'googletagmanager.com',
      'gtm.js',
      'gtag',
      'analytics.js',
      'ga.js',
      
      // Microsoft services
      'clarity.ms',
      'clarity-js',
      'c.clarity',
      
      // Facebook/Meta
      'connect.facebook.net',
      'facebook.com/tr',
      'fbevents.js',
      'fbq',
      
      // LinkedIn
      'linkedin.com/px',
      'snap.licdn.com',
      'linkedininsight',
      
      // Other common analytics
      'hotjar.com',
      'static.hotjar.com',
      'mixpanel.com',
      'segment.com',
      'segment.io',
      'amplitude.com',
      'fullstory.com',
      'logrocket',
      'heap-analytics',
      'mouseflow.com',
      'plausible.io',
      'matomo',
      'fathom',
      'splitbee',
      'posthog',
      
      // Marketing automation
      'hubspot.com',
      '_hsq',
      'marketo',
      'pardot',
      'salesforce',
      
      // Gozen
      'personalizer.gozen.io'
    ];
    
    // 1. Block script loading
    const originalCreateElement = document.createElement;
    document.createElement = function(tagName) {
      const element = originalCreateElement.call(document, tagName);
      
      if (tagName.toLowerCase() === 'script') {
        const originalSetAttribute = element.setAttribute;
        element.setAttribute = function(name, value) {
          if (name === 'src' && trackingServices.some(service => value.includes(service))) {
            console.log(`Blocked tracking script from loading: ${value}`);
            return element;
          }
          return originalSetAttribute.call(this, name, value);
        };
      }
      
      return element;
    };
    
    // 2. Block appending tracking scripts to DOM
    const originalAppendChild = Node.prototype.appendChild;
    Node.prototype.appendChild = function(node) {
      if (node.nodeName === 'SCRIPT' && node.src && 
          trackingServices.some(service => node.src.includes(service))) {
        console.log(`Blocked tracking script from being appended: ${node.src}`);
        return node;
      }
      return originalAppendChild.call(this, node);
    };
    
    // 3. Create stub/mock objects for common analytics services
    
    // Google Analytics / GTM
    window.ga = function() { 
      console.log('Google Analytics tracking disabled in development'); 
    };
    window.gtag = function() { 
      console.log('Google Tag Manager tracking disabled in development'); 
    };
    window.dataLayer = window.dataLayer || {
      push: function(data) {
        console.log('GTM dataLayer push blocked in development', data);
      }
    };
    
    // Facebook Pixel
    window.fbq = function() { 
      console.log('Facebook Pixel tracking disabled in development'); 
    };
    
    // Hotjar
    window.hj = function() { 
      console.log('Hotjar tracking disabled in development'); 
    };
    
    // Segment
    window.analytics = {
      track: function() { console.log('Segment tracking disabled in development'); },
      identify: function() { console.log('Segment identify disabled in development'); },
      page: function() { console.log('Segment page view disabled in development'); }
    };
    
    // Hubspot
    window._hsq = window._hsq || [];
    window._hsq.push = function() { 
      console.log('HubSpot tracking disabled in development'); 
    };
    
    console.log('All analytics and tracking services have been disabled in development environment');
  }
};