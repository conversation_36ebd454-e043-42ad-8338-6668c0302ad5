"use client";
import Link from "next/link";
import Image from "next/image";
import { motion } from "framer-motion";

// Updated testimonials without quotes and with revised content order
const testimonials = [
  {
    id: "startup",
    company: "Startup",
    title: "Move fast. Land bigger deals.",
    description:
      "Show customers and investors you take security seriously. With on-demand pentests, fast findings, and built-in retesting, startups use Capture The Bug to get compliant and build trust early.",
    buttonText: "Explore startup solutions",
    buttonLink: "/Company-size/Start-Up",
    image: "/images/Partly2.svg",
    imageAlt: "Startup Case Study",
    imagePosition: "left",
  },
  {
    id: "growing-teams",
    company: "Growing Teams",
    title: "More visibility. Less chaos.",
    description:
      "Scale your testing process with automated scheduling, real-time dashboards, and easy integration into your existing workflows. Mid-market teams use our platform to stay audit-ready and reduce risk as they grow.",
    buttonText: "Explore mid-market solutions",
    buttonLink: "/Company-size/Growing-Team",
    image: "/images/Lawvu-testimonial.png",
    imageAlt: "Growing Teams Case Study",
    imagePosition: "right",
  },
  {
    id: "enterprise",
    company: "Enterprise",
    title: "Enterprise-grade security. Startup-speed delivery.",
    description:
      "Run multiple pentests across business units, products, and regions-all under one platform. Get complete visibility, stakeholder reporting, and unlimited retesting-without the red tape.",
    buttonText: "Explore enterprise solutions",
    buttonLink: "/Company-size/Enterprise",
    image: "/images/Pay-sauce-test.svg",
    imageAlt: "Enterprise Case Study",
    imagePosition: "left",
  },
];

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.3,
      delayChildren: 0.2,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 40 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      type: "spring",
      damping: 25,
      stiffness: 100,
    },
  },
};

const fadeInUp = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      ease: "easeOut",
    },
  },
};

export default function PublicationsList() {
  return (
    <section className="w-full py-16 sm:py-20 md:py-24 bg-gradient-to-b from-tertiary-blue via-tertiary-blue to-secondary-blue/90 relative overflow-hidden">
      {/* Decorative lines and blobs */}
      <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-primary-blue/20 via-ctb-green-50/30 to-primary-blue/20"></div>
      <div className="absolute inset-0 bg-[url('/images/grid-pattern.svg')] bg-repeat opacity-5"></div>
      <div className="absolute -top-40 -right-40 w-80 h-80 bg-primary-blue/10 rounded-full blur-3xl"></div>
      <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-primary-blue/10 rounded-full blur-3xl"></div>

      <motion.div
        className="max-w-screen-2xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8 flex flex-col items-center relative z-10"
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.1 }}
        variants={containerVariants}
      >
        {/* Header */}
        <motion.div className="mb-12 sm:mb-16 md:mb-20 text-center" variants={fadeInUp}>
          <div className="max-w-5xl mx-auto relative">
            <span className="inline-block px-6 py-2 rounded-full bg-primary-blue/10 text-ctb-green-50 text-sm font-medium mb-4">
              TRUSTED BY INDUSTRY LEADERS
            </span>
            <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-[500] text-white mb-6 sm:mb-8 leading-tight">
              Built for Every Stage of {" "}
              <span className="text-ctb-green-50 font-[600] relative">
                Security Maturity
                <span className="absolute -bottom-1 left-0 w-full h-[3px] bg-ctb-green-50/60"></span>
              </span>
            </h2>
            <p className="text-base sm:text-lg md:text-xl text-ctb-bg-light/90 leading-relaxed max-w-3xl mx-auto">
              Whether you&apos;re just starting out or scaling security across global teams, Capture The Bug gives you always-on pentesting built to match your pace-no bottlenecks, no waiting for static reports.
            </p>
          </div>
        </motion.div>

        {/* Testimonial cards */}
        <div className="w-full md:px-4 lg:px-8 xl:px-16 space-y-16 sm:space-y-20 md:space-y-24">
          {testimonials.map((item) => (
            <motion.div
              key={item.id}
              variants={itemVariants}
              className={`flex flex-col ${item.imagePosition === "right" ? "lg:flex-row-reverse" : "lg:flex-row"} lg:items-stretch gap-8 sm:gap-10 md:gap-12 items-center`}
            >
              {/* Image */}
              <div className="w-full lg:w-5/12 flex justify-center items-center">
                <div className="relative w-full max-w-[340px] sm:max-w-md md:max-w-lg transition-all duration-700 group">
                  <div className="relative overflow-hidden">
                    <Image
                      src={item.image}
                      alt={item.imageAlt}
                      width={720}
                      height={450}
                      className="object-contain w-full h-auto transform group-hover:scale-[1.02] transition-transform duration-700"
                      quality={95}
                      priority={item.id === "startup"}
                      style={{ 
                        filter: 'drop-shadow(0px 8px 24px rgba(0, 0, 0, 0.2))',
                        mixBlendMode: 'lighten'
                      }}
                    />
                  </div>
                </div>
              </div>

              {/* Content */}
              <div className="w-full lg:w-7/12 flex flex-col p-8 sm:p-10 md:p-12 bg-gradient-to-br from-primary-blue/10 to-primary-blue/5 backdrop-blur-sm rounded-xl shadow-xl lg:justify-center border border-primary-blue/10 relative overflow-hidden group">
                <div className="absolute inset-0 bg-gradient-to-br from-white/[0.01] to-white/[0.02] opacity-0 group-hover:opacity-100 transition-opacity duration-1000"></div>
                <div className="absolute -right-20 -top-20 w-40 h-40 bg-primary-blue/5 rounded-full blur-2xl"></div>
                <div className="absolute top-1/2 right-0 w-1 h-16 bg-gradient-to-b from-ctb-green-50/0 via-ctb-green-50/60 to-ctb-green-50/0 transform -translate-y-1/2"></div>

                <div className="relative z-10">
                  <h3 className="text-2xl sm:text-3xl font-semibold text-white mb-4">
                    <span className="bg-clip-text text-transparent bg-gradient-to-r from-white to-ctb-bg-light">{item.company}</span>
                  </h3>
                  <h4 className="text-xl sm:text-2xl font-medium text-ctb-green-50 mb-3">{item.title}</h4>
                  <p className="text-base sm:text-lg text-ctb-bg-light/90 mb-8 leading-relaxed">{item.description}</p>

                  <Link href={item.buttonLink} className="relative inline-flex items-center group overflow-hidden">
                    <span className="relative z-10 inline-flex items-center bg-ctb-green-50 text-tertiary-blue hover:bg-ctb-green-50/90 px-6 py-3 rounded-lg font-medium transition-colors duration-300">
                      {item.buttonText}
                      <svg className="ml-2 w-5 h-5 transform group-hover:translate-x-1 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                      </svg>
                    </span>
                    <span className="absolute inset-0 w-full h-full bg-white/20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out"></span>
                  </Link>

                  {/* Decorative dots */}
                  <div className="absolute right-4 bottom-4 grid grid-cols-3 gap-1 opacity-50">
                    {[...Array(9)].map((_, i) => (
                      <div key={i} className="w-1 h-1 rounded-full bg-ctb-green-50" style={{ opacity: (i % 3) * 0.3 + 0.1 }} />
                    ))}
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* CTA */}
        {/* <motion.div variants={fadeInUp} className="mt-20 text-center">
          <Link
            href="/case-studies"
            className="inline-flex items-center justify-center bg-ctb-green-50 hover:bg-ctb-green-50/90 text-tertiary-blue font-semibold px-10 py-4 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-0.5 group relative overflow-hidden"
          >
            <span className="absolute inset-0 w-full h-full bg-white/20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out"></span>
            <span className="relative z-10">View All Case Studies</span>
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-3 transform group-hover:translate-x-1 transition-transform duration-300 relative z-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
            </svg>
          </Link>
        </motion.div> */}
      </motion.div>
    </section>
  );
}
