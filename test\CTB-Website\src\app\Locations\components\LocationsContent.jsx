import React from 'react';
import { motion } from 'framer-motion';
import { MapPin, Phone, Mail, Globe, ArrowRight, Building, Users, Shield, CheckCircle, ExternalLink } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';
import Button from '@/app/common/buttons/Button';

const LocationsContent = () => {
  // Animation variants for staggered animations
  const containerVariants = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    show: { y: 0, opacity: 1, transition: { duration: 0.5 } },
  };

  // Location data with SEO-optimized content
  const locations = [
    {
      country: "New Zealand",
      code: "NZ",
      cities: ["Auckland", "Wellington", "Christchurch", "Hamilton"],
      description: "Headquartered in New Zealand, our team offers penetration testing services built for Kiwi businesses - from startups to NZX Listed companies. We bring deep expertise in the NZ Privacy Act 2020, local compliance needs, and infrastructure-critical industries across both North and South Islands.",
      keyPoints: [
        "NZ Privacy Act 2020 & local data compliance readiness",
        "Local team of certified ethical hackers (OSCP, CREST)",
        "24/7 cyber support tailored for New Zealand time zones",
        "Specialized testing for critical infrastructure "
      ],
    },
    {
      country: "Australia",
      code: "AU",
      cities: ["Sydney", "Melbourne", "Brisbane", "Perth"],
      description: "Our Australian cybersecurity experts provide enterprise-grade penetration testing aligned with ACSC Essential Eight and the Security of Critical Infrastructure Act. We partner with fintech, healthcare, and critical service providers to ensure continuous protection and regulatory compliance.",
      keyPoints: [
        "Compliance with ACSC and Essential Eight maturity models",
        "Testing aligned with the Privacy Act 1988 and industry standards",
        "Sector-specific testing for finance, health, and government",
        "Continuous Vulnerability Assessment and Penetration Testing (VAPT)"
      ],
    },
    {
      country: "United States",
      code: "US",
      cities: ["New York", "San Francisco", "Chicago", "Austin"],
      description: "Our U.S. - based services focus on delivering cloud-native security testing and compliance support across SOC 2, PCI DSS, HIPAA, and NIST frameworks. From high-growth startups to global enterprises, we help companies harden systems and demonstrate security maturity.",
      keyPoints: [
        "SOC 2, PCI DSS, HIPAA, and NIST Cybersecurity Framework alignment",
        "Cloud security testing for AWS, Azure, and Google Cloud",
        "Advanced Red Team operations and adversary emulation",
        "CISO-ready reporting and compliance documentation"
      ],
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-b from-[#f8faff] to-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-[#062575] via-[#0B45DB] to-[#0B45DB] text-white py-16 md:py-24 px-4 relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-0 left-0 w-full h-full bg-[url('/images/global-cybersecurity-network-map.png')] bg-no-repeat bg-center bg-contain"></div>
        </div>
        
        <div className="max-w-7xl mx-auto relative z-10">
          <div className="flex flex-col md:flex-row items-center justify-between gap-8 md:gap-16">
            <div className="w-full md:w-1/2">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="space-y-6"
              >
                <h1 className="text-3xl md:text-5xl font-bold leading-tight">
                  Global Penetration Testing Services
                </h1>
                <p className="text-lg md:text-xl text-white/90 max-w-2xl">
                  Secure your digital assets with certified penetration testing tailored to regional compliance standards in Australia, New Zealand, and the United States - including ACSC, NZ Privacy Act, SOC 2, PCI DSS, and more.                </p>
                <div className="flex flex-wrap gap-4 pt-4">
                  <Button 
                    href="/Request-Demo" 
                    variant="success"
                    rightIcon={<ArrowRight className="w-4 h-4" />}
                  >
                    Schedule a Pentest     
                  </Button>
                  <Button 
                    href="/Company/Contact-Us" 
                    variant="secondary"
                    className="bg-white/10 border-white/30 text-white hover:bg-white/20"
                  >
                    Contact Us
                  </Button>
                </div>

                {/* Service highlights */}
                <div className="pt-6 grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="flex items-start">
                    <div className="bg-white/10 p-2 rounded-lg mr-3">
                      <Shield className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <h3 className="text-white font-medium mb-1">Compliance Expertise</h3>
                      <p className="text-white/80 text-sm">Deep alignment with ACSC, NZ Privacy Act, SOC 2, PCI DSS, and NIST frameworks.</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <div className="bg-white/10 p-2 rounded-lg mr-3">
                      <Users className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <h3 className="text-white font-medium mb-1">Certified Experts</h3>
                      <p className="text-white/80 text-sm">Team of OSCP, CREST, and CISSP-certified professionals experienced in cloud, infrastructure, and application security.</p>
                    </div>
                  </div>
                </div>
              </motion.div>
            </div>
            
            <div className="w-full md:w-1/2">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="flex justify-center"
              >
                <Image
                  src="/images/global-cybersecurity-network-map.png"
                  alt="Global coverage map showing locations in USA, Australia, and New Zealand"
                  width={800}
                  height={400}
                  className="w-full h-auto"
                  priority
                />
              </motion.div>
            </div>
          </div>
        </div>
      </section>

      {/* Global Presence Section */}
      <section className="py-16 md:py-24 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12 md:mb-16">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
            >
              <h2 className="text-2xl md:text-4xl font-bold text-gray-900 mb-4">Our Service Regions</h2>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto">
We deliver certified penetration testing and cybersecurity assessment services across New Zealand, Australia, and the United States. With a local-first mindset and 100% remote delivery capability, our experts ensure your organization meets regional compliance standards while staying ahead of threats.              </p>
            </motion.div>
          </div>

          <div className="relative p-4 md:p-8 bg-gradient-to-br from-slate-50 to-blue-50 rounded-3xl overflow-hidden mb-16">
            {/* Background elements */}
            <div className="absolute top-0 left-0 w-full h-full">
              <div className="absolute top-10 left-10 w-72 h-72 bg-blue-100/30 rounded-full blur-3xl"></div>
              <div className="absolute bottom-10 right-10 w-80 h-80 bg-indigo-100/30 rounded-full blur-3xl"></div>
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-blue-100/20 rounded-full blur-3xl"></div>
            </div>
            
          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="show"
            viewport={{ once: true }}
              className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8 relative z-10"
          >
            {locations.map((location) => (
              <motion.div
                key={location.country}
                variants={itemVariants}
                  whileHover={{ y: -8, transition: { duration: 0.3 } }}
                  className="backdrop-blur-md bg-white/70 border border-white/80 rounded-2xl overflow-hidden shadow-xl hover:shadow-2xl transition-all duration-500"
              >
                  <div className="h-2 bg-gradient-to-r from-[#062575] via-[#0B45DB] to-[#0B45DB]"></div>
                
                  <div className="p-8">
                    <div className="flex items-center justify-between mb-6">
                      <div className="flex items-center gap-3">
                        <div className="flex items-center justify-center w-12 h-12 bg-blue-50 rounded-xl">
                          <span className="text-[#0835A7] font-bold">{location.code}</span>
                        </div>
                        <h3 className="text-2xl font-bold text-gray-900">{location.country}</h3>
                      </div>
                  </div>
                  
                    <div className="mb-6">
                      <p className="text-gray-700 mb-6 leading-relaxed">{location.description}</p>
                    
                    <div className="flex flex-wrap gap-3 mb-6">
                      {location.cities.map((city) => (
                        <span
                          key={city}
                            className="inline-flex items-center text-sm text-gray-500"
                        >
                          <MapPin className="w-3 h-3 mr-1 text-gray-400" /> {city}
                        </span>
                      ))}
                    </div>
                  </div>
                  
                    <div className="space-y-3">
                      <h4 className="font-bold text-gray-900 flex items-center gap-2 mb-3">
                        <Shield className="w-5 h-5 text-[#0835A7]" /> Key Capabilities:
                      </h4>
                      <ul className="space-y-3">
                      {location.keyPoints.map((point, i) => (
                          <li key={i} className="flex items-start bg-blue-50/50 p-3 rounded-lg border border-blue-100/50">
                            <CheckCircle className="w-5 h-5 text-[#0835A7] mr-3 flex-shrink-0 mt-0.5" />
                            <span className="text-gray-700">{point}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                  
                    <div className="mt-8 pt-6 border-t border-gray-100">
                      <Link 
                        href={`/Locations/${location.code.toLowerCase()}`} 
                        className="inline-flex items-center text-[#0835A7] font-medium hover:text-[#062575] transition-colors"
                      >
                        Explore {location.country} Cybersecurity Services 
                        <ExternalLink className="ml-2 w-4 h-4" />
                      </Link>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
          </div>
        </div>
      </section>

      {/* Why Choose Our Global Services */}
      <section className="py-16 md:py-20 px-4 bg-gray-50">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="text-center mb-12"
          >
            <h2 className="text-2xl md:text-4xl font-bold text-gray-900 mb-4">Why Choose Our Global Services</h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Our international team delivers certified penetration testing tailored to regional compliance laws and global cybersecurity frameworks - ensuring consistent protection across borders.            </p>
          </motion.div>

          <div className="relative p-6 md:p-10 bg-gradient-to-br from-white to-slate-50 rounded-3xl overflow-hidden shadow-lg">
            {/* Background elements */}
            <div className="absolute top-0 right-0 w-96 h-96 bg-blue-100/20 rounded-full -translate-x-20 -translate-y-20 blur-3xl"></div>
            <div className="absolute bottom-0 left-0 w-80 h-80 bg-indigo-100/20 rounded-full translate-x-20 translate-y-20 blur-3xl"></div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 relative z-10">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.1 }}
                whileHover={{ y: -5, transition: { duration: 0.2 } }}
                className="relative backdrop-blur-sm bg-white/80 border border-white rounded-2xl shadow-lg overflow-hidden group"
              >
                <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-[#0835A7] to-[#0B45DB]"></div>
                <div className="p-8">
                  <div className="bg-gradient-to-br from-[#0835A7] to-[#062575] w-14 h-14 rounded-2xl flex items-center justify-center mb-6 shadow-md group-hover:shadow-blue-200 transition-all duration-300 transform group-hover:scale-110">
                    <Globe className="text-white w-6 h-6" />
              </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-4 group-hover:text-[#0835A7] transition-colors">Local Expertise, Global Standards</h3>
                  <p className="text-gray-600 leading-relaxed">
               Our security engineers understand regional compliance requirements - including NZ Privacy Act, ACSC, SOC 2, and NIST - while following global best practices for complete protection.
              </p>
                </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
                whileHover={{ y: -5, transition: { duration: 0.2 } }}
                className="relative backdrop-blur-sm bg-white/80 border border-white rounded-2xl shadow-lg overflow-hidden group"
              >
                <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-[#0835A7] to-[#0B45DB]"></div>
                <div className="p-8">
                  <div className="bg-gradient-to-br from-[#0835A7] to-[#062575] w-14 h-14 rounded-2xl flex items-center justify-center mb-6 shadow-md group-hover:shadow-blue-200 transition-all duration-300 transform group-hover:scale-110">
                    <Building className="text-white w-6 h-6" />
              </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-4 group-hover:text-[#0835A7] transition-colors">Industry-Specific Solutions</h3>
                  <p className="text-gray-600 leading-relaxed">
            We provide tailored penetration testing for finance, healthcare, technology, SaaS, and public sector clients, ensuring every test reflects your industry’s security priorities and risk profile.              </p>
                </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.3 }}
                whileHover={{ y: -5, transition: { duration: 0.2 } }}
                className="relative backdrop-blur-sm bg-white/80 border border-white rounded-2xl shadow-lg overflow-hidden group"
              >
                <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-[#0835A7] to-[#0B45DB]"></div>
                <div className="p-8">
                  <div className="bg-gradient-to-br from-[#0835A7] to-[#062575] w-14 h-14 rounded-2xl flex items-center justify-center mb-6 shadow-md group-hover:shadow-blue-200 transition-all duration-300 transform group-hover:scale-110">
                    <Users className="text-white w-6 h-6" />
              </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-4 group-hover:text-[#0835A7] transition-colors">24/7 Global Support</h3>
                  <p className="text-gray-600 leading-relaxed">
                    With globally distributed red team experts and remote-first delivery, we provide real-world attack simulation, executive-ready reporting, and responsive support -whenever and wherever you need it.              </p>
                </div>
            </motion.div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-16 md:py-24 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="bg-gradient-to-br from-[#062575] to-[#0B45DB] rounded-2xl overflow-hidden shadow-xl">
            <div className="p-8 md:p-12 flex flex-col md:flex-row items-center">
              <div className="w-full md:w-1/2 mb-8 md:mb-0">
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6 }}
                  className="text-white"
                >
                  <h2 className="text-2xl md:text-3xl font-bold mb-4">Ready to Secure Your Global Operations?</h2>
                  <p className="text-white/90 mb-6 text-lg">
              Connect with our security experts to learn how regionally compliant penetration testing can help secure your infrastructure whether you&apos;re based in Australia, New Zealand, the United States, or beyond.                  </p>
                  
                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <div className="bg-white/10 p-2 rounded-full">
                        <MapPin className="w-5 h-5 text-white" />
                      </div>
                      <span className="text-white/90">Based in New Zealand - operating across AU, NZ, and US</span>
                    </div>
                    
                    <div className="flex items-center gap-3">
                      <div className="bg-white/10 p-2 rounded-full">
                        <Mail className="w-5 h-5 text-white" />
                      </div>
                      <a href="mailto:<EMAIL>" className="text-white/90 hover:text-white transition-colors">
                        <EMAIL>
                      </a>
                    </div>
                    
                    <div className="flex items-center gap-3">
                      <div className="bg-white/10 p-2 rounded-full">
                        <Phone className="w-5 h-5 text-white" />
                      </div>
                      <a href="tel:+64221994320" className="text-white/90 hover:text-white transition-colors">
                        +64 22 199 4320
                      </a>
                    </div>
                  </div>
                </motion.div>
              </div>
              
              <div className="w-full md:w-1/2 md:pl-8">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                  className="bg-white p-6 md:p-8 rounded-xl shadow-lg"
                >
                  <h3 className="text-xl font-bold text-gray-900 mb-6">Get in Touch</h3>
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">First Name</label>
                        <input
                          type="text"
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent transition-all"
                          placeholder="Your first name"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Last Name</label>
                        <input
                          type="text"
                          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent transition-all"
                          placeholder="Your last name"
                        />
                      </div>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                      <input
                        type="email"
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent transition-all"
                        placeholder="<EMAIL>"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Company</label>
                      <input
                        type="text"
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent transition-all"
                        placeholder="Your company name"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Message</label>
                      <textarea
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent transition-all"
                        rows="3"
                        placeholder="How can we help you?"
                      ></textarea>
                    </div>
                    
                    <div>
                      <Button
                        variant="primary"
                        fullWidth={true}
                        className="mt-2"
                      >
                        Submit Request
                      </Button>
                    </div>
                  </div>
                </motion.div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 md:py-20 px-4 bg-gray-50">
        <div className="max-w-4xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="text-center mb-12"
          >
            <h2 className="text-2xl md:text-4xl font-bold text-gray-900 mb-4">Frequently Asked Questions</h2>
            <p className="text-lg text-gray-600">
              Common questions about our global penetration testing services
            </p>
          </motion.div>

          <div className="relative p-6 md:p-10 bg-gradient-to-br from-white to-slate-50 rounded-3xl overflow-hidden shadow-lg">
            {/* Background elements */}
            <div className="absolute top-0 right-0 w-64 h-64 bg-blue-50/50 rounded-full -translate-x-20 -translate-y-20 blur-3xl"></div>
            <div className="absolute bottom-0 left-0 w-64 h-64 bg-indigo-50/50 rounded-full translate-x-20 translate-y-20 blur-3xl"></div>
            
            <div className="space-y-6 relative z-10">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.1 }}
                className="backdrop-blur-sm bg-white/80 border border-white/80 p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <div className="flex items-start">
                  <div className="bg-gradient-to-br from-[#0835A7] to-[#062575] p-3 rounded-lg text-white mr-4 flex-shrink-0">
                    <span className="font-bold">Q</span>
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-gray-900 mb-3">How do you deliver services across multiple regions?</h3>
                    <div className="mt-4 pl-6 border-l-2 border-blue-100">
              <p className="text-gray-600">
                Our services are delivered through a global team of security experts, led by our New Zealand headquarters and supported by remote-first operations. Our PTaaS (Penetration Testing as a Service) platform ensures consistent quality, scalability, and reporting across all regions - without compromising on security standards.
              </p>
                    </div>
                  </div>
                </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
                className="backdrop-blur-sm bg-white/80 border border-white/80 p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <div className="flex items-start">
                  <div className="bg-gradient-to-br from-[#0835A7] to-[#062575] p-3 rounded-lg text-white mr-4 flex-shrink-0">
                    <span className="font-bold">Q</span>
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-gray-900 mb-3">Can you handle multi-regional compliance requirements?</h3>
                    <div className="mt-4 pl-6 border-l-2 border-blue-100">
              <p className="text-gray-600">
                        Absolutely. We align our penetration testing approach with global and regional compliance mandates such as SOC 2, PCI DSS, HIPAA, NZ Privacy Act, ACSC Essential Eight, and GDPR. Each engagement is tailored to your regulatory landscape -ensuring audit-readiness across jurisdictions.              </p>
                    </div>
                  </div>
                </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.3 }}
                className="backdrop-blur-sm bg-white/80 border border-white/80 p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <div className="flex items-start">
                  <div className="bg-gradient-to-br from-[#0835A7] to-[#062575] p-3 rounded-lg text-white mr-4 flex-shrink-0">
                    <span className="font-bold">Q</span>
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-gray-900 mb-3">Do you offer remote penetration testing for international clients?</h3>
                    <div className="mt-4 pl-6 border-l-2 border-blue-100">
              <p className="text-gray-600">
                              Yes - our PTaaS platform is built for remote, scalable testing. Whether you&apos;re in Australia, the U.S., or anywhere globally, we assess your systems securely and deliver results via a real-time client portal. All tests are conducted remotely by our certified pentesters, with zero compromise on depth or coverage.              </p>
                    </div>
                  </div>
                </div>
            </motion.div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 md:py-24 px-4">
        <div className="max-w-5xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="relative overflow-hidden rounded-3xl"
          >
            <div className="absolute inset-0 bg-gradient-to-r from-[#062575] to-[#0B45DB] opacity-95"></div>
            
            {/* Background decoration */}
            <div className="absolute top-0 right-0 w-96 h-96 bg-white/5 rounded-full -translate-x-20 -translate-y-20 blur-3xl"></div>
            <div className="absolute bottom-0 left-0 w-80 h-80 bg-white/5 rounded-full translate-x-20 translate-y-20 blur-3xl"></div>
            <div className="absolute top-1/2 left-1/3 w-64 h-64 bg-blue-400/10 rounded-full blur-3xl"></div>
            
            <div className="relative z-10 p-10 md:p-16 backdrop-blur-sm">
              <motion.div 
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.2 }}
              >
                <h2 className="text-3xl md:text-5xl font-bold text-white mb-6">Ready to Secure Your Digital Assets Globally?</h2>
                <p className="text-xl text-white/90 mb-10 max-w-2xl mx-auto leading-relaxed">
                  Partner with Capture The Bug for comprehensive penetration testing services across Australia, New Zealand, and the United States.
                </p>
                
                <div className="flex flex-col sm:flex-row gap-6 justify-center">
                  <Button 
                    href="/Request-Demo" 
                    variant="primary"
                    size="lg"
                    className="bg-white text-[#0835A7] hover:bg-blue-50 px-8 py-4 text-lg shadow-xl hover:shadow-2xl transition-all duration-300 border-0"
                    rightIcon={<ArrowRight className="w-5 h-5" />}
                  >
                    Request a Demo
                  </Button>
                  <Button 
                    href="/Company/Contact-Us" 
                    variant="secondary"
                    size="lg"
                    className="backdrop-blur-md bg-white/10 hover:bg-white/20 border border-white/30 text-white px-8 py-4 text-lg shadow-xl hover:shadow-2xl transition-all duration-300"
                  >
                    Contact Our Team
                  </Button>
                </div>
                
                <div className="mt-10 pt-10 border-t border-white/10 flex flex-wrap justify-center gap-8">
                  <div className="flex items-center">
                    <Shield className="text-white/70 w-5 h-5 mr-2" />
                    <span className="text-white/70">Enterprise-grade security</span>
                  </div>
                  <div className="flex items-center">
                    <CheckCircle className="text-white/70 w-5 h-5 mr-2" />
                    <span className="text-white/70">Global compliance expertise</span>
                  </div>
                  <div className="flex items-center">
                    <Users className="text-white/70 w-5 h-5 mr-2" />
                    <span className="text-white/70">24/7 Delivery Model</span>
                  </div>
                </div>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default LocationsContent; 