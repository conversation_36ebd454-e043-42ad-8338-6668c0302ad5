import React from 'react';
import Image from 'next/image';

const Card = ({ imageUrl, title, description }) => (
  <div className="p-6 rounded-lg">
    <div className="flex items-center mb-4">
      <div className="w-10 h-10 md:w-16 md:h-16 mr-4 overflow-hidden flex items-center justify-center">
        <Image src={imageUrl} alt={`Icon representing ${title}`} className="object-contain w-full h-full" width={64} height={64} />
      </div>
      <h3 className="text-lg md:text-xl font-semibold">{title}</h3>
    </div>
    <p className="text-gray-800 md:pl-20">{description}</p>
  </div>
);

const StandsOut = () => {
  const features = [
    {
      imageUrl: "/images/company_feature1.png",
      title: "Comprehensive VAPT Solutions",
      description: "Our platform provides end-to-end Vulnerability Assessment and Penetration Testing (VAPT) to identify and mitigate security threats across your digital assets. From web applications to cloud infrastructures, we cover it all with precision."
    },
    {
      imageUrl: "/images/company_feature2.png",
      title: "Real-Time Vulnerability Insights",
      description: "Experience the advantage of real-time vulnerability reporting. Address potential threats immediately, allowing your development teams to patch security gaps as soon as they are discovered."
    },
    {
      imageUrl: "/images/company_feature3.png",
      title: "Scalable Testing Solutions",
      description: "Adapt your security testing to match your growth. Our scalable services accommodate your evolving needs, from startups to large enterprises, ensuring you're always protected against emerging threats."
    },
    {
      imageUrl: "/images/company_feature3.png",
      title: "Seamless Integration and Reporting",
      description: "Integrate security effortlessly with our user-friendly platform. Receive detailed, real-time reports and actionable insights, enabling your team to quickly address and remediate vulnerabilities, ensuring compliance with industry standards like ISO 27001, SOC 2, GDPR, CIS, and HIPAA."
    }
  ];

  return (
    <section className="bg-gray-50 px-8 md:px-20 py-20">
      <h1 className="text-3xl md:text-5xl font-bold text-center mb-6 py-2">
        Why Our Platform Stands Out
      </h1>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-16 mt-10">
        {features.map((feature, index) => (
          <Card key={index} {...feature} />
        ))}
      </div>
    </section>
  );
};

export default StandsOut;
