"use client";
import React, { useState, useEffect } from 'react';
import { usePathname } from 'next/navigation';
import ContentSection from './ContentSection';
import HeaderSection from './HeaderSection';
import Image from 'next/image';
// BreadcrumbNavigation can be implemented or imported as needed

const FullBlogView = ({ headerSection, children, breadcrumbs, blogTitle, toc: manualToc }) => {
  const pathname = usePathname();
  const [autoGeneratedToc, setAutoGeneratedToc] = useState([]);
  const [activeSection, setActiveSection] = useState("");

  // Use auto-generated TOC if available, otherwise fall back to manual TOC
  const toc = autoGeneratedToc.length > 0 ? autoGeneratedToc : manualToc;

  // Scroll offset to account for fixed navbar (same as privacy policy)
  const SCROLL_OFFSET = 128;

  // Extract blog title from URL path if not provided
  const extractTitleFromPath = (path) => {
    const segments = path.split('/');
    const blogSlug = segments[segments.length - 1];
    return blogSlug
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  const title = blogTitle || extractTitleFromPath(pathname);

  // Generate breadcrumbs if not provided
  const blogBreadcrumbs = breadcrumbs || [
    {
      name: "Home",
      url: "/",
      iconKey: "home",
      description: "Return to homepage"
    },
    {
      name: "Blogs",
      url: "/blogs",
      iconKey: "blogs",
      description: "Cybersecurity insights and penetration testing guidance"
    },
    {
      name: title,
      url: pathname,
      current: true,
      description: `${title} - Expert cybersecurity insights and penetration testing guidance`
    }
  ];

  // Sidebar content (image, TOC, author, related)
  const { imageUrl, author, relatedArticles } = headerSection || {};

  // Set up intersection observer for active section tracking
  useEffect(() => {
    if (!toc || toc.length === 0) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setActiveSection(entry.target.id);
          }
        });
      },
      { rootMargin: `-${SCROLL_OFFSET}px 0px -80% 0px`, threshold: 0 }
    );

    // Collect all section IDs from TOC
    const collectIds = (items) => {
      let ids = [];
      items.forEach(item => {
        ids.push(item.id);
        if (item.subItems) {
          ids = ids.concat(collectIds(item.subItems));
        }
      });
      return ids;
    };

    const allIds = collectIds(toc);

    allIds.forEach((id) => {
      const element = document.getElementById(id);
      if (element) {
        observer.observe(element);
      }
    });

    return () => observer.disconnect();
  }, [toc, SCROLL_OFFSET]);

  // Callback to receive auto-generated TOC from ContentSection
  const handleTocGenerated = (generatedToc) => {
    setAutoGeneratedToc(generatedToc);
  };

  // Handle TOC link clicks with smooth scrolling
  const handleTocClick = (e, targetId) => {
    e.preventDefault();
    // Wait a moment for any dynamic content to render
    setTimeout(() => {
      // Try to find element by ID first
      let targetElement = document.getElementById(targetId);
      // Check if element is actually visible (has dimensions)
      if (targetElement) {
        const rect = targetElement.getBoundingClientRect();
        // If element has zero dimensions, it's likely in a hidden container (lg:hidden on desktop)
        if (rect.width === 0 && rect.height === 0) {
          targetElement = null; // Force search for visible element
        }
      }
      // If element not found or not visible, try alternative approaches
      if (!targetElement) {
        // Search for visible elements with the same ID or text content
        const isDesktop = window.innerWidth >= 1024;
        if (isDesktop) {
          // On desktop, look for elements NOT in lg:hidden containers
          const allHeadings = document.querySelectorAll('h1, h2, h3');
          const targetText = targetId.replace(/-/g, ' ');
          for (let heading of allHeadings) {
            const rect = heading.getBoundingClientRect();
            const headingText = heading.textContent?.toLowerCase() || '';
            if (rect.width > 0 && rect.height > 0 && 
                (heading.id === targetId || headingText.includes(targetText.toLowerCase()))) {
              targetElement = heading;
              break;
            }
          }
        } else {
          // Mobile: use original search logic
          const allHeadings = document.querySelectorAll('h1, h2, h3');
          const targetText = targetId.replace(/-/g, ' ');
          for (let heading of allHeadings) {
            const headingText = heading.textContent?.toLowerCase() || '';
            if (headingText.includes(targetText.toLowerCase())) {
              targetElement = heading;
              break;
            }
          }
        }
      }
      if (targetElement) {
        // Check if we're on desktop or mobile
        const isDesktop = window.innerWidth >= 1024; // lg breakpoint
        // Try to find if element is inside any scrollable containers
        let scrollableParent = targetElement.parentElement;
        while (scrollableParent) {
          const style = window.getComputedStyle(scrollableParent);
          if (style.overflow === 'auto' || style.overflow === 'scroll' || style.overflowY === 'auto' || style.overflowY === 'scroll') {
            break;
          }
          scrollableParent = scrollableParent.parentElement;
        }
        if (isDesktop) {
          // Method 1: Simple scrollIntoView with block center
          targetElement.scrollIntoView({ 
            behavior: 'smooth', 
            block: 'center',
            inline: 'nearest'
          });
          setTimeout(() => {
            const afterRect = targetElement.getBoundingClientRect();
            // If element is now visible, adjust for header
            if (afterRect.top >= 0 && afterRect.top <= window.innerHeight) {
              const adjustment = afterRect.top - 150; // Move element 150px from top
              if (Math.abs(adjustment) > 10) { // Only adjust if significant
                window.scrollBy({
                  top: adjustment,
                  behavior: 'smooth'
                });
              }
            }
          }, 100);
        } else {
          const offset = 120;
          const rect = targetElement.getBoundingClientRect();
          const currentScrollY = window.pageYOffset;
          const elementAbsoluteTop = rect.top + currentScrollY;
          const targetScrollPosition = elementAbsoluteTop - offset;
          window.scrollTo({
            top: targetScrollPosition,
            behavior: 'smooth'
          });
        }
        setTimeout(() => {
          const finalRect = targetElement.getBoundingClientRect();
          // If scroll didn't work properly, try scrollIntoView as fallback
          if (finalRect.top > window.innerHeight - 100 || finalRect.top < 0) {
            targetElement.scrollIntoView({
              behavior: 'smooth',
              block: 'start'
            });
            setTimeout(() => {
              if (isDesktop) {
                window.scrollBy({
                  top: -50,
                  behavior: 'smooth'
                });
              }
            }, 100);
          }
        }, 800);
      } else {
        // As a fallback, try to scroll to the main content area
        const mainContent = document.querySelector('.bg-white.sm\\:mx-8, .bg-white');
        if (mainContent) {
          mainContent.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      }
    }, 50); // Small delay to ensure DOM is ready
  };

  return (
    <div className="min-h-screen flex flex-col">
      {/* Hero Section with integrated breadcrumbs */}
      <HeaderSection
        {...headerSection}
        title={title}
        breadcrumbs={null /* Add breadcrumbs here if needed */}
        toc={null} // Remove TOC from header
      />
      
      {/* Mobile Layout: TOC first, then content */}
      <div className="lg:hidden w-full max-w-7xl mx-auto px-6 pb-8 flex flex-col gap-6 bg-white">
        {/* TOC above blog content for mobile */}
        {toc && toc.length > 0 && (
          <div className="bg-gray-50 rounded-xl shadow-sm p-4 max-h-[400px] overflow-y-auto custom-scrollbar">
            <h3 className="text-lg font-bold text-[var(--color-dark-blue)] mb-4">Table of Contents</h3>
            <nav className="space-y-1">
              {toc.map((item) => {
                const isActive = activeSection === item.id;
                return (
                  <div key={item.id}>
                    <a
                      href={`#${item.id}`}
                      onClick={(e) => handleTocClick(e, item.id)}
                      className={`block py-2 px-3 rounded-lg transition-all duration-200 text-sm font-medium ${
                        isActive
                          ? 'text-[var(--color-blue)] bg-[var(--color-blue)]/10'
                          : 'text-gray-600 hover:text-[var(--color-dark-blue)] hover:bg-gray-100'
                      }`}
                    >
                      {item.text}
                    </a>
                    {/* Sub-items (h2) */}
                    {item.subItems && item.subItems.length > 0 && (
                      <div className="ml-4 mt-1 space-y-1">
                        {item.subItems.map((subItem) => {
                          const isSubActive = activeSection === subItem.id;
                          return (
                            <div key={subItem.id}>
                              <a
                                href={`#${subItem.id}`}
                                onClick={(e) => handleTocClick(e, subItem.id)}
                                className={`block py-1 px-2 rounded-lg transition-all duration-200 text-xs font-medium ${
                                  isSubActive
                                    ? 'text-[var(--color-blue)] bg-[var(--color-blue)]/10'
                                    : 'text-gray-500 hover:text-[var(--color-dark-blue)] hover:bg-gray-100'
                                }`}
                              >
                                {subItem.text}
                              </a>
                              {/* Sub-sub-items (h3) */}
                              {subItem.subItems && subItem.subItems.length > 0 && (
                                <div className="ml-4 mt-1 space-y-1">
                                  {subItem.subItems.map((subSubItem) => {
                                    const isSubSubActive = activeSection === subSubItem.id;
                                    return (
                                      <a
                                        key={subSubItem.id}
                                        href={`#${subSubItem.id}`}
                                        onClick={(e) => handleTocClick(e, subSubItem.id)}
                                        className={`block py-1 px-2 rounded-lg transition-all duration-200 text-xs font-medium ${
                                          isSubSubActive
                                            ? 'text-[var(--color-blue)] bg-[var(--color-blue)]/10'
                                            : 'text-gray-400 hover:text-[var(--color-dark-blue)] hover:bg-gray-100'
                                        }`}
                                      >
                                        {subSubItem.text}
                                      </a>
                                    );
                                  })}
                                </div>
                              )}
                            </div>
                          );
                        })}
                      </div>
                    )}
                  </div>
                );
              })}
            </nav>
          </div>
        )}
        
        {/* Blog content for mobile */}
        <div className="flex-1 min-w-0">
          <ContentSection toc={toc} onTocGenerated={handleTocGenerated}>{children}</ContentSection>
        </div>
        
        {/* Image, Author, and Related Articles below blog content */}
        {imageUrl && (
          <div className="w-full aspect-[4/3] rounded-xl overflow-hidden shadow-lg bg-white border border-[var(--color-yellow)] flex items-center justify-center">
            <Image
              src={imageUrl}
              alt={title || "Blog post featured image"}
              className="object-contain w-full h-full"
              width={340}
              height={255}
              priority
            />
          </div>
        )}
        {author && (
          <div className="bg-gray-50 rounded-2xl p-6 border border-[var(--color-yellow)]">
            <h3 className="text-lg font-semibold text-[var(--color-dark-blue)] mb-4">About the Author</h3>
            <div className="flex items-center gap-3 mb-3">
              <div className="w-12 h-12 bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-dark-blue)] rounded-full flex items-center justify-center">
                <span className="text-white font-bold text-xl">{author.initials || author.name[0]}</span>
              </div>
              <div>
                <p className="font-semibold text-[var(--color-dark-blue)]">{author.name}</p>
                <p className="text-sm text-[var(--foreground-secondary)]">{author.role}</p>
              </div>
            </div>
            <p className="text-sm text-[var(--foreground-secondary)]">{author.bio}</p>
          </div>
        )}
        {relatedArticles && relatedArticles.length > 0 && (
          <div className="bg-gray-50 rounded-2xl p-6 border border-[var(--color-yellow)]">
            <h3 className="text-lg font-semibold text-[var(--color-dark-blue)] mb-4">Related Articles</h3>
            <div className="space-y-4">
              {relatedArticles.map((article, index) => (
                <a
                  key={index}
                  href={article.link}
                  className="block group"
                >
                  <div className="flex gap-3">
                    <Image
                      src={article.image}
                      alt={article.title}
                      width={60}
                      height={40}
                      className="rounded-lg object-cover border border-[var(--color-yellow)]"
                    />
                    <div>
                      <h4 className="text-sm font-semibold text-[var(--color-dark-blue)] group-hover:text-[var(--color-blue)] transition-colors line-clamp-2">
                        {article.title}
                      </h4>
                      <p className="text-xs text-[var(--foreground-secondary)]">
                        {article.date} • {article.readTime}
                      </p>
                    </div>
                  </div>
                </a>
              ))}
            </div>
          </div>
        )}
      </div>
      
      {/* Desktop Layout: Three columns */}
      <div className="hidden lg:flex flex-col lg:flex-row gap-2 w-full max-w-7xl mx-auto bg-white">
        {/* Left Sidebar - TOC only */}
        <aside className="hidden lg:flex flex-col w-[250px] flex-shrink-0 pt-6">
          {toc && toc.length > 0 && (
            <div className="sticky top-32 bg-gray-50 rounded-xl shadow-sm p-6 max-h-[calc(100vh-10rem)] overflow-y-auto custom-scrollbar">
              <h3 className="text-lg font-bold text-[var(--color-dark-blue)] mb-4">Table of Contents</h3>
              <nav className="space-y-1">
                {toc.map((item) => {
                  const isActive = activeSection === item.id;
                  return (
                    <div key={item.id}>
                      <a
                        href={`#${item.id}`}
                        onClick={(e) => {
                          handleTocClick(e, item.id);
                        }}
                        className={`block py-2 px-3 rounded-lg transition-all duration-200 text-sm font-medium ${
                          isActive
                            ? 'text-[var(--color-blue)] bg-[var(--color-blue)]/10'
                            : 'text-gray-600 hover:text-[var(--color-dark-blue)] hover:bg-gray-100'
                        }`}
                      >
                        {item.text}
                      </a>
                      {/* Sub-items (h2) */}
                      {item.subItems && item.subItems.length > 0 && (
                        <div className="ml-4 mt-1 space-y-1">
                          {item.subItems.map((subItem) => {
                            const isSubActive = activeSection === subItem.id;
                            return (
                              <div key={subItem.id}>
                                <a
                                  href={`#${subItem.id}`}
                                  onClick={(e) => {
                                    handleTocClick(e, subItem.id);
                                  }}
                                  className={`block py-1 px-2 rounded-lg transition-all duration-200 text-xs font-medium ${
                                    isSubActive
                                      ? 'text-[var(--color-blue)] bg-[var(--color-blue)]/10'
                                      : 'text-gray-500 hover:text-[var(--color-dark-blue)] hover:bg-gray-100'
                                  }`}
                            >
                              {subItem.text}
                            </a>
                                {/* Sub-sub-items (h3) */}
                                {subItem.subItems && subItem.subItems.length > 0 && (
                                  <div className="ml-4 mt-1 space-y-1">
                                    {subItem.subItems.map((subSubItem) => {
                                      const isSubSubActive = activeSection === subSubItem.id;
                                      return (
                                        <a
                                          key={subSubItem.id}
                                          href={`#${subSubItem.id}`}
                                          onClick={(e) => {
                                            handleTocClick(e, subSubItem.id);
                                          }}
                                          className={`block py-1 px-2 rounded-lg transition-all duration-200 text-xs font-medium ${
                                            isSubSubActive
                                              ? 'text-[var(--color-blue)] bg-[var(--color-blue)]/10'
                                              : 'text-gray-400 hover:text-[var(--color-dark-blue)] hover:bg-gray-100'
                                          }`}
                                        >
                                          {subSubItem.text}
                                        </a>
                                      );
                                    })}
                                  </div>
                                )}
                              </div>
                            );
                          })}
                        </div>
                      )}
                    </div>
                  );
                })}
              </nav>
            </div>
          )}
        </aside>
        
        {/* Main Content - Middle */}
        <div className="flex-1 min-w-0">
          <ContentSection toc={toc} onTocGenerated={handleTocGenerated}>{children}</ContentSection>
        </div>
        
        {/* Right Sidebar - Image, Author, Related Articles */}
        <aside className="hidden lg:flex flex-col w-[250px] flex-shrink-0 pt-6 gap-4">
          {/* Image at the top */}
          {imageUrl && (
            <div className="w-full aspect-[4/3] rounded-xl overflow-hidden shadow-lg bg-white border border-[var(--color-yellow)] flex items-center justify-center">
              <Image
                src={imageUrl}
                alt={title || "Blog post featured image"}
                className="object-contain w-full h-full"
                width={230}
                height={172}
                priority
              />
            </div>
          )}
          {/* Author Info (optional, if provided) */}
          {author && (
            <div className="bg-gray-50 rounded-2xl p-6 border border-[var(--color-yellow)]">
              <h3 className="text-lg font-semibold text-[var(--color-dark-blue)] mb-4">About the Author</h3>
              <div className="flex items-center gap-3 mb-3">
                <div className="w-12 h-12 bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-dark-blue)] rounded-full flex items-center justify-center">
                  <span className="text-white font-bold text-xl">{author.initials || author.name[0]}</span>
                </div>
                <div>
                  <p className="font-semibold text-[var(--color-dark-blue)]">{author.name}</p>
                  <p className="text-sm text-[var(--foreground-secondary)]">{author.role}</p>
                </div>
              </div>
              <p className="text-sm text-[var(--foreground-secondary)]">{author.bio}</p>
            </div>
          )}
          {/* Related Articles (optional, if provided) */}
          {relatedArticles && relatedArticles.length > 0 && (
            <div className="bg-gray-50 rounded-2xl p-6 border border-[var(--color-yellow)]">
              <h3 className="text-lg font-semibold text-[var(--color-dark-blue)] mb-4">Related Articles</h3>
              <div className="space-y-4">
                {relatedArticles.map((article, index) => (
                  <a
                    key={index}
                    href={article.link}
                    className="block group"
                  >
                    <div className="flex gap-3">
                      <Image
                        src={article.image}
                        alt={article.title}
                        width={50}
                        height={35}
                        className="rounded-lg object-cover border border-[var(--color-yellow)]"
                      />
                      <div>
                        <h4 className="text-sm font-semibold text-[var(--color-dark-blue)] group-hover:text-[var(--color-blue)] transition-colors line-clamp-2">
                          {article.title}
                        </h4>
                        <p className="text-xs text-[var(--foreground-secondary)]">
                          {article.date} • {article.readTime}
                        </p>
                      </div>
                    </div>
                  </a>
                ))}
              </div>
            </div>
          )}
        </aside>
      </div>
    </div>
  );
};

export default FullBlogView;