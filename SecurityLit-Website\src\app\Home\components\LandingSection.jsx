"use client"

import React, { useState, useEffect } from 'react';
import { Download, ArrowRight, Shield, CheckCircle, Users, Cloud } from 'lucide-react';
import Image from 'next/image';

export default function Landing() {
  const locations = ['New Zealand', 'India', 'Australia', 'USA'];
  const [currentContentIndex, setCurrentContentIndex] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);

 const contentVariations = [
  {
    statText: "6 out of 10 companies flunk their first security audit.",
    questionText: "Would your business pass or end up on the fail list?"
  },
  {
    statText: "Data breaches cost companies an average of $4.45 million in 2023.",
    questionText: "Can your business take that kind of hit and keep going?"
  },
  {
    statText: "Human error is behind 95% of successful cyber attacks.",
    questionText: "Is your team trained to spot threats or trigger them?"
  },
  {
    statText: "Nearly half of all cyberattacks target small and mid-sized businesses.",
    questionText: "Still think you're too small to be a target?"
  },
  {
    statText: "Ransomware attacks jumped 41% last year and they're not slowing down.",
    questionText: "If an attack lands today, do you fight back—or freeze?"
  }
];


  useEffect(() => {
    const interval = setInterval(() => {
      setIsTransitioning(true);
      
      setTimeout(() => {
        setCurrentContentIndex((prev) => (prev + 1) % contentVariations.length);
        setIsTransitioning(false);
      }, 300);
    }, 9000); 

    return () => clearInterval(interval);
  }, []);

  const currentContent = contentVariations[currentContentIndex];

  return (
    <section className="relative w-full min-h-screen bg-[var(--bg-light-blue)] py-16 lg:py:28 ">
      <div className="container mx-auto mt-16 sm:mt-26 max-w-7xl px-4 lg:px-8 relative z-10">
        <div className="grid lg:grid-cols-2 gap-12 lg:gap-16 items-center">
          
          {/* Left Column: Content */}
          <div className="space-y-8">
            <div className="space-y-6">
              <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-5xl font-bold text-[var(--color-dark-blue)] leading-tight">
                Enterprise-Grade <br />
                <span className="text-[var(--color-blue)]">Cybersecurity Consulting</span>
              </h1>
              
              <h2 className="text-xl md:text-2xl font-semibold text-yellow-500">
                Expert AWS & Azure Configuration Solutions
              </h2>
              
              <p className="text-base md:text-lg text-gray-600 max-w-xl">
                Strategic cybersecurity leadership and hands-on security expertise. From vCISO services to advanced threat simulation, we strengthen your security posture across all business dimensions.
              </p>
            </div>

            {/* Location Tags */}
            <div className="flex flex-wrap items-center gap-2 md:gap-3">
              <span className="text-xs md:text-sm font-medium text-gray-500">Serving:</span>
              {locations.map((loc, index) => (
                <span 
                  key={loc} 
                  className="px-3 py-1 bg-white border border-gray-200 text-gray-700 rounded-full text-xs md:text-sm hover:bg-gray-50 transition-colors cursor-pointer shadow-sm"
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  {loc}
                </span>
              ))}
              <span className="px-3 py-1 bg-blue-600 text-white rounded-full text-xs md:text-sm font-semibold shadow-sm">
                + more
              </span>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4">
              <button className="group bg-blue-600 text-white px-8 py-4 rounded-lg font-semibold text-base hover:bg-blue-700 transition-all duration-300 flex items-center justify-center gap-2 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                Start Configuration 
                <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
              </button>
              
              <button className="group bg-white text-blue-600 border-2 border-yellow-500 px-8 py-4 rounded-lg font-semibold text-base hover:border-blue-300 hover:bg-blue-50 transition-all duration-300 flex items-center justify-center gap-2 shadow-sm hover:shadow-md">
                <Download className="w-4 h-4" />
                Download Service Overview
              </button>
            </div>
          </div>

          {/* Right Column: Cards */}
          <div className="space-y-6 lg:pl-8">
            
            {/* Security Alert Card */}
            <div className="relative">
              <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-8 relative overflow-hidden"> 
                {/* Background Overlay Image
                <Image
                  src="/images/geometric-pattern.png" // change this path to your overlay image
                  alt="Background Overlay"
                  layout="fill"
                  objectFit="contain"
                  className="absolute inset-0 opacity-100 pointer-events-none"
                  priority
                /> */}

                {/* Logo */}
                <div className="flex justify-center mb-6 relative z-10">
                  <Image 
                    src="/images/securitylit_logo.png" 
                    alt="Cybersecurity Protection" 
                    width={50} 
                    height={50}
                    className="rounded-lg"
                    priority
                  />
                </div>

                {/* Content */}
                <div className={`text-center space-y-4 relative z-10 transition-all duration-500 ease-in-out transform ${
                  isTransitioning ? 'opacity-0 translate-y-4 scale-95' : 'opacity-100 translate-y-0 scale-100'
                }`}>
                  <p className="text-gray-900 max-w-2xl font-bold text-xl leading-tight">
                    {currentContent.statText}
                  </p>
                  <p className="text-blue-600 font-semibold text-lg">
                    {currentContent.questionText}
                  </p>
                </div>
              </div>
            </div>

            {/* Statistics Card */}
            <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-8">
              <div className="grid grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="w-12 h-12 bg-blue-600 rounded-xl flex items-center justify-center mx-auto mb-3">
                    <Cloud className="w-6 h-6 text-white" />
                  </div>
                  <div className="text-2xl font-bold text-yellow-500 mb-1">500+</div>
                  <div className="text-sm text-gray-600 font-medium">Projects Delivered</div>
                </div>
                
                <div className="text-center">
                  <div className="w-12 h-12 bg-blue-600 rounded-xl flex items-center justify-center mx-auto mb-3">
                    <CheckCircle className="w-6 h-6 text-white" />
                  </div>
                  <div className="text-2xl font-bold text-yellow-500 mb-1">99.9%</div>
                  <div className="text-sm text-gray-600 font-medium">Success Rate</div>
                </div>
                
                <div className="text-center">
                  <div className="w-12 h-12 bg-blue-600 rounded-xl flex items-center justify-center mx-auto mb-3">
                    <Shield className="w-6 h-6 text-white" />
                  </div>
                  <div className="text-2xl font-bold text-yellow-500 mb-1">24/7</div>
                  <div className="text-sm text-gray-600 font-medium">Expert Support</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}