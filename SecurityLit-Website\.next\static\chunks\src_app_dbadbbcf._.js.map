{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/common/components/BreadcrumbNavigation.jsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState } from 'react';\r\nimport Link from 'next/link';\r\nimport { ChevronRight, Home, FileText, Users, Settings, MapPin, Shield, Code, Smartphone, Globe, DollarSign, Building, Phone, Download } from 'lucide-react';\r\nimport { motion } from 'framer-motion';\r\n\r\n/**\r\n * Truncated Text Component for Mobile Breadcrumbs\r\n * Shows truncated text on mobile with click to expand functionality\r\n */\r\nconst TruncatedText = ({ text, maxLength = 25, onExpansionChange }) => {\r\n  const [isExpanded, setIsExpanded] = useState(false);\r\n\r\n  const handleToggle = () => {\r\n    const newExpanded = !isExpanded;\r\n    setIsExpanded(newExpanded);\r\n    if (onExpansionChange) {\r\n      onExpansionChange(newExpanded);\r\n    }\r\n  };\r\n\r\n  if (text.length <= maxLength) {\r\n    return <span>{text}</span>;\r\n  }\r\n\r\n  return (\r\n    <span\r\n      className=\"cursor-pointer\"\r\n      onClick={handleToggle}\r\n      title={isExpanded ? \"Click to collapse\" : \"Click to expand\"}\r\n    >\r\n      <span className=\"sm:hidden\">\r\n        {isExpanded ? text : `${text.substring(0, maxLength)}...`}\r\n      </span>\r\n      <span className=\"hidden sm:inline\">\r\n        {text}\r\n      </span>\r\n    </span>\r\n  );\r\n};\r\n\r\n/**\r\n * Structured Data Component for SEO\r\n */\r\nconst BreadcrumbStructuredData = ({ items }) => {\r\n  const structuredData = {\r\n    \"@context\": \"https://schema.org\",\r\n    \"@type\": \"BreadcrumbList\",\r\n    \"itemListElement\": items.map((item, index) => ({\r\n      \"@type\": \"ListItem\",\r\n      \"position\": index + 1,\r\n      \"name\": item.name,\r\n      \"item\": item.current ? undefined : `https://securitylit.com${item.url}`\r\n    }))\r\n  };\r\n\r\n  return (\r\n    <script\r\n      type=\"application/ld+json\"\r\n      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}\r\n    />\r\n  );\r\n};\r\n\r\n/**\r\n * Universal Breadcrumb Navigation Component with SEO structured data\r\n * Provides clear navigation hierarchy and improves SEO across all pages\r\n */\r\nconst BreadcrumbNavigation = ({ items, className = \"\" }) => {\r\n  const [hasExpandedText, setHasExpandedText] = useState(false);\r\n\r\n  // Check if this is a blog page for conditional padding\r\n  const isBlogPage = items?.some(item =>\r\n    item.url?.includes('/Blogs') ||\r\n    item.iconKey === 'blogs' ||\r\n    className?.includes('blog')\r\n  );\r\n\r\n  return (\r\n    <>\r\n      {/* Structured Data for SEO */}\r\n      <BreadcrumbStructuredData items={items} />\r\n      \r\n      {/* Simple Breadcrumb Navigation */}\r\n      <nav\r\n        className={`bg-transparent py-2 sm:py-3 ${\r\n          isBlogPage\r\n            ? hasExpandedText\r\n              ? 'pb-6 sm:pb-4'\r\n              : 'pb-3 sm:pb-4'\r\n            : 'pb-2 sm:pb-3'\r\n        } ${className}`}\r\n        aria-label=\"Breadcrumb navigation\"\r\n      >\r\n        <div className=\"max-w-7xl mx-auto px-4\">\r\n          <motion.ol\r\n            className={`flex items-center space-x-1 text-xs sm:text-sm ${\r\n              className?.includes('text-white')\r\n                ? 'text-white'\r\n                : className?.includes('text-blue-600')\r\n                ? 'text-blue-600'\r\n                : 'text-gray-600'\r\n            }`}\r\n            initial={{ opacity: 0 }}\r\n            animate={{ opacity: 1 }}\r\n            transition={{ duration: 0.4 }}\r\n          >\r\n            {items.map((item, index) => (\r\n              <motion.li\r\n                key={index}\r\n                className=\"flex items-center\"\r\n                initial={{ opacity: 0 }}\r\n                animate={{ opacity: 1 }}\r\n                transition={{ duration: 0.3, delay: index * 0.1 }}\r\n              >\r\n                {index > 0 && (\r\n                  <ChevronRight\r\n                    className={`w-3 h-3 mx-1 ${\r\n                      className?.includes('text-white')\r\n                        ? 'text-white/70'\r\n                        : className?.includes('text-blue-600')\r\n                        ? 'text-blue-400'\r\n                        : 'text-gray-400'\r\n                    }`}\r\n                    aria-hidden=\"true\"\r\n                  />\r\n                )}\r\n\r\n                {item.current ? (\r\n                  <span\r\n                    className={`font-medium flex items-center gap-1 ${\r\n                      className?.includes('text-white')\r\n                        ? 'text-white'\r\n                        : className?.includes('text-blue-600')\r\n                        ? 'text-blue-600'\r\n                        : 'text-gray-900'\r\n                    }`}\r\n                    aria-current=\"page\"\r\n                    title={item.description}\r\n                  >\r\n                    {item.iconKey === 'home' && (\r\n                      <Home className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-blue-600'\r\n                      }`} />\r\n                    )}\r\n                    {item.iconKey === 'blogs' && (\r\n                      <FileText className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-blue-600'\r\n                      }`} />\r\n                    )}\r\n                    {item.iconKey === 'contact-us' && (\r\n                      <Phone className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-blue-600'\r\n                      }`} />\r\n                    )}\r\n                    <TruncatedText\r\n                      text={item.name}\r\n                      onExpansionChange={setHasExpandedText}\r\n                    />\r\n                  </span>\r\n                ) : (\r\n                  <Link\r\n                    href={item.url}\r\n                    className={`transition-colors duration-200 flex items-center gap-1 hover:underline ${\r\n                      className?.includes('text-white')\r\n                        ? 'text-white hover:text-white/80'\r\n                        : className?.includes('text-blue-600')\r\n                        ? 'text-blue-600 hover:text-blue-800'\r\n                        : 'text-blue-600 hover:text-blue-800'\r\n                    }`}\r\n                    title={item.description}\r\n                  >\r\n                    {item.iconKey === 'home' && (\r\n                      <Home className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-current'\r\n                      }`} />\r\n                    )}\r\n                    {item.iconKey === 'blogs' && (\r\n                      <FileText className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-current'\r\n                      }`} />\r\n                    )}\r\n                    {item.iconKey === 'contact-us' && (\r\n                      <Phone className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-current'\r\n                      }`} />\r\n                    )}\r\n                    <TruncatedText\r\n                      text={item.name}\r\n                      onExpansionChange={setHasExpandedText}\r\n                    />\r\n                  </Link>\r\n                )}\r\n              </motion.li>\r\n            ))}\r\n          </motion.ol>\r\n        </div>\r\n      </nav>\r\n    </>\r\n  );\r\n};\r\n\r\n// Client-side helper function for generating breadcrumb items\r\nexport const generateBreadcrumbs = (pageType, params = {}) => {\r\n  const baseBreadcrumb = {\r\n    name: \"Home\",\r\n    url: \"/\",\r\n    iconKey: \"home\",\r\n    description: \"Return to homepage\"\r\n  };\r\n\r\n  const breadcrumbConfigs = {\r\n    blog: (slug) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Blog\",\r\n        url: \"/Blogs\",\r\n        iconKey: \"blogs\",\r\n        description: \"View all blog posts\"\r\n      },\r\n      {\r\n        name: params.title || \"Blog Post\",\r\n        url: `/Blogs/${slug}`,\r\n        current: true,\r\n        description: `${params.title || 'Blog post'} - Cybersecurity insights and penetration testing guidance`\r\n      }\r\n    ],\r\n    \r\n    service: (service) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Services\",\r\n        url: \"/Services\",\r\n        iconKey: \"services\",\r\n        description: \"View all cybersecurity services\"\r\n      },\r\n      {\r\n        name: params.title || service,\r\n        url: `/Services/${service}`,\r\n        current: true,\r\n        iconKey: service,\r\n        description: `${params.title || service} penetration testing services`\r\n      }\r\n    ],\r\n\r\n    location: (country) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Locations\",\r\n        url: \"/Locations\",\r\n        iconKey: \"locations\",\r\n        description: \"View all locations\"\r\n      },\r\n      {\r\n        name: params.countryName || country,\r\n        url: `/Locations/${country}`,\r\n        current: true,\r\n        description: `${params.countryName || country} cybersecurity services`\r\n      }\r\n    ],\r\n\r\n    company: (page) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Company\",\r\n        url: \"/Company\",\r\n        iconKey: \"company\",\r\n        description: \"Learn about SecurityLit\"\r\n      },\r\n      {\r\n        name: params.title || page,\r\n        url: `/Company/${page}`,\r\n        current: true,\r\n        description: params.description || `${page} information`\r\n      }\r\n    ],\r\n\r\n    'company-size': (size) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Company Size\",\r\n        url: \"/Company-size\",\r\n        iconKey: \"company\",\r\n        description: \"Solutions for different company sizes\"\r\n      },\r\n      {\r\n        name: params.title || size,\r\n        url: `/Company-size/${size}`,\r\n        current: true,\r\n        description: params.description || `${size} cybersecurity solutions`\r\n      }\r\n    ],\r\n\r\n    industry: (industry) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Industries\",\r\n        url: \"/Industries\",\r\n        iconKey: \"company\",\r\n        description: \"Industry-specific cybersecurity solutions\"\r\n      },\r\n      {\r\n        name: params.title || industry,\r\n        url: `/Industries/${industry}`,\r\n        current: true,\r\n        description: params.description || `${industry} cybersecurity solutions`\r\n      }\r\n    ],\r\n\r\n    product: (product) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Product\",\r\n        url: \"/Product\",\r\n        iconKey: \"product\",\r\n        description: \"Explore our cybersecurity products\"\r\n      },\r\n      {\r\n        name: params.title || product,\r\n        url: `/Product/${product}`,\r\n        current: true,\r\n        description: params.description || `${product} product information`\r\n      }\r\n    ],\r\n\r\n    simple: (pageName, url) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: pageName,\r\n        url: url,\r\n        current: true,\r\n        iconKey: params.iconKey,\r\n        description: params.description || `${pageName} page`\r\n      }\r\n    ]\r\n  };\r\n\r\n  return breadcrumbConfigs[pageType] || (() => [baseBreadcrumb]);\r\n};\r\n\r\nexport default BreadcrumbNavigation; "], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;;;AAJA;;;;;AAMA;;;CAGC,GACD,MAAM,gBAAgB;QAAC,EAAE,IAAI,EAAE,YAAY,EAAE,EAAE,iBAAiB,EAAE;;IAChE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,eAAe;QACnB,MAAM,cAAc,CAAC;QACrB,cAAc;QACd,IAAI,mBAAmB;YACrB,kBAAkB;QACpB;IACF;IAEA,IAAI,KAAK,MAAM,IAAI,WAAW;QAC5B,qBAAO,6LAAC;sBAAM;;;;;;IAChB;IAEA,qBACE,6LAAC;QACC,WAAU;QACV,SAAS;QACT,OAAO,aAAa,sBAAsB;;0BAE1C,6LAAC;gBAAK,WAAU;0BACb,aAAa,OAAO,AAAC,GAA+B,OAA7B,KAAK,SAAS,CAAC,GAAG,YAAW;;;;;;0BAEvD,6LAAC;gBAAK,WAAU;0BACb;;;;;;;;;;;;AAIT;GA7BM;KAAA;AA+BN;;CAEC,GACD,MAAM,2BAA2B;QAAC,EAAE,KAAK,EAAE;IACzC,MAAM,iBAAiB;QACrB,YAAY;QACZ,SAAS;QACT,mBAAmB,MAAM,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;gBAC7C,SAAS;gBACT,YAAY,QAAQ;gBACpB,QAAQ,KAAK,IAAI;gBACjB,QAAQ,KAAK,OAAO,GAAG,YAAY,AAAC,0BAAkC,OAAT,KAAK,GAAG;YACvE,CAAC;IACH;IAEA,qBACE,6LAAC;QACC,MAAK;QACL,yBAAyB;YAAE,QAAQ,KAAK,SAAS,CAAC;QAAgB;;;;;;AAGxE;MAlBM;AAoBN;;;CAGC,GACD,MAAM,uBAAuB;QAAC,EAAE,KAAK,EAAE,YAAY,EAAE,EAAE;;IACrD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,uDAAuD;IACvD,MAAM,aAAa,kBAAA,4BAAA,MAAO,IAAI,CAAC,CAAA;YAC7B;eAAA,EAAA,YAAA,KAAK,GAAG,cAAR,gCAAA,UAAU,QAAQ,CAAC,cACnB,KAAK,OAAO,KAAK,YACjB,sBAAA,gCAAA,UAAW,QAAQ,CAAC;;IAGtB,qBACE;;0BAEE,6LAAC;gBAAyB,OAAO;;;;;;0BAGjC,6LAAC;gBACC,WAAW,AAAC,+BAMR,OALF,aACI,kBACE,iBACA,iBACF,gBACL,KAAa,OAAV;gBACJ,cAAW;0BAEX,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;wBACR,WAAW,AAAC,kDAMX,OALC,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,iBAChB,eACA,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,oBACpB,kBACA;wBAEN,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,YAAY;4BAAE,UAAU;wBAAI;kCAE3B,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;gCAER,WAAU;gCACV,SAAS;oCAAE,SAAS;gCAAE;gCACtB,SAAS;oCAAE,SAAS;gCAAE;gCACtB,YAAY;oCAAE,UAAU;oCAAK,OAAO,QAAQ;gCAAI;;oCAE/C,QAAQ,mBACP,6LAAC,yNAAA,CAAA,eAAY;wCACX,WAAW,AAAC,gBAMX,OALC,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,iBAChB,kBACA,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,oBACpB,kBACA;wCAEN,eAAY;;;;;;oCAIf,KAAK,OAAO,iBACX,6LAAC;wCACC,WAAW,AAAC,uCAMX,OALC,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,iBAChB,eACA,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,oBACpB,kBACA;wCAEN,gBAAa;wCACb,OAAO,KAAK,WAAW;;4CAEtB,KAAK,OAAO,KAAK,wBAChB,6LAAC,sMAAA,CAAA,OAAI;gDAAC,WAAW,AAAC,WAMjB,OALC,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,iBAChB,eACA,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,oBACpB,kBACA;;;;;;4CAGP,KAAK,OAAO,KAAK,yBAChB,6LAAC,iNAAA,CAAA,WAAQ;gDAAC,WAAW,AAAC,WAMrB,OALC,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,iBAChB,eACA,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,oBACpB,kBACA;;;;;;4CAGP,KAAK,OAAO,KAAK,8BAChB,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAW,AAAC,WAMlB,OALC,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,iBAChB,eACA,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,oBACpB,kBACA;;;;;;0DAGR,6LAAC;gDACC,MAAM,KAAK,IAAI;gDACf,mBAAmB;;;;;;;;;;;iGAIvB,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAM,KAAK,GAAG;wCACd,WAAW,AAAC,0EAMX,OALC,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,iBAChB,mCACA,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,oBACpB,sCACA;wCAEN,OAAO,KAAK,WAAW;;4CAEtB,KAAK,OAAO,KAAK,wBAChB,6LAAC,sMAAA,CAAA,OAAI;gDAAC,WAAW,AAAC,WAMjB,OALC,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,iBAChB,eACA,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,oBACpB,kBACA;;;;;;4CAGP,KAAK,OAAO,KAAK,yBAChB,6LAAC,iNAAA,CAAA,WAAQ;gDAAC,WAAW,AAAC,WAMrB,OALC,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,iBAChB,eACA,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,oBACpB,kBACA;;;;;;4CAGP,KAAK,OAAO,KAAK,8BAChB,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAW,AAAC,WAMlB,OALC,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,iBAChB,eACA,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,oBACpB,kBACA;;;;;;0DAGR,6LAAC;gDACC,MAAM,KAAK,IAAI;gDACf,mBAAmB;;;;;;;;;;;;;+BAxGpB;;;;;;;;;;;;;;;;;;;;;;AAmHrB;IA5JM;MAAA;AA+JC,MAAM,sBAAsB,SAAC;QAAU,0EAAS,CAAC;IACtD,MAAM,iBAAiB;QACrB,MAAM;QACN,KAAK;QACL,SAAS;QACT,aAAa;IACf;IAEA,MAAM,oBAAoB;QACxB,MAAM,CAAC,OAAS;gBACd;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,AAAC,UAAc,OAAL;oBACf,SAAS;oBACT,aAAa,AAAC,GAA8B,OAA5B,OAAO,KAAK,IAAI,aAAY;gBAC9C;aACD;QAED,SAAS,CAAC,UAAY;gBACpB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,AAAC,aAAoB,OAAR;oBAClB,SAAS;oBACT,SAAS;oBACT,aAAa,AAAC,GAA0B,OAAxB,OAAO,KAAK,IAAI,SAAQ;gBAC1C;aACD;QAED,UAAU,CAAC,UAAY;gBACrB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,WAAW,IAAI;oBAC5B,KAAK,AAAC,cAAqB,OAAR;oBACnB,SAAS;oBACT,aAAa,AAAC,GAAgC,OAA9B,OAAO,WAAW,IAAI,SAAQ;gBAChD;aACD;QAED,SAAS,CAAC,OAAS;gBACjB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,AAAC,YAAgB,OAAL;oBACjB,SAAS;oBACT,aAAa,OAAO,WAAW,IAAI,AAAC,GAAO,OAAL,MAAK;gBAC7C;aACD;QAED,gBAAgB,CAAC,OAAS;gBACxB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,AAAC,iBAAqB,OAAL;oBACtB,SAAS;oBACT,aAAa,OAAO,WAAW,IAAI,AAAC,GAAO,OAAL,MAAK;gBAC7C;aACD;QAED,UAAU,CAAC,WAAa;gBACtB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,AAAC,eAAuB,OAAT;oBACpB,SAAS;oBACT,aAAa,OAAO,WAAW,IAAI,AAAC,GAAW,OAAT,UAAS;gBACjD;aACD;QAED,SAAS,CAAC,UAAY;gBACpB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,AAAC,YAAmB,OAAR;oBACjB,SAAS;oBACT,aAAa,OAAO,WAAW,IAAI,AAAC,GAAU,OAAR,SAAQ;gBAChD;aACD;QAED,QAAQ,CAAC,UAAU,MAAQ;gBACzB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,SAAS,OAAO,OAAO;oBACvB,aAAa,OAAO,WAAW,IAAI,AAAC,GAAW,OAAT,UAAS;gBACjD;aACD;IACH;IAEA,OAAO,iBAAiB,CAAC,SAAS,IAAI,CAAC,IAAM;YAAC;SAAe;AAC/D;uCAEe", "debugId": null}}, {"offset": {"line": 412, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/training/components/HeroBento.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport { Shield, Play, ArrowRight, GraduationCap, CheckCircle, Users, Star, Clock, Award } from 'lucide-react';\r\nimport BreadcrumbNavigation from '../../common/components/BreadcrumbNavigation';\r\n\r\nexport default function HeroBento() {\r\n  const breadcrumbItems = [\r\n    {\r\n      name: \"Home\",\r\n      url: \"/\",\r\n      iconKey: \"home\",\r\n      description: \"Return to homepage\"\r\n    },\r\n    {\r\n      name: \"Training\",\r\n      url: \"/training\",\r\n      current: true,\r\n      iconKey: \"graduation-cap\",\r\n      description: \"Explore SecurityLit's cybersecurity training programs\"\r\n    }\r\n  ];\r\n\r\n  const keyBenefits = [\r\n    \"Complete pentesting skills with real-time live projects\",\r\n    \"Latest tools and topics in cybersecurity\"\r\n  ];\r\n\r\n  const trustStats = [\r\n    { value: \"500+\", label: \"Students Trained\", icon: Users },\r\n    { value: \"98%\", label: \"Success Rate\", icon: Star },\r\n    { value: \"83\", label: \"Total Lessons\", icon: Clock },\r\n    { value: \"11\", label: \"Course Sections\", icon: Award }\r\n  ];\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-white\">\r\n      <div className=\"flex flex-col lg:flex-row min-h-screen relative\">\r\n\r\n        {/* Left Section - Optimized Content with Rounded Corner */}\r\n        <div className=\"w-full lg:w-1/2 bg-gradient-to-br from-[var(--color-dark-blue)] to-[var(--color-dark-blue-hover)] relative overflow-hidden lg:rounded-br-[100px]\">\r\n          {/* Background Pattern */}\r\n          <div className=\"absolute inset-0 bg-cover bg-center bg-no-repeat opacity-10\"\r\n               style={{ backgroundImage: 'url(/images/hexagon.svg)' }}>\r\n          </div>\r\n          <div className=\"absolute inset-0 bg-[var(--color-dark-blue)]/90 lg:rounded-br-[100px]\"></div>\r\n          \r\n          <div className=\"relative z-10 flex justify-center px-4 sm:px-6 lg:px-12 py-8 sm:py-12 lg:pt-20 lg:pb-8 min-h-screen lg:h-full\">\r\n            <div className=\"max-w-lg w-full flex flex-col justify-center\">\r\n              {/* Breadcrumb */}\r\n              <div className=\"mb-4 mt-2 lg:mt-0\">\r\n                <BreadcrumbNavigation items={breadcrumbItems} className=\"text-white\" />\r\n              </div>\r\n\r\n              {/* Main Heading - Enhanced */}\r\n              <motion.h1\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.8 }}\r\n                className=\"text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-4 leading-tight\"\r\n              >\r\n                Elite Security\r\n                <span className=\"block bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] bg-clip-text text-transparent\">\r\n                  Training\r\n                </span>\r\n              </motion.h1>\r\n              \r\n              {/* Streamlined Subtitle */}\r\n              <motion.h2\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.8, delay: 0.2 }}\r\n                className=\"text-lg sm:text-xl lg:text-2xl font-bold text-white mb-3 leading-tight\"\r\n              >\r\n                Launch Your Cyber Security Career\r\n              </motion.h2>\r\n\r\n              <motion.h3\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.8, delay: 0.3 }}\r\n                className=\"text-base sm:text-lg text-[var(--color-yellow)] mb-4 font-semibold\"\r\n              >\r\n                Free and Premium Pathways\r\n              </motion.h3>\r\n              \r\n              {/* Compact Description - Reduced content */}\r\n              <motion.p\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.8, delay: 0.4 }}\r\n                className=\"text-sm sm:text-base text-white/90 mb-6 leading-relaxed\"\r\n              >\r\n                Dive into penetration testing with our comprehensive program designed for aspiring security professionals.\r\n              </motion.p>\r\n\r\n              {/* Streamlined Key Benefits - 3 items only */}\r\n              <motion.div \r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.8, delay: 0.5 }}\r\n                className=\"space-y-3 mb-8\"\r\n              >\r\n                {keyBenefits.map((benefit, index) => (\r\n                  <div key={index} className=\"flex items-center gap-3\">\r\n                    <div className=\"w-5 h-5 bg-[var(--color-yellow)] rounded-full flex items-center justify-center flex-shrink-0\">\r\n                      <CheckCircle className=\"w-3 h-3 text-[var(--color-dark-blue)]\" />\r\n                    </div>\r\n                    <span className=\"text-white/90 text-sm sm:text-base\">{benefit}</span>\r\n                  </div>\r\n                ))}\r\n              </motion.div>\r\n\r\n              {/* Enhanced CTA Buttons */}\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.8, delay: 0.6 }}\r\n                className=\"flex flex-col gap-3\"\r\n              >\r\n                <motion.button\r\n                  whileHover={{ scale: 1.02 }}\r\n                  whileTap={{ scale: 0.98 }}\r\n                  className=\"group bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] text-white px-4 sm:px-6 py-2.5 sm:py-3 rounded-lg font-semibold text-sm sm:text-base transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center gap-2\"\r\n                >\r\n                  <Play className=\"w-4 h-4\" />\r\n                  Start Free Training\r\n                  <ArrowRight className=\"w-4 h-4 group-hover:translate-x-1 transition-transform\" />\r\n                </motion.button>\r\n\r\n                <motion.button\r\n                  whileHover={{ scale: 1.02 }}\r\n                  whileTap={{ scale: 0.98 }}\r\n                  className=\"group bg-white/10 backdrop-blur-sm text-white px-4 sm:px-6 py-2.5 sm:py-3 rounded-lg font-semibold text-sm sm:text-base transition-all duration-300 border-2 border-white/20 hover:bg-white/20 flex items-center justify-center gap-2\"\r\n                >\r\n                  <GraduationCap className=\"w-4 h-4\" />\r\n                  Upgrade to Premium\r\n                </motion.button>\r\n              </motion.div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n\r\n\r\n        {/* Right Section - Enhanced Visual Focus */}\r\n        <div className=\"w-full lg:w-1/2 bg-white\">\r\n          <div className=\"max-w-2xl mx-auto px-4 sm:px-6 lg:px-12 py-8 sm:py-12 lg:pt-20 lg:pb-8 min-h-screen lg:h-full flex flex-col justify-center\">\r\n            \r\n            {/* Enhanced Hero Visual */}\r\n            <motion.div \r\n              initial={{ opacity: 0, scale: 0.9 }}\r\n              animate={{ opacity: 1, scale: 1 }}\r\n              transition={{ duration: 1, delay: 0.3 }}\r\n              className=\"relative mb-6\"\r\n            >\r\n              <div className=\"relative bg-gradient-to-br from-[var(--color-blue)]/10 to-[var(--color-blue-secondary)]/10 rounded-2xl p-4 sm:p-6 lg:p-8 border-2 border-[var(--color-blue)]/20\">\r\n                <div className=\"absolute inset-0 bg-gradient-to-br from-[var(--color-blue)]/5 to-[var(--color-blue-secondary)]/5 rounded-2xl\"></div>\r\n                \r\n                <div className=\"relative z-10 text-center\">\r\n                  <div className=\"w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-xl flex items-center justify-center mx-auto mb-4 shadow-lg\">\r\n                    <img\r\n                      src=\"/SecurityLit_Icon_White.png\"\r\n                      alt=\"SecurityLit Logo\"\r\n                      className=\"w-8 h-8 sm:w-12 sm:h-12 object-contain\"\r\n                    />\r\n                  </div>\r\n\r\n                  <h3 className=\"text-lg sm:text-xl font-bold text-[var(--color-dark-blue)] mb-2\">\r\n                    SecurityLit Presents\r\n                  </h3>\r\n                  <p className=\"text-[var(--foreground-secondary)] text-sm sm:text-base\">\r\n                    Professional cybersecurity training designed by industry experts\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            </motion.div>\r\n\r\n            {/* Enhanced Trust Indicators */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.8, delay: 0.6 }}\r\n              className=\"grid grid-cols-2 gap-3 sm:gap-4 mb-6\"\r\n            >\r\n              {trustStats.map((stat, index) => (\r\n                <motion.div \r\n                  key={index}\r\n                  initial={{ opacity: 0, y: 20 }}\r\n                  animate={{ opacity: 1, y: 0 }}\r\n                  transition={{ delay: 0.7 + index * 0.1, duration: 0.6 }}\r\n                  className=\"text-center group\"\r\n                >\r\n                  <div className=\"w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-br from-[var(--color-blue)]/10 to-[var(--color-blue-secondary)]/10 rounded-lg flex items-center justify-center mx-auto mb-2 group-hover:scale-110 transition-transform\">\r\n                    <stat.icon className=\"w-4 h-4 sm:w-5 sm:h-5 text-[var(--color-blue)]\" />\r\n                  </div>\r\n                  <div className=\"text-xl sm:text-2xl lg:text-3xl font-bold text-[var(--color-dark-blue)] mb-1\">\r\n                    {stat.value}\r\n                  </div>\r\n                  <div className=\"text-[var(--foreground-secondary)] text-xs font-medium\">\r\n                    {stat.label}\r\n                  </div>\r\n                </motion.div>\r\n              ))}\r\n            </motion.div>\r\n\r\n            \r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,kBAAkB;QACtB;YACE,MAAM;YACN,KAAK;YACL,SAAS;YACT,aAAa;QACf;QACA;YACE,MAAM;YACN,KAAK;YACL,SAAS;YACT,SAAS;YACT,aAAa;QACf;KACD;IAED,MAAM,cAAc;QAClB;QACA;KACD;IAED,MAAM,aAAa;QACjB;YAAE,OAAO;YAAQ,OAAO;YAAoB,MAAM,uMAAA,CAAA,QAAK;QAAC;QACxD;YAAE,OAAO;YAAO,OAAO;YAAgB,MAAM,qMAAA,CAAA,OAAI;QAAC;QAClD;YAAE,OAAO;YAAM,OAAO;YAAiB,MAAM,uMAAA,CAAA,QAAK;QAAC;QACnD;YAAE,OAAO;YAAM,OAAO;YAAmB,MAAM,uMAAA,CAAA,QAAK;QAAC;KACtD;IAED,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAGb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;4BACV,OAAO;gCAAE,iBAAiB;4BAA2B;;;;;;sCAE1D,6LAAC;4BAAI,WAAU;;;;;;sCAEf,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,8JAAA,CAAA,UAAoB;4CAAC,OAAO;4CAAiB,WAAU;;;;;;;;;;;kDAI1D,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;wCACR,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;wCAAI;wCAC5B,WAAU;;4CACX;0DAEC,6LAAC;gDAAK,WAAU;0DAAiH;;;;;;;;;;;;kDAMnI,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;wCACR,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,WAAU;kDACX;;;;;;kDAID,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;wCACR,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,WAAU;kDACX;;;;;;kDAKD,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;wCACP,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,WAAU;kDACX;;;;;;kDAKD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,WAAU;kDAET,YAAY,GAAG,CAAC,CAAC,SAAS,sBACzB,6LAAC;gDAAgB,WAAU;;kEACzB,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,8NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;;;;;;kEAEzB,6LAAC;wDAAK,WAAU;kEAAsC;;;;;;;+CAJ9C;;;;;;;;;;kDAUd,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,WAAU;;0DAEV,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gDACZ,YAAY;oDAAE,OAAO;gDAAK;gDAC1B,UAAU;oDAAE,OAAO;gDAAK;gDACxB,WAAU;;kEAEV,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAY;kEAE5B,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;0DAGxB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gDACZ,YAAY;oDAAE,OAAO;gDAAK;gDAC1B,UAAU;oDAAE,OAAO;gDAAK;gDACxB,WAAU;;kEAEV,6LAAC,2NAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;oDAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAW/C,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CAGb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAG,OAAO;gCAAI;gCACtC,WAAU;0CAEV,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDAEf,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDACC,KAAI;wDACJ,KAAI;wDACJ,WAAU;;;;;;;;;;;8DAId,6LAAC;oDAAG,WAAU;8DAAkE;;;;;;8DAGhF,6LAAC;oDAAE,WAAU;8DAA0D;;;;;;;;;;;;;;;;;;;;;;;0CAQ7E,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,WAAU;0CAET,WAAW,GAAG,CAAC,CAAC,MAAM,sBACrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO,MAAM,QAAQ;4CAAK,UAAU;wCAAI;wCACtD,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;;;;;;0DAEvB,6LAAC;gDAAI,WAAU;0DACZ,KAAK,KAAK;;;;;;0DAEb,6LAAC;gDAAI,WAAU;0DACZ,KAAK,KAAK;;;;;;;uCAbR;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyBvB;KA9MwB", "debugId": null}}, {"offset": {"line": 936, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/training/components/CurriculumHighlights.jsx"], "sourcesContent": ["\"use client\";\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { BookOpen, Brain, Code, Compass, Target, Lock, Zap, Users, Shield, ArrowRight, Star, Clock, Award } from 'lucide-react';\nimport { PrimaryButton } from '../../common/buttons/BrandButtons';\n\nconst curriculumItems = [\n  {\n    icon: BookOpen,\n    title: \"Advanced Assessment\",\n    description: \"Master penetration testing methodologies and tools\",\n    color: \"from-[var(--color-blue)] to-[var(--color-blue-secondary)]\",\n    delay: 0.1,\n    size: \"large\",\n    position: \"top-left\",\n    features: [\"OWASP Top 10\", \"Vulnerability Assessment\", \"Report Writing\"]\n  },\n  {\n    icon: Brain,\n    title: \"API Security\",\n    description: \"Learn to secure REST APIs and GraphQL endpoints\",\n    color: \"from-[var(--color-dark-blue)] to-[var(--color-dark-blue-hover)]\",\n    delay: 0.2,\n    size: \"medium\",\n    position: \"top-right\",\n    features: [\"Authentication\", \"Authorization\", \"Rate Limiting\"]\n  },\n  {\n    icon: Code,\n    title: \"Hands-on Labs\",\n    description: \"Real-world experience with live environments\",\n    color: \"from-[var(--color-blue)] to-[var(--color-blue-secondary)]\",\n    delay: 0.3,\n    size: \"medium\",\n    position: \"middle-left\",\n    features: [\"Live Environments\", \"Real Scenarios\", \"Practice Labs\"]\n  },\n  {\n    icon: Compass,\n    title: \"Navigation & Recon\",\n    description: \"Advanced reconnaissance and information gathering\",\n    color: \"from-[var(--color-blue)] to-[var(--color-blue-secondary)]\",\n    delay: 0.4,\n    size: \"small\",\n    position: \"middle-center\",\n    features: [\"OSINT\", \"Network Mapping\", \"Footprinting\"]\n  },\n  {\n    icon: Target,\n    title: \"Role-based Training\",\n    description: \"Specialized paths for different career goals\",\n    color: \"from-[var(--color-dark-blue)] to-[var(--color-dark-blue-subtle)]\",\n    delay: 0.5,\n    size: \"large\",\n    position: \"middle-right\",\n    features: [\"Penetration Tester\", \"Security Analyst\", \"Security Engineer\"]\n  },\n  {\n    icon: Lock,\n    title: \"Authentication & Auth\",\n    description: \"Deep dive into modern authentication systems\",\n    color: \"from-[var(--color-blue)] to-[var(--color-blue-secondary)]\",\n    delay: 0.6,\n    size: \"medium\",\n    position: \"bottom-left\",\n    features: [\"OAuth 2.0\", \"JWT\", \"Multi-Factor Auth\"]\n  }\n];\n\nexport default function CurriculumHighlights() {\n  return (\n    <section className=\"py-12 sm:py-16 lg:py-24 bg-white relative overflow-hidden\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0\">\n        <div className=\"absolute top-20 left-10 w-72 h-72 bg-[var(--color-blue)]/5 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob\"></div>\n        <div className=\"absolute top-40 right-10 w-72 h-72 bg-[var(--color-yellow)]/5 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-2000\"></div>\n        <div className=\"absolute -bottom-8 left-20 w-72 h-72 bg-[var(--color-blue-secondary)]/5 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-4000\"></div>\n      </div>\n\n      <div className=\"relative z-10 container mx-auto px-4 max-w-7xl\">\n        {/* Header */}\n        <div className=\"text-center mb-20\">\n          <div className=\"inline-flex items-center bg-gradient-to-r from-[var(--color-blue)]/10 to-[var(--color-blue-secondary)]/10 px-6 py-3 rounded-full mb-8 border border-[var(--color-blue)]/20\">\n            <Zap className=\"w-5 h-5 text-[var(--color-blue)] mr-3\" />\n            <span className=\"text-sm font-semibold text-[var(--color-blue)]\">Cybersecurity Training</span>\n          </div>\n          \n          <h2 className=\"text-2xl sm:text-3xl lg:text-4xl xl:text-5xl 2xl:text-6xl font-bold text-[var(--color-dark-blue)] mb-8 leading-tight\">\n            Explore Our\n            <span className=\"block bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] bg-clip-text text-transparent\">\n              Comprehensive Curriculum\n            </span>\n          </h2>\n          \n          <p className=\"text-base sm:text-lg lg:text-xl text-[var(--foreground-secondary)] max-w-4xl mx-auto leading-relaxed px-4\">\n            Dive into essential areas such as web application security, network penetration testing, cloud security, API security,\n            and ethical hacking fundamentals.\n          </p>\n        </div>\n\n        {/* Professional Bento Grid - Fixed Layout */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-12 gap-4 sm:gap-6 mb-12 sm:mb-16 lg:mb-20\" style={{ gridTemplateRows: 'repeat(3, minmax(240px, auto))' }}>\n          {/* Large Card - Advanced Assessment */}\n            <div className=\"col-span-1 lg:col-span-8 lg:row-span-2 group relative\">\n            <div className=\"relative h-full bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-3xl p-6 lg:p-8 overflow-hidden\">\n              {/* Background Pattern */}\n              <div className=\"absolute inset-0 bg-[url('/images/hexagon.svg')] bg-cover opacity-10\"></div>\n              <div className=\"absolute inset-0 bg-gradient-to-br from-[var(--color-blue)]/90 to-[var(--color-blue-secondary)]/90\"></div>\n              \n              <div className=\"relative z-10 h-full flex flex-col justify-between\">\n                <div className=\"flex-1\">\n                  <div className=\"flex items-start gap-4 mb-6\">\n                    <div className=\"w-14 h-14 lg:w-16 lg:h-16 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center flex-shrink-0 shadow-lg\">\n                      <BookOpen className=\"w-7 h-7 lg:w-8 lg:h-8 text-white\" />\n                    </div>\n                    <div className=\"flex-1 min-w-0\">\n                      <h3 className=\"text-2xl lg:text-3xl font-bold text-white drop-shadow mb-3 leading-tight\">Advanced Assessment</h3>\n                      <p className=\"text-white/90 text-base lg:text-lg leading-relaxed\">Master penetration testing methodologies and tools</p>\n                    </div>\n                  </div>\n                  \n                  {/* Image and Features Grid */}\n                  <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6\">\n                    {/* Features */}\n                    <div className=\"space-y-4\">\n                      {curriculumItems[0].features.map((feature, index) => (\n                        <div key={index} className=\"bg-white/15 backdrop-blur-sm rounded-2xl p-4 lg:p-5 border border-white/25 shadow-lg\">\n                          <div className=\"text-white font-semibold text-sm lg:text-base mb-2 leading-tight\">{feature}</div>\n                          <div className=\"text-white/70 text-sm leading-relaxed\">\n                            {feature === \"OWASP Top 10\" && \"Industry-standard web application security risks\"}\n                            {feature === \"Vulnerability Assessment\" && \"Systematic identification and analysis\"}\n                            {feature === \"Report Writing\" && \"Professional documentation and recommendations\"}\n                          </div>\n                        </div>\n                      ))}\n                    </div>\n                    \n                    {/* Image */}\n                    <div className=\"relative\">\n                      <div className=\"relative h-full min-h-[140px] lg:min-h-[180px] bg-white/15 backdrop-blur-sm rounded-2xl overflow-hidden border border-white/25 shadow-lg\">\n                        <div className=\"absolute inset-0 bg-gradient-to-br from-white/5 to-white/10\"></div>\n                        <div className=\"relative z-10 h-full flex items-center justify-center p-4\">\n                          <img \n                            src=\"/images/about-us-image-3.jpg\" \n                            alt=\"Cybersecurity Assessment\" \n                            className=\"w-full h-full object-cover rounded-xl\"\n                          />\n                          <div className=\"absolute inset-0 bg-gradient-to-br from-[var(--color-blue)]/20 to-[var(--color-blue-secondary)]/20 rounded-xl\"></div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n                \n                <div className=\"flex items-center justify-between pt-6 border-t border-white/20\">\n                  <div className=\"flex items-center gap-4 text-white/90\">\n                    <div className=\"flex items-center gap-2 bg-white/10 px-3 py-2 rounded-xl\">\n                      <Clock className=\"w-4 h-4\" />\n                      <span className=\"text-sm font-medium\">40+ hours</span>\n                    </div>\n                    <div className=\"flex items-center gap-2 bg-white/10 px-3 py-2 rounded-xl\">\n                      <Star className=\"w-4 h-4\" />\n                      <span className=\"text-sm font-medium\">Advanced</span>\n                    </div>\n                  </div>\n                  <ArrowRight className=\"w-6 h-6 text-white group-hover:translate-x-2 transition-transform\" />\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Medium Card - API Security */}\n                <div className=\"col-span-12 lg:col-span-4 row-span-1 group relative\">\n            <div className=\"relative h-full bg-gradient-to-br from-[var(--color-dark-blue)] to-[var(--color-dark-blue-hover)] rounded-3xl p-6 overflow-hidden\">\n              <div className=\"absolute inset-0 bg-[url('/images/hexagon.svg')] bg-cover opacity-5\"></div>\n              <div className=\"absolute inset-0 bg-gradient-to-br from-[var(--color-dark-blue)]/95 to-[var(--color-dark-blue-hover)]/95\"></div>\n              \n              <div className=\"relative z-10 h-full flex flex-col justify-between\">\n                <div className=\"flex-1\">\n                  <div className=\"w-12 h-12 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center mb-4 shadow-lg\">\n                    <Brain className=\"w-6 h-6 text-white\" />\n                  </div>\n                  <h3 className=\"text-xl font-bold text-white mb-3 leading-tight\">API Security</h3>\n                  <p className=\"text-white/80 text-sm mb-4 leading-relaxed\">Learn to secure REST APIs and GraphQL endpoints</p>\n                  \n                  <div className=\"space-y-3\">\n                    {curriculumItems[1].features.map((feature, index) => (\n                      <div key={index} className=\"flex items-center gap-3 bg-white/10 rounded-xl p-3\">\n                        <div className=\"w-2 h-2 bg-[var(--color-yellow)] rounded-full flex-shrink-0\"></div>\n                        <span className=\"text-white/90 text-sm font-medium leading-tight\">{feature}</span>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n                \n                <ArrowRight className=\"w-5 h-5 text-white/60 group-hover:translate-x-1 transition-transform mt-4\" />\n              </div>\n            </div>\n                </div>\n\n          {/* Medium Card - Hands-on Labs */}\n          <div className=\"col-span-12 lg:col-span-4 row-span-1 group relative\">\n            <div className=\"relative h-full bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-3xl p-6 overflow-hidden\">\n              <div className=\"absolute inset-0 bg-[url('/images/hexagon.svg')] bg-cover opacity-5\"></div>\n              <div className=\"absolute inset-0 bg-gradient-to-br from-[var(--color-blue)]/95 to-[var(--color-blue-secondary)]/95\"></div>\n              \n              <div className=\"relative z-10 h-full flex flex-col justify-between\">\n                <div className=\"flex-1\">\n                  <div className=\"w-12 h-12 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center mb-4 shadow-lg\">\n                    <Code className=\"w-6 h-6 text-white\" />\n                  </div>\n                  <h3 className=\"text-xl font-bold text-white mb-3 leading-tight\">Hands-on Labs</h3>\n                  <p className=\"text-white/80 text-sm mb-4 leading-relaxed\">Real-world experience with live environments</p>\n                  \n                  <div className=\"space-y-3\">\n                    {curriculumItems[2].features.map((feature, index) => (\n                      <div key={index} className=\"flex items-center gap-3 bg-white/10 rounded-xl p-3\">\n                        <div className=\"w-2 h-2 bg-white rounded-full flex-shrink-0\"></div>\n                        <span className=\"text-white/90 text-sm font-medium leading-tight\">{feature}</span>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n                \n                <ArrowRight className=\"w-5 h-5 text-white/60 group-hover:translate-x-1 transition-transform mt-4\" />\n              </div>\n            </div>\n          </div>\n\n          {/* Small Card - Navigation & Recon */}\n          <div className=\"col-span-12 lg:col-span-4 row-span-1 group relative\">\n            <div className=\"relative h-full bg-white rounded-3xl p-6 shadow-[0_8px_30px_rgb(0,0,0,0.12)] hover:shadow-[0_20px_40px_rgb(0,0,0,0.15)] transition-all duration-300 border border-gray-100\">\n              <div className=\"h-full flex flex-col justify-between\">\n                <div className=\"flex-1\">\n                  <div className=\"w-12 h-12 bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-2xl flex items-center justify-center mb-4 shadow-lg\">\n                    <Compass className=\"w-6 h-6 text-white\" />\n                  </div>\n                  <h3 className=\"text-xl font-bold text-[var(--color-dark-blue)] mb-3 leading-tight\">Navigation & Recon</h3>\n                  <p className=\"text-[var(--foreground-secondary)] text-sm leading-relaxed\">Advanced reconnaissance and information gathering</p>\n                </div>\n                \n                <ArrowRight className=\"w-5 h-5 text-[var(--color-blue)] group-hover:translate-x-1 transition-transform mt-4\" />\n              </div>\n              </div>\n            </div>\n\n          {/* Large Card - Role-based Training */}\n          <div className=\"col-span-12 lg:col-span-8 row-span-2 group relative\">\n            <div className=\"relative h-full bg-gradient-to-br from-[var(--color-dark-blue)] to-[var(--color-dark-blue-subtle)] rounded-3xl p-6 lg:p-8 overflow-hidden\">\n              <div className=\"absolute inset-0 bg-[url('/images/hexagon.svg')] bg-cover opacity-10\"></div>\n              <div className=\"absolute inset-0 bg-gradient-to-br from-[var(--color-dark-blue)]/90 to-[var(--color-dark-blue-subtle)]/90\"></div>\n              \n              <div className=\"relative z-10 h-full flex flex-col justify-between\">\n                <div className=\"flex-1\">\n                  <div className=\"flex items-start gap-4 mb-6\">\n                    <div className=\"w-14 h-14 lg:w-16 lg:h-16 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center flex-shrink-0 shadow-lg\">\n                      <Target className=\"w-7 h-7 lg:w-8 lg:h-8 text-white\" />\n                    </div>\n                    <div className=\"flex-1 min-w-0\">\n                      <h3 className=\"text-2xl lg:text-3xl font-bold text-white drop-shadow mb-3 leading-tight\">Role-based Training</h3>\n                      <p className=\"text-white/90 text-base lg:text-lg leading-relaxed\">Specialized paths for different career goals</p>\n                    </div>\n                  </div>\n                  \n                  {/* Image and Features Grid */}\n                  <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6\">\n                    {/* Features */}\n                    <div className=\"space-y-4\">\n                      {curriculumItems[4].features.map((feature, index) => (\n                        <div key={index} className=\"bg-white/15 backdrop-blur-sm rounded-2xl p-4 lg:p-5 border border-white/25 shadow-lg\">\n                          <div className=\"text-white font-semibold text-sm lg:text-base mb-2 leading-tight\">{feature}</div>\n                          <div className=\"text-white/70 text-sm leading-relaxed\">\n                            {feature === \"Penetration Tester\" && \"Ethical hacking and security testing\"}\n                            {feature === \"Security Analyst\" && \"Threat detection and incident response\"}\n                            {feature === \"Security Engineer\" && \"Security architecture and implementation\"}\n                          </div>\n                        </div>\n                      ))}\n                    </div>\n                    \n                    {/* Image */}\n                    <div className=\"relative\">\n                      <div className=\"relative h-full min-h-[140px] lg:min-h-[180px] bg-white/15 backdrop-blur-sm rounded-2xl overflow-hidden border border-white/25 shadow-lg\">\n                        <div className=\"absolute inset-0 bg-gradient-to-br from-white/5 to-white/10\"></div>\n                        <div className=\"relative z-10 h-full flex items-center justify-center p-4\">\n                          <img \n                            src=\"/images/about-us-image-3.jpg\" \n                            alt=\"Cybersecurity Assessment\" \n                            className=\"w-full h-full object-cover rounded-xl\"\n                          />\n                          <div className=\"absolute inset-0 bg-gradient-to-br from-[var(--color-blue)]/20 to-[var(--color-blue-secondary)]/20 rounded-xl\"></div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n                \n                <div className=\"flex items-center justify-between pt-6 border-t border-white/20\">\n                  <div className=\"flex items-center gap-4 text-white/90\">\n                    <div className=\"flex items-center gap-2 bg-white/10 px-3 py-2 rounded-xl\">\n                      <Award className=\"w-4 h-4\" />\n                      <span className=\"text-sm font-medium\">Certified</span>\n                    </div>\n                    <div className=\"flex items-center gap-2 bg-white/10 px-3 py-2 rounded-xl\">\n                      <Users className=\"w-4 h-4\" />\n                      <span className=\"text-sm font-medium\">Career-focused</span>\n                    </div>\n                  </div>\n                  <ArrowRight className=\"w-6 h-6 text-white group-hover:translate-x-2 transition-transform\" />\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Medium Card - Authentication & Auth */}\n          <div className=\"col-span-12 lg:col-span-4 row-span-1 group relative\">\n            <div className=\"relative h-full bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-3xl p-6 overflow-hidden\">\n              <div className=\"absolute inset-0 bg-[url('/images/hexagon.svg')] bg-cover opacity-5\"></div>\n              <div className=\"absolute inset-0 bg-gradient-to-br from-[var(--color-blue)]/95 to-[var(--color-blue-secondary)]/95\"></div>\n              \n              <div className=\"relative z-10 h-full flex flex-col justify-between\">\n                <div className=\"flex-1\">\n                  <div className=\"w-12 h-12 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center mb-4 shadow-lg\">\n                    <Lock className=\"w-6 h-6 text-white\" />\n                  </div>\n                  <h3 className=\"text-xl font-bold text-white mb-3 leading-tight\">Authentication & Auth</h3>\n                  <p className=\"text-white/80 text-sm mb-4 leading-relaxed\">Deep dive into modern authentication systems</p>\n                  \n                  <div className=\"space-y-3\">\n                    {curriculumItems[5].features.map((feature, index) => (\n                      <div key={index} className=\"flex items-center gap-3 bg-white/10 rounded-xl p-3\">\n                        <div className=\"w-2 h-2 bg-[var(--color-yellow)] rounded-full flex-shrink-0\"></div>\n                        <span className=\"text-white/90 text-sm font-medium leading-tight\">{feature}</span>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n                \n                <ArrowRight className=\"w-5 h-5 text-white/60 group-hover:translate-x-1 transition-transform mt-4\" />\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom CTA */}\n        <div className=\"text-center pb-8\">\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\">\n            <PrimaryButton className=\"inline-flex items-center gap-3 sm:gap-6 px-6 sm:px-10 py-4 sm:py-5 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer group\">\n              <Shield className=\"w-5 h-5 sm:w-6 sm:h-6\" />\n              <span className=\"font-semibold text-base sm:text-lg lg:text-xl\">Join 500+ Security Professionals</span>\n              <ArrowRight className=\"w-5 h-5 sm:w-6 sm:h-6 group-hover:translate-x-2 transition-transform\" />\n            </PrimaryButton>\n            \n            <a href=\"/CyberSecTraining\" className=\"inline-flex items-center gap-3 sm:gap-6 px-6 sm:px-10 py-4 sm:py-5 rounded-2xl bg-gradient-to-r from-[var(--color-dark-blue)] to-[var(--color-dark-blue-hover)] text-white shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer group\">\n              <BookOpen className=\"w-5 h-5 sm:w-6 sm:h-6\" />\n              <span className=\"font-semibold text-base sm:text-lg lg:text-xl\">Advanced Training Program</span>\n              <ArrowRight className=\"w-5 h-5 sm:w-6 sm:h-6 group-hover:translate-x-2 transition-transform\" />\n            </a>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n} "], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AALA;;;;;;AAOA,MAAM,kBAAkB;IACtB;QACE,MAAM,iNAAA,CAAA,WAAQ;QACd,OAAO;QACP,aAAa;QACb,OAAO;QACP,OAAO;QACP,MAAM;QACN,UAAU;QACV,UAAU;YAAC;YAAgB;YAA4B;SAAiB;IAC1E;IACA;QACE,MAAM,uMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;QACb,OAAO;QACP,OAAO;QACP,MAAM;QACN,UAAU;QACV,UAAU;YAAC;YAAkB;YAAiB;SAAgB;IAChE;IACA;QACE,MAAM,qMAAA,CAAA,OAAI;QACV,OAAO;QACP,aAAa;QACb,OAAO;QACP,OAAO;QACP,MAAM;QACN,UAAU;QACV,UAAU;YAAC;YAAqB;YAAkB;SAAgB;IACpE;IACA;QACE,MAAM,2MAAA,CAAA,UAAO;QACb,OAAO;QACP,aAAa;QACb,OAAO;QACP,OAAO;QACP,MAAM;QACN,UAAU;QACV,UAAU;YAAC;YAAS;YAAmB;SAAe;IACxD;IACA;QACE,MAAM,yMAAA,CAAA,SAAM;QACZ,OAAO;QACP,aAAa;QACb,OAAO;QACP,OAAO;QACP,MAAM;QACN,UAAU;QACV,UAAU;YAAC;YAAsB;YAAoB;SAAoB;IAC3E;IACA;QACE,MAAM,qMAAA,CAAA,OAAI;QACV,OAAO;QACP,aAAa;QACb,OAAO;QACP,OAAO;QACP,MAAM;QACN,UAAU;QACV,UAAU;YAAC;YAAa;YAAO;SAAoB;IACrD;CACD;AAEc,SAAS;IACtB,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,mMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;kDACf,6LAAC;wCAAK,WAAU;kDAAiD;;;;;;;;;;;;0CAGnE,6LAAC;gCAAG,WAAU;;oCAAuH;kDAEnI,6LAAC;wCAAK,WAAU;kDAAiH;;;;;;;;;;;;0CAKnI,6LAAC;gCAAE,WAAU;0CAA4G;;;;;;;;;;;;kCAO3H,6LAAC;wBAAI,WAAU;wBAA0E,OAAO;4BAAE,kBAAkB;wBAAiC;;0CAEjJ,6LAAC;gCAAI,WAAU;0CACf,cAAA,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;sDAEf,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC,iNAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;;;;;;8EAEtB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAG,WAAU;sFAA2E;;;;;;sFACzF,6LAAC;4EAAE,WAAU;sFAAqD;;;;;;;;;;;;;;;;;;sEAKtE,6LAAC;4DAAI,WAAU;;8EAEb,6LAAC;oEAAI,WAAU;8EACZ,eAAe,CAAC,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBACzC,6LAAC;4EAAgB,WAAU;;8FACzB,6LAAC;oFAAI,WAAU;8FAAoE;;;;;;8FACnF,6LAAC;oFAAI,WAAU;;wFACZ,YAAY,kBAAkB;wFAC9B,YAAY,8BAA8B;wFAC1C,YAAY,oBAAoB;;;;;;;;2EAL3B;;;;;;;;;;8EAYd,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAI,WAAU;;;;;;0FACf,6LAAC;gFAAI,WAAU;;kGACb,6LAAC;wFACC,KAAI;wFACJ,KAAI;wFACJ,WAAU;;;;;;kGAEZ,6LAAC;wFAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8DAOzB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,uMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;sFACjB,6LAAC;4EAAK,WAAU;sFAAsB;;;;;;;;;;;;8EAExC,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,qMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;sFAChB,6LAAC;4EAAK,WAAU;sFAAsB;;;;;;;;;;;;;;;;;;sEAG1C,6LAAC,qNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOxB,6LAAC;gCAAI,WAAU;0CACnB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;sDAEf,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;;;;;;sEAEnB,6LAAC;4DAAG,WAAU;sEAAkD;;;;;;sEAChE,6LAAC;4DAAE,WAAU;sEAA6C;;;;;;sEAE1D,6LAAC;4DAAI,WAAU;sEACZ,eAAe,CAAC,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBACzC,6LAAC;oEAAgB,WAAU;;sFACzB,6LAAC;4EAAI,WAAU;;;;;;sFACf,6LAAC;4EAAK,WAAU;sFAAmD;;;;;;;mEAF3D;;;;;;;;;;;;;;;;8DAQhB,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0CAM5B,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;sDAEf,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;sEAElB,6LAAC;4DAAG,WAAU;sEAAkD;;;;;;sEAChE,6LAAC;4DAAE,WAAU;sEAA6C;;;;;;sEAE1D,6LAAC;4DAAI,WAAU;sEACZ,eAAe,CAAC,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBACzC,6LAAC;oEAAgB,WAAU;;sFACzB,6LAAC;4EAAI,WAAU;;;;;;sFACf,6LAAC;4EAAK,WAAU;sFAAmD;;;;;;;mEAF3D;;;;;;;;;;;;;;;;8DAQhB,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0CAM5B,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,2MAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;;;;;;kEAErB,6LAAC;wDAAG,WAAU;kEAAqE;;;;;;kEACnF,6LAAC;wDAAE,WAAU;kEAA6D;;;;;;;;;;;;0DAG5E,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0CAM5B,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;sDAEf,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;;;;;;8EAEpB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAG,WAAU;sFAA2E;;;;;;sFACzF,6LAAC;4EAAE,WAAU;sFAAqD;;;;;;;;;;;;;;;;;;sEAKtE,6LAAC;4DAAI,WAAU;;8EAEb,6LAAC;oEAAI,WAAU;8EACZ,eAAe,CAAC,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBACzC,6LAAC;4EAAgB,WAAU;;8FACzB,6LAAC;oFAAI,WAAU;8FAAoE;;;;;;8FACnF,6LAAC;oFAAI,WAAU;;wFACZ,YAAY,wBAAwB;wFACpC,YAAY,sBAAsB;wFAClC,YAAY,uBAAuB;;;;;;;;2EAL9B;;;;;;;;;;8EAYd,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAI,WAAU;;;;;;0FACf,6LAAC;gFAAI,WAAU;;kGACb,6LAAC;wFACC,KAAI;wFACJ,KAAI;wFACJ,WAAU;;;;;;kGAEZ,6LAAC;wFAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8DAOzB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,uMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;sFACjB,6LAAC;4EAAK,WAAU;sFAAsB;;;;;;;;;;;;8EAExC,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,uMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;sFACjB,6LAAC;4EAAK,WAAU;sFAAsB;;;;;;;;;;;;;;;;;;sEAG1C,6LAAC,qNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAO9B,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;sDAEf,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;sEAElB,6LAAC;4DAAG,WAAU;sEAAkD;;;;;;sEAChE,6LAAC;4DAAE,WAAU;sEAA6C;;;;;;sEAE1D,6LAAC;4DAAI,WAAU;sEACZ,eAAe,CAAC,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBACzC,6LAAC;oEAAgB,WAAU;;sFACzB,6LAAC;4EAAI,WAAU;;;;;;sFACf,6LAAC;4EAAK,WAAU;sFAAmD;;;;;;;mEAF3D;;;;;;;;;;;;;;;;8DAQhB,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO9B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,mJAAA,CAAA,gBAAa;oCAAC,WAAU;;sDACvB,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;4CAAK,WAAU;sDAAgD;;;;;;sDAChE,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;8CAGxB,6LAAC;oCAAE,MAAK;oCAAoB,WAAU;;sDACpC,6LAAC,iNAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6LAAC;4CAAK,WAAU;sDAAgD;;;;;;sDAChE,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpC;KAtSwB", "debugId": null}}, {"offset": {"line": 2200, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/training/components/WhoCanJoin.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport { User, GraduationCap, Briefcase, Star, ArrowRight, Shield, Zap } from 'lucide-react';\r\n\r\nconst audienceTypes = [\r\n  {\r\n    icon: User,\r\n    title: \"Cybersecurity Enthusiasts\",\r\n    subtitle: \"All Experience Levels\",\r\n    description: \"Whether you're new to the field or have some experience, our program caters to learners of all levels. Start with the free tier to build a strong foundation, then upgrade to the premium tier for a more immersive, mentor-guided experience.\",\r\n    features: [\"Free tier available\", \"Progressive learning path\", \"Mentor-guided experience\"],\r\n    color: \"from-[var(--color-blue)] to-[var(--color-blue-secondary)]\",\r\n    badge: \"All Levels\",\r\n    delay: 0.1\r\n  },\r\n  {\r\n    icon: GraduationCap,\r\n    title: \"Information Security Professionals\",\r\n    subtitle: \"Experienced Practitioners\",\r\n    description: \"If you have been practicing cybersecurity topics, you can join the premium tier to take your skills to the next level through hands-on projects and industry-relevant training.\",\r\n    features: [\"Advanced skill development\", \"Industry-relevant training\", \"Hands-on projects\"],\r\n    color: \"from-[var(--color-dark-blue)] to-[var(--color-dark-blue-hover)]\",\r\n    badge: \"Advanced\",\r\n    delay: 0.3\r\n  },\r\n  {\r\n    icon: Briefcase,\r\n    title: \"Career Switchers\",\r\n    subtitle: \"Transitioning to Security\",\r\n    description: \"Looking to transition into the exciting world of cybersecurity? Our program provides the knowledge and practical experience you need to kickstart your career in this high-demand field.\",\r\n    features: [\"Career transition support\", \"Practical experience\", \"High-demand field\"],\r\n    color: \"from-[var(--color-yellow)] to-[var(--color-yellow-hover)]\",\r\n    badge: \"Career Change\",\r\n    delay: 0.5\r\n  }\r\n];\r\n\r\nexport default function WhoCanJoin() {\r\n  return (\r\n    <section className=\"py-12 sm:py-16 lg:py-24 bg-white relative overflow-hidden\">\r\n      {/* Background Elements */}\r\n      <div className=\"absolute inset-0\">\r\n        <div className=\"absolute top-0 left-1/4 w-96 h-96 bg-gradient-to-br from-[var(--color-blue)]/10 to-[var(--color-blue-secondary)]/10 rounded-full blur-3xl\"></div>\r\n        <div className=\"absolute bottom-0 right-1/4 w-96 h-96 bg-gradient-to-br from-[var(--color-yellow)]/10 to-[var(--color-yellow-hover)]/10 rounded-full blur-3xl\"></div>\r\n      </div>\r\n\r\n      <div className=\"relative z-10 container mx-auto px-4 max-w-7xl\">\r\n        {/* Header */}\r\n        <motion.div \r\n          initial={{ opacity: 0, y: 30 }}\r\n          whileInView={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.8 }}\r\n          viewport={{ once: true }}\r\n          className=\"text-center mb-16\"\r\n        >\r\n          <motion.div \r\n            initial={{ opacity: 0, scale: 0.8 }}\r\n            whileInView={{ opacity: 1, scale: 1 }}\r\n            transition={{ delay: 0.2, duration: 0.6 }}\r\n            viewport={{ once: true }}\r\n            className=\"inline-flex items-center bg-gradient-to-r from-[var(--color-blue)]/10 to-[var(--color-blue-secondary)]/10 px-4 py-2 rounded-full mb-6\"\r\n          >\r\n            <Star className=\"w-4 h-4 text-[var(--color-blue)] mr-2\" />\r\n            <span className=\"text-sm font-medium text-[var(--color-blue)]\">Who Can Join</span>\r\n          </motion.div>\r\n          \r\n          <motion.h2 \r\n            initial={{ opacity: 0, y: 20 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ delay: 0.4, duration: 0.6 }}\r\n            viewport={{ once: true }}\r\n            className=\"text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-bold text-[var(--color-dark-blue)] mb-6\"\r\n          >\r\n            So Who Can Take Up\r\n            <span className=\"block bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] bg-clip-text text-transparent\">\r\n              This Training?\r\n            </span>\r\n          </motion.h2>\r\n          \r\n          <motion.p \r\n            initial={{ opacity: 0, y: 20 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ delay: 0.6, duration: 0.6 }}\r\n            viewport={{ once: true }}\r\n            className=\"text-base sm:text-lg lg:text-xl text-[var(--foreground-secondary)] max-w-4xl mx-auto leading-relaxed px-4\"\r\n          >\r\n            Our program is designed to be accessible and beneficial for a wide range of learners, from beginners to experienced professionals. Whether you're looking to start your cybersecurity journey or take it to new heights, we've got you covered.\r\n          </motion.p>\r\n        </motion.div>\r\n\r\n        {/* Cards with Equal Heights */}\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 lg:gap-12\">\r\n          {audienceTypes.map((audience, index) => (\r\n            <motion.div\r\n              key={index}\r\n              initial={{ opacity: 0, y: 50 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ delay: audience.delay, duration: 0.8 }}\r\n              viewport={{ once: true }}\r\n              whileHover={{ \r\n                y: -10,\r\n                transition: { duration: 0.3 }\r\n              }}\r\n              className=\"group relative h-full\"\r\n            >\r\n              {/* Card with Equal Height */}\r\n              <div className=\"relative bg-white rounded-3xl p-8 shadow-[0_20px_40px_rgb(0,0,0,0.08)] hover:shadow-[0_30px_60px_rgb(0,0,0,0.12)] transition-all duration-500 border border-gray-100 h-full flex flex-col\">\r\n                \r\n                {/* Fixed Badge - No Transparency Issues */}\r\n                <motion.div \r\n                  initial={{ opacity: 0, scale: 0 }}\r\n                  whileInView={{ opacity: 1, scale: 1 }}\r\n                  transition={{ delay: audience.delay + 0.2, duration: 0.5 }}\r\n                  viewport={{ once: true }}\r\n                  className={`absolute -top-4 left-8 bg-gradient-to-r ${audience.color} text-white px-4 py-2 rounded-full text-sm font-semibold shadow-lg z-10 border-2 border-white`}\r\n                >\r\n                  {audience.badge}\r\n                </motion.div>\r\n\r\n                {/* Icon Container */}\r\n                <motion.div \r\n                  whileHover={{ \r\n                    rotate: 360,\r\n                    scale: 1.1\r\n                  }}\r\n                  transition={{ duration: 0.6 }}\r\n                  className={`w-20 h-20 bg-gradient-to-br ${audience.color} rounded-3xl flex items-center justify-center mb-6 shadow-xl group-hover:shadow-2xl transition-all duration-300`}\r\n                >\r\n                  <audience.icon className=\"w-10 h-10 text-white\" />\r\n                </motion.div>\r\n\r\n                {/* Content - Flex Grow to Fill Space */}\r\n                <div className=\"space-y-4 flex-1 flex flex-col\">\r\n                  <div>\r\n                    <h3 className=\"text-2xl font-bold text-[var(--color-dark-blue)] mb-1\">\r\n                      {audience.title}\r\n                    </h3>\r\n                    <p className=\"text-[var(--foreground-secondary)] font-medium\">\r\n                      {audience.subtitle}\r\n                    </p>\r\n                  </div>\r\n                  \r\n                  <p className=\"text-[var(--foreground-secondary)] leading-relaxed flex-1\">\r\n                    {audience.description}\r\n                  </p>\r\n\r\n                  {/* Features List */}\r\n                  <ul className=\"space-y-2 mb-6\">\r\n                    {audience.features.map((feature, featureIndex) => (\r\n                      <motion.li \r\n                        key={featureIndex}\r\n                        initial={{ opacity: 0, x: -20 }}\r\n                        whileInView={{ opacity: 1, x: 0 }}\r\n                        transition={{ delay: audience.delay + 0.3 + featureIndex * 0.1, duration: 0.5 }}\r\n                        viewport={{ once: true }}\r\n                        className=\"flex items-center gap-3 text-[var(--foreground-secondary)]\"\r\n                      >\r\n                        <div className={`w-2 h-2 bg-gradient-to-r ${audience.color} rounded-full flex-shrink-0`} />\r\n                        <span className=\"text-sm\">{feature}</span>\r\n                      </motion.li>\r\n                    ))}\r\n                  </ul>\r\n\r\n                  {/* CTA Button - Fixed at Bottom */}\r\n                  <motion.button\r\n                    whileHover={{ scale: 1.05 }}\r\n                    whileTap={{ scale: 0.95 }}\r\n                    className={`w-full bg-gradient-to-r ${audience.color} text-white py-3 px-6 rounded-2xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center gap-2 group/btn mt-auto`}\r\n                  >\r\n                    Get Started\r\n                    <ArrowRight className=\"w-4 h-4 group-hover/btn:translate-x-1 transition-transform\" />\r\n                  </motion.button>\r\n                </div>\r\n\r\n                {/* Enhanced Claymorphic Effects */}\r\n                <div className=\"absolute inset-0 bg-gradient-to-br from-white/30 to-transparent rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none\" />\r\n                <div className={`absolute inset-0 bg-gradient-to-br ${audience.color} rounded-3xl opacity-0 group-hover:opacity-5 transition-opacity duration-300 blur-xl pointer-events-none`} />\r\n              </div>\r\n            </motion.div>\r\n          ))}\r\n        </div>\r\n\r\n        {/* Bottom Stats */}\r\n        <motion.div \r\n          initial={{ opacity: 0, y: 30 }}\r\n          whileInView={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.8, duration: 0.6 }}\r\n          viewport={{ once: true }}\r\n          className=\"mt-20 grid grid-cols-1 md:grid-cols-3 gap-8\"\r\n        >\r\n          <div className=\"text-center\">\r\n            <div className=\"text-4xl font-bold text-[var(--color-blue)] mb-2\">95%</div>\r\n            <div className=\"text-[var(--foreground-secondary)]\">Success Rate</div>\r\n          </div>\r\n          <div className=\"text-center\">\r\n            <div className=\"text-4xl font-bold text-[var(--color-dark-blue)] mb-2\">24/7</div>\r\n            <div className=\"text-[var(--foreground-secondary)]\">Support Available</div>\r\n          </div>\r\n          <div className=\"text-center\">\r\n            <div className=\"text-4xl font-bold text-[var(--color-yellow)] mb-2\">30 Days</div>\r\n            <div className=\"text-[var(--foreground-secondary)]\">Money Back Guarantee</div>\r\n          </div>\r\n        </motion.div>\r\n      </div>\r\n    </section>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAMA,MAAM,gBAAgB;IACpB;QACE,MAAM,qMAAA,CAAA,OAAI;QACV,OAAO;QACP,UAAU;QACV,aAAa;QACb,UAAU;YAAC;YAAuB;YAA6B;SAA2B;QAC1F,OAAO;QACP,OAAO;QACP,OAAO;IACT;IACA;QACE,MAAM,2NAAA,CAAA,gBAAa;QACnB,OAAO;QACP,UAAU;QACV,aAAa;QACb,UAAU;YAAC;YAA8B;YAA8B;SAAoB;QAC3F,OAAO;QACP,OAAO;QACP,OAAO;IACT;IACA;QACE,MAAM,+MAAA,CAAA,YAAS;QACf,OAAO;QACP,UAAU;QACV,aAAa;QACb,UAAU;YAAC;YAA6B;YAAwB;SAAoB;QACpF,OAAO;QACP,OAAO;QACP,OAAO;IACT;CACD;AAEc,SAAS;IACtB,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,aAAa;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCACpC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAEV,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,6LAAC;wCAAK,WAAU;kDAA+C;;;;;;;;;;;;0CAGjE,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;gCACR,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;oCACX;kDAEC,6LAAC;wCAAK,WAAU;kDAAiH;;;;;;;;;;;;0CAKnI,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;gCACP,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;0CACX;;;;;;;;;;;;kCAMH,6LAAC;wBAAI,WAAU;kCACZ,cAAc,GAAG,CAAC,CAAC,UAAU,sBAC5B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,OAAO,SAAS,KAAK;oCAAE,UAAU;gCAAI;gCACnD,UAAU;oCAAE,MAAM;gCAAK;gCACvB,YAAY;oCACV,GAAG,CAAC;oCACJ,YAAY;wCAAE,UAAU;oCAAI;gCAC9B;gCACA,WAAU;0CAGV,cAAA,6LAAC;oCAAI,WAAU;;sDAGb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,SAAS;gDAAG,OAAO;4CAAE;4CAChC,aAAa;gDAAE,SAAS;gDAAG,OAAO;4CAAE;4CACpC,YAAY;gDAAE,OAAO,SAAS,KAAK,GAAG;gDAAK,UAAU;4CAAI;4CACzD,UAAU;gDAAE,MAAM;4CAAK;4CACvB,WAAW,AAAC,2CAAyD,OAAf,SAAS,KAAK,EAAC;sDAEpE,SAAS,KAAK;;;;;;sDAIjB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,YAAY;gDACV,QAAQ;gDACR,OAAO;4CACT;4CACA,YAAY;gDAAE,UAAU;4CAAI;4CAC5B,WAAW,AAAC,+BAA6C,OAAf,SAAS,KAAK,EAAC;sDAEzD,cAAA,6LAAC,SAAS,IAAI;gDAAC,WAAU;;;;;;;;;;;sDAI3B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEACX,SAAS,KAAK;;;;;;sEAEjB,6LAAC;4DAAE,WAAU;sEACV,SAAS,QAAQ;;;;;;;;;;;;8DAItB,6LAAC;oDAAE,WAAU;8DACV,SAAS,WAAW;;;;;;8DAIvB,6LAAC;oDAAG,WAAU;8DACX,SAAS,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC/B,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;4DAER,SAAS;gEAAE,SAAS;gEAAG,GAAG,CAAC;4DAAG;4DAC9B,aAAa;gEAAE,SAAS;gEAAG,GAAG;4DAAE;4DAChC,YAAY;gEAAE,OAAO,SAAS,KAAK,GAAG,MAAM,eAAe;gEAAK,UAAU;4DAAI;4DAC9E,UAAU;gEAAE,MAAM;4DAAK;4DACvB,WAAU;;8EAEV,6LAAC;oEAAI,WAAW,AAAC,4BAA0C,OAAf,SAAS,KAAK,EAAC;;;;;;8EAC3D,6LAAC;oEAAK,WAAU;8EAAW;;;;;;;2DARtB;;;;;;;;;;8DAcX,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oDACZ,YAAY;wDAAE,OAAO;oDAAK;oDAC1B,UAAU;wDAAE,OAAO;oDAAK;oDACxB,WAAW,AAAC,2BAAyC,OAAf,SAAS,KAAK,EAAC;;wDACtD;sEAEC,6LAAC,qNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;;;;;;;;sDAK1B,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAW,AAAC,sCAAoD,OAAf,SAAS,KAAK,EAAC;;;;;;;;;;;;+BAlFlE;;;;;;;;;;kCAyFX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,OAAO;4BAAK,UAAU;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAmD;;;;;;kDAClE,6LAAC;wCAAI,WAAU;kDAAqC;;;;;;;;;;;;0CAEtD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAwD;;;;;;kDACvE,6LAAC;wCAAI,WAAU;kDAAqC;;;;;;;;;;;;0CAEtD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAqD;;;;;;kDACpE,6LAAC;wCAAI,WAAU;kDAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMhE;KAzKwB", "debugId": null}}, {"offset": {"line": 2744, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/training/components/LearningModules.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useRef } from 'react';\r\nimport { motion, useScroll, useTransform } from 'framer-motion';\r\nimport { ChevronLeft, ChevronRight, Play, Lock, CheckCircle, Clock, Users } from 'lucide-react';\r\n\r\nconst learningModules = [\r\n  {\r\n    id: 1,\r\n    title: \"Web Application Security\",\r\n    subtitle: \"Fortifying the digital frontline against cyber threats\",\r\n    description: \"Master web app security from OWASP Top 10 to advanced exploitation techniques\",\r\n    progress: 85,\r\n    duration: \"12 hours\",\r\n    students: 234,\r\n    lessons: 24,\r\n    color: \"from-[var(--color-blue)] to-[var(--color-blue-secondary)]\",\r\n    status: \"in-progress\",\r\n    image: \"/images/web-security.jpg\"\r\n  },\r\n  {\r\n    id: 2,\r\n    title: \"API & Network Security\",\r\n    subtitle: \"Safeguarding the backbone of modern interconnected systems\",\r\n    description: \"Learn to identify and exploit API vulnerabilities in modern applications\",\r\n    progress: 60,\r\n    duration: \"8 hours\",\r\n    students: 189,\r\n    lessons: 18,\r\n    color: \"from-[var(--color-dark-blue)] to-[var(--color-dark-blue-hover)]\",\r\n    status: \"in-progress\",\r\n    image: \"/images/api-security.jpg\"\r\n  },\r\n  {\r\n    id: 3,\r\n    title: \"Practical Skills Development\",\r\n    subtitle: \"Honing real-world cybersecurity expertise through hands-on learning\",\r\n    description: \"Comprehensive network security assessment and exploitation techniques\",\r\n    progress: 0,\r\n    duration: \"15 hours\",\r\n    students: 156,\r\n    lessons: 32,\r\n    color: \"from-[var(--color-blue)] to-[var(--color-blue-secondary)]\",\r\n    status: \"locked\",\r\n    image: \"/images/network-security.jpg\"\r\n  },\r\n  {\r\n    id: 4,\r\n    title: \"Soft Skill & Professional Growth\",\r\n    subtitle: \"Cultivating the human element in technical cybersecurity roles\",\r\n    description: \"Security testing for mobile applications and reverse engineering\",\r\n    progress: 0,\r\n    duration: \"10 hours\",\r\n    students: 98,\r\n    lessons: 20,\r\n    color: \"from-[var(--color-blue)] to-[var(--color-blue-secondary)]\",\r\n    status: \"locked\",\r\n    image: \"/images/mobile-security.jpg\"\r\n  },\r\n  {\r\n    id: 5,\r\n    title: \"Real World Environment Navigation\",\r\n    subtitle: \"Mastering the art of securing complex, live digital ecosystems\",\r\n    description: \"Cloud infrastructure security and misconfiguration exploitation\",\r\n    progress: 0,\r\n    duration: \"14 hours\",\r\n    students: 76,\r\n    lessons: 28,\r\n    color: \"from-[var(--color-dark-blue)] to-[var(--color-dark-blue-subtle)]\",\r\n    status: \"locked\",\r\n    image: \"/images/cloud-security.jpg\"\r\n  },\r\n  {\r\n    id: 6,\r\n    title: \"Active Directory & Cloud Security\",\r\n    subtitle: \"Protecting the nerve centers of enterprise and cloud infrastructures\",\r\n    description: \"Psychological manipulation techniques and defense strategies\",\r\n    progress: 0,\r\n    duration: \"6 hours\",\r\n    students: 45,\r\n    lessons: 12,\r\n    color: \"from-[var(--color-blue)] to-[var(--color-blue-secondary)]\",\r\n    status: \"locked\",\r\n    image: \"/images/social-engineering.jpg\"\r\n  },\r\n  {\r\n    id: 7,\r\n    title: \"Continuous Learning & Adaption\",\r\n    subtitle: \"Staying ahead in the ever-evolving cybersecurity landscape\",\r\n    description: \"Advanced techniques for continuous learning and adaptation\",\r\n    progress: 0,\r\n    duration: \"8 hours\",\r\n    students: 67,\r\n    lessons: 16,\r\n    color: \"from-[var(--color-blue)] to-[var(--color-blue-secondary)]\",\r\n    status: \"locked\",\r\n    image: \"/images/continuous-learning.jpg\"\r\n  },\r\n  {\r\n    id: 8,\r\n    title: \"Report Writing Skills\",\r\n    subtitle: \"Crafting clear, concise, and impactful cybersecurity documentation\",\r\n    description: \"Professional documentation and stakeholder communication\",\r\n    progress: 0,\r\n    duration: \"5 hours\",\r\n    students: 89,\r\n    lessons: 10,\r\n    color: \"from-[var(--color-dark-blue)] to-[var(--color-dark-blue-hover)]\",\r\n    status: \"locked\",\r\n    image: \"/images/report-writing.jpg\"\r\n  }\r\n];\r\n\r\nexport default function LearningModules() {\r\n  const containerRef = useRef(null);\r\n  const scrollContainerRef = useRef(null);\r\n\r\n  const scrollLeft = () => {\r\n    if (scrollContainerRef.current) {\r\n      scrollContainerRef.current.scrollBy({ left: -400, behavior: 'smooth' });\r\n    }\r\n  };\r\n\r\n  const scrollRight = () => {\r\n    if (scrollContainerRef.current) {\r\n      scrollContainerRef.current.scrollBy({ left: 400, behavior: 'smooth' });\r\n    }\r\n  };\r\n\r\n  return (\r\n    <section className=\"py-24 bg-gradient-to-br from-[var(--color-dark-blue)] via-[var(--color-dark-blue-hover)] to-[var(--color-dark-blue-bg)] relative overflow-hidden\">\r\n      {/* Background Pattern */}\r\n      <div className=\"absolute inset-0\">\r\n        <div className=\"absolute top-0 left-0 w-full h-full bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.03%22%3E%3Ccircle%20cx%3D%2230%22%20cy%3D%2230%22%20r%3D%221%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-50\"></div>\r\n      </div>\r\n\r\n      <div className=\"relative z-10 container mx-auto px-4 max-w-7xl\">\r\n        {/* Header */}\r\n        <motion.div \r\n          initial={{ opacity: 0, y: 30 }}\r\n          whileInView={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.8 }}\r\n          viewport={{ once: true }}\r\n          className=\"text-center mb-16\"\r\n        >\r\n          <motion.div \r\n            initial={{ opacity: 0, scale: 0.8 }}\r\n            whileInView={{ opacity: 1, scale: 1 }}\r\n            transition={{ delay: 0.2, duration: 0.6 }}\r\n            viewport={{ once: true }}\r\n            className=\"inline-flex items-center bg-[var(--color-blue)]/20 px-4 py-2 rounded-full mb-6\"\r\n          >\r\n            <Play className=\"w-4 h-4 text-[var(--color-blue)] mr-2\" />\r\n            <span className=\"text-sm font-medium text-[var(--color-blue)]\">Here's A Short Teaser</span>\r\n          </motion.div>\r\n          \r\n          <motion.h2 \r\n            initial={{ opacity: 0, y: 20 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ delay: 0.4, duration: 0.6 }}\r\n            viewport={{ once: true }}\r\n            className=\"text-4xl lg:text-5xl font-bold text-white mb-6\"\r\n          >\r\n            Of What You Will\r\n            <span className=\"block bg-gradient-to-r from-[var(--color-blue)] via-[var(--color-blue-secondary)] to-[var(--color-blue-secondary)] bg-clip-text text-transparent\">\r\n              Learn\r\n            </span>\r\n          </motion.h2>\r\n          \r\n          <motion.p \r\n            initial={{ opacity: 0, y: 20 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ delay: 0.6, duration: 0.6 }}\r\n            viewport={{ once: true }}\r\n            className=\"text-xl text-white/80 max-w-4xl mx-auto leading-relaxed\"\r\n          >\r\n            Explore our comprehensive curriculum covering essential cybersecurity areas including web application security, \r\n            network penetration testing, cloud security, API security, and ethical hacking fundamentals.\r\n          </motion.p>\r\n        </motion.div>\r\n\r\n        {/* Navigation Controls */}\r\n        <div className=\"flex justify-between items-center mb-8\">\r\n          <motion.button\r\n            whileHover={{ scale: 1.1 }}\r\n            whileTap={{ scale: 0.9 }}\r\n            onClick={scrollLeft}\r\n            className=\"w-12 h-12 bg-white/10 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-white/20 transition-all duration-300 border border-white/20\"\r\n          >\r\n            <ChevronLeft className=\"w-6 h-6\" />\r\n          </motion.button>\r\n          \r\n          <div className=\"text-center\">\r\n            <p className=\"text-white/70 text-sm\">Scroll to explore modules</p>\r\n          </div>\r\n          \r\n          <motion.button\r\n            whileHover={{ scale: 1.1 }}\r\n            whileTap={{ scale: 0.9 }}\r\n            onClick={scrollRight}\r\n            className=\"w-12 h-12 bg-white/10 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-white/20 transition-all duration-300 border border-white/20\"\r\n          >\r\n            <ChevronRight className=\"w-6 h-6\" />\r\n          </motion.button>\r\n        </div>\r\n\r\n        {/* Scrollable Modules Container */}\r\n        <div \r\n          ref={scrollContainerRef}\r\n          className=\"flex gap-6 overflow-x-auto scrollbar-hide scroll-smooth pb-8\"\r\n          style={{ scrollSnapType: 'x mandatory' }}\r\n        >\r\n          {learningModules.map((module, index) => (\r\n            <motion.div\r\n              key={module.id}\r\n              initial={{ opacity: 0, x: 50 }}\r\n              whileInView={{ opacity: 1, x: 0 }}\r\n              transition={{ delay: index * 0.1, duration: 0.6 }}\r\n              viewport={{ once: true }}\r\n              whileHover={{ \r\n                y: -10,\r\n                scale: 1.02,\r\n                transition: { duration: 0.2 }\r\n              }}\r\n              className=\"group relative flex-shrink-0 w-80 bg-white/10 backdrop-blur-xl rounded-3xl p-6 border border-white/20 cursor-pointer\"\r\n              style={{ scrollSnapAlign: 'start' }}\r\n            >\r\n              {/* Module Image */}\r\n              <div className=\"relative h-40 bg-gradient-to-br from-gray-800 to-gray-900 rounded-2xl mb-6 overflow-hidden\">\r\n                <div className={`absolute inset-0 bg-gradient-to-br ${module.color} opacity-20`} />\r\n                <div className=\"absolute inset-0 flex items-center justify-center\">\r\n                  <div className={`w-16 h-16 bg-gradient-to-br ${module.color} rounded-2xl flex items-center justify-center shadow-xl`}>\r\n                    {module.status === 'locked' ? (\r\n                      <Lock className=\"w-8 h-8 text-white\" />\r\n                    ) : (\r\n                      <Play className=\"w-8 h-8 text-white\" />\r\n                    )}\r\n                  </div>\r\n                </div>\r\n                \r\n                {/* Progress Ring */}\r\n                {module.status === 'in-progress' && (\r\n                  <div className=\"absolute top-4 right-4\">\r\n                    <svg className=\"w-12 h-12 transform -rotate-90\" viewBox=\"0 0 36 36\">\r\n                      <path\r\n                        className=\"text-gray-700\"\r\n                        stroke=\"currentColor\"\r\n                        strokeWidth=\"2\"\r\n                        fill=\"none\"\r\n                        d=\"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831\"\r\n                      />\r\n                      <path\r\n                        className=\"text-[var(--color-blue)]\"\r\n                        stroke=\"currentColor\"\r\n                        strokeWidth=\"2\"\r\n                        strokeDasharray={`${module.progress}, 100`}\r\n                        fill=\"none\"\r\n                        d=\"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831\"\r\n                      />\r\n                    </svg>\r\n                    <div className=\"absolute inset-0 flex items-center justify-center text-xs font-bold text-white\">\r\n                      {module.progress}%\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n\r\n              {/* Module Content */}\r\n              <div className=\"space-y-4\">\r\n                <div>\r\n                  <h3 className=\"text-xl font-bold text-white mb-1 group-hover:text-[var(--color-blue)] transition-colors\">\r\n                    {module.title}\r\n                  </h3>\r\n                  <p className=\"text-[var(--color-blue)] text-sm font-medium\">\r\n                    {module.subtitle}\r\n                  </p>\r\n                </div>\r\n                \r\n                <p className=\"text-white/70 text-sm leading-relaxed\">\r\n                  {module.description}\r\n                </p>\r\n\r\n                {/* Module Stats */}\r\n                <div className=\"flex items-center justify-between text-sm text-white/60\">\r\n                  <div className=\"flex items-center gap-1\">\r\n                    <Clock className=\"w-4 h-4\" />\r\n                    <span>{module.duration}</span>\r\n                  </div>\r\n                  <div className=\"flex items-center gap-1\">\r\n                    <Users className=\"w-4 h-4\" />\r\n                    <span>{module.students}</span>\r\n                  </div>\r\n                  <div className=\"flex items-center gap-1\">\r\n                    <CheckCircle className=\"w-4 h-4\" />\r\n                    <span>{module.lessons} lessons</span>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Status Badge */}\r\n                <div className=\"flex justify-between items-center\">\r\n                  <div className={`px-3 py-1 rounded-full text-xs font-semibold ${\r\n                    module.status === 'in-progress' \r\n                      ? 'bg-[var(--color-blue)]/20 text-[var(--color-blue)]' \r\n                      : 'bg-white/20 text-white/70'\r\n                  }`}>\r\n                    {module.status === 'in-progress' ? 'In Progress' : 'Locked'}\r\n                  </div>\r\n                  <motion.button\r\n                    whileHover={{ scale: 1.05 }}\r\n                    whileTap={{ scale: 0.95 }}\r\n                    className={`px-4 py-2 rounded-xl font-semibold text-sm transition-all duration-300 ${\r\n                      module.status === 'in-progress'\r\n                        ? 'bg-[var(--color-blue)] hover:bg-[var(--color-blue-secondary)] text-white'\r\n                        : 'bg-white/20 hover:bg-white/30 text-white/70'\r\n                    }`}\r\n                  >\r\n                    {module.status === 'in-progress' ? 'Continue' : 'Unlock'}\r\n                  </motion.button>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Hover Effects */}\r\n              <div className=\"absolute inset-0 bg-gradient-to-br from-[var(--color-blue)]/5 to-[var(--color-blue-secondary)]/5 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300\" />\r\n              <div className={`absolute inset-0 bg-gradient-to-br ${module.color} rounded-3xl opacity-0 group-hover:opacity-5 transition-opacity duration-300 blur-xl`} />\r\n            </motion.div>\r\n          ))}\r\n        </div>\r\n\r\n        {/* Scroll Indicator */}\r\n        <div className=\"flex justify-center mt-8\">\r\n          <div className=\"flex gap-2\">\r\n            {learningModules.map((_, index) => (\r\n              <div\r\n                key={index}\r\n                className=\"w-2 h-2 bg-white/30 rounded-full\"\r\n              />\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;;AAMA,MAAM,kBAAkB;IACtB;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,UAAU;QACV,UAAU;QACV,UAAU;QACV,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,UAAU;QACV,UAAU;QACV,UAAU;QACV,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,UAAU;QACV,UAAU;QACV,UAAU;QACV,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,UAAU;QACV,UAAU;QACV,UAAU;QACV,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,UAAU;QACV,UAAU;QACV,UAAU;QACV,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,UAAU;QACV,UAAU;QACV,UAAU;QACV,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,UAAU;QACV,UAAU;QACV,UAAU;QACV,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,UAAU;QACV,UAAU;QACV,UAAU;QACV,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;IACT;CACD;AAEc,SAAS;;IACtB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC5B,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAElC,MAAM,aAAa;QACjB,IAAI,mBAAmB,OAAO,EAAE;YAC9B,mBAAmB,OAAO,CAAC,QAAQ,CAAC;gBAAE,MAAM,CAAC;gBAAK,UAAU;YAAS;QACvE;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,mBAAmB,OAAO,EAAE;YAC9B,mBAAmB,OAAO,CAAC,QAAQ,CAAC;gBAAE,MAAM;gBAAK,UAAU;YAAS;QACtE;IACF;IAEA,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;;;;;;;;;;0BAGjB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,aAAa;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCACpC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAEV,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,6LAAC;wCAAK,WAAU;kDAA+C;;;;;;;;;;;;0CAGjE,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;gCACR,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;oCACX;kDAEC,6LAAC;wCAAK,WAAU;kDAAmJ;;;;;;;;;;;;0CAKrK,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;gCACP,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;0CACX;;;;;;;;;;;;kCAOH,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,YAAY;oCAAE,OAAO;gCAAI;gCACzB,UAAU;oCAAE,OAAO;gCAAI;gCACvB,SAAS;gCACT,WAAU;0CAEV,cAAA,6LAAC,uNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;0CAGzB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;0CAGvC,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,YAAY;oCAAE,OAAO;gCAAI;gCACzB,UAAU;oCAAE,OAAO;gCAAI;gCACvB,SAAS;gCACT,WAAU;0CAEV,cAAA,6LAAC,yNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAK5B,6LAAC;wBACC,KAAK;wBACL,WAAU;wBACV,OAAO;4BAAE,gBAAgB;wBAAc;kCAEtC,gBAAgB,GAAG,CAAC,CAAC,QAAQ,sBAC5B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,OAAO,QAAQ;oCAAK,UAAU;gCAAI;gCAChD,UAAU;oCAAE,MAAM;gCAAK;gCACvB,YAAY;oCACV,GAAG,CAAC;oCACJ,OAAO;oCACP,YAAY;wCAAE,UAAU;oCAAI;gCAC9B;gCACA,WAAU;gCACV,OAAO;oCAAE,iBAAiB;gCAAQ;;kDAGlC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAW,AAAC,sCAAkD,OAAb,OAAO,KAAK,EAAC;;;;;;0DACnE,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAW,AAAC,+BAA2C,OAAb,OAAO,KAAK,EAAC;8DACzD,OAAO,MAAM,KAAK,yBACjB,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;6EAEhB,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;;;;;;4CAMrB,OAAO,MAAM,KAAK,+BACjB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;wDAAiC,SAAQ;;0EACtD,6LAAC;gEACC,WAAU;gEACV,QAAO;gEACP,aAAY;gEACZ,MAAK;gEACL,GAAE;;;;;;0EAEJ,6LAAC;gEACC,WAAU;gEACV,QAAO;gEACP,aAAY;gEACZ,iBAAiB,AAAC,GAAkB,OAAhB,OAAO,QAAQ,EAAC;gEACpC,MAAK;gEACL,GAAE;;;;;;;;;;;;kEAGN,6LAAC;wDAAI,WAAU;;4DACZ,OAAO,QAAQ;4DAAC;;;;;;;;;;;;;;;;;;;kDAOzB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEACX,OAAO,KAAK;;;;;;kEAEf,6LAAC;wDAAE,WAAU;kEACV,OAAO,QAAQ;;;;;;;;;;;;0DAIpB,6LAAC;gDAAE,WAAU;0DACV,OAAO,WAAW;;;;;;0DAIrB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,6LAAC;0EAAM,OAAO,QAAQ;;;;;;;;;;;;kEAExB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,6LAAC;0EAAM,OAAO,QAAQ;;;;;;;;;;;;kEAExB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,8NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;0EACvB,6LAAC;;oEAAM,OAAO,OAAO;oEAAC;;;;;;;;;;;;;;;;;;;0DAK1B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAW,AAAC,gDAIhB,OAHC,OAAO,MAAM,KAAK,gBACd,uDACA;kEAEH,OAAO,MAAM,KAAK,gBAAgB,gBAAgB;;;;;;kEAErD,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wDACZ,YAAY;4DAAE,OAAO;wDAAK;wDAC1B,UAAU;4DAAE,OAAO;wDAAK;wDACxB,WAAW,AAAC,0EAIX,OAHC,OAAO,MAAM,KAAK,gBACd,6EACA;kEAGL,OAAO,MAAM,KAAK,gBAAgB,aAAa;;;;;;;;;;;;;;;;;;kDAMtD,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAW,AAAC,sCAAkD,OAAb,OAAO,KAAK,EAAC;;;;;;;+BA7G9D,OAAO,EAAE;;;;;;;;;;kCAmHpB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAC,GAAG,sBACvB,6LAAC;oCAEC,WAAU;mCADL;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrB;GArOwB;KAAA", "debugId": null}}, {"offset": {"line": 3448, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/training/components/CoursePreview.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState } from 'react';\r\nimport { motion, AnimatePresence } from 'framer-motion';\r\nimport { X, Play, Clock, CheckCircle, Lock, ArrowRight, Calendar, Users, Star } from 'lucide-react';\r\nimport { PrimaryButton } from '../../common/buttons/BrandButtons';\r\n\r\nconst courseWeeks = [\r\n  {\r\n    id: 1,\r\n    title: \"Welcome To Security & Training\",\r\n    subtitle: \"Introduction to Cybersecurity\",\r\n    description: \"Introduction to cybersecurity concepts, threat modeling, and basic tools\",\r\n    lessons: [\r\n      { id: 1, title: \"Introduction to Cybersecurity\", duration: \"45 min\", status: \"completed\", type: \"video\" },\r\n      { id: 2, title: \"Threat Modeling Basics\", duration: \"60 min\", status: \"completed\", type: \"video\" },\r\n      { id: 3, title: \"Setting Up Your Lab Environment\", duration: \"30 min\", status: \"completed\", type: \"lab\" },\r\n      { id: 4, title: \"Basic Reconnaissance Techniques\", duration: \"90 min\", status: \"in-progress\", type: \"video\" },\r\n      { id: 5, title: \"Week 1 Assessment\", duration: \"45 min\", status: \"locked\", type: \"quiz\" }\r\n    ],\r\n    color: \"from-[var(--color-blue)] to-[var(--color-blue-secondary)]\",\r\n    progress: 60,\r\n    totalLessons: 5,\r\n    completedLessons: 3\r\n  },\r\n  {\r\n    id: 2,\r\n    title: \"Week 1 : Web Application Security\",\r\n    subtitle: \"OWASP Top 10\",\r\n    description: \"Deep dive into web application vulnerabilities and exploitation techniques\",\r\n    lessons: [\r\n      { id: 1, title: \"OWASP Top 10 Overview\", duration: \"60 min\", status: \"locked\", type: \"video\" },\r\n      { id: 2, title: \"SQL Injection Attacks\", duration: \"90 min\", status: \"locked\", type: \"video\" },\r\n      { id: 3, title: \"XSS and CSRF Vulnerabilities\", duration: \"75 min\", status: \"locked\", type: \"video\" },\r\n      { id: 4, title: \"Web Security Lab\", duration: \"120 min\", status: \"locked\", type: \"lab\" },\r\n      { id: 5, title: \"Authentication Bypass Techniques\", duration: \"60 min\", status: \"locked\", type: \"video\" },\r\n      { id: 6, title: \"Week 2 Assessment\", duration: \"60 min\", status: \"locked\", type: \"quiz\" }\r\n    ],\r\n    color: \"from-[var(--color-dark-blue)] to-[var(--color-dark-blue-hover)]\",\r\n    progress: 0,\r\n    totalLessons: 6,\r\n    completedLessons: 0\r\n  },\r\n  {\r\n    id: 3,\r\n    title: \"Week 7 : Network Pentesting\",\r\n    subtitle: \"Network Security Assessment\",\r\n    description: \"Advanced network penetration testing and infrastructure security\",\r\n    lessons: [\r\n      { id: 1, title: \"Network Reconnaissance\", duration: \"90 min\", status: \"locked\", type: \"video\" },\r\n      { id: 2, title: \"Port Scanning Techniques\", duration: \"120 min\", status: \"locked\", type: \"video\" },\r\n      { id: 3, title: \"Service Enumeration\", duration: \"90 min\", status: \"locked\", type: \"video\" },\r\n      { id: 4, title: \"Network Exploitation Lab\", duration: \"180 min\", status: \"locked\", type: \"lab\" },\r\n      { id: 5, title: \"Network Security Report\", duration: \"60 min\", status: \"locked\", type: \"video\" },\r\n      { id: 6, title: \"Final Assessment\", duration: \"120 min\", status: \"locked\", type: \"quiz\" }\r\n    ],\r\n    color: \"from-[var(--color-yellow)] to-[var(--color-yellow-hover)]\",\r\n    progress: 0,\r\n    totalLessons: 6,\r\n    completedLessons: 0\r\n  },\r\n  {\r\n    id: 4,\r\n    title: \"Week 9 : Active Directory Attacks & HTB Machines Practice\",\r\n    subtitle: \"AD Security & HTB Practice\",\r\n    description: \"Active Directory attack techniques and HackTheBox machine practice\",\r\n    lessons: [\r\n      { id: 1, title: \"Active Directory Basics\", duration: \"90 min\", status: \"locked\", type: \"video\" },\r\n      { id: 2, title: \"AD Enumeration\", duration: \"120 min\", status: \"locked\", type: \"video\" },\r\n      { id: 3, title: \"Kerberoasting & Golden Ticket\", duration: \"90 min\", status: \"locked\", type: \"video\" },\r\n      { id: 4, title: \"HTB Machine Practice\", duration: \"180 min\", status: \"locked\", type: \"lab\" },\r\n      { id: 5, title: \"AD Security Report\", duration: \"60 min\", status: \"locked\", type: \"video\" }\r\n    ],\r\n    color: \"from-[var(--color-blue)] to-[var(--color-blue-secondary)]\",\r\n    progress: 0,\r\n    totalLessons: 5,\r\n    completedLessons: 0\r\n  },\r\n  {\r\n    id: 5,\r\n    title: \"Week 10 : AWS Cloud Pentesting\",\r\n    subtitle: \"Cloud Security Assessment\",\r\n    description: \"AWS cloud penetration testing and security assessment\",\r\n    lessons: [\r\n      { id: 1, title: \"AWS Security Fundamentals\", duration: \"90 min\", status: \"locked\", type: \"video\" },\r\n      { id: 2, title: \"Cloud Reconnaissance\", duration: \"120 min\", status: \"locked\", type: \"video\" },\r\n      { id: 3, title: \"IAM Security Testing\", duration: \"90 min\", status: \"locked\", type: \"video\" },\r\n      { id: 4, title: \"Cloud Security Lab\", duration: \"180 min\", status: \"locked\", type: \"lab\" },\r\n      { id: 5, title: \"Cloud Security Report\", duration: \"60 min\", status: \"locked\", type: \"video\" }\r\n    ],\r\n    color: \"from-[var(--color-dark-blue)] to-[var(--color-dark-blue-hover)]\",\r\n    progress: 0,\r\n    totalLessons: 5,\r\n    completedLessons: 0\r\n  }\r\n];\r\n\r\nexport default function CoursePreview() {\r\n  const [selectedWeek, setSelectedWeek] = useState(null);\r\n  const [isModalOpen, setIsModalOpen] = useState(false);\r\n\r\n  const openModal = (week) => {\r\n    setSelectedWeek(week);\r\n    setIsModalOpen(true);\r\n  };\r\n\r\n  const closeModal = () => {\r\n    setIsModalOpen(false);\r\n    setSelectedWeek(null);\r\n  };\r\n\r\n  return (\r\n    <section className=\"py-16 bg-white relative overflow-hidden\">\r\n      {/* Background Elements */}\r\n      <div className=\"absolute inset-0\">\r\n        <div className=\"absolute top-20 left-10 w-72 h-72 bg-[var(--color-blue)]/10 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob\"></div>\r\n        <div className=\"absolute bottom-20 right-10 w-72 h-72 bg-[var(--color-yellow)]/10 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-2000\"></div>\r\n      </div>\r\n\r\n      <div className=\"relative z-10 container mx-auto px-4 max-w-7xl\">\r\n        {/* Header */}\r\n        <motion.div \r\n          initial={{ opacity: 0, y: 30 }}\r\n          whileInView={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.8 }}\r\n          viewport={{ once: true }}\r\n          className=\"text-center mb-12\"\r\n        >\r\n          <motion.div \r\n            initial={{ opacity: 0, scale: 0.8 }}\r\n            whileInView={{ opacity: 1, scale: 1 }}\r\n            transition={{ delay: 0.2, duration: 0.6 }}\r\n            viewport={{ once: true }}\r\n            className=\"inline-flex items-center bg-[var(--color-blue)]/10 px-4 py-2 rounded-full mb-6\"\r\n          >\r\n            <Calendar className=\"w-4 h-4 text-[var(--color-blue)] mr-2\" />\r\n            <span className=\"text-sm font-medium text-[var(--color-blue)]\">SecurityLit Security Training</span>\r\n          </motion.div>\r\n          \r\n          <motion.h2 \r\n            initial={{ opacity: 0, y: 20 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ delay: 0.4, duration: 0.6 }}\r\n            viewport={{ once: true }}\r\n            className=\"text-4xl lg:text-5xl font-bold text-[var(--color-dark-blue)] mb-6\"\r\n          >\r\n            Free Learning -\r\n            <span className=\"block bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] bg-clip-text text-transparent\">\r\n              Premium Experience\r\n            </span>\r\n          </motion.h2>\r\n          \r\n          <motion.p \r\n            initial={{ opacity: 0, y: 20 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ delay: 0.6, duration: 0.6 }}\r\n            viewport={{ once: true }}\r\n            className=\"text-xl text-[var(--foreground-secondary)] max-w-4xl mx-auto leading-relaxed\"\r\n          >\r\n            Created by SecurityLit Team. Explore the detailed curriculum structure with 11 sections and 83 total lessons. \r\n            Click on any week to view the complete lesson plan.\r\n          </motion.p>\r\n        </motion.div>\r\n\r\n        {/* Course Week Cards */}\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12\">\r\n          {courseWeeks.map((week, index) => (\r\n            <motion.div\r\n              key={week.id}\r\n              initial={{ opacity: 0, y: 30 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ delay: index * 0.1, duration: 0.6 }}\r\n              viewport={{ once: true }}\r\n              whileHover={{ \r\n                y: -8,\r\n                scale: 1.02,\r\n                transition: { duration: 0.2 }\r\n              }}\r\n              onClick={() => openModal(week)}\r\n              className=\"group relative bg-white rounded-3xl p-6 shadow-[0_20px_40px_rgb(0,0,0,0.08)] hover:shadow-[0_30px_60px_rgb(0,0,0,0.12)] transition-all duration-300 border border-gray-100 cursor-pointer\"\r\n            >\r\n              {/* Week Header */}\r\n              <div className=\"flex items-center justify-between mb-4\">\r\n                <div className={`w-10 h-10 bg-gradient-to-br ${week.color} rounded-2xl flex items-center justify-center shadow-lg`}>\r\n                  <span className=\"text-white font-bold text-lg\">{week.id}</span>\r\n                </div>\r\n                <div className=\"text-right\">\r\n                  <div className=\"text-sm text-[var(--foreground-secondary)]\">Progress</div>\r\n                  <div className=\"text-2xl font-bold text-[var(--color-dark-blue)]\">{week.progress}%</div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Week Content */}\r\n              <div className=\"space-y-3\">\r\n                <div>\r\n                  <h3 className=\"text-lg font-bold text-[var(--color-dark-blue)] mb-1 group-hover:text-[var(--color-blue)] transition-colors\">\r\n                    {week.title}\r\n                  </h3>\r\n                  <p className=\"text-[var(--color-blue)] font-medium text-sm\">\r\n                    {week.subtitle}\r\n                  </p>\r\n                </div>\r\n                \r\n                <p className=\"text-[var(--foreground-secondary)] leading-relaxed text-sm\">\r\n                  {week.description}\r\n                </p>\r\n\r\n                {/* Progress Bar */}\r\n                <div className=\"space-y-2\">\r\n                  <div className=\"flex justify-between text-sm text-[var(--foreground-secondary)]\">\r\n                    <span>{week.completedLessons} of {week.totalLessons} lessons</span>\r\n                    <span>{week.progress}%</span>\r\n                  </div>\r\n                  <div className=\"w-full bg-gray-200 rounded-full h-2\">\r\n                    <div \r\n                      className={`h-2 bg-gradient-to-r ${week.color} rounded-full transition-all duration-300`}\r\n                      style={{ width: `${week.progress}%` }}\r\n                    />\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Lesson Types */}\r\n                <div className=\"flex gap-2\">\r\n                  <div className=\"flex items-center gap-1 text-xs text-[var(--foreground-secondary)]\">\r\n                    <Play className=\"w-3 h-3\" />\r\n                    <span>Video</span>\r\n                  </div>\r\n                  <div className=\"flex items-center gap-1 text-xs text-[var(--foreground-secondary)]\">\r\n                    <CheckCircle className=\"w-3 h-3\" />\r\n                    <span>Lab</span>\r\n                  </div>\r\n                  <div className=\"flex items-center gap-1 text-xs text-[var(--foreground-secondary)]\">\r\n                    <Star className=\"w-3 h-3\" />\r\n                    <span>Quiz</span>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* CTA */}\r\n                <div className=\"flex items-center justify-between pt-3\">\r\n                  <span className=\"text-sm text-[var(--foreground-secondary)]\">Click to view details</span>\r\n                  <ArrowRight className=\"w-5 h-5 text-gray-400 group-hover:text-[var(--color-blue)] group-hover:translate-x-1 transition-all\" />\r\n                </div>\r\n              </div>\r\n\r\n              {/* Hover Effects */}\r\n              <div className=\"absolute inset-0 bg-gradient-to-br from-[var(--color-blue)]/5 to-[var(--color-blue-secondary)]/5 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300\" />\r\n              <div className={`absolute inset-0 bg-gradient-to-br ${week.color} rounded-3xl opacity-0 group-hover:opacity-5 transition-opacity duration-300 blur-xl`} />\r\n            </motion.div>\r\n          ))}\r\n        </div>\r\n\r\n        {/* Course Stats */}\r\n        <motion.div \r\n          initial={{ opacity: 0, y: 30 }}\r\n          whileInView={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.8, duration: 0.6 }}\r\n          viewport={{ once: true }}\r\n          className=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\"\r\n        >\r\n          <div className=\"text-center\">\r\n            <div className=\"text-4xl font-bold text-[var(--color-blue)] mb-2\">17</div>\r\n            <div className=\"text-[var(--foreground-secondary)]\">Total Lessons</div>\r\n          </div>\r\n          <div className=\"text-center\">\r\n            <div className=\"text-4xl font-bold text-[var(--color-dark-blue)] mb-2\">24</div>\r\n            <div className=\"text-[var(--foreground-secondary)]\">Hours Content</div>\r\n          </div>\r\n          <div className=\"text-center\">\r\n            <div className=\"text-4xl font-bold text-[var(--color-yellow)] mb-2\">3</div>\r\n            <div className=\"text-[var(--foreground-secondary)]\">Hands-on Labs</div>\r\n          </div>\r\n          <div className=\"text-center\">\r\n            <div className=\"text-4xl font-bold text-[var(--color-blue-secondary)] mb-2\">3</div>\r\n            <div className=\"text-[var(--foreground-secondary)]\">Assessments</div>\r\n          </div>\r\n        </motion.div>\r\n      </div>\r\n\r\n      {/* Modal */}\r\n      <AnimatePresence>\r\n        {isModalOpen && selectedWeek && (\r\n          <motion.div\r\n            initial={{ opacity: 0 }}\r\n            animate={{ opacity: 1 }}\r\n            exit={{ opacity: 0 }}\r\n            className=\"fixed inset-0 z-50 flex items-center justify-center p-4\"\r\n          >\r\n            {/* Backdrop */}\r\n            <motion.div\r\n              initial={{ opacity: 0 }}\r\n              animate={{ opacity: 1 }}\r\n              exit={{ opacity: 0 }}\r\n              onClick={closeModal}\r\n              className=\"absolute inset-0 bg-black/50 backdrop-blur-sm\"\r\n            />\r\n\r\n            {/* Modal Content */}\r\n            <motion.div\r\n              initial={{ opacity: 0, scale: 0.9, y: 20 }}\r\n              animate={{ opacity: 1, scale: 1, y: 0 }}\r\n              exit={{ opacity: 0, scale: 0.9, y: 20 }}\r\n              transition={{ type: \"spring\", damping: 25, stiffness: 300 }}\r\n              className=\"relative bg-white rounded-3xl shadow-2xl max-w-4xl w-full max-h-[80vh] overflow-hidden\"\r\n            >\r\n              {/* Modal Header */}\r\n              <div className={`bg-gradient-to-r ${selectedWeek.color} p-6 text-white`}>\r\n                <div className=\"flex items-center justify-between\">\r\n                  <div>\r\n                    <h2 className=\"text-2xl font-bold mb-1\">{selectedWeek.title}</h2>\r\n                    <p className=\"text-lg opacity-90\">{selectedWeek.subtitle}</p>\r\n                  </div>\r\n                  <motion.button\r\n                    whileHover={{ scale: 1.1 }}\r\n                    whileTap={{ scale: 0.9 }}\r\n                    onClick={closeModal}\r\n                    className=\"w-8 h-8 bg-white/20 rounded-full flex items-center justify-center hover:bg-white/30 transition-colors\"\r\n                  >\r\n                    <X className=\"w-5 h-5\" />\r\n                  </motion.button>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Modal Body */}\r\n              <div className=\"p-6 max-h-[50vh] overflow-y-auto\">\r\n                <div className=\"space-y-4\">\r\n                  <p className=\"text-[var(--foreground-secondary)] text-base leading-relaxed\">\r\n                    {selectedWeek.description}\r\n                  </p>\r\n\r\n                  {/* Progress Summary */}\r\n                  <div className=\"bg-gray-50 rounded-2xl p-4\">\r\n                    <div className=\"flex items-center justify-between mb-3\">\r\n                      <h3 className=\"text-base font-semibold text-[var(--color-dark-blue)]\">Progress</h3>\r\n                      <span className=\"text-xl font-bold text-[var(--color-blue)]\">{selectedWeek.progress}%</span>\r\n                    </div>\r\n                    <div className=\"w-full bg-gray-200 rounded-full h-2\">\r\n                      <div \r\n                        className={`h-2 bg-gradient-to-r ${selectedWeek.color} rounded-full transition-all duration-300`}\r\n                        style={{ width: `${selectedWeek.progress}%` }}\r\n                      />\r\n                    </div>\r\n                    <div className=\"flex justify-between text-sm text-[var(--foreground-secondary)] mt-2\">\r\n                      <span>{selectedWeek.completedLessons} of {selectedWeek.totalLessons} lessons completed</span>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Lessons List */}\r\n                  <div>\r\n                    <h3 className=\"text-lg font-semibold text-[var(--color-dark-blue)] mb-3\">Lessons</h3>\r\n                    <div className=\"space-y-2\">\r\n                      {selectedWeek.lessons.map((lesson, index) => (\r\n                        <motion.div\r\n                          key={lesson.id}\r\n                          initial={{ opacity: 0, x: -20 }}\r\n                          animate={{ opacity: 1, x: 0 }}\r\n                          transition={{ delay: index * 0.1 }}\r\n                          className=\"flex items-center justify-between p-3 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors\"\r\n                        >\r\n                          <div className=\"flex items-center gap-3\">\r\n                            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${\r\n                              lesson.status === 'completed' ? 'bg-green-500' :\r\n                              lesson.status === 'in-progress' ? 'bg-[var(--color-blue)]' : 'bg-gray-400'\r\n                            }`}>\r\n                              {lesson.status === 'completed' ? (\r\n                                <CheckCircle className=\"w-4 h-4 text-white\" />\r\n                              ) : lesson.status === 'in-progress' ? (\r\n                                <Play className=\"w-4 h-4 text-white\" />\r\n                              ) : (\r\n                                <Lock className=\"w-4 h-4 text-white\" />\r\n                              )}\r\n                            </div>\r\n                            <div>\r\n                              <h4 className=\"font-semibold text-[var(--color-dark-blue)] text-sm\">{lesson.title}</h4>\r\n                              <div className=\"flex items-center gap-3 text-xs text-[var(--foreground-secondary)]\">\r\n                                <span className=\"flex items-center gap-1\">\r\n                                  <Clock className=\"w-3 h-3\" />\r\n                                  {lesson.duration}\r\n                                </span>\r\n                                <span className=\"capitalize\">{lesson.type}</span>\r\n                              </div>\r\n                            </div>\r\n                          </div>\r\n                          <div className={`px-2 py-1 rounded-full text-xs font-semibold ${\r\n                            lesson.status === 'completed' ? 'bg-green-100 text-green-700' :\r\n                            lesson.status === 'in-progress' ? 'bg-[var(--color-blue)]/10 text-[var(--color-blue)]' : 'bg-gray-100 text-gray-700'\r\n                          }`}>\r\n                            {lesson.status === 'completed' ? 'Completed' :\r\n                             lesson.status === 'in-progress' ? 'In Progress' : 'Locked'}\r\n                          </div>\r\n                        </motion.div>\r\n                      ))}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Modal Footer */}\r\n              <div className=\"p-6 border-t border-gray-200\">\r\n                <div className=\"flex justify-between items-center\">\r\n                  <div className=\"text-sm text-[var(--foreground-secondary)]\">\r\n                    Total duration: {selectedWeek.lessons.reduce((acc, lesson) => {\r\n                      const minutes = parseInt(lesson.duration.split(' ')[0]);\r\n                      return acc + minutes;\r\n                    }, 0)} minutes\r\n                  </div>\r\n                  <PrimaryButton className=\"px-6 py-2 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300\">\r\n                    Continue Learning\r\n                  </PrimaryButton>\r\n                </div>\r\n              </div>\r\n            </motion.div>\r\n          </motion.div>\r\n        )}\r\n      </AnimatePresence>\r\n    </section>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AALA;;;;;AAOA,MAAM,cAAc;IAClB;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,SAAS;YACP;gBAAE,IAAI;gBAAG,OAAO;gBAAiC,UAAU;gBAAU,QAAQ;gBAAa,MAAM;YAAQ;YACxG;gBAAE,IAAI;gBAAG,OAAO;gBAA0B,UAAU;gBAAU,QAAQ;gBAAa,MAAM;YAAQ;YACjG;gBAAE,IAAI;gBAAG,OAAO;gBAAmC,UAAU;gBAAU,QAAQ;gBAAa,MAAM;YAAM;YACxG;gBAAE,IAAI;gBAAG,OAAO;gBAAmC,UAAU;gBAAU,QAAQ;gBAAe,MAAM;YAAQ;YAC5G;gBAAE,IAAI;gBAAG,OAAO;gBAAqB,UAAU;gBAAU,QAAQ;gBAAU,MAAM;YAAO;SACzF;QACD,OAAO;QACP,UAAU;QACV,cAAc;QACd,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,SAAS;YACP;gBAAE,IAAI;gBAAG,OAAO;gBAAyB,UAAU;gBAAU,QAAQ;gBAAU,MAAM;YAAQ;YAC7F;gBAAE,IAAI;gBAAG,OAAO;gBAAyB,UAAU;gBAAU,QAAQ;gBAAU,MAAM;YAAQ;YAC7F;gBAAE,IAAI;gBAAG,OAAO;gBAAgC,UAAU;gBAAU,QAAQ;gBAAU,MAAM;YAAQ;YACpG;gBAAE,IAAI;gBAAG,OAAO;gBAAoB,UAAU;gBAAW,QAAQ;gBAAU,MAAM;YAAM;YACvF;gBAAE,IAAI;gBAAG,OAAO;gBAAoC,UAAU;gBAAU,QAAQ;gBAAU,MAAM;YAAQ;YACxG;gBAAE,IAAI;gBAAG,OAAO;gBAAqB,UAAU;gBAAU,QAAQ;gBAAU,MAAM;YAAO;SACzF;QACD,OAAO;QACP,UAAU;QACV,cAAc;QACd,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,SAAS;YACP;gBAAE,IAAI;gBAAG,OAAO;gBAA0B,UAAU;gBAAU,QAAQ;gBAAU,MAAM;YAAQ;YAC9F;gBAAE,IAAI;gBAAG,OAAO;gBAA4B,UAAU;gBAAW,QAAQ;gBAAU,MAAM;YAAQ;YACjG;gBAAE,IAAI;gBAAG,OAAO;gBAAuB,UAAU;gBAAU,QAAQ;gBAAU,MAAM;YAAQ;YAC3F;gBAAE,IAAI;gBAAG,OAAO;gBAA4B,UAAU;gBAAW,QAAQ;gBAAU,MAAM;YAAM;YAC/F;gBAAE,IAAI;gBAAG,OAAO;gBAA2B,UAAU;gBAAU,QAAQ;gBAAU,MAAM;YAAQ;YAC/F;gBAAE,IAAI;gBAAG,OAAO;gBAAoB,UAAU;gBAAW,QAAQ;gBAAU,MAAM;YAAO;SACzF;QACD,OAAO;QACP,UAAU;QACV,cAAc;QACd,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,SAAS;YACP;gBAAE,IAAI;gBAAG,OAAO;gBAA2B,UAAU;gBAAU,QAAQ;gBAAU,MAAM;YAAQ;YAC/F;gBAAE,IAAI;gBAAG,OAAO;gBAAkB,UAAU;gBAAW,QAAQ;gBAAU,MAAM;YAAQ;YACvF;gBAAE,IAAI;gBAAG,OAAO;gBAAiC,UAAU;gBAAU,QAAQ;gBAAU,MAAM;YAAQ;YACrG;gBAAE,IAAI;gBAAG,OAAO;gBAAwB,UAAU;gBAAW,QAAQ;gBAAU,MAAM;YAAM;YAC3F;gBAAE,IAAI;gBAAG,OAAO;gBAAsB,UAAU;gBAAU,QAAQ;gBAAU,MAAM;YAAQ;SAC3F;QACD,OAAO;QACP,UAAU;QACV,cAAc;QACd,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,SAAS;YACP;gBAAE,IAAI;gBAAG,OAAO;gBAA6B,UAAU;gBAAU,QAAQ;gBAAU,MAAM;YAAQ;YACjG;gBAAE,IAAI;gBAAG,OAAO;gBAAwB,UAAU;gBAAW,QAAQ;gBAAU,MAAM;YAAQ;YAC7F;gBAAE,IAAI;gBAAG,OAAO;gBAAwB,UAAU;gBAAU,QAAQ;gBAAU,MAAM;YAAQ;YAC5F;gBAAE,IAAI;gBAAG,OAAO;gBAAsB,UAAU;gBAAW,QAAQ;gBAAU,MAAM;YAAM;YACzF;gBAAE,IAAI;gBAAG,OAAO;gBAAyB,UAAU;gBAAU,QAAQ;gBAAU,MAAM;YAAQ;SAC9F;QACD,OAAO;QACP,UAAU;QACV,cAAc;QACd,kBAAkB;IACpB;CACD;AAEc,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,YAAY,CAAC;QACjB,gBAAgB;QAChB,eAAe;IACjB;IAEA,MAAM,aAAa;QACjB,eAAe;QACf,gBAAgB;IAClB;IAEA,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,aAAa;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCACpC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAEV,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;wCAAK,WAAU;kDAA+C;;;;;;;;;;;;0CAGjE,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;gCACR,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;oCACX;kDAEC,6LAAC;wCAAK,WAAU;kDAAiH;;;;;;;;;;;;0CAKnI,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;gCACP,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;0CACX;;;;;;;;;;;;kCAOH,6LAAC;wBAAI,WAAU;kCACZ,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,OAAO,QAAQ;oCAAK,UAAU;gCAAI;gCAChD,UAAU;oCAAE,MAAM;gCAAK;gCACvB,YAAY;oCACV,GAAG,CAAC;oCACJ,OAAO;oCACP,YAAY;wCAAE,UAAU;oCAAI;gCAC9B;gCACA,SAAS,IAAM,UAAU;gCACzB,WAAU;;kDAGV,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAW,AAAC,+BAAyC,OAAX,KAAK,KAAK,EAAC;0DACxD,cAAA,6LAAC;oDAAK,WAAU;8DAAgC,KAAK,EAAE;;;;;;;;;;;0DAEzD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAA6C;;;;;;kEAC5D,6LAAC;wDAAI,WAAU;;4DAAoD,KAAK,QAAQ;4DAAC;;;;;;;;;;;;;;;;;;;kDAKrF,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEACX,KAAK,KAAK;;;;;;kEAEb,6LAAC;wDAAE,WAAU;kEACV,KAAK,QAAQ;;;;;;;;;;;;0DAIlB,6LAAC;gDAAE,WAAU;0DACV,KAAK,WAAW;;;;;;0DAInB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;oEAAM,KAAK,gBAAgB;oEAAC;oEAAK,KAAK,YAAY;oEAAC;;;;;;;0EACpD,6LAAC;;oEAAM,KAAK,QAAQ;oEAAC;;;;;;;;;;;;;kEAEvB,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DACC,WAAW,AAAC,wBAAkC,OAAX,KAAK,KAAK,EAAC;4DAC9C,OAAO;gEAAE,OAAO,AAAC,GAAgB,OAAd,KAAK,QAAQ,EAAC;4DAAG;;;;;;;;;;;;;;;;;0DAM1C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,6LAAC;0EAAK;;;;;;;;;;;;kEAER,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,8NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;0EACvB,6LAAC;0EAAK;;;;;;;;;;;;kEAER,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,6LAAC;0EAAK;;;;;;;;;;;;;;;;;;0DAKV,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAA6C;;;;;;kEAC7D,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;;;;;;;kDAK1B,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAW,AAAC,sCAAgD,OAAX,KAAK,KAAK,EAAC;;;;;;;+BA9E5D,KAAK,EAAE;;;;;;;;;;kCAoFlB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,OAAO;4BAAK,UAAU;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAmD;;;;;;kDAClE,6LAAC;wCAAI,WAAU;kDAAqC;;;;;;;;;;;;0CAEtD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAwD;;;;;;kDACvE,6LAAC;wCAAI,WAAU;kDAAqC;;;;;;;;;;;;0CAEtD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAqD;;;;;;kDACpE,6LAAC;wCAAI,WAAU;kDAAqC;;;;;;;;;;;;0CAEtD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAA6D;;;;;;kDAC5E,6LAAC;wCAAI,WAAU;kDAAqC;;;;;;;;;;;;;;;;;;;;;;;;0BAM1D,6LAAC,4LAAA,CAAA,kBAAe;0BACb,eAAe,8BACd,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,WAAU;;sCAGV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;4BAAE;4BACtB,SAAS;gCAAE,SAAS;4BAAE;4BACtB,MAAM;gCAAE,SAAS;4BAAE;4BACnB,SAAS;4BACT,WAAU;;;;;;sCAIZ,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,OAAO;gCAAK,GAAG;4BAAG;4BACzC,SAAS;gCAAE,SAAS;gCAAG,OAAO;gCAAG,GAAG;4BAAE;4BACtC,MAAM;gCAAE,SAAS;gCAAG,OAAO;gCAAK,GAAG;4BAAG;4BACtC,YAAY;gCAAE,MAAM;gCAAU,SAAS;gCAAI,WAAW;4BAAI;4BAC1D,WAAU;;8CAGV,6LAAC;oCAAI,WAAW,AAAC,oBAAsC,OAAnB,aAAa,KAAK,EAAC;8CACrD,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA2B,aAAa,KAAK;;;;;;kEAC3D,6LAAC;wDAAE,WAAU;kEAAsB,aAAa,QAAQ;;;;;;;;;;;;0DAE1D,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gDACZ,YAAY;oDAAE,OAAO;gDAAI;gDACzB,UAAU;oDAAE,OAAO;gDAAI;gDACvB,SAAS;gDACT,WAAU;0DAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;8CAMnB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DACV,aAAa,WAAW;;;;;;0DAI3B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EAAwD;;;;;;0EACtE,6LAAC;gEAAK,WAAU;;oEAA8C,aAAa,QAAQ;oEAAC;;;;;;;;;;;;;kEAEtF,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DACC,WAAW,AAAC,wBAA0C,OAAnB,aAAa,KAAK,EAAC;4DACtD,OAAO;gEAAE,OAAO,AAAC,GAAwB,OAAtB,aAAa,QAAQ,EAAC;4DAAG;;;;;;;;;;;kEAGhD,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;;gEAAM,aAAa,gBAAgB;gEAAC;gEAAK,aAAa,YAAY;gEAAC;;;;;;;;;;;;;;;;;;0DAKxE,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA2D;;;;;;kEACzE,6LAAC;wDAAI,WAAU;kEACZ,aAAa,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,sBACjC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gEAET,SAAS;oEAAE,SAAS;oEAAG,GAAG,CAAC;gEAAG;gEAC9B,SAAS;oEAAE,SAAS;oEAAG,GAAG;gEAAE;gEAC5B,YAAY;oEAAE,OAAO,QAAQ;gEAAI;gEACjC,WAAU;;kFAEV,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAI,WAAW,AAAC,yDAGhB,OAFC,OAAO,MAAM,KAAK,cAAc,iBAChC,OAAO,MAAM,KAAK,gBAAgB,2BAA2B;0FAE5D,OAAO,MAAM,KAAK,4BACjB,6LAAC,8NAAA,CAAA,cAAW;oFAAC,WAAU;;;;;2FACrB,OAAO,MAAM,KAAK,8BACpB,6LAAC,qMAAA,CAAA,OAAI;oFAAC,WAAU;;;;;yGAEhB,6LAAC,qMAAA,CAAA,OAAI;oFAAC,WAAU;;;;;;;;;;;0FAGpB,6LAAC;;kGACC,6LAAC;wFAAG,WAAU;kGAAuD,OAAO,KAAK;;;;;;kGACjF,6LAAC;wFAAI,WAAU;;0GACb,6LAAC;gGAAK,WAAU;;kHACd,6LAAC,uMAAA,CAAA,QAAK;wGAAC,WAAU;;;;;;oGAChB,OAAO,QAAQ;;;;;;;0GAElB,6LAAC;gGAAK,WAAU;0GAAc,OAAO,IAAI;;;;;;;;;;;;;;;;;;;;;;;;kFAI/C,6LAAC;wEAAI,WAAW,AAAC,gDAGhB,OAFC,OAAO,MAAM,KAAK,cAAc,gCAChC,OAAO,MAAM,KAAK,gBAAgB,uDAAuD;kFAExF,OAAO,MAAM,KAAK,cAAc,cAChC,OAAO,MAAM,KAAK,gBAAgB,gBAAgB;;;;;;;+DAnChD,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;8CA6C1B,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;oDAA6C;oDACzC,aAAa,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK;wDACjD,MAAM,UAAU,SAAS,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;wDACtD,OAAO,MAAM;oDACf,GAAG;oDAAG;;;;;;;0DAER,6LAAC,mJAAA,CAAA,gBAAa;gDAAC,WAAU;0DAA2F;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWtI;GA/TwB;KAAA", "debugId": null}}, {"offset": {"line": 4739, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/training/components/ProgramStructure.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport { CheckCircle, Code, Network, Cloud, Clock, Users, Award } from 'lucide-react';\r\n\r\nconst phases = [\r\n  {\r\n    id: 1,\r\n    title: \"Web And API Penetration Testing\",\r\n    description: \"Our program introduces participants to VAPT (Vulnerability Assessment and Penetration Testing) for mobile and web applications, focusing on identifying and mitigating security flaws. With the growing reliance on mobile apps and APIs, security is critical for organizations.\",\r\n    icon: Code,\r\n    color: \"from-[var(--color-blue)] to-[var(--color-blue-secondary)]\",\r\n    features: [\"Assignments\", \"Labs\", \"Mentoring (Premium Only)\", \"Hands-On Real Project (Premium Only)\"],\r\n    image: \"/images/p2s3.png\"\r\n  },\r\n  {\r\n    id: 2,\r\n    title: \"Network and Cloud Penetration Testing\",\r\n    description: \"This phase focuses on network security, equipping participants with the skills to assess and secure organizational networks and endpoints. The emphasis is on identifying vulnerabilities in network infrastructure and developing robust defenses.\",\r\n    icon: Network,\r\n    color: \"from-[var(--color-dark-blue)] to-[var(--color-dark-blue-hover)]\",\r\n    features: [\"Assignments\", \"Labs\", \"Mentoring (Premium Only)\", \"Hands-On Real Project (Premium Only)\"],\r\n    image: \"/images/p2s4.png\"\r\n  },\r\n  {\r\n    id: 3,\r\n    title: \"Cloud Security\",\r\n    description: \"This phase introduces participants to essential cloud security practices, focusing on securing AWS environments. With more organizations moving to the cloud, understanding foundational cloud security principles is crucial for safeguarding cloud-based infrastructure.\",\r\n    icon: Cloud,\r\n    color: \"from-[var(--color-blue)] to-[var(--color-blue-secondary)]\",\r\n    features: [\"Assignments\", \"Labs\", \"Mentoring (Premium Only)\", \"Hands-On Real Project (Premium Only)\"],\r\n    image: \"/images/p2s5.png\"\r\n  }\r\n];\r\n\r\nexport default function ProgramStructure() {\r\n  return (\r\n    <section className=\"py-24 bg-gradient-to-br from-gray-50 to-white relative overflow-hidden\">\r\n      {/* Background Elements */}\r\n      <div className=\"absolute inset-0\">\r\n        <div className=\"absolute top-20 left-10 w-72 h-72 bg-[var(--color-blue)]/5 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob\"></div>\r\n        <div className=\"absolute bottom-20 right-10 w-72 h-72 bg-[var(--color-blue-secondary)]/5 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-2000\"></div>\r\n      </div>\r\n\r\n      <div className=\"relative z-10 container mx-auto px-4 max-w-7xl\">\r\n        {/* Header */}\r\n        <motion.div \r\n          initial={{ opacity: 0, y: 30 }}\r\n          whileInView={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.8 }}\r\n          viewport={{ once: true }}\r\n          className=\"text-center mb-20\"\r\n        >\r\n          <motion.div \r\n            initial={{ opacity: 0, scale: 0.8 }}\r\n            whileInView={{ opacity: 1, scale: 1 }}\r\n            transition={{ delay: 0.2, duration: 0.6 }}\r\n            viewport={{ once: true }}\r\n            className=\"inline-flex items-center bg-gradient-to-r from-[var(--color-blue)]/10 to-[var(--color-blue-secondary)]/10 px-6 py-3 rounded-full mb-8 border border-[var(--color-blue)]/20\"\r\n          >\r\n            <Award className=\"w-5 h-5 text-[var(--color-blue)] mr-3\" />\r\n            <span className=\"text-sm font-semibold text-[var(--color-blue)]\">Job-Focused Curriculum</span>\r\n          </motion.div>\r\n          \r\n          <motion.h2 \r\n            initial={{ opacity: 0, y: 20 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ delay: 0.4, duration: 0.6 }}\r\n            viewport={{ once: true }}\r\n            className=\"text-5xl lg:text-6xl font-bold text-[var(--color-dark-blue)] mb-8 leading-tight\"\r\n          >\r\n            Program\r\n            <span className=\"block bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] bg-clip-text text-transparent\">\r\n              Structure\r\n            </span>\r\n          </motion.h2>\r\n          \r\n          <motion.p \r\n            initial={{ opacity: 0, y: 20 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ delay: 0.6, duration: 0.6 }}\r\n            viewport={{ once: true }}\r\n            className=\"text-xl text-[var(--foreground-secondary)] max-w-4xl mx-auto leading-relaxed\"\r\n          >\r\n            We provide a job-focused, hands-on curriculum designed to take participants from foundational to advanced cybersecurity skills across three comprehensive phases.\r\n          </motion.p>\r\n        </motion.div>\r\n\r\n        {/* Phases Grid */}\r\n        <div className=\"space-y-16\">\r\n          {phases.map((phase, index) => (\r\n            <motion.div\r\n              key={phase.id}\r\n              initial={{ opacity: 0, y: 50 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ delay: index * 0.2, duration: 0.8 }}\r\n              viewport={{ once: true }}\r\n              className={`flex flex-col lg:flex-row items-center gap-12 ${\r\n                index % 2 === 1 ? 'lg:flex-row-reverse' : ''\r\n              }`}\r\n            >\r\n              {/* Content */}\r\n              <div className=\"lg:w-1/2 space-y-6\">\r\n                <div className=\"flex items-center gap-4\">\r\n                  <div className={`w-16 h-16 bg-gradient-to-br ${phase.color} rounded-2xl flex items-center justify-center shadow-lg`}>\r\n                    <phase.icon className=\"w-8 h-8 text-white\" />\r\n                  </div>\r\n                  <div>\r\n                    <div className=\"text-sm font-semibold text-[var(--color-blue)] uppercase tracking-wide\">Phase {phase.id}</div>\r\n                    <h3 className=\"text-3xl lg:text-4xl font-bold text-[var(--color-dark-blue)] leading-tight\">{phase.title}</h3>\r\n                  </div>\r\n                </div>\r\n                \r\n                <p className=\"text-lg text-[var(--foreground-secondary)] leading-relaxed\">\r\n                  {phase.description}\r\n                </p>\r\n\r\n                {/* Features */}\r\n                <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4\">\r\n                  {phase.features.map((feature, featureIndex) => (\r\n                    <div key={featureIndex} className=\"flex items-center gap-3\">\r\n                      <CheckCircle className=\"w-5 h-5 text-[var(--color-blue)] flex-shrink-0\" />\r\n                      <span className=\"text-[var(--color-dark-blue)] font-medium\">{feature}</span>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              </div>\r\n\r\n              {/* Image */}\r\n              <div className=\"lg:w-1/2\">\r\n                <div className=\"relative\">\r\n                  <div className={`absolute inset-0 bg-gradient-to-br ${phase.color} rounded-3xl opacity-10 blur-xl`}></div>\r\n                  <div className=\"relative bg-white rounded-3xl p-8 shadow-[0_20px_40px_rgb(0,0,0,0.08)] border border-gray-100\">\r\n                    <img \r\n                      src={phase.image} \r\n                      alt={phase.title}\r\n                      className=\"w-full h-auto rounded-2xl\"\r\n                    />\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </motion.div>\r\n          ))}\r\n        </div>\r\n\r\n        {/* Bottom Stats */}\r\n        <motion.div \r\n          initial={{ opacity: 0, y: 30 }}\r\n          whileInView={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.8, duration: 0.6 }}\r\n          viewport={{ once: true }}\r\n          className=\"mt-20 grid grid-cols-1 md:grid-cols-3 gap-8\"\r\n        >\r\n          <div className=\"text-center\">\r\n            <div className=\"text-4xl font-bold text-[var(--color-blue)] mb-2\">3</div>\r\n            <div className=\"text-[var(--foreground-secondary)]\">Comprehensive Phases</div>\r\n          </div>\r\n          <div className=\"text-center\">\r\n            <div className=\"text-4xl font-bold text-[var(--color-dark-blue)] mb-2\">6</div>\r\n            <div className=\"text-[var(--foreground-secondary)]\">Months Duration</div>\r\n          </div>\r\n          <div className=\"text-center\">\r\n            <div className=\"text-4xl font-bold text-[var(--color-blue-secondary)] mb-2\">100%</div>\r\n            <div className=\"text-[var(--foreground-secondary)]\">Hands-On Learning</div>\r\n          </div>\r\n        </motion.div>\r\n      </div>\r\n    </section>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAMA,MAAM,SAAS;IACb;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM,qMAAA,CAAA,OAAI;QACV,OAAO;QACP,UAAU;YAAC;YAAe;YAAQ;YAA4B;SAAuC;QACrG,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM,2MAAA,CAAA,UAAO;QACb,OAAO;QACP,UAAU;YAAC;YAAe;YAAQ;YAA4B;SAAuC;QACrG,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM,uMAAA,CAAA,QAAK;QACX,OAAO;QACP,UAAU;YAAC;YAAe;YAAQ;YAA4B;SAAuC;QACrG,OAAO;IACT;CACD;AAEc,SAAS;IACtB,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,aAAa;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCACpC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAEV,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,6LAAC;wCAAK,WAAU;kDAAiD;;;;;;;;;;;;0CAGnE,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;gCACR,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;oCACX;kDAEC,6LAAC;wCAAK,WAAU;kDAAiH;;;;;;;;;;;;0CAKnI,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;gCACP,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;0CACX;;;;;;;;;;;;kCAMH,6LAAC;wBAAI,WAAU;kCACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,OAAO,QAAQ;oCAAK,UAAU;gCAAI;gCAChD,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAW,AAAC,iDAEX,OADC,QAAQ,MAAM,IAAI,wBAAwB;;kDAI5C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAW,AAAC,+BAA0C,OAAZ,MAAM,KAAK,EAAC;kEACzD,cAAA,6LAAC,MAAM,IAAI;4DAAC,WAAU;;;;;;;;;;;kEAExB,6LAAC;;0EACC,6LAAC;gEAAI,WAAU;;oEAAyE;oEAAO,MAAM,EAAE;;;;;;;0EACvG,6LAAC;gEAAG,WAAU;0EAA8E,MAAM,KAAK;;;;;;;;;;;;;;;;;;0DAI3G,6LAAC;gDAAE,WAAU;0DACV,MAAM,WAAW;;;;;;0DAIpB,6LAAC;gDAAI,WAAU;0DACZ,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC5B,6LAAC;wDAAuB,WAAU;;0EAChC,6LAAC,8NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;0EACvB,6LAAC;gEAAK,WAAU;0EAA6C;;;;;;;uDAFrD;;;;;;;;;;;;;;;;kDAShB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAW,AAAC,sCAAiD,OAAZ,MAAM,KAAK,EAAC;;;;;;8DAClE,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDACC,KAAK,MAAM,KAAK;wDAChB,KAAK,MAAM,KAAK;wDAChB,WAAU;;;;;;;;;;;;;;;;;;;;;;;+BA5Cb,MAAM,EAAE;;;;;;;;;;kCAsDnB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,OAAO;4BAAK,UAAU;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAmD;;;;;;kDAClE,6LAAC;wCAAI,WAAU;kDAAqC;;;;;;;;;;;;0CAEtD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAwD;;;;;;kDACvE,6LAAC;wCAAI,WAAU;kDAAqC;;;;;;;;;;;;0CAEtD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAA6D;;;;;;kDAC5E,6LAAC;wCAAI,WAAU;kDAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMhE;KAtIwB", "debugId": null}}, {"offset": {"line": 5239, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/training/components/StudentLogos.jsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useEffect, useRef } from 'react';\nimport { motion } from 'framer-motion';\nimport { Building2, GraduationCap, Users, Star } from 'lucide-react';\n\nconst studentLogos = [\n  {\n    id: 1,\n    name: \"AIA NZ\",\n    logo: \"/images/company1.png\",\n    type: \"Insurance\",\n    students: 45,\n    rating: 4.9\n  },\n  {\n    id: 2,\n    name: \"Data Torque\",\n    logo: \"/images/company2.png\",\n    type: \"Data Analytics\",\n    students: 32,\n    rating: 4.8\n  },\n  {\n    id: 3,\n    name: \"CyberDefense Inc\",\n    logo: \"/images/company3.png\",\n    type: \"Security Firm\",\n    students: 28,\n    rating: 4.9\n  },\n  {\n    id: 4,\n    name: \"CloudTech Systems\",\n    logo: \"/images/company4.png\",\n    type: \"Cloud Provider\",\n    students: 38,\n    rating: 4.7\n  },\n  {\n    id: 5,\n    name: \"SecureNet\",\n    logo: \"/images/company5.png\",\n    type: \"Network Security\",\n    students: 25,\n    rating: 4.8\n  },\n  {\n    id: 6,\n    name: \"DataVault\",\n    logo: \"/images/company6.png\",\n    type: \"Data Protection\",\n    students: 30,\n    rating: 4.9\n  },\n  {\n    id: 7,\n    name: \"AppShield\",\n    logo: \"/images/company7.png\",\n    type: \"Application Security\",\n    students: 22,\n    rating: 4.7\n  },\n  {\n    id: 8,\n    name: \"InfraGuard\",\n    logo: \"/images/company8.png\",\n    type: \"Infrastructure\",\n    students: 35,\n    rating: 4.8\n  }\n];\n\nexport default function StudentLogos() {\n  const containerRef = useRef(null);\n\n  useEffect(() => {\n    const container = containerRef.current;\n    if (!container) return;\n\n    let animationId;\n    let scrollPosition = 0;\n    const scrollSpeed = 0.5;\n\n    const animate = () => {\n      scrollPosition += scrollSpeed;\n      if (container) {\n        container.style.transform = `translateX(-${scrollPosition}px)`;\n        \n        // Reset position when scrolled too far\n        if (scrollPosition >= container.scrollWidth / 2) {\n          scrollPosition = 0;\n        }\n      }\n      animationId = requestAnimationFrame(animate);\n    };\n\n    animate();\n\n    return () => {\n      if (animationId) {\n        cancelAnimationFrame(animationId);\n      }\n    };\n  }, []);\n\n  return (\n    <section className=\"py-24 bg-gradient-to-br from-[var(--color-dark-blue)] via-[var(--color-dark-blue-hover)] to-[var(--color-dark-blue-bg)] relative overflow-hidden\">\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0\">\n        <div className=\"absolute top-0 left-0 w-full h-full bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.03%22%3E%3Ccircle%20cx%3D%2230%22%20cy%3D%2230%22%20r%3D%221%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-50\"></div>\n      </div>\n\n      <div className=\"relative z-10 container mx-auto px-4 max-w-7xl\">\n        {/* Header */}\n        <motion.div \n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <motion.div \n            initial={{ opacity: 0, scale: 0.8 }}\n            whileInView={{ opacity: 1, scale: 1 }}\n            transition={{ delay: 0.2, duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"inline-flex items-center bg-[var(--color-blue)]/20 px-4 py-2 rounded-full mb-6\"\n          >\n            <Building2 className=\"w-4 h-4 text-[var(--color-blue)] mr-2\" />\n            <span className=\"text-sm font-medium text-[var(--color-blue)]\">Our Trained Students works at</span>\n          </motion.div>\n          \n          <motion.h2 \n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.4, duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"text-4xl lg:text-5xl font-bold text-white mb-6\"\n          >\n            Companies That\n            <span className=\"block bg-gradient-to-r from-[var(--color-blue)] via-[var(--color-blue-secondary)] to-[var(--color-yellow)] bg-clip-text text-transparent\">\n              Trust Our Training\n            </span>\n          </motion.h2>\n          \n          <motion.p \n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.6, duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"text-xl text-white/80 max-w-4xl mx-auto leading-relaxed\"\n          >\n            Join hundreds of organizations that have upskilled their teams with our \n            comprehensive cybersecurity training programs.\n          </motion.p>\n        </motion.div>\n\n        {/* Floating Logos Carousel */}\n        <div className=\"relative overflow-hidden py-8\">\n          <div \n            ref={containerRef}\n            className=\"flex gap-8 items-center\"\n            style={{ width: 'max-content' }}\n          >\n            {/* Duplicate logos for seamless loop */}\n            {[...studentLogos, ...studentLogos].map((company, index) => (\n              <motion.div\n                key={`${company.id}-${index}`}\n                initial={{ opacity: 0, y: 30 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ delay: index * 0.1, duration: 0.6 }}\n                viewport={{ once: true }}\n                whileHover={{ \n                  y: -5,\n                  scale: 1.02,\n                  transition: { duration: 0.3 }\n                }}\n                className=\"group relative flex-shrink-0\"\n              >\n                {/* 3D Card */}\n                <div className=\"relative bg-white/10 backdrop-blur-xl rounded-3xl p-6 border border-white/20 w-64 h-32 flex items-center justify-center shadow-lg hover:shadow-2xl transition-all duration-300\">\n                  \n                  {/* Company Logo Placeholder */}\n                  <div className=\"text-center space-y-3\">\n                    <div className=\"w-16 h-16 bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-2xl flex items-center justify-center mx-auto shadow-lg group-hover:shadow-xl transition-all duration-300\">\n                      <Building2 className=\"w-8 h-8 text-white\" />\n                    </div>\n                    <div>\n                      <h3 className=\"font-semibold text-white text-lg group-hover:text-[var(--color-blue)] transition-colors\">\n                        {company.name}\n                      </h3>\n                      <p className=\"text-[var(--color-blue)] text-sm\">{company.type}</p>\n                    </div>\n                  </div>\n\n                  {/* Stats Overlay */}\n                  <div className=\"absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300\">\n                    <div className=\"bg-black/50 backdrop-blur-sm rounded-lg p-2 text-white text-xs\">\n                      <div className=\"flex items-center gap-1\">\n                        <Users className=\"w-3 h-3\" />\n                        <span>{company.students}</span>\n                      </div>\n                      <div className=\"flex items-center gap-1\">\n                        <Star className=\"w-3 h-3 text-[var(--color-yellow)]\" />\n                        <span>{company.rating}</span>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Hover Effects */}\n                  <div className=\"absolute inset-0 bg-gradient-to-br from-[var(--color-blue)]/10 to-[var(--color-blue-secondary)]/10 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300\" />\n                  <div className=\"absolute inset-0 bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-3xl opacity-0 group-hover:opacity-5 transition-opacity duration-300 blur-xl\" />\n                </div>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n\n        {/* Stats Section */}\n        <motion.div \n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.8, duration: 0.6 }}\n          viewport={{ once: true }}\n          className=\"mt-20 grid grid-cols-1 md:grid-cols-4 gap-8\"\n        >\n          <div className=\"text-center\">\n            <div className=\"text-4xl font-bold text-[var(--color-blue)] mb-2\">500+</div>\n            <div className=\"text-white/70\">Students Trained</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-4xl font-bold text-[var(--color-blue-secondary)] mb-2\">50+</div>\n            <div className=\"text-white/70\">Companies</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-4xl font-bold text-[var(--color-yellow)] mb-2\">98%</div>\n            <div className=\"text-white/70\">Success Rate</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-4xl font-bold text-[var(--color-blue)] mb-2\">4.9</div>\n            <div className=\"text-white/70\">Average Rating</div>\n          </div>\n        </motion.div>\n\n        {/* CTA Section */}\n        <motion.div \n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ delay: 1, duration: 0.6 }}\n          viewport={{ once: true }}\n          className=\"text-center mt-16\"\n        >\n          <div className=\"inline-flex items-center gap-4 bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] text-white px-8 py-4 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer group\">\n            <GraduationCap className=\"w-5 h-5\" />\n            <span className=\"font-semibold text-lg\">Join These Companies</span>\n            <Star className=\"w-5 h-5 group-hover:scale-110 transition-transform\" />\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;;;AAJA;;;;AAMA,MAAM,eAAe;IACnB;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,MAAM;QACN,UAAU;QACV,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,MAAM;QACN,UAAU;QACV,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,MAAM;QACN,UAAU;QACV,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,MAAM;QACN,UAAU;QACV,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,MAAM;QACN,UAAU;QACV,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,MAAM;QACN,UAAU;QACV,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,MAAM;QACN,UAAU;QACV,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,MAAM;QACN,UAAU;QACV,QAAQ;IACV;CACD;AAEc,SAAS;;IACtB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM,YAAY,aAAa,OAAO;YACtC,IAAI,CAAC,WAAW;YAEhB,IAAI;YACJ,IAAI,iBAAiB;YACrB,MAAM,cAAc;YAEpB,MAAM;kDAAU;oBACd,kBAAkB;oBAClB,IAAI,WAAW;wBACb,UAAU,KAAK,CAAC,SAAS,GAAG,AAAC,eAA6B,OAAf,gBAAe;wBAE1D,uCAAuC;wBACvC,IAAI,kBAAkB,UAAU,WAAW,GAAG,GAAG;4BAC/C,iBAAiB;wBACnB;oBACF;oBACA,cAAc,sBAAsB;gBACtC;;YAEA;YAEA;0CAAO;oBACL,IAAI,aAAa;wBACf,qBAAqB;oBACvB;gBACF;;QACF;iCAAG,EAAE;IAEL,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;;;;;;;;;;0BAGjB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,aAAa;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCACpC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAEV,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,6LAAC;wCAAK,WAAU;kDAA+C;;;;;;;;;;;;0CAGjE,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;gCACR,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;oCACX;kDAEC,6LAAC;wCAAK,WAAU;kDAA2I;;;;;;;;;;;;0CAK7J,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;gCACP,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;0CACX;;;;;;;;;;;;kCAOH,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,KAAK;4BACL,WAAU;4BACV,OAAO;gCAAE,OAAO;4BAAc;sCAG7B;mCAAI;mCAAiB;6BAAa,CAAC,GAAG,CAAC,CAAC,SAAS,sBAChD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,OAAO,QAAQ;wCAAK,UAAU;oCAAI;oCAChD,UAAU;wCAAE,MAAM;oCAAK;oCACvB,YAAY;wCACV,GAAG,CAAC;wCACJ,OAAO;wCACP,YAAY;4CAAE,UAAU;wCAAI;oCAC9B;oCACA,WAAU;8CAGV,cAAA,6LAAC;wCAAI,WAAU;;0DAGb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,mNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;;;;;;kEAEvB,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EACX,QAAQ,IAAI;;;;;;0EAEf,6LAAC;gEAAE,WAAU;0EAAoC,QAAQ,IAAI;;;;;;;;;;;;;;;;;;0DAKjE,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;8EACjB,6LAAC;8EAAM,QAAQ,QAAQ;;;;;;;;;;;;sEAEzB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,6LAAC;8EAAM,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;;;;0DAM3B,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;;;;;;;mCA5CZ,AAAC,GAAgB,OAAd,QAAQ,EAAE,EAAC,KAAS,OAAN;;;;;;;;;;;;;;;kCAoD9B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,OAAO;4BAAK,UAAU;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAmD;;;;;;kDAClE,6LAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;0CAEjC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAA6D;;;;;;kDAC5E,6LAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;0CAEjC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAqD;;;;;;kDACpE,6LAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;0CAEjC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAmD;;;;;;kDAClE,6LAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;kCAKnC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,OAAO;4BAAG,UAAU;wBAAI;wBACtC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;kCAEV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,2NAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;8CACzB,6LAAC;oCAAK,WAAU;8CAAwB;;;;;;8CACxC,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM5B;GA7LwB;KAAA", "debugId": null}}, {"offset": {"line": 5877, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/training/components/Testimonials.jsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Quote, Star, Building2, User, ChevronLeft, ChevronRight } from 'lucide-react';\n\nconst testimonials = [\n  {\n    id: 1,\n    quote: \"SecurityLit transformed my perspective on IT security. Through identifying risks for organizations, I've grown in my role and feel more capable now. The supportive work environment and knowledgeable colleagues at SecurityLit have been invaluable.\",\n    author: \"<PERSON><PERSON><PERSON>\",\n    position: \"Information Security Analyst at DataTorque Ltd\",\n    company: \"DataTorque Ltd\",\n    rating: 5\n  },\n  {\n    id: 2,\n    quote: \"The training boosted my expertise and confidence, helping me excel as a pentester. Hands-on projects deepened my skills in web security and advanced exploitation, which I now apply regularly, along with improved communication skills.\",\n    author: \"<PERSON><PERSON><PERSON>\",\n    position: \"Associate Penetration Tester at SecurityLit\",\n    company: \"SecurityLit\",\n    rating: 5\n  },\n  {\n    id: 3,\n    quote: \"This training improved my approach to pentesting, enhancing my process, documentation, and teamwork. Collaborating on tests and following a structured process was far more effective than my previous ad-hoc methods in bug hunting.\",\n    author: \"<PERSON>\",\n    position: \"Associate Penetration Tester at SecurityLit\",\n    company: \"SecurityLit\",\n    rating: 5\n  },\n  {\n    id: 4,\n    quote: \"I believe I got the job thanks to the training, support, and feedback from Ankita and the SecurityLit team, which helped me excel in the interviews. Thank you for all the guidance that led me to this role.\",\n    author: \"Rini Sebastian\",\n    position: \"Information Security Analyst at AIA NZ\",\n    company: \"AIA NZ\",\n    rating: 5\n  }\n];\n\nexport default function Testimonials() {\n  const [currentIndex, setCurrentIndex] = useState(0);\n  const [direction, setDirection] = useState(0);\n\n  // Auto-play functionality\n  useEffect(() => {\n    const timer = setInterval(() => {\n      setDirection(1);\n      setCurrentIndex((prevIndex) => (prevIndex + 1) % testimonials.length);\n    }, 5000); // Change slide every 5 seconds\n\n    return () => clearInterval(timer);\n  }, [testimonials.length]);\n\n  const slideVariants = {\n    enter: (direction) => ({\n      x: direction > 0 ? 1000 : -1000,\n      opacity: 0\n    }),\n    center: {\n      zIndex: 1,\n      x: 0,\n      opacity: 1\n    },\n    exit: (direction) => ({\n      zIndex: 0,\n      x: direction < 0 ? 1000 : -1000,\n      opacity: 0\n    })\n  };\n\n  const swipeConfidenceThreshold = 10000;\n  const swipePower = (offset, velocity) => {\n    return Math.abs(offset) * velocity;\n  };\n\n  const paginate = (newDirection) => {\n    setDirection(newDirection);\n    setCurrentIndex((prevIndex) => {\n      if (newDirection === 1) {\n        return (prevIndex + 1) % testimonials.length;\n      } else {\n        return prevIndex === 0 ? testimonials.length - 1 : prevIndex - 1;\n      }\n    });\n  };\n\n  const goToSlide = (index) => {\n    setDirection(index > currentIndex ? 1 : -1);\n    setCurrentIndex(index);\n  };\n\n  return (\n    <section className=\"py-24 bg-gradient-to-br from-[var(--color-dark-blue)] via-[var(--color-dark-blue-hover)] to-[var(--color-dark-blue-bg)] relative overflow-hidden\">\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0\">\n        <div className=\"absolute top-0 left-0 w-full h-full bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.03%22%3E%3Ccircle%20cx%3D%2230%22%20cy%3D%2230%22%20r%3D%221%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-50\"></div>\n      </div>\n\n      <div className=\"relative z-10 container mx-auto px-4 max-w-7xl\">\n        {/* Header */}\n        <motion.div \n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-20\"\n        >\n          <motion.div \n            initial={{ opacity: 0, scale: 0.8 }}\n            whileInView={{ opacity: 1, scale: 1 }}\n            transition={{ delay: 0.2, duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"inline-flex items-center bg-[var(--color-blue)]/20 px-4 py-2 rounded-full mb-6\"\n          >\n            <Quote className=\"w-4 h-4 text-[var(--color-blue)] mr-2\" />\n            <span className=\"text-sm font-medium text-[var(--color-blue)]\">Real Success Stories</span>\n          </motion.div>\n          \n          <motion.h2 \n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.4, duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"text-4xl lg:text-5xl font-bold text-white mb-6\"\n          >\n            Real Feedback from\n            <span className=\"block bg-gradient-to-r from-[var(--color-blue)] via-[var(--color-blue-secondary)] to-[var(--color-blue)] bg-clip-text text-transparent\">\n              Professionals\n            </span>\n          </motion.h2>\n          \n          <motion.p \n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.6, duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"text-xl text-white/80 max-w-4xl mx-auto leading-relaxed\"\n          >\n            Hear from professionals who completed our Security Training and transformed their careers in cybersecurity.\n          </motion.p>\n        </motion.div>\n\n        {/* Testimonials Slider */}\n        <div className=\"relative max-w-4xl mx-auto\">\n          {/* Navigation Arrows */}\n          <motion.button\n            whileHover={{ scale: 1.1 }}\n            whileTap={{ scale: 0.9 }}\n            onClick={() => paginate(-1)}\n            className=\"absolute left-4 top-1/2 transform -translate-y-1/2 z-20 w-12 h-12 bg-white/10 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-white/20 transition-all duration-300 border border-white/20\"\n          >\n            <ChevronLeft className=\"w-6 h-6\" />\n          </motion.button>\n          \n          <motion.button\n            whileHover={{ scale: 1.1 }}\n            whileTap={{ scale: 0.9 }}\n            onClick={() => paginate(1)}\n            className=\"absolute right-4 top-1/2 transform -translate-y-1/2 z-20 w-12 h-12 bg-white/10 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-white/20 transition-all duration-300 border border-white/20\"\n          >\n            <ChevronRight className=\"w-6 h-6\" />\n          </motion.button>\n\n          {/* Slider Container */}\n          <div className=\"relative h-[400px] overflow-hidden\">\n            <AnimatePresence initial={false} custom={direction}>\n              <motion.div\n                key={currentIndex}\n                custom={direction}\n                variants={slideVariants}\n                initial=\"enter\"\n                animate=\"center\"\n                exit=\"exit\"\n                transition={{\n                  x: { type: \"spring\", stiffness: 300, damping: 30 },\n                  opacity: { duration: 0.2 }\n                }}\n                drag=\"x\"\n                dragConstraints={{ left: 0, right: 0 }}\n                dragElastic={1}\n                onDragEnd={(e, { offset, velocity }) => {\n                  const swipe = swipePower(offset.x, velocity.x);\n\n                  if (swipe < -swipeConfidenceThreshold) {\n                    paginate(1);\n                  } else if (swipe > swipeConfidenceThreshold) {\n                    paginate(-1);\n                  }\n                }}\n                className=\"absolute inset-0\"\n              >\n                <div className=\"relative bg-white/10 backdrop-blur-xl rounded-3xl p-8 border border-white/20 shadow-2xl h-full flex flex-col justify-center\">\n                  {/* Quote Icon */}\n                  <div className=\"absolute top-6 right-6 opacity-20\">\n                    <Quote className=\"w-8 h-8 text-white\" />\n                  </div>\n\n                  {/* Rating */}\n                  <div className=\"flex items-center gap-1 mb-6\">\n                    {[...Array(testimonials[currentIndex].rating)].map((_, i) => (\n                      <Star key={i} className=\"w-5 h-5 text-[var(--color-yellow)] fill-current\" />\n                    ))}\n                  </div>\n\n                  {/* Quote */}\n                  <blockquote className=\"text-white/90 text-xl leading-relaxed mb-8 italic flex-1\">\n                    \"{testimonials[currentIndex].quote}\"\n                  </blockquote>\n\n                  {/* Author */}\n                  <div className=\"flex items-center gap-4\">\n                    <div className=\"w-12 h-12 bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-full flex items-center justify-center shadow-lg\">\n                      <User className=\"w-6 h-6 text-white\" />\n                    </div>\n                    <div>\n                      <div className=\"text-white font-semibold text-lg\">{testimonials[currentIndex].author}</div>\n                      <div className=\"text-white/70 text-sm\">{testimonials[currentIndex].position}</div>\n                      <div className=\"flex items-center gap-2 text-white/60 text-sm\">\n                        <Building2 className=\"w-4 h-4\" />\n                        <span>{testimonials[currentIndex].company}</span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </motion.div>\n            </AnimatePresence>\n          </div>\n\n          {/* Dots Navigation */}\n          <div className=\"flex justify-center mt-8 space-x-2\">\n            {testimonials.map((_, index) => (\n              <motion.button\n                key={index}\n                whileHover={{ scale: 1.2 }}\n                whileTap={{ scale: 0.8 }}\n                onClick={() => goToSlide(index)}\n                className={`w-3 h-3 rounded-full transition-all duration-300 ${\n                  index === currentIndex \n                    ? 'bg-[var(--color-blue)] scale-125' \n                    : 'bg-white/30 hover:bg-white/50'\n                }`}\n              />\n            ))}\n          </div>\n\n          {/* Slide Counter */}\n          <div className=\"text-center mt-4\">\n            <span className=\"text-white/60 text-sm\">\n              {currentIndex + 1} of {testimonials.length}\n            </span>\n          </div>\n        </div>\n\n        {/* Bottom CTA */}\n        <motion.div \n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.8, duration: 0.6 }}\n          viewport={{ once: true }}\n          className=\"text-center mt-16\"\n        >\n          <div className=\"inline-flex items-center gap-4 bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] text-white px-8 py-4 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer group\">\n            <Star className=\"w-5 h-5\" />\n            <span className=\"font-semibold text-lg\">Join These Success Stories</span>\n            <Quote className=\"w-5 h-5 group-hover:scale-110 transition-transform\" />\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;;AAMA,MAAM,eAAe;IACnB;QACE,IAAI;QACJ,OAAO;QACP,QAAQ;QACR,UAAU;QACV,SAAS;QACT,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,QAAQ;QACR,UAAU;QACV,SAAS;QACT,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,QAAQ;QACR,UAAU;QACV,SAAS;QACT,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,QAAQ;QACR,UAAU;QACV,SAAS;QACT,QAAQ;IACV;CACD;AAEc,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,0BAA0B;IAC1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM,QAAQ;gDAAY;oBACxB,aAAa;oBACb;wDAAgB,CAAC,YAAc,CAAC,YAAY,CAAC,IAAI,aAAa,MAAM;;gBACtE;+CAAG,OAAO,+BAA+B;YAEzC;0CAAO,IAAM,cAAc;;QAC7B;iCAAG;QAAC,aAAa,MAAM;KAAC;IAExB,MAAM,gBAAgB;QACpB,OAAO,CAAC,YAAc,CAAC;gBACrB,GAAG,YAAY,IAAI,OAAO,CAAC;gBAC3B,SAAS;YACX,CAAC;QACD,QAAQ;YACN,QAAQ;YACR,GAAG;YACH,SAAS;QACX;QACA,MAAM,CAAC,YAAc,CAAC;gBACpB,QAAQ;gBACR,GAAG,YAAY,IAAI,OAAO,CAAC;gBAC3B,SAAS;YACX,CAAC;IACH;IAEA,MAAM,2BAA2B;IACjC,MAAM,aAAa,CAAC,QAAQ;QAC1B,OAAO,KAAK,GAAG,CAAC,UAAU;IAC5B;IAEA,MAAM,WAAW,CAAC;QAChB,aAAa;QACb,gBAAgB,CAAC;YACf,IAAI,iBAAiB,GAAG;gBACtB,OAAO,CAAC,YAAY,CAAC,IAAI,aAAa,MAAM;YAC9C,OAAO;gBACL,OAAO,cAAc,IAAI,aAAa,MAAM,GAAG,IAAI,YAAY;YACjE;QACF;IACF;IAEA,MAAM,YAAY,CAAC;QACjB,aAAa,QAAQ,eAAe,IAAI,CAAC;QACzC,gBAAgB;IAClB;IAEA,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;;;;;;;;;;0BAGjB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,aAAa;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCACpC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAEV,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,6LAAC;wCAAK,WAAU;kDAA+C;;;;;;;;;;;;0CAGjE,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;gCACR,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;oCACX;kDAEC,6LAAC;wCAAK,WAAU;kDAAyI;;;;;;;;;;;;0CAK3J,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;gCACP,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;0CACX;;;;;;;;;;;;kCAMH,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,YAAY;oCAAE,OAAO;gCAAI;gCACzB,UAAU;oCAAE,OAAO;gCAAI;gCACvB,SAAS,IAAM,SAAS,CAAC;gCACzB,WAAU;0CAEV,cAAA,6LAAC,uNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;0CAGzB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,YAAY;oCAAE,OAAO;gCAAI;gCACzB,UAAU;oCAAE,OAAO;gCAAI;gCACvB,SAAS,IAAM,SAAS;gCACxB,WAAU;0CAEV,cAAA,6LAAC,yNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;;;;;;0CAI1B,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,4LAAA,CAAA,kBAAe;oCAAC,SAAS;oCAAO,QAAQ;8CACvC,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,QAAQ;wCACR,UAAU;wCACV,SAAQ;wCACR,SAAQ;wCACR,MAAK;wCACL,YAAY;4CACV,GAAG;gDAAE,MAAM;gDAAU,WAAW;gDAAK,SAAS;4CAAG;4CACjD,SAAS;gDAAE,UAAU;4CAAI;wCAC3B;wCACA,MAAK;wCACL,iBAAiB;4CAAE,MAAM;4CAAG,OAAO;wCAAE;wCACrC,aAAa;wCACb,WAAW,CAAC;gDAAG,EAAE,MAAM,EAAE,QAAQ,EAAE;4CACjC,MAAM,QAAQ,WAAW,OAAO,CAAC,EAAE,SAAS,CAAC;4CAE7C,IAAI,QAAQ,CAAC,0BAA0B;gDACrC,SAAS;4CACX,OAAO,IAAI,QAAQ,0BAA0B;gDAC3C,SAAS,CAAC;4CACZ;wCACF;wCACA,WAAU;kDAEV,cAAA,6LAAC;4CAAI,WAAU;;8DAEb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;8DAInB,6LAAC;oDAAI,WAAU;8DACZ;2DAAI,MAAM,YAAY,CAAC,aAAa,CAAC,MAAM;qDAAE,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrD,6LAAC,qMAAA,CAAA,OAAI;4DAAS,WAAU;2DAAb;;;;;;;;;;8DAKf,6LAAC;oDAAW,WAAU;;wDAA2D;wDAC7E,YAAY,CAAC,aAAa,CAAC,KAAK;wDAAC;;;;;;;8DAIrC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;sEAElB,6LAAC;;8EACC,6LAAC;oEAAI,WAAU;8EAAoC,YAAY,CAAC,aAAa,CAAC,MAAM;;;;;;8EACpF,6LAAC;oEAAI,WAAU;8EAAyB,YAAY,CAAC,aAAa,CAAC,QAAQ;;;;;;8EAC3E,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,mNAAA,CAAA,YAAS;4EAAC,WAAU;;;;;;sFACrB,6LAAC;sFAAM,YAAY,CAAC,aAAa,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uCApD5C;;;;;;;;;;;;;;;0CA8DX,6LAAC;gCAAI,WAAU;0CACZ,aAAa,GAAG,CAAC,CAAC,GAAG,sBACpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wCAEZ,YAAY;4CAAE,OAAO;wCAAI;wCACzB,UAAU;4CAAE,OAAO;wCAAI;wCACvB,SAAS,IAAM,UAAU;wCACzB,WAAW,AAAC,oDAIX,OAHC,UAAU,eACN,qCACA;uCAPD;;;;;;;;;;0CAcX,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;;wCACb,eAAe;wCAAE;wCAAK,aAAa,MAAM;;;;;;;;;;;;;;;;;;kCAMhD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,OAAO;4BAAK,UAAU;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;kCAEV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,6LAAC;oCAAK,WAAU;8CAAwB;;;;;;8CACxC,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM7B;GAvOwB;KAAA", "debugId": null}}, {"offset": {"line": 6467, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/training/components/PremiumCTA.jsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { Crown, ArrowRight, CheckCircle, Star, Zap, Shield } from 'lucide-react';\n\nconst premiumFeatures = [\n  \"Access to advance Lab Subscription (TryHackMe, HackTheBox)\",\n  \"Personalized mentorship and guided learning\",\n  \"3 months of hands-on experience with SecurityLit\",\n  \"Professional report writing training\",\n  \"Experience letter upon completion\"\n];\n\nexport default function PremiumCTA() {\n  const [showForm, setShowForm] = useState(false);\n\n  return (\n    <>\n      {/* Main Premium Section */}\n      <section className=\"py-24 bg-gradient-to-br from-[var(--color-dark-blue)] via-[var(--color-dark-blue-hover)] to-[var(--color-dark-blue-bg)] relative overflow-hidden\">\n        {/* Background Elements */}\n        <div className=\"absolute inset-0\">\n          <div className=\"absolute top-0 left-0 w-full h-full bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.03%22%3E%3Ccircle%20cx%3D%2230%22%20cy%3D%2230%22%20r%3D%221%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-50\"></div>\n          <div className=\"absolute top-20 left-10 w-72 h-72 bg-[var(--color-blue)]/20 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob\"></div>\n          <div className=\"absolute bottom-20 right-10 w-72 h-72 bg-[var(--color-blue-secondary)]/20 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000\"></div>\n        </div>\n\n        <div className=\"relative z-10 container mx-auto px-4 max-w-7xl\">\n          {/* Header */}\n          <motion.div \n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n            className=\"text-center mb-16\"\n          >\n            <motion.div \n              initial={{ opacity: 0, scale: 0.8 }}\n              whileInView={{ opacity: 1, scale: 1 }}\n              transition={{ delay: 0.2, duration: 0.6 }}\n              viewport={{ once: true }}\n              className=\"inline-flex items-center bg-[var(--color-yellow)]/20 px-4 py-2 rounded-full mb-6\"\n            >\n              <Crown className=\"w-4 h-4 text-[var(--color-yellow)] mr-2\" />\n              <span className=\"text-sm font-medium text-[var(--color-yellow)]\">Premium Tier Benefits</span>\n            </motion.div>\n            \n            <motion.h2 \n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.4, duration: 0.6 }}\n              viewport={{ once: true }}\n              className=\"text-4xl lg:text-5xl font-bold text-white mb-6\"\n            >\n              As a Premium member, you'll work directly with\n              <span className=\"block bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] bg-clip-text text-transparent\">\n                SecurityLit\n              </span>\n            </motion.h2>\n            \n            <motion.p \n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.6, duration: 0.6 }}\n              viewport={{ once: true }}\n              className=\"text-xl text-white/80 max-w-4xl mx-auto leading-relaxed\"\n            >\n              on live penetration testing projects. This hands-on experience will immerse you in real-time cybersecurity challenges, teaching you how professional pentests are conducted. You'll learn the intricacies of report writing, client communication, and industry best practices. Upon completion, you'll receive an experience letter, validating your practical skills and boosting your career prospects in the cybersecurity field.\n            </motion.p>\n          </motion.div>\n\n          {/* Premium Features Grid */}\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-16\">\n            {/* Features List */}\n            <motion.div \n              initial={{ opacity: 0, x: -50 }}\n              whileInView={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.8 }}\n              viewport={{ once: true }}\n              className=\"space-y-6\"\n            >\n              {premiumFeatures.map((feature, index) => (\n                <motion.div\n                  key={index}\n                  initial={{ opacity: 0, x: -30 }}\n                  whileInView={{ opacity: 1, x: 0 }}\n                  transition={{ delay: index * 0.1, duration: 0.6 }}\n                  viewport={{ once: true }}\n                  className=\"flex items-center gap-4\"\n                >\n                  <div className=\"w-12 h-12 bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-2xl flex items-center justify-center shadow-lg\">\n                    <CheckCircle className=\"w-6 h-6 text-[var(--color-yellow)]\" />\n                  </div>\n                  <div>\n                    <h3 className=\"text-lg font-semibold text-white\">{feature}</h3>\n                  </div>\n                </motion.div>\n              ))}\n            </motion.div>\n\n            {/* Premium Card */}\n            <motion.div \n              initial={{ opacity: 0, x: 50 }}\n              whileInView={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.8 }}\n              viewport={{ once: true }}\n              className=\"relative\"\n            >\n              <div className=\"bg-white/10 backdrop-blur-xl rounded-3xl p-8 border border-white/20 shadow-2xl\">\n                <div className=\"text-center space-y-6\">\n                  {/* Premium Badge */}\n                  <div className=\"inline-flex items-center gap-2 bg-gradient-to-r from-[var(--color-yellow)] to-[var(--color-yellow-hover)] text-white px-6 py-3 rounded-full\">\n                    <Crown className=\"w-5 h-5\" />\n                    <span className=\"font-semibold\">Premium Plan</span>\n                  </div>\n\n                  {/* Pricing */}\n                  <div>\n                    <div className=\"text-4xl font-bold text-white mb-2\">\n                      $299\n                      <span className=\"text-lg text-white/70 font-normal\">/month</span>\n                    </div>\n                    <p className=\"text-white/70\">or $2,999/year (save 17%)</p>\n                  </div>\n\n                  {/* Features */}\n                  <div className=\"space-y-3\">\n                    <div className=\"flex items-center justify-between text-sm\">\n                      <span className=\"text-white/70\">All Basic Features</span>\n                      <CheckCircle className=\"w-4 h-4 text-green-400\" />\n                    </div>\n                    <div className=\"flex items-center justify-between text-sm\">\n                      <span className=\"text-white/70\">Advanced Modules</span>\n                      <CheckCircle className=\"w-4 h-4 text-green-400\" />\n                    </div>\n                    <div className=\"flex items-center justify-between text-sm\">\n                      <span className=\"text-white/70\">1-on-1 Mentoring</span>\n                      <CheckCircle className=\"w-4 h-4 text-green-400\" />\n                    </div>\n                    <div className=\"flex items-center justify-between text-sm\">\n                      <span className=\"text-white/70\">Certification</span>\n                      <CheckCircle className=\"w-4 h-4 text-green-400\" />\n                    </div>\n                    <div className=\"flex items-center justify-between text-sm\">\n                      <span className=\"text-white/70\">Priority Support</span>\n                      <CheckCircle className=\"w-4 h-4 text-green-400\" />\n                    </div>\n                  </div>\n\n                  {/* CTA Button */}\n                  <motion.button\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                    onClick={() => setShowForm(true)}\n                    className=\"w-full bg-gradient-to-r from-[var(--color-yellow)] to-[var(--color-yellow-hover)] hover:from-[var(--color-yellow-hover)] hover:to-[var(--color-yellow)] text-white py-4 px-8 rounded-2xl font-bold text-lg shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center gap-2\"\n                  >\n                    <Zap className=\"w-5 h-5\" />\n                    Start Premium Trial\n                    <ArrowRight className=\"w-5 h-5\" />\n                  </motion.button>\n\n                  <p className=\"text-xs text-white/50\">\n                    30-day money-back guarantee • Cancel anytime\n                  </p>\n                </div>\n              </div>\n\n              {/* Floating Elements */}\n              <motion.div\n                animate={{ y: [-10, 10, -10] }}\n                transition={{ duration: 3, repeat: Infinity, ease: \"easeInOut\" }}\n                className=\"absolute -top-4 -right-4 w-8 h-8 bg-[var(--color-blue)] rounded-full opacity-80\"\n              />\n              <motion.div\n                animate={{ y: [10, -10, 10] }}\n                transition={{ duration: 4, repeat: Infinity, ease: \"easeInOut\" }}\n                className=\"absolute -bottom-4 -left-4 w-6 h-6 bg-[var(--color-blue-secondary)] rounded-full opacity-80\"\n              />\n            </motion.div>\n          </div>\n\n          {/* Trust Indicators */}\n          <motion.div \n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.8, duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"text-center\"\n          >\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-[var(--color-yellow)] mb-2\">10,000+</div>\n                <div className=\"text-white/70\">Premium Students</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-green-400 mb-2\">99%</div>\n                <div className=\"text-white/70\">Satisfaction Rate</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-[var(--color-blue)] mb-2\">24/7</div>\n                <div className=\"text-white/70\">Expert Support</div>\n              </div>\n            </div>\n          </motion.div>\n\n          {/* Bottom CTA */}\n          <motion.div \n            initial={{ opacity: 0, y: 30 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ delay: 1, duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"text-center mt-16\"\n          >\n            <motion.button\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              onClick={() => setShowForm(true)}\n              className=\"bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] text-[var(--color-dark-blue)] px-12 py-4 rounded-2xl font-bold text-xl shadow-2xl hover:shadow-3xl transition-all duration-300 flex items-center gap-3 mx-auto\"\n            >\n              <Crown className=\"w-6 h-6\" />\n              Upgrade to the Premium Tier\n              <ArrowRight className=\"w-6 h-6\" />\n            </motion.button>\n            <p className=\"text-white/70 mt-4 text-lg\">\n              Take your cybersecurity training to the next level with our premium tier. Unlock exclusive access to advanced labs, personalized mentorship, and hands-on projects that will transform your skills and career.\n            </p>\n          </motion.div>\n        </div>\n      </section>\n\n      {/* Premium Form Modal */}\n      {showForm && (\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          exit={{ opacity: 0 }}\n          className=\"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm\"\n          onClick={() => setShowForm(false)}\n        >\n          <motion.div\n            initial={{ opacity: 0, scale: 0.9, y: 20 }}\n            animate={{ opacity: 1, scale: 1, y: 0 }}\n            exit={{ opacity: 0, scale: 0.9, y: 20 }}\n            transition={{ type: \"spring\", damping: 25, stiffness: 300 }}\n            className=\"relative bg-white rounded-3xl shadow-2xl max-w-md w-full p-8\"\n            onClick={(e) => e.stopPropagation()}\n          >\n            <div className=\"text-center space-y-6\">\n              <div className=\"w-16 h-16 bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-3xl flex items-center justify-center mx-auto\">\n                <Crown className=\"w-8 h-8 text-[var(--color-yellow)]\" />\n              </div>\n              \n              <div>\n                <h3 className=\"text-2xl font-bold text-[var(--color-dark-blue)] mb-2\">Upgrade to Premium</h3>\n                <p className=\"text-[var(--foreground-secondary)]\">Get started with your premium learning journey</p>\n              </div>\n\n              <form className=\"space-y-4\">\n                <input\n                  type=\"email\"\n                  placeholder=\"Enter your email\"\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:border-[var(--color-blue)]\"\n                />\n                <input\n                  type=\"text\"\n                  placeholder=\"Full name\"\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:border-[var(--color-blue)]\"\n                />\n                <motion.button\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                  className=\"w-full bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] text-white py-3 px-6 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300\"\n                >\n                  Start Premium Trial\n                </motion.button>\n              </form>\n\n              <button\n                onClick={() => setShowForm(false)}\n                className=\"text-[var(--foreground-secondary)] hover:text-[var(--color-dark-blue)] transition-colors\"\n              >\n                Maybe later\n              </button>\n            </div>\n          </motion.div>\n        </motion.div>\n      )}\n    </>\n  );\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;;;AAJA;;;;AAMA,MAAM,kBAAkB;IACtB;IACA;IACA;IACA;IACA;CACD;AAEc,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,qBACE;;0BAEE,6LAAC;gBAAQ,WAAU;;kCAEjB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;;;;;;;kCAGjB,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAEV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAI;wCAClC,aAAa;4CAAE,SAAS;4CAAG,OAAO;wCAAE;wCACpC,YAAY;4CAAE,OAAO;4CAAK,UAAU;wCAAI;wCACxC,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;;0DAEV,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC;gDAAK,WAAU;0DAAiD;;;;;;;;;;;;kDAGnE,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;wCACR,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,OAAO;4CAAK,UAAU;wCAAI;wCACxC,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;;4CACX;0DAEC,6LAAC;gDAAK,WAAU;0DAAiH;;;;;;;;;;;;kDAKnI,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;wCACP,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,OAAO;4CAAK,UAAU;wCAAI;wCACxC,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;kDACX;;;;;;;;;;;;0CAMH,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,UAAU;wCAAI;wCAC5B,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;kDAET,gBAAgB,GAAG,CAAC,CAAC,SAAS,sBAC7B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,SAAS;oDAAG,GAAG,CAAC;gDAAG;gDAC9B,aAAa;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAChC,YAAY;oDAAE,OAAO,QAAQ;oDAAK,UAAU;gDAAI;gDAChD,UAAU;oDAAE,MAAM;gDAAK;gDACvB,WAAU;;kEAEV,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,8NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;;;;;;kEAEzB,6LAAC;kEACC,cAAA,6LAAC;4DAAG,WAAU;sEAAoC;;;;;;;;;;;;+CAX/C;;;;;;;;;;kDAkBX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,UAAU;wCAAI;wCAC5B,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEAEb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;8EACjB,6LAAC;oEAAK,WAAU;8EAAgB;;;;;;;;;;;;sEAIlC,6LAAC;;8EACC,6LAAC;oEAAI,WAAU;;wEAAqC;sFAElD,6LAAC;4EAAK,WAAU;sFAAoC;;;;;;;;;;;;8EAEtD,6LAAC;oEAAE,WAAU;8EAAgB;;;;;;;;;;;;sEAI/B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;sFAAgB;;;;;;sFAChC,6LAAC,8NAAA,CAAA,cAAW;4EAAC,WAAU;;;;;;;;;;;;8EAEzB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;sFAAgB;;;;;;sFAChC,6LAAC,8NAAA,CAAA,cAAW;4EAAC,WAAU;;;;;;;;;;;;8EAEzB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;sFAAgB;;;;;;sFAChC,6LAAC,8NAAA,CAAA,cAAW;4EAAC,WAAU;;;;;;;;;;;;8EAEzB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;sFAAgB;;;;;;sFAChC,6LAAC,8NAAA,CAAA,cAAW;4EAAC,WAAU;;;;;;;;;;;;8EAEzB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;sFAAgB;;;;;;sFAChC,6LAAC,8NAAA,CAAA,cAAW;4EAAC,WAAU;;;;;;;;;;;;;;;;;;sEAK3B,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;4DACZ,YAAY;gEAAE,OAAO;4DAAK;4DAC1B,UAAU;gEAAE,OAAO;4DAAK;4DACxB,SAAS,IAAM,YAAY;4DAC3B,WAAU;;8EAEV,6LAAC,mMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;gEAAY;8EAE3B,6LAAC,qNAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;;;;;;;sEAGxB,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;0DAOzC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,GAAG;wDAAC,CAAC;wDAAI;wDAAI,CAAC;qDAAG;gDAAC;gDAC7B,YAAY;oDAAE,UAAU;oDAAG,QAAQ;oDAAU,MAAM;gDAAY;gDAC/D,WAAU;;;;;;0DAEZ,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,GAAG;wDAAC;wDAAI,CAAC;wDAAI;qDAAG;gDAAC;gDAC5B,YAAY;oDAAE,UAAU;oDAAG,QAAQ;oDAAU,MAAM;gDAAY;gDAC/D,WAAU;;;;;;;;;;;;;;;;;;0CAMhB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;0CAEV,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAqD;;;;;;8DACpE,6LAAC;oDAAI,WAAU;8DAAgB;;;;;;;;;;;;sDAEjC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAyC;;;;;;8DACxD,6LAAC;oDAAI,WAAU;8DAAgB;;;;;;;;;;;;sDAEjC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAmD;;;;;;8DAClE,6LAAC;oDAAI,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;;;;;;0CAMrC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,OAAO;oCAAG,UAAU;gCAAI;gCACtC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAEV,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;wCACxB,SAAS,IAAM,YAAY;wCAC3B,WAAU;;0DAEV,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAY;0DAE7B,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;kDAExB,6LAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;;;;;;;;;;;;;YAQ/C,0BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,MAAM;oBAAE,SAAS;gBAAE;gBACnB,WAAU;gBACV,SAAS,IAAM,YAAY;0BAE3B,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,OAAO;wBAAK,GAAG;oBAAG;oBACzC,SAAS;wBAAE,SAAS;wBAAG,OAAO;wBAAG,GAAG;oBAAE;oBACtC,MAAM;wBAAE,SAAS;wBAAG,OAAO;wBAAK,GAAG;oBAAG;oBACtC,YAAY;wBAAE,MAAM;wBAAU,SAAS;wBAAI,WAAW;oBAAI;oBAC1D,WAAU;oBACV,SAAS,CAAC,IAAM,EAAE,eAAe;8BAEjC,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;0CAGnB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAwD;;;;;;kDACtE,6LAAC;wCAAE,WAAU;kDAAqC;;;;;;;;;;;;0CAGpD,6LAAC;gCAAK,WAAU;;kDACd,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;kDAEZ,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;kDAEZ,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;wCACxB,WAAU;kDACX;;;;;;;;;;;;0CAKH,6LAAC;gCACC,SAAS,IAAM,YAAY;gCAC3B,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;AASf;GApRwB;KAAA", "debugId": null}}, {"offset": {"line": 7373, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/training/components/TrainingAccessForm.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState } from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport { Shield, User, Mail, Phone, Building, GraduationCap, ArrowRight, CheckCircle } from 'lucide-react';\r\n\r\nexport default function TrainingAccessForm() {\r\n  const [formData, setFormData] = useState({\r\n    name: '',\r\n    organization: '',\r\n    phone: '',\r\n    email: '',\r\n    disclaimer: false,\r\n    terms: false\r\n  });\r\n\r\n  const handleInputChange = (e) => {\r\n    const { name, value, type, checked } = e.target;\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      [name]: type === 'checkbox' ? checked : value\r\n    }));\r\n  };\r\n\r\n  const handleSubmit = (e) => {\r\n    e.preventDefault();\r\n    // Handle form submission\r\n    console.log('Form submitted:', formData);\r\n  };\r\n\r\n  return (\r\n    <section className=\"py-24 bg-gradient-to-br from-[var(--color-dark-blue)] via-[var(--color-dark-blue-hover)] to-[var(--color-dark-blue-bg)] relative overflow-hidden\">\r\n      {/* Background Pattern */}\r\n      <div className=\"absolute inset-0\">\r\n        <div className=\"absolute top-0 left-0 w-full h-full bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.03%22%3E%3Ccircle%20cx%3D%2230%22%20cy%3D%2230%22%20r%3D%221%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-50\"></div>\r\n      </div>\r\n\r\n      <div className=\"relative z-10 container mx-auto px-4 max-w-7xl\">\r\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center\">\r\n          {/* Content */}\r\n          <motion.div \r\n            initial={{ opacity: 0, x: -50 }}\r\n            whileInView={{ opacity: 1, x: 0 }}\r\n            transition={{ duration: 0.8 }}\r\n            viewport={{ once: true }}\r\n            className=\"space-y-8\"\r\n          >\r\n            <div>\r\n              <motion.div \r\n                initial={{ opacity: 0, scale: 0.8 }}\r\n                whileInView={{ opacity: 1, scale: 1 }}\r\n                transition={{ delay: 0.2, duration: 0.6 }}\r\n                viewport={{ once: true }}\r\n                className=\"inline-flex items-center bg-[var(--color-blue)]/20 px-4 py-2 rounded-full mb-6\"\r\n              >\r\n                <Shield className=\"w-4 h-4 text-[var(--color-blue)] mr-2\" />\r\n                <span className=\"text-sm font-medium text-[var(--color-blue)]\">Free Access</span>\r\n              </motion.div>\r\n              \r\n              <motion.h2 \r\n                initial={{ opacity: 0, y: 20 }}\r\n                whileInView={{ opacity: 1, y: 0 }}\r\n                transition={{ delay: 0.4, duration: 0.6 }}\r\n                viewport={{ once: true }}\r\n                className=\"text-4xl lg:text-5xl font-bold text-white mb-6\"\r\n              >\r\n                Start Your\r\n                <span className=\"block bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] bg-clip-text text-transparent\">\r\n                  Training Now!\r\n                </span>\r\n              </motion.h2>\r\n              \r\n              <motion.p \r\n                initial={{ opacity: 0, y: 20 }}\r\n                whileInView={{ opacity: 1, y: 0 }}\r\n                transition={{ delay: 0.6, duration: 0.6 }}\r\n                viewport={{ once: true }}\r\n                className=\"text-xl text-white/80 leading-relaxed mb-8\"\r\n              >\r\n                Become part of the next generation of cybersecurity professionals with SecurityLit. Our industry-aligned training program is designed to equip you with the skills needed to succeed in today's cybersecurity landscape.\r\n              </motion.p>\r\n            </div>\r\n\r\n            {/* Benefits */}\r\n            <div className=\"space-y-4\">\r\n              <div className=\"flex items-center gap-4\">\r\n                <div className=\"w-12 h-12 bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-xl flex items-center justify-center shadow-lg\">\r\n                  <CheckCircle className=\"w-6 h-6 text-white\" />\r\n                </div>\r\n                <div>\r\n                  <h3 className=\"text-white font-semibold text-lg\">Free Access</h3>\r\n                  <p className=\"text-white/70\">Get started with our comprehensive cybersecurity training program at no cost.</p>\r\n                </div>\r\n              </div>\r\n              \r\n              <div className=\"flex items-center gap-4\">\r\n                <div className=\"w-12 h-12 bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-xl flex items-center justify-center shadow-lg\">\r\n                  <GraduationCap className=\"w-6 h-6 text-white\" />\r\n                </div>\r\n                <div>\r\n                  <h3 className=\"text-white font-semibold text-lg\">Industry-Relevant Skills</h3>\r\n                  <p className=\"text-white/70\">Learn the latest tools and techniques used by cybersecurity professionals.</p>\r\n                </div>\r\n              </div>\r\n              \r\n              <div className=\"flex items-center gap-4\">\r\n                <div className=\"w-12 h-12 bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-xl flex items-center justify-center shadow-lg\">\r\n                  <Shield className=\"w-6 h-6 text-white\" />\r\n                </div>\r\n                <div>\r\n                  <h3 className=\"text-white font-semibold text-lg\">Career Ready</h3>\r\n                  <p className=\"text-white/70\">Develop practical skills that prepare you for real-world cybersecurity challenges.</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </motion.div>\r\n\r\n          {/* Form */}\r\n          <motion.div \r\n            initial={{ opacity: 0, x: 50 }}\r\n            whileInView={{ opacity: 1, x: 0 }}\r\n            transition={{ duration: 0.8 }}\r\n            viewport={{ once: true }}\r\n            className=\"relative\"\r\n          >\r\n            <div className=\"bg-white/10 backdrop-blur-xl rounded-3xl p-8 border border-white/20 shadow-2xl\">\r\n              <div className=\"text-center mb-8\">\r\n                <div className=\"w-16 h-16 bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-3xl flex items-center justify-center mx-auto mb-4\">\r\n                  <Shield className=\"w-8 h-8 text-white\" />\r\n                </div>\r\n                <h3 className=\"text-2xl font-bold text-white mb-2\">Sign Up for Free Access</h3>\r\n                <p className=\"text-white/70\">Join our Cybersecurity Training Program</p>\r\n              </div>\r\n\r\n              <form onSubmit={handleSubmit} className=\"space-y-6\">\r\n                <div>\r\n                  <label className=\"block text-white font-medium mb-2\">Name *</label>\r\n                  <div className=\"relative\">\r\n                    <User className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-white/50\" />\r\n                    <input\r\n                      type=\"text\"\r\n                      name=\"name\"\r\n                      value={formData.name}\r\n                      onChange={handleInputChange}\r\n                      required\r\n                      className=\"w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/50 focus:outline-none focus:border-[var(--color-blue)] transition-colors\"\r\n                      placeholder=\"Enter your full name\"\r\n                    />\r\n                  </div>\r\n                </div>\r\n\r\n                <div>\r\n                  <label className=\"block text-white font-medium mb-2\">Name of Organization *</label>\r\n                  <div className=\"relative\">\r\n                    <Building className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-white/50\" />\r\n                    <input\r\n                      type=\"text\"\r\n                      name=\"organization\"\r\n                      value={formData.organization}\r\n                      onChange={handleInputChange}\r\n                      required\r\n                      className=\"w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/50 focus:outline-none focus:border-[var(--color-blue)] transition-colors\"\r\n                      placeholder=\"Enter your organization name\"\r\n                    />\r\n                  </div>\r\n                </div>\r\n\r\n                <div>\r\n                  <label className=\"block text-white font-medium mb-2\">Contact Number</label>\r\n                  <div className=\"relative\">\r\n                    <Phone className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-white/50\" />\r\n                    <input\r\n                      type=\"tel\"\r\n                      name=\"phone\"\r\n                      value={formData.phone}\r\n                      onChange={handleInputChange}\r\n                      className=\"w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/50 focus:outline-none focus:border-[var(--color-blue)] transition-colors\"\r\n                      placeholder=\"Enter your phone number\"\r\n                    />\r\n                  </div>\r\n                </div>\r\n\r\n                <div>\r\n                  <label className=\"block text-white font-medium mb-2\">Work Email *</label>\r\n                  <div className=\"relative\">\r\n                    <Mail className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-white/50\" />\r\n                    <input\r\n                      type=\"email\"\r\n                      name=\"email\"\r\n                      value={formData.email}\r\n                      onChange={handleInputChange}\r\n                      required\r\n                      className=\"w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/50 focus:outline-none focus:border-[var(--color-blue)] transition-colors\"\r\n                      placeholder=\"Enter your work email\"\r\n                    />\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"space-y-4\">\r\n                  <label className=\"flex items-center gap-3 cursor-pointer\">\r\n                    <input\r\n                      type=\"checkbox\"\r\n                      name=\"disclaimer\"\r\n                      checked={formData.disclaimer}\r\n                      onChange={handleInputChange}\r\n                      required\r\n                      className=\"w-4 h-4 text-[var(--color-blue)] bg-white/10 border-white/20 rounded focus:ring-[var(--color-blue)]\"\r\n                    />\r\n                    <span className=\"text-white/80 text-sm\">\r\n                      I consent to receive communications about the security training program\r\n                    </span>\r\n                  </label>\r\n\r\n                  <label className=\"flex items-center gap-3 cursor-pointer\">\r\n                    <input\r\n                      type=\"checkbox\"\r\n                      name=\"terms\"\r\n                      checked={formData.terms}\r\n                      onChange={handleInputChange}\r\n                      required\r\n                      className=\"w-4 h-4 text-[var(--color-blue)] bg-white/10 border-white/20 rounded focus:ring-[var(--color-blue)]\"\r\n                    />\r\n                    <span className=\"text-white/80 text-sm\">\r\n                      I agree to the terms and conditions\r\n                    </span>\r\n                  </label>\r\n                </div>\r\n\r\n                <motion.button\r\n                  whileHover={{ scale: 1.02 }}\r\n                  whileTap={{ scale: 0.98 }}\r\n                  type=\"submit\"\r\n                  className=\"w-full bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] text-white py-4 px-8 rounded-xl font-bold text-lg shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center gap-2\"\r\n                >\r\n                  <Shield className=\"w-5 h-5\" />\r\n                  Submit Now\r\n                  <ArrowRight className=\"w-5 h-5\" />\r\n                </motion.button>\r\n              </form>\r\n            </div>\r\n          </motion.div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,cAAc;QACd,OAAO;QACP,OAAO;QACP,YAAY;QACZ,OAAO;IACT;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM;QAC/C,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE,SAAS,aAAa,UAAU;YAC1C,CAAC;IACH;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,yBAAyB;QACzB,QAAQ,GAAG,CAAC,mBAAmB;IACjC;IAEA,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;;;;;;;;;;0BAGjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,6LAAC;;sDACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,SAAS;gDAAG,OAAO;4CAAI;4CAClC,aAAa;gDAAE,SAAS;gDAAG,OAAO;4CAAE;4CACpC,YAAY;gDAAE,OAAO;gDAAK,UAAU;4CAAI;4CACxC,UAAU;gDAAE,MAAM;4CAAK;4CACvB,WAAU;;8DAEV,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC;oDAAK,WAAU;8DAA+C;;;;;;;;;;;;sDAGjE,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;4CACR,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,aAAa;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAChC,YAAY;gDAAE,OAAO;gDAAK,UAAU;4CAAI;4CACxC,UAAU;gDAAE,MAAM;4CAAK;4CACvB,WAAU;;gDACX;8DAEC,6LAAC;oDAAK,WAAU;8DAAiH;;;;;;;;;;;;sDAKnI,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;4CACP,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,aAAa;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAChC,YAAY;gDAAE,OAAO;gDAAK,UAAU;4CAAI;4CACxC,UAAU;gDAAE,MAAM;4CAAK;4CACvB,WAAU;sDACX;;;;;;;;;;;;8CAMH,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,8NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;;;;;;8DAEzB,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAAmC;;;;;;sEACjD,6LAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;sDAIjC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;;;;;;8DAE3B,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAAmC;;;;;;sEACjD,6LAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;sDAIjC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;8DAEpB,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAAmC;;;;;;sEACjD,6LAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOrC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;sCAEV,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;0DAEpB,6LAAC;gDAAG,WAAU;0DAAqC;;;;;;0DACnD,6LAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;kDAG/B,6LAAC;wCAAK,UAAU;wCAAc,WAAU;;0DACtC,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAAoC;;;;;;kEACrD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,6LAAC;gEACC,MAAK;gEACL,MAAK;gEACL,OAAO,SAAS,IAAI;gEACpB,UAAU;gEACV,QAAQ;gEACR,WAAU;gEACV,aAAY;;;;;;;;;;;;;;;;;;0DAKlB,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAAoC;;;;;;kEACrD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,6LAAC;gEACC,MAAK;gEACL,MAAK;gEACL,OAAO,SAAS,YAAY;gEAC5B,UAAU;gEACV,QAAQ;gEACR,WAAU;gEACV,aAAY;;;;;;;;;;;;;;;;;;0DAKlB,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAAoC;;;;;;kEACrD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,6LAAC;gEACC,MAAK;gEACL,MAAK;gEACL,OAAO,SAAS,KAAK;gEACrB,UAAU;gEACV,WAAU;gEACV,aAAY;;;;;;;;;;;;;;;;;;0DAKlB,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAAoC;;;;;;kEACrD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,6LAAC;gEACC,MAAK;gEACL,MAAK;gEACL,OAAO,SAAS,KAAK;gEACrB,UAAU;gEACV,QAAQ;gEACR,WAAU;gEACV,aAAY;;;;;;;;;;;;;;;;;;0DAKlB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAM,WAAU;;0EACf,6LAAC;gEACC,MAAK;gEACL,MAAK;gEACL,SAAS,SAAS,UAAU;gEAC5B,UAAU;gEACV,QAAQ;gEACR,WAAU;;;;;;0EAEZ,6LAAC;gEAAK,WAAU;0EAAwB;;;;;;;;;;;;kEAK1C,6LAAC;wDAAM,WAAU;;0EACf,6LAAC;gEACC,MAAK;gEACL,MAAK;gEACL,SAAS,SAAS,KAAK;gEACvB,UAAU;gEACV,QAAQ;gEACR,WAAU;;;;;;0EAEZ,6LAAC;gEAAK,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;0DAM5C,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gDACZ,YAAY;oDAAE,OAAO;gDAAK;gDAC1B,UAAU;oDAAE,OAAO;gDAAK;gDACxB,MAAK;gDACL,WAAU;;kEAEV,6LAAC,yMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAY;kEAE9B,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxC;GA/OwB;KAAA", "debugId": null}}, {"offset": {"line": 8100, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/CyberSecTraining/page.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport HeroBento from '../training/components/HeroBento';\r\nimport CurriculumHighlights from '../training/components/CurriculumHighlights';\r\nimport WhoCanJoin from '../training/components/WhoCanJoin';\r\nimport LearningModules from '../training/components/LearningModules';\r\nimport CoursePreview from '../training/components/CoursePreview';\r\nimport ProgramStructure from '../training/components/ProgramStructure';\r\nimport StudentLogos from '../training/components/StudentLogos';\r\nimport Testimonials from '../training/components/Testimonials';\r\nimport PremiumCTA from '../training/components/PremiumCTA';\r\nimport TrainingAccessForm from '../training/components/TrainingAccessForm';\r\n\r\nexport default function CyberSecTrainingPage() {\r\n  return (\r\n    <div className=\"min-h-screen bg-white\">\r\n      {/* Hero Bento Section */}\r\n      <HeroBento />\r\n      \r\n      {/* Curriculum Highlights */}\r\n      <CurriculumHighlights />\r\n      \r\n      {/* Who Can Join */}\r\n      <WhoCanJoin />\r\n      \r\n      {/* Learning Modules */}\r\n      <LearningModules />\r\n      \r\n      {/* Course Preview */}\r\n      <CoursePreview />\r\n      \r\n      {/* Program Structure */}\r\n      <ProgramStructure />\r\n      \r\n      {/* Student Logos */}\r\n      <StudentLogos />\r\n      \r\n      {/* Testimonials */}\r\n      <Testimonials />\r\n      \r\n      {/* Premium CTA */}\r\n      <PremiumCTA />\r\n      \r\n      {/* Training Access Form */}\r\n      <TrainingAccessForm />\r\n    </div>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAZA;;;;;;;;;;;;;AAce,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,qJAAA,CAAA,UAAS;;;;;0BAGV,6LAAC,gKAAA,CAAA,UAAoB;;;;;0BAGrB,6LAAC,sJAAA,CAAA,UAAU;;;;;0BAGX,6LAAC,2JAAA,CAAA,UAAe;;;;;0BAGhB,6LAAC,yJAAA,CAAA,UAAa;;;;;0BAGd,6LAAC,4JAAA,CAAA,UAAgB;;;;;0BAGjB,6LAAC,wJAAA,CAAA,UAAY;;;;;0BAGb,6LAAC,wJAAA,CAAA,UAAY;;;;;0BAGb,6LAAC,sJAAA,CAAA,UAAU;;;;;0BAGX,6LAAC,8JAAA,CAAA,UAAkB;;;;;;;;;;;AAGzB;KAlCwB", "debugId": null}}]}