import Image from 'next/image';
import React from 'react';

const Teams = () => {
  return (
    <div className="mx-auto md:p-20 p-10 bg-gray-50">
    <div className="space-y-12">
      <div className="flex flex-col md:flex-row gap-8">
        <div className="md:w-1/2 space-y-4">
          <h2 className="text-sm font-semibold text-gray-600 uppercase">Expert API Penetration Testing
          </h2>
          <h1 className="text-3xl font-bold text-blue-700">Tailored API Testing</h1>
          <p className="text-gray-600">
          Your APIs deserve more than a generic testing approach. We assemble specialized teams, handpicked and curated to match your unique API environment. With Capture The Bug, you benefit from experienced pentesters who understand the intricacies of API vulnerabilities, ensuring comprehensive and effective testing that’s tailored to your specific needs.
          </p>
        </div>
        <div className="md:w-1/2 flex items-center justify-center h-64 md:h-auto">
        <Image src="/images/android-app-2-finalized.jpg" width={500} height={500} alt="" />
        </div>
      </div>
      
      <div className="flex flex-col md:flex-row gap-8">
      <div className="md:w-1/2 flex items-center justify-center h-64 md:h-auto">
        <Image src="/images/android-app-3.png" width={500} height={500} alt="" />
        </div>
        <div className="md:w-1/2 space-y-4">
          {/* <h2 className="text-sm font-semibold text-gray-600 uppercase">Real-Time Penetration Test Dashboard
          </h2> */}
          <h1 className="text-3xl font-bold text-blue-700">Real-Time API Testing         </h1>
          <p className="text-gray-600">
          Stay informed throughout the API testing process with our intuitive dashboard. Monitor findings, track progress, and view detailed analytics in real-time. Our platform provides you with clear, prioritized action items, so you’re never left in the dark. Once testing is complete, download your final, audit-ready report directly from the dashboard, allowing you to take immediate action to secure your APIs.
          </p>
        </div>
      </div>
    </div>
  </div>
  );
};

export default Teams;