import React, { useState } from 'react';
import Image from 'next/image';

const HeaderSection = ({ title, description, imageUrl, breadcrumbs, toc }) => {
  const [showToc, setShowToc] = useState(false);
  return (
    <div className="bg-[var(--color-dark-blue)] text-white relative px-0 md:px-0 py-0 flex flex-col lg:flex-row items-stretch gap-0">
      {/* Breadcrumbs positioned absolutely at the top */}
      {breadcrumbs && (
        <div className="absolute top-0 left-0 right-0 pt-2 sm:pt-2 md:pt-5 xl:pt-14 z-10">
          <div className="max-w-7xl px-2 sm:px-2 md:px-16">
            {breadcrumbs}
          </div>
        </div>
      )}
      {/* Mobile: Image first, then text */}
      <div className="lg:hidden w-full flex flex-col items-center gap-6 px-6 md:px-8 py-8 lg:py-12 bg-transparent mt-20">
        {/* Image at the top for mobile */}
        {imageUrl && (
          <div className="w-full max-w-md aspect-[4/3] rounded-xl overflow-hidden shadow-lg bg-white border border-[var(--color-yellow)] flex items-center justify-center">
            <Image
              src={imageUrl}
              alt={title || "Blog post featured image"}
              className="object-contain w-full h-full"
              width={400}
              height={300}
              priority
            />
          </div>
        )}
        {/* Text content below image for mobile */}
        <div className="flex flex-col justify-center text-center">
          <h1 className="text-2xl md:text-4xl font-bold mb-4 leading-tight">{title}</h1>
          <p className="text-md md:text-lg text-white/90 max-w-2xl">{description}</p>
        </div>
      </div>
      
      {/* Desktop: Text and image side by side */}
      <div className="hidden lg:flex flex-1 min-w-0 flex-col justify-center px-6 md:px-16 py-12 lg:py-20 mt-20">
        <h1 className="text-2xl md:text-4xl font-bold mb-4 leading-tight">{title}</h1>
        <p className="text-md md:text-lg text-white/90 max-w-2xl">{description}</p>
      </div>
      
      {/* Desktop: Right Sidebar - Image and TOC */}
      <aside className="hidden lg:flex flex-col justify-center items-center gap-6 px-6 md:px-8 py-8 lg:py-12 bg-transparent lg:bg-white/0 mt-20 w-full lg:w-[380px] flex-shrink-0">
        {/* Image always at the top */}
        {imageUrl && (
          <div className="w-full aspect-[4/3] rounded-xl overflow-hidden shadow-lg bg-white border border-[var(--color-yellow)] flex items-center justify-center">
            <Image
              src={imageUrl}
              alt={title || "Blog post featured image"}
              className="object-contain w-full h-full"
              width={340}
              height={255}
              priority
            />
          </div>
        )}
        {/* TOC for desktop (always visible) */}
        {toc && toc.length > 0 && (
          <div className="hidden lg:block w-full">
            <div className="bg-white rounded-xl shadow-lg border border-[var(--color-yellow)] p-6">
              <h3 className="text-lg font-semibold mb-3 text-[var(--color-dark-blue)]">Table of Contents</h3>
              <ul className="space-y-2">
                {toc.map((item) => (
                  <li key={item.id}>
                    <a href={`#${item.id}`} className="text-[var(--color-blue)] hover:underline text-sm block truncate">
                      {item.text}
                    </a>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        )}
        {/* TOC for mobile (collapsible, more prominent) */}
        {toc && toc.length > 0 && (
          <div className="block lg:hidden w-full">
            <button
              className="w-full px-4 py-2 bg-white/20 text-white rounded-lg font-semibold mb-2 border border-[var(--color-yellow)]"
              onClick={() => setShowToc((v) => !v)}
            >
              {showToc ? 'Hide' : 'Show'} Table of Contents
            </button>
            {showToc && (
              <div className="bg-white rounded-xl shadow-lg border border-[var(--color-yellow)] p-6 mb-2">
                <h3 className="text-lg font-semibold mb-3 text-[var(--color-dark-blue)]">Table of Contents</h3>
                <ul className="space-y-2">
                  {toc.map((item) => (
                    <li key={item.id}>
                      <a href={`#${item.id}`} className="text-[var(--color-blue)] hover:underline text-sm block truncate">
                        {item.text}
                      </a>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        )}
      </aside>
    </div>
  );
};

export default HeaderSection;