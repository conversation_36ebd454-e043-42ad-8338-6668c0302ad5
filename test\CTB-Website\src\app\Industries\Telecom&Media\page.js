import React from "react";
import PartnersList from "@/app/Product/Penetration-Testing/components/PartnersList";
import BlogSection from "@/app/Home/components/Blogs";
import Landing from "./components/Landing";
import Security from "./components/Security";
import Testimonial from "../Testimonial";

export const metadata = {
  title: "Capture The Bug | Telecom & Media Security",
  description:
    "Secure 5G networks, VoIP systems, and OTT platforms with Capture The Bug’s telecom-specific penetration testing. Detect SIM swapping, API abuse, and session hijacking threats.",
  keywords:
    "telecom penetration testing, 5G network security, VoIP security, OTT platform protection, SIM swapping detection, telecom data protection, subscriber data security, media infrastructure testing, telecom compliance, Capture The Bug",
  robots: "index, follow",
  icons: {
    icon: "/favicon.ico",
  },
  openGraph: {
    title: "Capture The Bug | Telecom & Media Security",
    type: "website",
    url: "https://capturethebug.xyz/Industries/Telecom&Media",
    description:
      "From 5G to streaming platforms, Capture The Bug helps telcos and media enterprises secure networks and user data with proactive threat detection and expert-led penetration testing.",
    images: "https://ibb.co/chNDKJV7",
  },
  twitter: {
    card: "summary_large_image",
    title: "Capture The Bug | Telecom & Media Security",
    description:
      "Capture The Bug enables global telecoms and media companies to protect subscriber systems and digital infrastructure from evolving threats.",
    images: "https://ibb.co/chNDKJV7",
  },
};


const testimonial = {
  company: "Devoli",
  logo: "/images/devoli_logo.jpeg",
  quote: "At Devoli, we simplify telco for our customers across ANZ. InfoSec and data privacy are critically important to everything we do. We are a fast-moving tech company and have used various traditional pen-testing providers in the past. We were intrigued by Capture The Bug's more continuous testing approach. Three months into our journey with CTB, we are impressed by the responsiveness of their team and the immediate insights from CTB's App. We can now launch pen-test programs on-demand and start seeing results within a couple of days. The real-time visibility and collaborative workflow let's our team triage, comment, and request retests directly in the App. The workflow overall feels far more natural and is much better aligned with our delivery cadence – and provides learning opportunities for our team at the same time. If you are a fast-moving tech company looking for a more continuous security assessment as part of your development lifecycle, I would recommend checking them out.",
  author: "Jan Behrens",
  position: "Chief Technology Officer "
}; 


export default function Telecom() {
  return (
    <>
    <Landing/>
   <Security/>
   <PartnersList/>
   <Testimonial
   company={testimonial.company}
   logo={testimonial.logo}
   quote={testimonial.quote}
   author={testimonial.author}
   position={testimonial.position}
   logoSize={{ width: 120, height: 100 }}
   logoStyle={{ marginLeft: -10 }}
      />
     <BlogSection/> 
</>
  );
}