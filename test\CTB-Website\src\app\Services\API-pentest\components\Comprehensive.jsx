import Button from "@/app/common/buttons/Button";
import Image from "next/image";
import Link from "next/link";
import React from "react";

const BulletPoint = ({ imageSrc, content }) => (
    <div className="flex items-start mb-4">
      <Image src={imageSrc} alt="Checkmark icon indicating API penetration testing benefit" className="w-6 h-6 mr-4 mt-1 " width={24} height={24} />
      <p className="flex-1 text-slate-600">{content}</p>
    </div>
  );

export default function Comprehensive() {
    const bulletPoints = [
        {
          imageSrc: "/images/tick.png",
          content: "Leverage our continuous scanning, powered by the latest hacker methodologies, to stay protected against emerging threats."
        },
        {
          imageSrc: "/images/tick.png",
          content: "Our platform evolves with the latest intelligence on new vulnerabilities and CVEs, ensuring your defenses are always up-to-date."
        },
        {
          imageSrc: "/images/tick.png",
          content: "Capture The Bug's intelligent testing process adapts based on your past results, delivering precise, customized security assessments."
        }
      ];
  return (
    <div>
      <div className="md:p-12 md:px-20 p-8 flex md:flex-row flex-col justify-between md:py-20">
        <div className="Content md:w-[50%] gap-6 flex flex-col">
            <div className="font-bold">
            COMPREHENSIVE PENTESTING
            </div>
          <div className="Title md:text-3xl text-2xl font-bold bg-gradient-to-r from-[#062575] via-[#0B45DB] to-[#0B45DB] bg-clip-text text-transparent">
          Uncover Vulnerabilities That Traditional Pentests Overlook
          </div>

          <div className="subTitle2 leading-8 md:pr-10 text-slate-600 text-sm md:text-lg">
          Our expert pentesters use advanced techniques to simulate real-world attacks, uncovering hidden vulnerabilities in your web applications. With Capture The Bug PTaaS, you can secure your app with comprehensive testing that adapts to the evolving threat landscape.
          </div>
            <div>
            {bulletPoints.map((point, index) => (
        <BulletPoint key={index} imageSrc={point.imageSrc} content={point.content} />
      ))}
            </div>
        </div>
        <div className="Image md:mt-4 mt-10">
          <Image src="/images/web-app-2.png" width={400} height={350} alt="API penetration testing dashboard showing comprehensive security assessment results and vulnerability analysis for API endpoints" />
        </div>
      </div>
    </div>
  );
}
