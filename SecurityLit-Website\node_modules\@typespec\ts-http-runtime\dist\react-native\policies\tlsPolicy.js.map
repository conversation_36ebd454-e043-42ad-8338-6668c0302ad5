{"version": 3, "file": "tlsPolicy.js", "sourceRoot": "", "sources": ["../../../src/policies/tlsPolicy.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAKlC;;GAEG;AACH,MAAM,CAAC,MAAM,aAAa,GAAG,WAAW,CAAC;AAEzC;;GAEG;AACH,MAAM,UAAU,SAAS,CAAC,WAAyB;IACjD,OAAO;QACL,IAAI,EAAE,aAAa;QACnB,WAAW,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE;YAC/B,gFAAgF;YAChF,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC;gBACrB,GAAG,CAAC,WAAW,GAAG,WAAW,CAAC;YAChC,CAAC;YACD,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC;QACnB,CAAC;KACF,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { PipelinePolicy } from \"../pipeline.js\";\nimport type { TlsSettings } from \"../interfaces.js\";\n\n/**\n * Name of the TLS Policy\n */\nexport const tlsPolicyName = \"tlsPolicy\";\n\n/**\n * Gets a pipeline policy that adds the client certificate to the HttpClient agent for authentication.\n */\nexport function tlsPolicy(tlsSettings?: TlsSettings): PipelinePolicy {\n  return {\n    name: tlsPolicyName,\n    sendRequest: async (req, next) => {\n      // Users may define a request tlsSettings, honor those over the client level one\n      if (!req.tlsSettings) {\n        req.tlsSettings = tlsSettings;\n      }\n      return next(req);\n    },\n  };\n}\n"]}