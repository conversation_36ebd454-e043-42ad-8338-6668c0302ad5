
import Button from "@/app/common/buttons/Button";
import Image from "next/image";
import Link from "next/link";
import { ArrowRight, Shield, Zap, Target, Network, Lock, AlertTriangle, Globe, Wifi, Server } from "lucide-react";

export default function InternalPentestingSection() {
  return (
    <div className="relative min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 overflow-hidden">
       
      <div className="relative z-10 container mx-auto md:px-28 pt-16 md:pt-4 pb-16">
        
        <div className="flex flex-col lg:flex-row items-center gap-12 lg:gap-16 xl:gap-20 min-h-[80vh]">
          
          {/* Left content */}
          <div className="flex-1 text-center lg:text-left max-w-2xl space-y-8">
             
            <div className="space-y-4">
              <h1 className="text-xl sm:text-2xl md:text-4xl font-bold leading-tight">
                Internal Network
              </h1>
              <h1 className="text-xl sm:text-2xl md:text-4xl font-bold text-blue-700 bg-clip-text leading-tight">
                Pentesting
              </h1>
            </div>

            {/* Description */}
            <p className="text-slate-600 text-sm sm:text-base md:text-lg leading-relaxed  ">
              Detect and prioritize internal threats from within your network infrastructure. 
              Get visibility into <span className="font-semibold text-slate-800 bg-yellow-100 px-2 py-1 rounded-md">insecure protocols, misconfigured services</span>, 
              and lateral movement pathways.
            </p>

            {/* What we test section */}
            <div className="space-y-4">
              <h2 className="text-xl font-semibold text-slate-800">What we test?</h2>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                <div className="flex items-center gap-3 p-3 bg-white/60 backdrop-blur-sm rounded-lg border border-blue-100/50">
                  <Lock className="w-5 h-5 text-blue-600" />
                  <span className="text-sm font-medium text-slate-700">Weak Authentication</span>
                </div>
                <div className="flex items-center gap-3 p-3 bg-white/60 backdrop-blur-sm rounded-lg border border-indigo-100/50">
                  <ArrowRight className="w-5 h-5 text-blue-600" />
                  <span className="text-sm font-medium text-slate-700">Privilege Escalation</span>
                </div>
                <div className="flex items-center gap-3 p-3 bg-white/60 backdrop-blur-sm rounded-lg border border-purple-100/50">
                  <Network className="w-5 h-5 text-blue-600" />
                  <span className="text-sm font-medium text-slate-700">Lateral Movement</span>
                </div>
                <div className="flex items-center gap-3 p-3 bg-white/60 backdrop-blur-sm rounded-lg border border-red-100/50">
                  <AlertTriangle className="w-5 h-5 text-blue-600" />
                  <span className="text-sm font-medium text-slate-700">Misconfigured Services</span>
                </div>
                <div className="flex items-center gap-3 p-3 bg-white/60 backdrop-blur-sm rounded-lg border border-green-100/50">
                  <Target className="w-5 h-5 text-blue-600" />
                  <span className="text-sm font-medium text-slate-700">Shadow IT Devices</span>
                </div>
              </div>
            </div>

            {/* Why it matters */}
            <div className="bg-white p-6 rounded-2xl border border-blue-100/50">
              <h3 className="text-lg font-semibold text-slate-800 mb-3 flex items-center gap-2">
                <Shield className="w-5 h-5 text-blue-600" />
                Why it matters?
              </h3>
              <p className="text-slate-600 text-md leading-relaxed">
                Internal misconfigurations are one of the most exploited paths during targeted attacks. 
                We simulate real-world attacker movement to find what others miss.
              </p>
            </div>
 
          </div>
 
          {/* Right side image */}
          <div className="flex-1 w-full max-w-3xl relative">
            <div className="md:px-16 -mt-5">
              <Image
                src="/images/Internal.svg"
                alt="Internal Network Pentesting Dashboard"
                width={1440} 
                height={100} 
                className="md:w-full w-[600px] scale-90 rounded-xl"
                priority
                quality={80} 
              />
            </div>
          </div>
        </div>
      </div>

       <div className="relative z-10 container mx-auto md:px-28 pt-16 md:pt-4 pb-16">
        
        {/* Main content section */}
        <div className="flex flex-col lg:flex-row items-center gap-12 lg:gap-16 xl:gap-20 min-h-[80vh]">
          
          {/* Left side image */}
          <div className="flex-1 w-full max-w-3xl relative">
            <div className="md:px-16 -mt-5">
              <Image
                src="/images/External.svg"
                alt="External Network Pentesting Dashboard"
                width={1440} 
                height={100} 
                className="md:w-full w-[600px] scale-90 rounded-xl"
                priority
                quality={80} 
              />
            </div>
          </div>

          {/* Right content */}
          <div className="flex-1 text-center lg:text-left max-w-2xl space-y-8">
             
            <div className="space-y-4">
              <h1 className="text-xl sm:text-2xl md:text-4xl font-bold leading-tight">
                External Network
              </h1>
              <h1 className="text-xl sm:text-2xl md:text-4xl font-bold text-blue-700 bg-clip-text leading-tight">
                Pentesting
              </h1>
            </div>

            {/* Description */}
            <p className="text-slate-600 text-sm sm:text-base md:text-lg leading-relaxed">
              Protect your public-facing infrastructure from real-world exploits. 
              We assess <span className="font-semibold text-slate-800 bg-yellow-100 px-2 py-1 rounded-md">open ports, DNS misconfigurations, outdated services</span>, 
              and DoS exposure.
            </p>

            {/* What we test section */}
            <div className="space-y-4">
              <h2 className="text-xl font-semibold text-slate-800">What we test?</h2>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                <div className="flex items-center gap-3 p-3 bg-white/60 backdrop-blur-sm rounded-lg border border-blue-100/50">
                  <Server className="w-5 h-5 text-blue-600" />
                  <span className="text-sm font-medium text-slate-700">Open Ports & Services</span>
                </div>
                <div className="flex items-center gap-3 p-3 bg-white/60 backdrop-blur-sm rounded-lg border border-indigo-100/50">
                  <Globe className="w-5 h-5 text-blue-600" />
                  <span className="text-sm font-medium text-slate-700">DNS Hijacking</span>
                </div>
                <div className="flex items-center gap-3 p-3 bg-white/60 backdrop-blur-sm rounded-lg border border-purple-100/50">
                  <Lock className="w-5 h-5 text-blue-600" />
                  <span className="text-sm font-medium text-slate-700">Insecure Protocols</span>
                </div>
                <div className="flex items-center gap-3 p-3 bg-white/60 backdrop-blur-sm rounded-lg border border-red-100/50">
                  <AlertTriangle className="w-5 h-5 text-blue-600" />
                  <span className="text-sm font-medium text-slate-700">Denial of Service Vectors</span>
                </div>
                <div className="flex items-center gap-3 p-3 bg-white/60 backdrop-blur-sm rounded-lg border border-green-100/50">
                  <Wifi className="w-5 h-5 text-blue-600" />
                  <span className="text-sm font-medium text-slate-700">Web/API Gateway Security</span>
                </div>
              </div>
            </div>

            {/* Why it matters */}
            <div className="bg-white p-6 rounded-2xl border border-blue-100/50">
              <h3 className="text-lg font-semibold text-slate-800 mb-3 flex items-center gap-2">
                <Shield className="w-5 h-5 text-blue-600" />
                Why it matters?
              </h3>
              <p className="text-slate-600 text-md leading-relaxed">
                One exposed service is enough for attackers to get in. We validate your perimeter before someone else does.
              </p>
            </div>
 
          </div>
        </div>
      </div>

       <div className="relative z-10 container mx-auto md:px-28 pt-16 md:pt-4 pb-16">
        
        {/* Thick Client section */}
        <div className="flex flex-col lg:flex-row items-center gap-12 lg:gap-16 xl:gap-20 min-h-[80vh]">
          
          {/* Left content */}
          <div className="flex-1 text-center lg:text-left max-w-2xl space-y-8">
             
            <div className="space-y-4">
              <h1 className="text-xl sm:text-2xl md:text-4xl font-bold leading-tight">
                Thick Client Network
              </h1>
              <h1 className="text-xl sm:text-2xl md:text-4xl font-bold text-blue-700 bg-clip-text leading-tight">
                Pentesting
              </h1>
            </div>

            {/* Description */}
            <p className="text-slate-600 text-sm sm:text-base md:text-lg leading-relaxed">
              Simulate real-world risks across desktop applications and internal components. 
              Our team performs deep validation of <span className="font-semibold text-slate-800 bg-yellow-100 px-2 py-1 rounded-md">binaries, transmission layers, and access controls</span>.
            </p>

            {/* What we test section */}
            <div className="space-y-4">
              <h2 className="text-xl font-semibold text-slate-800">What we test?</h2>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                <div className="flex items-center gap-3 p-3 bg-white/60 backdrop-blur-sm rounded-lg border border-blue-100/50">
                  <Target className="w-5 h-5 text-blue-600" />
                  <span className="text-sm font-medium text-slate-700">Client-Side Injection</span>
                </div>
                <div className="flex items-center gap-3 p-3 bg-white/60 backdrop-blur-sm rounded-lg border border-indigo-100/50">
                  <Lock className="w-5 h-5 text-blue-600" />
                  <span className="text-sm font-medium text-slate-700">Session Management</span>
                </div>
                <div className="flex items-center gap-3 p-3 bg-white/60 backdrop-blur-sm rounded-lg border border-purple-100/50">
                  <Server className="w-5 h-5 text-blue-600" />
                  <span className="text-sm font-medium text-slate-700">Local Storage Weaknesses</span>
                </div>
                <div className="flex items-center gap-3 p-3 bg-white/60 backdrop-blur-sm rounded-lg border border-red-100/50">
                  <Wifi className="w-5 h-5 text-blue-600" />
                  <span className="text-sm font-medium text-slate-700">Insecure Data Transmission</span>
                </div>
              </div>
            </div>

            {/* Why it matters */}
            <div className="bg-white p-6 rounded-2xl border border-blue-100/50">
              <h3 className="text-lg font-semibold text-slate-800 mb-3 flex items-center gap-2">
                <Shield className="w-5 h-5 text-blue-600" />
                Why it matters?
              </h3>
              <p className="text-slate-600 text-md leading-relaxed">
                Thick clients often fly under the radar - but they&apos;re complex, stateful, and risky. 
                We bring clarity to their attack surface.
              </p>
            </div>
 
          </div>
 
          {/* Right side image */}
          <div className="flex-1 w-full max-w-3xl relative">
            <div className="md:px-16 -mt-5">
              <Image
                src="/images/Thick-client.svg"
                alt="Thick Client Network Pentesting Dashboard"
                width={1440} 
                height={100} 
                className="md:w-full w-[600px] scale-90 rounded-xl"
                priority
                quality={80} 
              />
            </div>
          </div>
        </div>
      </div>

       <div className="relative z-10 container mx-auto md:px-28 pt-16 md:pt-4 pb-16">
        
        {/* Main content section */}
        <div className="flex flex-col lg:flex-row items-center gap-12 lg:gap-16 xl:gap-20 min-h-[80vh]">
          
          {/* Left side image */}
          <div className="flex-1 w-full max-w-3xl relative">
            <div className="md:px-16 -mt-5">
              <Image
                src="/images/Host-based.svg"
                alt="External Network Pentesting Dashboard"
                width={1440} 
                height={100} 
                className="md:w-full w-[600px] scale-90 rounded-xl"
                priority
                quality={80} 
              />
            </div>
          </div>

          {/* Right content */}
          <div className="flex-1 text-center lg:text-left max-w-2xl space-y-8">
             
            <div className="space-y-4">
              <h1 className="text-xl sm:text-2xl md:text-4xl font-bold leading-tight">
                Host-Based  
              </h1>
              <h1 className="text-xl sm:text-2xl md:text-4xl font-bold text-blue-700 bg-clip-text leading-tight">
                Penetration Testing
              </h1>
            </div>

            {/* Description */}
            <p className="text-slate-600 text-sm sm:text-base md:text-lg leading-relaxed">
              Gain deep visibility into endpoint security posture across your infrastructure. 
            </p>

                <div className="space-y-4">
              <h2 className="text-xl font-semibold text-slate-800">What we test?</h2>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                <div className="flex items-center gap-3 p-3 bg-white/60 backdrop-blur-sm rounded-lg border border-blue-100/50">
                  <Lock className="w-5 h-5 text-blue-600" />
                  <span className="text-sm font-medium text-slate-700">Weak Local Services & Users</span>
                </div>
                <div className="flex items-center gap-3 p-3 bg-white/60 backdrop-blur-sm rounded-lg border border-indigo-100/50">
                  <Shield className="w-5 h-5 text-blue-600" />
                  <span className="text-sm font-medium text-slate-700">Insecure Sudo/Root Configs</span>
                </div>
                <div className="flex items-center gap-3 p-3 bg-white/60 backdrop-blur-sm rounded-lg border border-purple-100/50">
                  <AlertTriangle className="w-5 h-5 text-blue-600" />
                  <span className="text-sm font-medium text-slate-700">File Permission Issues</span>
                </div>
                <div className="flex items-center gap-3 p-3 bg-white/60 backdrop-blur-sm rounded-lg border border-red-100/50">
                  <Server className="w-5 h-5 text-blue-600" />
                  <span className="text-sm font-medium text-slate-700">Patch & OS Hardening Gaps</span>
                </div>
                <div className="flex items-center gap-3 p-3 bg-white/60 backdrop-blur-sm rounded-lg border border-green-100/50">
                  <ArrowRight className="w-5 h-5 text-blue-600" />
                  <span className="text-sm font-medium text-slate-700">Privilege Escalation Paths</span>
                </div>
              </div>
            </div>

            {/* Why it matters */}
            <div className="bg-white p-6 rounded-2xl border border-blue-100/50">
              <h3 className="text-lg font-semibold text-slate-800 mb-3 flex items-center gap-2">
                <Shield className="w-5 h-5 text-blue-600" />
                Why it matters?
              </h3>
              <p className="text-slate-600 text-md leading-relaxed">
Once an attacker is inside your network, hosts are the next frontier. We validate real-world exploit paths from local access to full control.              </p>
            </div>
 
          </div>
        </div>
      </div>
           
    </div>
  );
}

   
   