'use client';

import React from 'react';
import { TrendingUp, Setting<PERSON>, Target } from 'lucide-react';

export default function CybersecurityChallenges() {
  return (
    <div className="bg-white py-8 sm:py-12 md:py-16 lg:py-20 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8 sm:mb-12 md:mb-16">
          <p className="text-blue-600 text-xs sm:text-sm font-semibold tracking-wider uppercase mb-4 sm:mb-6">
            MODERN SECURITY REMEDIATION CHALLENGES
          </p>
          <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 max-w-4xl mx-auto leading-tight px-4">
            Threats don&apos;t wait - and in fast-moving SaaS, neither can you
          </h2>
        </div>

        {/* Three Challenges Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 lg:gap-12">
          
          {/* Challenge 1 - Vulnerabilities Stack Up Fast */}
          <div className="text-center space-y-4 sm:space-y-6 px-4 sm:px-2 group cursor-pointer transform transition-all duration-300 hover:scale-105 hover:shadow-lg rounded-xl p-4 hover:bg-gradient-to-br hover:from-blue-50 hover:to-indigo-50">
            <div className="flex justify-center">
              <div className="w-14 h-14 sm:w-16 sm:h-16 rounded-full bg-blue-50 flex items-center justify-center transition-all duration-300 group-hover:bg-blue-100 group-hover:scale-110 group-hover:shadow-md">
                <TrendingUp className="w-6 h-6 sm:w-8 sm:h-8 text-blue-600 transition-all duration-300 group-hover:text-blue-700 group-hover:scale-110" strokeWidth={1.5} />
              </div>
            </div>
            <div className="space-y-3 sm:space-y-4">
              <h3 className="text-lg sm:text-xl font-bold text-blue-600 leading-tight transition-colors duration-300 group-hover:text-blue-700">
                Vulnerabilities Stack Up Fast
              </h3>
              <p className="text-sm sm:text-base text-gray-600 leading-relaxed max-w-sm mx-auto transition-colors duration-300 group-hover:text-gray-700">
                Rapid deployments mean faster exposure. Even brief delays in triage or patching can put sensitive data and customer trust at risk.
              </p>
            </div>
          </div>

          {/* Challenge 2 - Manual Workflows Break at Scale */}
          <div className="text-center space-y-4 sm:space-y-6 px-4 sm:px-2 group cursor-pointer transform transition-all duration-300 hover:scale-105 hover:shadow-lg rounded-xl p-4 hover:bg-gradient-to-br hover:from-blue-50 hover:to-indigo-50">
            <div className="flex justify-center">
              <div className="w-14 h-14 sm:w-16 sm:h-16 rounded-full bg-blue-50 flex items-center justify-center transition-all duration-300 group-hover:bg-blue-100 group-hover:scale-110 group-hover:shadow-md">
                <Settings className="w-6 h-6 sm:w-8 sm:h-8 text-blue-600 transition-all duration-300 group-hover:text-blue-700 group-hover:scale-110 group-hover:rotate-45" strokeWidth={1.5} />
              </div>
            </div>
            <div className="space-y-3 sm:space-y-4">
              <h3 className="text-lg sm:text-xl font-bold text-blue-600 leading-tight transition-colors duration-300 group-hover:text-blue-700">
                Manual Workflows Break at Scale
              </h3>
              <p className="text-sm sm:text-base text-gray-600 leading-relaxed max-w-sm mx-auto transition-colors duration-300 group-hover:text-gray-700">
                Enterprise security can&apos;t rely on scattered tools, tickets, or email threads. Manual coordination leads to missed SLAs and inconsistent patching.
              </p>
            </div>
          </div>

          {/* Challenge 3 - Delayed Response = Bigger Blast Radius */}
          <div className="text-center space-y-4 sm:space-y-6 px-4 sm:px-2 sm:col-span-2 lg:col-span-1 group cursor-pointer transform transition-all duration-300 hover:scale-105 hover:shadow-lg rounded-xl p-4 hover:bg-gradient-to-br hover:from-blue-50 hover:to-indigo-50">
            <div className="flex justify-center">
              <div className="w-14 h-14 sm:w-16 sm:h-16 rounded-full bg-blue-50 flex items-center justify-center transition-all duration-300 group-hover:bg-blue-100 group-hover:scale-110 group-hover:shadow-md">
                <Target className="w-6 h-6 sm:w-8 sm:h-8 text-blue-600 transition-all duration-300 group-hover:text-blue-700 group-hover:scale-110" strokeWidth={1.5} />
              </div>
            </div>
            <div className="space-y-3 sm:space-y-4 ">
              <h3 className="text-lg sm:text-[19px] font-bold text-blue-600 leading-tight transition-colors duration-300 group-hover:text-blue-700 whitespace-nowrap ">
                Delayed Response = Bigger Blast Radius
              </h3>
              <p className="text-sm sm:text-base text-gray-600 leading-relaxed max-w-sm mx-auto transition-colors duration-300 group-hover:text-gray-700">
                Slow remediation cycles increase the window for attackers to exploit known CVEs - and make incident response more expensive. Looking to offer these solutions to your clients? Check out our <a href="/Company/Partner-With-Us" className="text-blue-600 hover:underline font-semibold">partner program</a>.
              </p>
            </div>
          </div>

        </div>
      </div>
    </div>
  );
}