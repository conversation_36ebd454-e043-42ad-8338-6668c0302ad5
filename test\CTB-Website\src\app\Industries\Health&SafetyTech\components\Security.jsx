import React from "react";
import { Shield, Users, Target } from "lucide-react";
 
export default function  Security() {
  return (
    <> 
  <div className="bg-white py-16 sm:py-24">
  <div className="container mx-auto px-4 md:px-24">
    <div className="mb-16">
      <div className="text-secondary-blue text-xl font-bold mb-5">
        How Capture The Bug Helps
      </div>
      <h2 className="text-4xl sm:text-5xl font-bold text-[#010D2C] leading-tight mb-5">
Securing the Future of Digital Health & Safety Technology     </h2>
      <p className="text-[#6B7280] text-lg font-medium leading-relaxed  ">
As site safety platforms digitize inductions, contractor tracking, inspections, and compliance workflows, the risk of cyberattack increases.
We help safety and workforce tech vendors proactively identify and mitigate vulnerabilities- <span className="font-bold"> without disrupting operations or end-user trust. </span>
Penetration testing plays a vital role in protecting the integrity of safety-critical platforms that power workforce access, accountability, and regulatory reporting.
      </p>
    </div>

        <div className="bg-white border-l-4 border-[#58CC02] pl-4 sm:pl-6 pr-4 sm:pr-6 py-6 sm:py-8 rounded-r-lg shadow-sm hover:shadow-md transition-shadow duration-300"> 
          <div className="">
            <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-[#58CC02] to-[#4BAF02] rounded-lg flex items-center justify-center mb-4 sm:mb-6">
              <Shield className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
            </div>
            <h3 className="text-xl sm:text-2xl font-bold text-[#010D2C] mb-3 sm:mb-4">
              End-to-End Protection for Safety Workflows
            </h3>
            <p className="text-[#6B7280] text-base sm:text-lg leading-relaxed">
              We secure your full <strong>H&S tech stack</strong>-from presence detection systems and mobile check-in apps to hazard registers and compliance dashboards. Our assessments focus on <strong>end-to-end data flow integrity</strong>, ensuring that <strong>safety-critical processes</strong> remain <strong>resilient</strong>, <strong>tamper-proof</strong>, and <strong>audit-ready</strong> from development to live deployment.
            </p>
          </div>
        </div>

        <div className="bg-white border-l-4 border-[#58CC02] pl-4 sm:pl-6 pr-4 sm:pr-6 py-6 sm:py-8 rounded-r-lg shadow-sm hover:shadow-md transition-shadow duration-300">
          <div className="">
            <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-[#58CC02] to-[#4BAF02] rounded-lg flex items-center justify-center mb-4 sm:mb-6">
              <Users className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
            </div>
            <h3 className="text-xl sm:text-2xl font-bold text-[#010D2C] mb-3 sm:mb-4">
              Safety-Aware Offensive Testing
            </h3>
            <p className="text-[#6B7280] text-base sm:text-lg leading-relaxed">
              Our <strong>red team</strong> simulates real-world attacks unique to safety platforms: <strong>spoofed site check-ins</strong>, <strong>incident log manipulation</strong>, <strong>role escalation</strong> in audit workflows, and <strong>backend tampering</strong>. We uncover vulnerabilities that could compromise <strong>trust</strong>, delay <strong>emergency response</strong>, or breach <strong>contractor accountability</strong>-before they become active threats.
            </p>
          </div>
        </div>

        <div className="bg-white border-l-4 border-[#58CC02] pl-4 sm:pl-6 pr-4 sm:pr-6 py-6 sm:py-8 rounded-r-lg shadow-sm hover:shadow-md transition-shadow duration-300 md:col-span-2 xl:col-span-1">
          <div className="">
            <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-[#58CC02] to-[#4BAF02] rounded-lg flex items-center justify-center mb-4 sm:mb-6">
              <Target className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
            </div>
            <h3 className="text-xl sm:text-2xl font-bold text-[#010D2C] mb-3 sm:mb-4">
              Compliance-Aligned Security (Globally)
            </h3>
            <p className="text-[#6B7280] text-base sm:text-lg leading-relaxed">
              We align with <strong>ISO 45001</strong>, <strong>OSHA</strong>, and regional safety frameworks to ensure your digital systems meet the same high standards as your physical operations. Our penetration tests help validate <strong>secure contractor access</strong>, <strong>audit log integrity</strong>, and <strong>uptime of critical workflows</strong>-essential for maintaining <strong>business continuity</strong> and passing <strong>compliance checks</strong> globally.
            </p>
          </div>
        </div>
      </div>
 
      </div> 
    </>
  );
}