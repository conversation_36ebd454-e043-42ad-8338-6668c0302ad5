import React from "react";
import PartnersList from "@/app/Product/Penetration-Testing/components/PartnersList";
import BlogSection from "@/app/Home/components/Blogs";
import Landing from "./components/Landing";
import Security from "./components/Security";
import Testimonial from "../Testimonial";

export const metadata = {
  title: "Capture The Bug | EdTech & eLearning Platform Security",
  description:
    "Safeguard online learning systems, student data, and academic operations with Capture The Bug. Specialized penetration testing for educational institutions and EdTech firms.",
  keywords:
    "edtech security testing, eLearning cybersecurity, student data protection, LMS vulnerability assessment, academic continuity, education penetration testing, online learning platform security, Capture The Bug",
  robots: "index, follow",
  icons: {
    icon: "/favicon.ico",
  },
  openGraph: {
    title: "Capture The Bug | EdTech & eLearning Platform Security",
    type: "website",
    url: "https://capturethebug.xyz/Industries/Education&EdTech",
    description:
      "Capture The Bug delivers security testing tailored to EdTech platforms and learning institutions-protecting students, staff, and critical educational systems.",
    images: "https://ibb.co/V0dFcCtC", 
  },
  twitter: {
    card: "summary_large_image",
    title: "Capture The Bug | EdTech & eLearning Platform Security",
    description:
      "Protect learning platforms and academic data with offensive security testing. Capture The Bug helps EdTech firms prevent cyber threats and ensure academic continuity.",
    images: "https://ibb.co/V0dFcCtC",
  },
};


export default function Edtech() {
  return (
    <>
      <Landing />
      <Security />
      <PartnersList />
      <Testimonial
        company="ImmerseMe"
        logo="/images/immerseme_logo.jpeg"
        quote="Working with Capture The Bug made our first pentest feel effortless. After reviewing multiple vendors, it was clear their approach stood out-fast onboarding, clear communication, and a platform that just made sense for our dev team.
Throughout the test, we had full visibility into progress and could easily collaborate on findings using built-in feedback tools. The bug reports were clear, actionable, and easy to discuss with our internal team.
The experience felt less like working with a vendor and more like adding a true extension to our engineering workflow. We'll definitely be partnering with them again. "
        author="Tomas Brown"
        position="Software Engineer"
        logoSize={{ width: 140, height: 100 }}
        logoStyle={{ marginLeft: -20 }}
      />
      <BlogSection />
    </>
  );
}