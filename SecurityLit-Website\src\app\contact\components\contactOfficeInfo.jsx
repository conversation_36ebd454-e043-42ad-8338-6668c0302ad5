"use client";

import React from "react";
import { MapPin, Phone, Mail, Clock, Globe, Building, Users, Award, Shield, Target } from "lucide-react";
import { LinkButton, OutlinedLinkButton } from "../../common/buttons/BrandButtons";
import EmailCallCTA from "../../common/components/EmailCallCTA";

export default function ContactOfficeInfo() {
  const officeDetails = {
    address: {
      street: "5th Floor, DLF Two Horizon Centre",
      city: "DLF Phase 5, Gurugram",
      country: "India"
    },
    contact: {
      phone: "+91 8527 800769",
      email: "<EMAIL>",
      website: "www.securitylit.com"
    },
    team: {
      experts: "OSCP, CEH, EJPT, eWPTX V2 certified experts",
      experience: "10+ years average experience",
      specializations: "Penetration testing, Red teaming, vCISO"
    },
    certifications: [
      {
        name: "OSCP",
        fullName: "Offensive Security Certified Professional",
        logo: "/images/oscp-logo.png",
        description: "Advanced penetration testing certification"
      },
      {
        name: "EJPT",
        fullName: "eLearnSecurity Junior Penetration Tester",
        logo: "/images/ejpt-logo.png", 
        description: "Entry-level penetration testing certification"
      },
      {
        name: "CEH",
        fullName: "Certified Ethical Hacker",
        logo: "/images/ceh-logo.png",
        description: "Ethical hacking and security assessment"
      },
      {
        name: "eWPTX V2",
        fullName: "eLearnSecurity Web Application Penetration Tester",
        logo: "/images/ewptx-logo.png",
        description: "Advanced security management certification"
      }
    ]
  };

  return (
    <section className="py-20 bg-gradient-to-br from-gray-50 to-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-[var(--color-dark-blue)] mb-6">
            Reach Out to Us
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Get in touch with our cybersecurity experts. We're here to help secure your organization.
          </p>
        </div>

        <div className="grid grid-cols-1 xl:grid-cols-3 gap-8 lg:gap-12">
          {/* Contact Information */}
          <div className="bg-white rounded-3xl shadow-xl border border-gray-100 p-8 hover:shadow-2xl transition-all duration-300">
            <div className="text-center mb-6">
              <div className="w-16 h-16 bg-[var(--color-blue)]/10 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <Building className="w-8 h-8 text-[var(--color-blue)]" />
              </div>
              <h3 className="text-2xl font-bold text-[var(--color-dark-blue)]">Contact Details</h3>
            </div>
            
            <div className="space-y-6">
              {/* Email */}
              <div className="bg-gray-50 rounded-xl p-4 hover:bg-[var(--color-blue)]/5 transition-colors">
                <div className="flex items-center gap-4">
                  <div className="flex-shrink-0 w-10 h-10 bg-[var(--color-blue)]/10 rounded-xl flex items-center justify-center">
                    <Mail className="w-5 h-5 text-[var(--color-blue)]" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-1">Email Address</h4>
                    <a 
                      href={`mailto:${officeDetails.contact.email}`}
                      className="text-[var(--color-blue)] hover:text-[var(--color-blue-secondary)] transition-colors font-medium"
                    >
                      {officeDetails.contact.email}
                    </a>
                  </div>
                </div>
              </div>

              {/* Website */}
              <div className="bg-gray-50 rounded-xl p-4 hover:bg-[var(--color-blue)]/5 transition-colors">
                <div className="flex items-center gap-4">
                  <div className="flex-shrink-0 w-10 h-10 bg-[var(--color-blue)]/10 rounded-xl flex items-center justify-center">
                    <Globe className="w-5 h-5 text-[var(--color-blue)]" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-1">Website</h4>
                    <a 
                      href={`https://${officeDetails.contact.website}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-[var(--color-blue)] hover:text-[var(--color-blue-secondary)] transition-colors font-medium"
                    >
                      {officeDetails.contact.website}
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Team Information */}
          <div className="bg-white rounded-3xl shadow-xl border border-gray-100 p-8 hover:shadow-2xl transition-all duration-300">
            <div className="text-center mb-6">
              <div className="w-16 h-16 bg-[var(--color-blue)]/10 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <Users className="w-8 h-8 text-[var(--color-blue)]" />
              </div>
              <h3 className="text-2xl font-bold text-[var(--color-dark-blue)]">Our Expert Team</h3>
            </div>
            
            <div className="space-y-6">
              <div className="bg-gray-50 rounded-xl p-4 hover:bg-[var(--color-blue)]/5 transition-colors">
                <div className="flex items-start gap-4">
                  <div className="flex-shrink-0 w-10 h-10 bg-[var(--color-blue)]/10 rounded-xl flex items-center justify-center">
                    <Shield className="w-5 h-5 text-[var(--color-blue)]" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-2">Certified Professionals</h4>
                    <p className="text-gray-600 text-sm leading-relaxed">{officeDetails.team.experts}</p>
                  </div>
                </div>
              </div>
              
              <div className="bg-gray-50 rounded-xl p-4 hover:bg-[var(--color-blue)]/5 transition-colors">
                <div className="flex items-start gap-4">
                  <div className="flex-shrink-0 w-10 h-10 bg-[var(--color-blue)]/10 rounded-xl flex items-center justify-center">
                    <Target className="w-5 h-5 text-[var(--color-blue)]" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-2">Experience</h4>
                    <p className="text-gray-600 text-sm leading-relaxed">{officeDetails.team.experience}</p>
                  </div>
                </div>
              </div>
              
              <div className="bg-gray-50 rounded-xl p-4 hover:bg-[var(--color-blue)]/5 transition-colors">
                <div className="flex items-start gap-4">
                  <div className="flex-shrink-0 w-10 h-10 bg-[var(--color-blue)]/10 rounded-xl flex items-center justify-center">
                    <Award className="w-5 h-5 text-[var(--color-blue)]" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-2">Specializations</h4>
                    <p className="text-gray-600 text-sm leading-relaxed">{officeDetails.team.specializations}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Certifications */}
          <div className="bg-white rounded-3xl shadow-xl border border-gray-100 p-8 hover:shadow-2xl transition-all duration-300">
            <div className="text-center mb-6">
              <div className="w-16 h-16 bg-[var(--color-blue)]/10 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <Award className="w-8 h-8 text-[var(--color-blue)]" />
              </div>
              <h3 className="text-2xl font-bold text-[var(--color-dark-blue)]">Certifications & Credentials</h3>
            </div>
            
            <div className="space-y-4">
              {officeDetails.certifications.map((cert, index) => (
                <div key={index} className="bg-gray-50 rounded-xl p-4 hover:bg-[var(--color-blue)]/5 transition-colors">
                  <div className="flex items-center gap-4">
                    <div className="flex-shrink-0 w-12 h-12 bg-white rounded-xl flex items-center justify-center shadow-sm">
                      <img 
                        src={cert.logo} 
                        alt={`${cert.name} logo`}
                        className="w-8 h-8 object-contain"
                      />
                    </div>
                    <div className="flex-1 min-w-0">
                      <h4 className="font-semibold text-gray-900 text-sm mb-1">{cert.name}</h4>
                      <p className="text-gray-600 text-xs leading-tight">{cert.description}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Call to Action */}
        <div className="mt-16">
          <EmailCallCTA />
        </div>
      </div>
    </section>
  );
} 