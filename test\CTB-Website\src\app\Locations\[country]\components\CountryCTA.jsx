'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { ArrowRight, Phone, Mail, Calendar } from 'lucide-react';
import Button from '@/app/common/buttons/Button';
import { designSystem, getSectionWrapper, getButtonStyles } from '../styles/designSystem';

const CountryCTA = ({ country }) => {
  const countryData = {
    nz: {
      title: "New Zealand Penetration Testing Services",
      subtitle: "Join 100+ Kiwi organizations that trust our cybersecurity expertise",
      description: "Partner with New Zealand's leading penetration testing company for comprehensive cybersecurity protection tailored to local regulations and threats.",
      primaryCTA: "Get Free Security Assessment",
      secondaryCTA: "Schedule a Consultation"
    },
    au: {
      title: "Secure Your Australian Business Against Modern Cyber Threats",
      subtitle: "Trusted by leading Australian enterprises and government agencies",
      description: "Work with a trusted Australian-based team to strengthen your cybersecurity posture. Our penetration testing services are aligned with ACSC guidelines, tailored for tech-forward startups and scaling enterprises.",
      primaryCTA: "Start Free Assessment",
      secondaryCTA: "Book a Demo"
    },
    us: {
      title: "United States Penetration Testing Services",
      subtitle: "Securing America's leading enterprises since 2015",
      description: "Partner with our expert security team for comprehensive penetration testing services aligned with US compliance frameworks and industry standards.",
      primaryCTA: "Get Instant Quote",
      secondaryCTA: "Talk to an Expert"
    }
  };

  const data = countryData[country.toLowerCase()];

  return (
    <section className={getSectionWrapper('white')}>
      <div className="max-w-7xl mx-auto">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          whileInView={{ opacity: 1, scale: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="relative"
        >
          {/* Main CTA Card */}
          <div className={`${designSystem.container.gradient} rounded-3xl shadow-2xl overflow-hidden relative`}>
            {/* Enhanced Background Pattern */}
            <div className="absolute inset-0">
              <div className="absolute top-0 right-0 w-96 h-96 bg-white/5 rounded-full -translate-x-32 -translate-y-32 blur-3xl"></div>
              <div className="absolute bottom-0 left-0 w-80 h-80 bg-white/5 rounded-full translate-x-20 translate-y-20 blur-2xl"></div>
              <div className="absolute top-1/2 left-1/2 w-64 h-64 bg-white/3 rounded-full -translate-x-32 -translate-y-32"></div>
            </div>

            {/* Content */}
            <div className="relative z-10 px-8 py-16 lg:px-16 lg:py-20">
              <div className="max-w-4xl mx-auto text-center">


                {/* Main Heading */}
                <motion.h2
                  className="text-4xl md:text-5xl lg:text-6xl font-bold mb-4 leading-tight"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.8, delay: 0.3 }}
                >
                  {data.title}
                </motion.h2>

                {/* Subtitle */}
                <motion.p
                  className="text-xl md:text-2xl text-white/90 mb-6 font-medium"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.8, delay: 0.4 }}
                >
                  {data.subtitle}
                </motion.p>

                {/* Description */}
                <motion.p
                  className="text-lg text-white/80 mb-10 leading-relaxed max-w-3xl mx-auto"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.8, delay: 0.5 }}
                >
                  {data.description}
                </motion.p>



                {/* CTA Buttons */}
                <motion.div
                  className="flex flex-col sm:flex-row gap-6 justify-center"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.8, delay: 0.6 }}
                >
                  <Button
                    href="/Request-Demo"
                    variant="success"
                    size="lg"
                    rightIcon={<ArrowRight className="w-5 h-5" />}
                  >
                    {data.primaryCTA}
                  </Button>
                  <Button
                    href="/Company/Contact-Us"
                    variant="primary"
                    size="lg"
                    rightIcon={<Calendar className="w-5 h-5" />}
                  >
                    {data.secondaryCTA}
                  </Button>
                </motion.div>

                {/* Contact Info */}
                <motion.div
                  className="mt-12 pt-8 border-t border-white/20"
                  initial={{ opacity: 0 }}
                  whileInView={{ opacity: 1 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.8, delay: 0.7 }}
                >
                  <p className="text-white/70 text-sm mb-4">Need immediate assistance?</p>
                  <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                    <a href="tel:+1234567890" className="flex items-center gap-2 text-white hover:text-white/80 transition-colors duration-300">
                      <Phone className="w-4 h-4" />
                      <span className="text-sm font-medium">Call us now</span>
                    </a>
                    <div className="hidden sm:block w-px h-4 bg-white/30"></div>
                    <a href="mailto:<EMAIL>" className="flex items-center gap-2 text-white hover:text-white/80 transition-colors duration-300">
                      <Mail className="w-4 h-4" />
                      <span className="text-sm font-medium">Email us</span>
                    </a>
                  </div>
                </motion.div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default CountryCTA; 