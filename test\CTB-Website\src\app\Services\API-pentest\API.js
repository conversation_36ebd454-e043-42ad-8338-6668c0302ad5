
'use client';
import React from 'react'
import LandingSection from './components/LandingSection'
import Integration from './components/Integrations'
import FAQ from './components/FAQSection'
import Blogs from '@/app/Home/components/Blogs';
import PentestingFeatures from './components/Features';
import PartnersList from '@/app/Home/components/PartnersList';
import { motion } from 'framer-motion';
import BreadcrumbNavigation from '@/app/common/components/BreadcrumbNavigation';
import { createServiceBreadcrumbs } from '@/app/common/hooks/useBreadcrumbs';


export default function API() {
  const breadcrumbs = createServiceBreadcrumbs('API Penetration Testing', 'API-pentest');

  return (
    <div className="relative">
       <title>Capture The Bug | API  </title>

       {/* Breadcrumb Navigation - positioned absolutely at the top */}
       <div className="absolute top-0 left-0 right-0 pt-2 sm:pt-2 md:pt-5 xl:pt-14 z-10">
         <div className="max-w-7xl px-2 sm:px-2 md:px-16">
           <BreadcrumbNavigation items={breadcrumbs} />
         </div>
       </div>

       <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.6, delay: 0.1 }}
      >
      <LandingSection/>
      </motion.div> 

       <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.5 }}
            >
      <PartnersList/>
      </motion.div>

      <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
      <Integration/>
      </motion.div>

       <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
      <PentestingFeatures/>
      </motion.div>

      <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
      <FAQ/> 
      </motion.div>

       <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
      <Blogs/>
      </motion.div>
      </motion.div>
    </div>
  )
}
