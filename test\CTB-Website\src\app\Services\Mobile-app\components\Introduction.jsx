import React from "react";

export default function Introduction() {
  return (
    <div>
      <div className="Container flex flex-col gap-4 md:p-12 p-8 bg-gray-50 md:py-20">
      <h2 className="text-lg font-semibold text-gray-600">Comprehensive Pentest
      </h2>
        <div className="Title md:text-3xl text-2xl bg-gradient-to-r from-[#062575] via-[#0B45DB] to-[#0B45DB] bg-clip-text text-transparent font-bold">
        Keep Your Assets Protected with Expert Network Penetration Testing
        </div>
        <div className="text-slate-700 md:text-lg">
        Say goodbye to slow, outdated pen testing. Capture The Bug offers an agile, streamlined approach to network security. Test as frequently as needed with our on-demand service, ensuring your network stays protected, compliant, and audit-ready-all at a cost that aligns with your budget.
        </div>
      </div>
    </div>
  );
}
