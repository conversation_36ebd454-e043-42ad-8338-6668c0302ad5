{"version": 3, "file": "typeGuards.js", "sourceRoot": "", "sources": ["../../../src/util/typeGuards.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,MAAM,UAAU,oBAAoB,CAAC,CAAU;IAC7C,OAAO,OAAO,CAAC,CAAC,IAAI,OAAQ,CAA2B,CAAC,MAAM,CAAC,KAAK,UAAU,CAAC,CAAC;AAClF,CAAC;AAED,MAAM,UAAU,mBAAmB,CAAC,CAAU;IAC5C,OAAO,OAAO,CACZ,CAAC;QACC,OAAQ,CAAoB,CAAC,SAAS,KAAK,UAAU;QACrD,OAAQ,CAAoB,CAAC,GAAG,KAAK,UAAU,CAClD,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,YAAY,CAC1B,IAAa;IAQb,OAAO,CACL,IAAI,KAAK,SAAS;QAClB,CAAC,IAAI,YAAY,UAAU;YACzB,gBAAgB,CAAC,IAAI,CAAC;YACtB,OAAO,IAAI,KAAK,UAAU;YAC1B,IAAI,YAAY,IAAI,CAAC,CACxB,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,gBAAgB,CAAC,CAAU;IACzC,OAAO,oBAAoB,CAAC,CAAC,CAAC,IAAI,mBAAmB,CAAC,CAAC,CAAC,CAAC;AAC3D,CAAC;AAED,MAAM,UAAU,MAAM,CAAC,CAAU;IAC/B,OAAO,OAAQ,CAAU,CAAC,MAAM,KAAK,UAAU,CAAC;AAClD,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nexport function isNodeReadableStream(x: unknown): x is NodeJS.ReadableStream {\n  return Boolean(x && typeof (x as NodeJS.ReadableStream)[\"pipe\"] === \"function\");\n}\n\nexport function isWebReadableStream(x: unknown): x is ReadableStream {\n  return Boolean(\n    x &&\n      typeof (x as ReadableStream).getReader === \"function\" &&\n      typeof (x as ReadableStream).tee === \"function\",\n  );\n}\n\nexport function isBinaryBody(\n  body: unknown,\n): body is\n  | Uint8Array\n  | NodeJS.ReadableStream\n  | ReadableStream<Uint8Array>\n  | (() => NodeJS.ReadableStream)\n  | (() => ReadableStream<Uint8Array>)\n  | Blob {\n  return (\n    body !== undefined &&\n    (body instanceof Uint8Array ||\n      isReadableStream(body) ||\n      typeof body === \"function\" ||\n      body instanceof Blob)\n  );\n}\n\nexport function isReadableStream(x: unknown): x is ReadableStream | NodeJS.ReadableStream {\n  return isNodeReadableStream(x) || isWebReadableStream(x);\n}\n\nexport function isBlob(x: unknown): x is Blob {\n  return typeof (x as Blob).stream === \"function\";\n}\n"]}