"use client";

import React, { useState, useEffect } from "react";
import BreadcrumbNavigation from "../../common/components/BreadcrumbNavigation";

export default function PrivacyPolicy() {
  const [activeSection, setActiveSection] = useState("Introduction");

  const breadcrumbItems = [
    { name: "Home", url: "/", iconKey: "home" },
    { name: "Privacy Policy", url: "/privacy", current: true, iconKey: "company" }
  ];

  const sections = [
    "Introduction",
    "Changes to this Policy",
    "Collection of Personal Information",
    "Use of Personal Information",
    "Disclosing your Personal Information",
    "Protecting your Personal Information",
    "Accessing and Correcting your Personal Information",
    "Destruction of your information",
    "Third party cookies and other technologies"
  ];

  // This offset should match the scroll-margin-top utility (e.g., scroll-mt-32)
  // to account for your fixed navbar. 128px = 8rem = pt-32/scroll-mt-32
  const SCROLL_OFFSET = 128; 

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setActiveSection(entry.target.getAttribute("data-section"));
          }
        });
      },
      // rootMargin defines a "trigger line" at the top of the viewport
      { rootMargin: `-${SCROLL_OFFSET}px 0px -80% 0px`, threshold: 0 }
    );

    sections.forEach((section) => {
      const element = document.getElementById(section.toLowerCase().replace(/\s+/g, '-'));
      if (element) {
        element.setAttribute('data-section', section);
        observer.observe(element);
      }
    });

    return () => observer.disconnect();
  }, [sections]);

  const handleTocClick = (e, section) => {
    e.preventDefault();
    setActiveSection(section.name); // Instantly update active state

    const element = document.getElementById(section.id);
    if (element) {
      const elementPosition = element.getBoundingClientRect().top;
      const offsetPosition = elementPosition + window.pageYOffset - SCROLL_OFFSET;

      window.scrollTo({
        top: offsetPosition,
        behavior: "smooth"
      });
    }
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section with Dark Background */}
      <section className="bg-[var(--color-dark-blue)] py-20 lg:py-28">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl lg:text-5xl font-bold text-white mb-4">
            Privacy Policy
          </h1>
          <p className="text-lg lg:text-xl text-white/80 max-w-2xl mx-auto">
            How we collect, use, and protect your personal information.
          </p>
        </div>
      </section>

      {/* Content Section */}
      <section className="py-16 lg:py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mb-12">
            <BreadcrumbNavigation items={breadcrumbItems} />
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8 lg:gap-12">
            
            {/* Left Content */}
            <div className="lg:col-span-3 prose prose-lg max-w-none">
              
              <div id="introduction" className="mb-12 scroll-mt-32">
                <h2 className="text-2xl font-bold text-[var(--color-dark-blue)] mb-6">Introduction</h2>
                <p>Security Lit Ltd. complies with the New Zealand Privacy Act 1993 when dealing with personal information. This policy sets out how we will collect, use, disclose and protect your personal information. This policy does not limit or exclude any of your rights under the Act. If you wish to seek further information on the Act, see <a href="https://www.privacy.org.nz" className="text-[var(--color-blue)] hover:underline" target="_blank" rel="noopener noreferrer">www.privacy.org.nz</a>.</p>
              </div>

              <div id="changes-to-this-policy" className="mb-12 scroll-mt-32">
                <h2 className="text-2xl font-bold text-[var(--color-dark-blue)] mb-6">Changes to this Policy</h2>
                <p>We reserve the right to make updates and changes this policy from time to time. If we do revise this privacy policy, we will notify you either by making site announcements that are visible the next time you visit the website or we will send an email notification to you.</p>
              </div>
              
              <div id="collection-of-personal-information" className="mb-12 scroll-mt-32">
                <h2 className="text-2xl font-bold text-[var(--color-dark-blue)] mb-6">Collection of Personal Information</h2>
                <p>We may collect personal information directly or indirectly through your use of this website and its services or functions, through any contact with us (e.g. telephone call or email), or when you use our services. We may also collect it from third parties where you have authorised this or the information is publicly available.</p>
              </div>

              <div id="use-of-personal-information" className="mb-12 scroll-mt-32">
                <h2 className="text-2xl font-bold text-[var(--color-dark-blue)] mb-6">Use of Personal Information</h2>
                <p>Any personal information we receive will be used primarily to respond to inquiries, to sell, supply and deliver services which you order, to provide you with news and information about us and products, services and activities that we believe you may be interested in, to respond to complaints, to improve this website and our products and services and for such other purposes as may be disclosed to you at the time the personal information is collected or as required by law.</p>
              </div>

              <div id="disclosing-your-personal-information" className="mb-12 scroll-mt-32">
                <h2 className="text-2xl font-bold text-[var(--color-dark-blue)] mb-6">Disclosing your Personal Information</h2>
                <p>We may disclose your personal information to:</p>
                <ul className="list-disc list-inside space-y-2 ml-4">
                  <li>Any business that supports our services and products, including any person that hosts or maintains any underlying IT system or data centre that we use to provide the website or other services and products.</li>
                  <li>A credit reference agency for the purpose of credit checking you.</li>
                  <li>Any person if required to do so by law.</li>
                  <li>Any other person authorised by you.</li>
                </ul>
              </div>

              <div id="protecting-your-personal-information" className="mb-12 scroll-mt-32">
                <h2 className="text-2xl font-bold text-[var(--color-dark-blue)] mb-6">Protecting your Personal Information</h2>
                <p>We will take all reasonable steps to ensure that the personal information we collect, use, or disclose is accurate, complete, up to date and stored in a secure environment protected from unauthorised access, modification or disclosure.</p>
              </div>

              <div id="accessing-and-correcting-your-personal-information" className="mb-12 scroll-mt-32">
                <h2 className="text-2xl font-bold text-[var(--color-dark-blue)] mb-6">Accessing and Correcting your Personal Information</h2>
                <p>You may access and request correction of personal information that we hold about you by contacting us. We will deal with requests for access to and correction of personal information as quickly as possible.</p>
              </div>

              <div id="destruction-of-your-information" className="mb-12 scroll-mt-32">
                <h2 className="text-2xl font-bold text-[var(--color-dark-blue)] mb-6">Destruction of your information</h2>
                <p>We take all reasonable steps to ensure your personal information is appropriately disposed of once it is no longer needed for the purpose for which it was collected.</p>
              </div>

              <div id="third-party-cookies-and-other-technologies" className="mb-12 scroll-mt-32">
                <h2 className="text-2xl font-bold text-[var(--color-dark-blue)] mb-6">Third party cookies and other technologies</h2>
                <p>We use third party cookies and other technologies for marketing and to gather website analytics. This includes:</p>
                <ul className="list-disc list-inside space-y-2 ml-4">
                  <li><strong>Remarketing and emails:</strong> we use third party cookies – to keep track of the services you are interested in.</li>
                  <li><strong>Impression reporting:</strong> we use web beacons to estimate the number of users that have viewed and clicked on our website. (as a result, we are able to gauge the success of a campaign).</li>
                </ul>
              </div>

              {/* Contact Information Box */}
              <div className="bg-gray-50 rounded-xl p-8 mt-16 not-prose">
                <h2 className="text-2xl font-bold text-[var(--color-dark-blue)] mb-6">Contact Us</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8 text-base">
                  <div>
                    <h3 className="text-lg font-semibold text-[var(--color-dark-blue)] mb-3">Our Address</h3>
                    <p><strong>New Zealand - Waikato, Hamilton</strong><br /><strong>India - 5th Floor, DLF Two Horizon Centre, DLF Phase 5, Gurugram</strong></p>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-[var(--color-dark-blue)] mb-3">Contact Information</h3>
                    <p><strong>Email:</strong> <a href="mailto:<EMAIL>" className="text-[var(--color-blue)] hover:underline"><EMAIL></a><br /><strong>New Zealand:</strong> +64 (03) 394 0821<br /><strong>India:</strong> +91 8527 800769</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Sidebar - Table of Contents */}
            <div className="lg:col-span-1">
              <div className="lg:sticky top-32 max-h-[calc(100vh-10rem)] overflow-y-auto bg-gray-50 rounded-xl shadow-sm p-4 lg:p-6">
                <h3 className="text-lg font-bold text-[var(--color-dark-blue)] mb-4">Table of Contents</h3>
                <nav className="space-y-1">
                  {sections.map((sectionName) => {
                    const sectionId = sectionName.toLowerCase().replace(/\s+/g, '-');
                    const isActive = activeSection === sectionName;
                    
                    return (
                      <a
                        key={sectionName}
                        href={`#${sectionId}`}
                        className={`block py-2 px-3 rounded-lg transition-all duration-200 text-sm font-medium ${
                          isActive
                            ? 'text-[var(--color-blue)] bg-[var(--color-blue)]/10'
                            : 'text-gray-600 hover:text-[var(--color-dark-blue)] hover:bg-gray-100'
                        }`}
                        onClick={(e) => handleTocClick(e, { name: sectionName, id: sectionId })}
                      >
                        {sectionName}
                      </a>
                    );
                  })}
                </nav>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
