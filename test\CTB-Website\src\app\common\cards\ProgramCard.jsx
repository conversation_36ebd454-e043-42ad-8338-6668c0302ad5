"use client";

import React from "react";
import Image from "next/image";
import DarkButton from "../buttons/DarkButton";

const ProgramCard = ({
  title,
  vulnerabilities,
  imageSrc,
  onSubmit,
  onFavorite,
  isFavorite,
}) => {
  return (
    <div className="bg-white rounded-lg shadow-md p-10 max-w-6xl mx-auto flex items-center border border-gray-200 relative gap-8 md:flex-row flex-col">
      <div className="absolute inset-0 border border-gray-300 rounded-lg pointer-events-none" />
      <div className="flex-grow relative z-10">
        <h2 className="md:text-3xl text-xl font-bold bg-gradient-to-r from-[#062575] via-[#0B45DB] to-[#0B45DB] bg-clip-text text-transparent mb-4">
          {title}
        </h2>
        <ul className="list-disc list-outside space-y-2 text-gray-500 pl-4 md:text-lg text-sm">
          {vulnerabilities.map((item, index) => (
            <li key={index}>{item}</li>
          ))}
        </ul>
        <div className="mt-6 relative">
          <DarkButton className="px-6 py-2 relative z-10">
            Submit Report
          </DarkButton>
        </div>
      </div>
      <div className="flex-shrink-0 ml-4 relative z-10 md:pl-8">
        <Image src={imageSrc} alt="Program Logo" width={200} height={100} />
      </div>
    </div>
  );
};

export default ProgramCard;
