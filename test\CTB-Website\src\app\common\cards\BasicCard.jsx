import PropTypes from 'prop-types';
import Image from 'next/image';

const BasicCard = ({ image, title, subtitle, imageWidth, imageHeight }) => {
  return (
    <div className="overflow-hidden flex flex-col w-full md:w-[27%] group">
      <Image 
        src={image} 
        alt={title} 
        width={imageWidth} 
        height={imageHeight} 
        className="object-cover object-center px-4" 
      />
      <div className="py-6">
        <h3 className="text-2xl font-semibold text-black group-hover:bg-gradient-to-r group-hover:from-[#062575] group-hover:via-[#0B45DB] group-hover:to-[#0B45DB] group-hover:bg-clip-text group-hover:text-transparent px-4">
          {title}
        </h3>
        <p className="mt-2 text-sm text-slate-500 px-4 font-normal pr-14">{subtitle}</p>
      </div>
    </div>
  );
};

BasicCard.propTypes = {
  image: PropTypes.string.isRequired,
  title: PropTypes.string.isRequired,
  subtitle: PropTypes.string.isRequired,
  imageWidth: PropTypes.number.isRequired,
  imageHeight: PropTypes.number.isRequired,
};

export default BasicCard;
