'use client';

import React from 'react';
import { Play } from 'lucide-react';
import Image from 'next/image';

export default function AutomationApplicationSecurity() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-indigo-900 relative overflow-hidden">
      
      {/* Subtle grid pattern overlay */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0" style={{
          backgroundImage: `
            linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)
          `,
          backgroundSize: '50px 50px'
        }}></div>
      </div>

      <div className="container mx-auto px-4 sm:px-6  xl:px-28 py-12 sm:py-16 lg:py-20 relative z-10">
        
        {/* Header */}
        <div className="text-center mb-12 sm:mb-16">
          <p className="text-ctb-green-50 text-xs sm:text-sm font-semibold tracking-widest uppercase mb-4 sm:mb-6">
            AUTOMATION FOR APPLICATION SECURITY
          </p>
          <h2 className="text-white text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-bold max-w-6xl mx-auto leading-tight px-2">
            Reduce vulnerability risk and response time with automated, intelligent remediation
          </h2>
        </div>

        <div className="grid lg:grid-cols-2 gap-8 sm:gap-12 lg:gap-8 items-center">
          
          {/* Left Side - Dashboard Image */}
          <div className="flex justify-center lg:justify-center order-2 lg:order-1 w-full">
            <div className="relative w-full">
              <Image
                src="/images/Remediation2.svg"
                alt="Application Security Dashboard"
                width={850}
                height={200}
                className="rounded-lg shadow-2xl w-full"
              />
            </div>
          </div>

          {/* Right Side - Feature List */}
          <div className="space-y-8 sm:space-y-10 lg:space-y-20 order-1 lg:order-2 w-full">
            
            {/* Feature 1 */}
            <div className="flex items-start space-x-3 sm:space-x-4">
              <div className="flex-shrink-0 mt-1 sm:mt-1.5">
                <div className="w-0 h-0 border-l-[5px] sm:border-l-[6px] border-l-transparent border-r-[5px] sm:border-r-[6px] border-r-transparent border-b-[8px] sm:border-b-[10px] border-b-lime-400 rotate-90"></div>
              </div>
              <div>
                <p className="text-white text-base sm:text-lg lg:text-xl leading-relaxed">
                  <span className="font-semibold">Centralized Vulnerability Detection</span> - Get real-time visibility into high-risk network and application vulnerabilities across your environments - act on critical issues before attackers do.
                </p>
              </div>
            </div>

            {/* Feature 2 */}
            <div className="flex items-start space-x-3 sm:space-x-4">
              <div className="flex-shrink-0 mt-1 sm:mt-1.5">
                <div className="w-0 h-0 border-l-[5px] sm:border-l-[6px] border-l-transparent border-r-[5px] sm:border-r-[6px] border-r-transparent border-b-[8px] sm:border-b-[10px] border-b-lime-400 rotate-90"></div>
              </div>
              <div>
                <p className="text-white text-base sm:text-lg lg:text-xl leading-relaxed">
                  <span className="font-semibold">Automated Patch Orchestration</span> - Trigger patch workflows based on severity, asset type, or custom remediation SLAs - streamline how your team responds to vulnerabilities.
                </p>
              </div>
            </div>

            {/* Feature 3 */}
            <div className="flex items-start space-x-3 sm:space-x-4">
              <div className="flex-shrink-0 mt-1 sm:mt-1.5">
                <div className="w-0 h-0 border-l-[5px] sm:border-l-[6px] border-l-transparent border-r-[5px] sm:border-r-[6px] border-r-transparent border-b-[8px] sm:border-b-[10px] border-b-lime-400 rotate-90"></div>
              </div>
              <div>
                <p className="text-white text-base sm:text-lg lg:text-xl leading-relaxed">
                  <span className="font-semibold">Instant Ticketing & Alert Routing</span> - Automatically create and assign tickets when exploitable bugs are reported - integrated with your tools like Jira, Slack, and email.
                </p>
              </div>
            </div>

          </div>
        </div>
      </div>
    </div>
  );
}