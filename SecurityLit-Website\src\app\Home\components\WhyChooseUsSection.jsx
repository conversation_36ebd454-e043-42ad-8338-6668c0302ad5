"use client";

import React, { useState, useEffect } from "react";
import { Shield, Clock, AlertCircle, Award, Users, Lock, Headphones } from "lucide-react";

// Counter component for animated numbers
const Counter = ({ end, duration = 2000, suffix = "" }) => {
  const [count, setCount] = useState(0);

  useEffect(() => {
    let startTime = null;
    const startValue = 0;
    const endValue = end;

    const animate = (currentTime) => {
      if (!startTime) startTime = currentTime;
      const progress = Math.min((currentTime - startTime) / duration, 1);
      
      const currentCount = Math.floor(startValue + (endValue - startValue) * progress);
      setCount(currentCount);

      if (progress < 1) {
        requestAnimationFrame(animate);
      }
    };

    requestAnimationFrame(animate);
  }, [end, duration]);

  return <span>{count}{suffix}</span>;
};

export default function TrustedByLeadersSection() {
  const trustMetrics = [
    { 
      metric: 472, 
      label: "Companies Protected", 
      icon: Shield, 
      suffix: "+",
      color: "bg-blue-500"
    },
    { 
      metric: 94.3, 
      label: "Uptime Guarantee", 
      icon: Clock, 
      suffix: "",
      color: "bg-blue-500"
    },
    { 
      metric: 0, 
      label: "Security Breaches", 
      icon: AlertCircle, 
      suffix: "+",
      color: "bg-blue-500"
    },
    { 
      metric: 14.2, 
      label: "Years Experience", 
      icon: Award, 
      suffix: "",
      color: "bg-blue-500"
    }
  ];

  const serviceCards = [
    {
      icon: Shield,
      title: "Industry Compliance",
      description: "ISO 27001, SOC 2, GDPR, and industry-specific compliance frameworks covered",
      iconBg: "bg-yellow-500"
    },
    {
      icon: Lock,
      title: "Advanced Technology", 
      description: "AI-powered threat detection and automated response systems",
      iconBg: "bg-yellow-500"
    },
    {
      icon: Headphones,
      title: "Dedicated Support",
      description: "Personal security consultant assigned to your account",
      iconBg: "bg-yellow-500"
    }
  ];

  return (
    <section className="py-24 bg-slate-800 text-white">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 ">
        {/* Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold mb-4">Trusted by Industry Leaders</h2>
          <p className="text-slate-300 max-w-2xl mx-auto">
            Our numbers speak for themselves - delivering exceptional cybersecurity results across industries
          </p>
        </div>

        {/* Trust Metrics Grid */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {trustMetrics.map((item, index) => {
            const IconComponent = item.icon;
            return (
              <div key={index} className="bg-white rounded-2xl p-6 text-center">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <IconComponent className="w-6 h-6 text-blue-600" />
                </div>
                <div className="text-3xl font-bold text-slate-800 mb-2">
                  {typeof item.metric === 'number' && item.metric !== 0 ? (
                    <Counter end={item.metric} suffix={item.suffix} />
                  ) : item.metric === 0 ? (
                    <span>0{item.suffix}</span>
                  ) : (
                    <span>{item.metric}{item.suffix}</span>
                  )}
                </div>
                <div className="text-slate-600 text-sm font-medium">
                  {item.label}
                </div>
              </div>
            );
          })}
        </div>

        {/* Service Cards */}
        <div className="grid lg:grid-cols-3 gap-6">
          {serviceCards.map((card, index) => {
            const IconComponent = card.icon;
            return (
              <div key={index} className="bg-[#1E40AF]/20 rounded-2xl p-6">
                <div className="w-12 h-12 bg-yellow-500 rounded-lg flex items-center justify-center mb-4">
                  <IconComponent className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-xl font-bold text-white mb-3">
                  {card.title}
                </h3>
                <p className="text-slate-300 leading-relaxed">
                  {card.description}
                </p>
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
}