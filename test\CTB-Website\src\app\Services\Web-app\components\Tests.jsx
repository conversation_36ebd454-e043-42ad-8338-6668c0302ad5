"use client";
import { useState } from "react";
import Button from "../../../common/buttons/Button";
import Image from "next/image";
import React from "react";
import Link from "next/link";

const navItems = [
  "For Developers",
  "For CXOs",
  "For Compliance",
];

const FeatureContent = React.memo(function FeatureContent({ title, image, bulletPoints }) {
  return (
    <div className="flex flex-col lg:flex-row items-start justify-between px-4 lg:px-20">
      <div className="w-full lg:w-1/2 lg:pr-10 text-left mb-4 lg:mb-0">
        <h2 className="text-2xl font-bold mb-4">
          {title}
        </h2>
        {/* <p className="mb-4 text-lg text-slate-600 leading-8">{description}</p> */}
        <ul className="list-disc pl-5 mb-4 text-slate-600 text-lg">
          {bulletPoints.map((point, index) => (
            <li key={index}>{point}</li>
          ))}
        </ul>
        <Button
          href="https://outlook.office.com/bookwithme/user/<EMAIL>?anonymous&ep=pcard"
          variant="primary"
          className="px-6 rounded-3xl mt-10"
        >
          Book a pentest
        </Button>
      </div>
      <div className="w-full lg:w-1/2">
        <Image src={image} alt={title} width={600} height={400} priority
          quality={80} />
      </div>
    </div>
  );
});

export default function Tests() {
  const [activeTab, setActiveTab] = useState("For Developers");
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => setIsMenuOpen(prev => !prev);

  const tabContent = {
    "For Developers": {
      title: "Empower your team to tackle vulnerabilities efficiently with clear, actionable steps and seamless collaboration.",
      image: "/images/forDevelopers.png",
      // description: "Our expert-driven manual pentests involve skilled cybersecurity professionals simulating real-world attacks on your systems and networks. Unlike automated tools, manual testing provides deeper analysis, uncovering complex vulnerabilities that scans might miss. This approach offers valuable insights to enhance your defences against real-world threats.",
      bulletPoints: [
        "Work effortlessly with your team, CXOs, and security experts through our intuitive dashboard.",
        "Access all critical details about every vulnerability in a single, easy-to-navigate platform.",
        "Comment and discuss issues directly where they’re listed, eliminating unnecessary calls and emails.",
        "Receive detailed, step-by-step guidance to resolve every vulnerability effectively.",
        "Easily reproduce and test issues with precise instructions provided.",
      ]
    },
    "For CXOs": {
      title: "Track progress with our CXO friendly dashboard and priortize the right fixes",
      image: "/images/forCxos-final.png",
      // description: "Our AI-powered patch assistance focuses on helping developers with remediation support. Our platform offers actionable guidance to address vulnerabilities and features real-time collaboration tools for direct interaction between pentesters and developers. This ensures that vulnerabilities are not only identified but rectified swiftly and effectively, enhancing overall cybersecurity resilience.",
      bulletPoints: [
        "Stay informed with a comprehensive overview of your company’s security posture in real-time.",
        "Quickly assess risks and prioritize actions based on clear, concise reporting.",
        "Engage with your security and development teams directly through the platform, ensuring alignment and efficiency."
      ]
    },
    "For Compliance": {
      title: "Get ISO, SOC2, GDPR, CIS, compliance ready without the hassle",
      image: "/images/Bug_Bounty.jpg",
      // description: "Our platform offers real-time tracking of both penetration tests and bug bounty programs, providing immediate alerts for any vulnerabilities detected. The intuitive dashboard delivers comprehensive analytics and detailed reports, empowering proactive and informed decision-making.",
      bulletPoints: [
        "Ensure your company meets regulatory requirements with up-to-date security assessments.",
        "Access detailed reports that simplify compliance checks and demonstrate your commitment to security.",
        "Keep your compliance status in check with continuous updates and alerts on critical vulnerabilities.",
      ]
    },
  };

  return (
    <div className="bg-gray-50">
      <div className="container py-16 flex flex-col items-center mx-auto text-center lg:text-center">

        {/* Mobile Dropdown Navbar */}
        <div className="lg:hidden w-full mb-8 relative px-10">
          <button
            onClick={toggleMenu}
            className="w-full py-2 px-4 text-left bg-white border border-gray-300 rounded-md shadow-sm"
          >
            {activeTab} ▼
          </button>
          {isMenuOpen && (
            <ul className="w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg z-10">
              {navItems.map((item) => (
                <li key={item}>
                  <button
                    className={`w-full py-2 px-4 text-left ${
                      activeTab === item
                        ? "bg-blue-100 text-blue-700"
                        : "text-gray-700 hover:bg-gray-100"
                    }`}
                    onClick={() => {
                      setActiveTab(item);
                      setIsMenuOpen(false);
                    }}
                  >
                    {item}
                  </button>
                </li>
              ))}
            </ul>
          )}
        </div>

        {/* Desktop Navigation */}
        <div className="hidden lg:flex mb-8 justify-center space-x-40 border-b border-gray-200">
          {navItems.map((item) => (
            <button
              key={item}
              className={`py-2 px-1 font-medium text-[23px] transition-colors duration-300 relative ${
                activeTab === item
                  ? "text-blue-700"
                  : "text-gray-500 hover:text-blue-700"
              }`}
              onClick={() => setActiveTab(item)}
            >
              {item}
              {activeTab === item && (
                <span className="absolute bottom-0 left-0 w-full h-0.5 bg-blue-700"></span>
              )}
            </button>
          ))}
        </div>

        <main className="w-full">
          <FeatureContent {...tabContent[activeTab]} />
        </main>
      </div>
    </div>
  );
}
