'use client';
import React from "react";
import Head from 'next/head';
import PartnersList from "@/app/Product/Penetration-Testing/components/PartnersList";
import BlogSection from "@/app/Home/components/Blogs";
import Landing from "./components/Landing";
import Security from "./components/Security";
import Testimonial from "../Testimonial";
import BreadcrumbNavigation from "@/app/common/components/BreadcrumbNavigation";
import { createIndustryBreadcrumbs } from "@/app/common/hooks/useBreadcrumbs";



const testimonial = {
  company: "PaySauce",
  logo: "/images/paysauce_logo.png",
  quote: "Capture The Bug helped us level up our security game quickly. In just two weeks, we surfaced more relevant, high-impact vulnerabilities than we ever got from our previous pentesting vendor. The difference was clear: always-on testing, real-time visibility, and the ability to manage our entire vulnerability lifecycle-assign, comment, retest-all within the platform. Their pentesters felt like an extension of our team, and the quality of reports made stakeholder communication effortless. For any listed company that needs continuous assurance and speed without compromising depth, Capture The Bug is the platform to trust.",
  author: "<PERSON>",
  position: "Chief Technology Officer "
};
  
export default function BFS() {
  const breadcrumbs = createIndustryBreadcrumbs('Banking & Financial Services', 'Banking&FinancialServices');

  return (
    <>
      <Head>
        <title>Capture The Bug | Fintech & Banking Cybersecurity Security</title>
        <meta name="description" content="Capture The Bug protects fintech and banking platforms from cyber threats. We secure financial APIs, mobile apps, and cloud infrastructure while ensuring regulatory compliance." />
        <meta name="keywords" content="fintech penetration testing, banking cybersecurity, financial API security, PCI DSS compliance, RBI mandates, BFSI cybersecurity, mobile app testing, Capture The Bug" />
        <meta name="robots" content="index, follow" />
        <link rel="icon" href="/favicon.ico" />

        {/* Open Graph */}
        <meta property="og:title" content="Capture The Bug | Fintech & Banking Cybersecurity Security" />
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://capturethebug.xyz/Industries/Banking&FinancialServices" />
        <meta property="og:description" content="Prevent breaches in banking and fintech platforms with Capture The Bug. Ensure GDPR, PCI-DSS, and RBI compliance through offensive security testing." />
        <meta property="og:image" content="https://ibb.co/ZRNHK4YQ" />

        {/* Twitter */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="Capture The Bug | Fintech & Banking Cybersecurity Security" />
        <meta name="twitter:description" content="Capture The Bug helps BFSI platforms uncover vulnerabilities in financial APIs, mobile apps, and cloud infra before attackers strike." />
        <meta name="twitter:image" content="https://ibb.co/ZRNHK4YQ" />
      </Head>

      <div className="relative">
        {/* Breadcrumb Navigation - positioned absolutely at the top */}
        <div className="absolute top-0 left-0 right-0 pt-2 sm:pt-2 md:pt-5 xl:pt-14 z-10">
          <div className="max-w-7xl px-2 sm:px-2 md:px-16">
            <BreadcrumbNavigation items={breadcrumbs} />
          </div>
        </div>

        <Landing/>
        <Security/>
        <PartnersList/>
        <Testimonial
          company={testimonial.company}
          logo={testimonial.logo}
          quote={testimonial.quote}
          author={testimonial.author}
          position={testimonial.position}
          logoSize={{ width: 140, height: 100 }}
          logoStyle={{ marginLeft: 10 }}
        />
        <BlogSection/>
      </div>
    </>
  );
}