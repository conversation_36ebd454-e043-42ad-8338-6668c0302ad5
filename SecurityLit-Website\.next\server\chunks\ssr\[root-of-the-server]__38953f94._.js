module.exports = {

"[project]/.next-internal/server/app/CyberSecTraining/page/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
}}),
"[project]/src/app/favicon.ico.mjs { IMAGE => \"[project]/src/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript, Next.js Server Component)": ((__turbopack_context__) => {

__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/favicon.ico.mjs { IMAGE => \"[project]/src/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript)"));
}),
"[project]/src/app/layout.js [app-rsc] (ecmascript, Next.js Server Component)": ((__turbopack_context__) => {

__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/layout.js [app-rsc] (ecmascript)"));
}),
"[project]/src/app/training/components/HeroBento.jsx [app-rsc] (client reference proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/src/app/training/components/HeroBento.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/app/training/components/HeroBento.jsx <module evaluation>", "default");
}),
"[project]/src/app/training/components/HeroBento.jsx [app-rsc] (client reference proxy)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/src/app/training/components/HeroBento.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/app/training/components/HeroBento.jsx", "default");
}),
"[project]/src/app/training/components/HeroBento.jsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$training$2f$components$2f$HeroBento$2e$jsx__$5b$app$2d$rsc$5d$__$28$client__reference__proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/app/training/components/HeroBento.jsx [app-rsc] (client reference proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$training$2f$components$2f$HeroBento$2e$jsx__$5b$app$2d$rsc$5d$__$28$client__reference__proxy$29$__ = __turbopack_context__.i("[project]/src/app/training/components/HeroBento.jsx [app-rsc] (client reference proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$training$2f$components$2f$HeroBento$2e$jsx__$5b$app$2d$rsc$5d$__$28$client__reference__proxy$29$__);
}),
"[project]/src/app/training/components/ProgramStructure.jsx [app-rsc] (client reference proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/src/app/training/components/ProgramStructure.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/app/training/components/ProgramStructure.jsx <module evaluation>", "default");
}),
"[project]/src/app/training/components/ProgramStructure.jsx [app-rsc] (client reference proxy)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/src/app/training/components/ProgramStructure.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/app/training/components/ProgramStructure.jsx", "default");
}),
"[project]/src/app/training/components/ProgramStructure.jsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$training$2f$components$2f$ProgramStructure$2e$jsx__$5b$app$2d$rsc$5d$__$28$client__reference__proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/app/training/components/ProgramStructure.jsx [app-rsc] (client reference proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$training$2f$components$2f$ProgramStructure$2e$jsx__$5b$app$2d$rsc$5d$__$28$client__reference__proxy$29$__ = __turbopack_context__.i("[project]/src/app/training/components/ProgramStructure.jsx [app-rsc] (client reference proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$training$2f$components$2f$ProgramStructure$2e$jsx__$5b$app$2d$rsc$5d$__$28$client__reference__proxy$29$__);
}),
"[project]/src/app/training/components/Testimonials.jsx [app-rsc] (client reference proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/src/app/training/components/Testimonials.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/app/training/components/Testimonials.jsx <module evaluation>", "default");
}),
"[project]/src/app/training/components/Testimonials.jsx [app-rsc] (client reference proxy)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/src/app/training/components/Testimonials.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/app/training/components/Testimonials.jsx", "default");
}),
"[project]/src/app/training/components/Testimonials.jsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$training$2f$components$2f$Testimonials$2e$jsx__$5b$app$2d$rsc$5d$__$28$client__reference__proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/app/training/components/Testimonials.jsx [app-rsc] (client reference proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$training$2f$components$2f$Testimonials$2e$jsx__$5b$app$2d$rsc$5d$__$28$client__reference__proxy$29$__ = __turbopack_context__.i("[project]/src/app/training/components/Testimonials.jsx [app-rsc] (client reference proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$training$2f$components$2f$Testimonials$2e$jsx__$5b$app$2d$rsc$5d$__$28$client__reference__proxy$29$__);
}),
"[project]/src/app/training/components/TrainingAccessForm.jsx [app-rsc] (client reference proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/src/app/training/components/TrainingAccessForm.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/app/training/components/TrainingAccessForm.jsx <module evaluation>", "default");
}),
"[project]/src/app/training/components/TrainingAccessForm.jsx [app-rsc] (client reference proxy)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/src/app/training/components/TrainingAccessForm.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/app/training/components/TrainingAccessForm.jsx", "default");
}),
"[project]/src/app/training/components/TrainingAccessForm.jsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$training$2f$components$2f$TrainingAccessForm$2e$jsx__$5b$app$2d$rsc$5d$__$28$client__reference__proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/app/training/components/TrainingAccessForm.jsx [app-rsc] (client reference proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$training$2f$components$2f$TrainingAccessForm$2e$jsx__$5b$app$2d$rsc$5d$__$28$client__reference__proxy$29$__ = __turbopack_context__.i("[project]/src/app/training/components/TrainingAccessForm.jsx [app-rsc] (client reference proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$training$2f$components$2f$TrainingAccessForm$2e$jsx__$5b$app$2d$rsc$5d$__$28$client__reference__proxy$29$__);
}),
"[project]/src/app/training/content/trainingContent.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Content configuration for different training pages
__turbopack_context__.s({
    "cyberSecTrainingContent": ()=>cyberSecTrainingContent,
    "cybersecurityTrainingContent": ()=>cybersecurityTrainingContent
});
const cybersecurityTrainingContent = {
    hero: {
        breadcrumbItems: [
            {
                name: "Home",
                url: "/",
                iconKey: "home",
                description: "Return to homepage"
            },
            {
                name: "Cybersecurity Training",
                url: "/CybersecurityTraining",
                current: true,
                iconKey: "graduation-cap",
                description: "Elite Security Training - Launch Your Cyber Security Career"
            }
        ],
        title: "Elite Security Training",
        subtitle: "Launch Your Cyber Security Career",
        tagline: "Free and Premium Pathways",
        description: "Dive into the world of penetration testing with our refined program. Designed for aspiring security professionals with complete pentesting skills and real-time live projects.",
        keyBenefits: [
            "Complete pentesting skills with real-time live projects",
            "Latest tools and topics in cybersecurity",
            "Free and premium pathways available"
        ],
        buttons: [
            {
                text: "Enroll Now",
                href: "#form",
                primary: true
            },
            {
                text: "Program Details",
                href: "/CyberSecTraining",
                primary: false
            }
        ],
        heroImage: "/images/p1s1.png"
    },
    curriculumHighlights: {
        sectionTitle: "Security Lit Presents",
        title: "Explore Our Comprehensive Curriculum",
        description: "Dive into essential areas such as web application security, network penetration testing, cloud security, API security, and ethical hacking fundamentals.",
        brochureLink: "/Brochure%20Security%20Training.pdf",
        highlights: [
            {
                title: "Conduct Advanced Security Assessments",
                description: "In the free tier, you'll gain the skills to perform comprehensive security assessments, vulnerability analyses, and penetration testing on various systems and networks, applying both theoretical knowledge and practical techniques learned during the training."
            },
            {
                title: "Join Elite Cybersecurity Teams",
                description: "As a premium member, you'll be prepared to join similar high-level security teams, armed with industry-relevant skills and insider knowledge."
            },
            {
                title: "Pursue Diverse Cybersecurity Careers",
                description: "Whether you choose the free or premium path, you'll explore various cybersecurity career options, understanding the roles and responsibilities in different organizations. This knowledge will help you make informed decisions about your career trajectory in the cybersecurity field."
            }
        ]
    },
    whoCanJoin: {
        title: "So Who Can Take Up This Training?",
        description: "Our program is designed to be accessible and beneficial for a wide range of learners, from beginners to experienced professionals. Whether you're looking to start your cybersecurity journey or take it to new heights, we've got you covered.",
        targetAudience: [
            {
                title: "Cybersecurity Enthusiasts",
                description: "Whether you're new to the field or have some experience, our program caters to learners of all levels. Start with the free tier to build a strong foundation, then upgrade to the premium tier for a more immersive, mentor-guided experience."
            },
            {
                title: "Information Security Professionals",
                description: "If you have been practicing cybersecurity topics, you can join the premium tier to take your skills to the next level through hands-on projects and industry-relevant training."
            },
            {
                title: "Career Switchers",
                description: "Looking to transition into the exciting world of cybersecurity? Our program provides the knowledge and practical experience you need to kickstart your career in this high-demand field."
            }
        ],
        bottomImage: "/images/TrainingHacker.png"
    },
    learningModules: {
        title: "Here's A Short Teaser Of What You Will Learn",
        modules: [
            {
                title: "Web Application Security",
                description: "Fortifying the digital frontline against cyber threats."
            },
            {
                title: "API & Network Security",
                description: "Safeguarding the backbone of modern interconnected systems."
            },
            {
                title: "Practical Skills Development",
                description: "Honing real-world cybersecurity expertise through hands-on learning."
            },
            {
                title: "Soft Skill & Professional Growth",
                description: "Cultivating the human element in technical cybersecurity roles."
            },
            {
                title: "Real World Environment Navigation",
                description: "Mastering the art of securing complex, live digital ecosystems."
            },
            {
                title: "Active Directory & Cloud Security",
                description: "Protecting the nerve centers of enterprise and cloud infrastructures."
            },
            {
                title: "Continuous Learning & Adaption",
                description: "Staying ahead in the ever-evolving cybersecurity landscape."
            },
            {
                title: "Report Writing Skills",
                description: "Crafting clear, concise, and impactful cybersecurity documentation for stakeholders at all levels."
            }
        ]
    },
    studentLogos: {
        title: "Our Trained Students Work At",
        companies: [
            {
                name: "AIA NZ",
                logo: "/images/aia_nz_logo.jpg",
                alt: "AIA New Zealand"
            },
            {
                name: "Data Torque",
                logo: "/images/data_torque_ltd_logo.jpg",
                alt: "Data Torque Ltd"
            }
        ]
    },
    premiumCTA: {
        title: "Premium Tier Benefits",
        description: "As a Premium member, you'll work directly with SecurityLit on live penetration testing projects. This hands-on experience will immerse you in real-time cybersecurity challenges, teaching you how professional pentests are conducted. You'll learn the intricacies of report writing, client communication, and industry best practices. Upon completion, you'll receive an experience letter, validating your practical skills and boosting your career prospects in the cybersecurity field.",
        benefits: [
            "Access to advance Lab Subscription (TryHackMe, HackTheBox)",
            "Personalized mentorship and guided learning",
            "3 months of hands-on experience with SecurityLit",
            "Professional report writing training",
            "Experience letter upon completion"
        ],
        upgradeTitle: "Upgrade to the Premium Tier",
        upgradeDescription: "Take your cybersecurity training to the next level with our premium tier. Unlock exclusive access to advanced labs, personalized mentorship, and hands-on projects that will transform your skills and career.",
        contactEmail: "mailto:<EMAIL>,<EMAIL>"
    }
};
const cyberSecTrainingContent = {
    hero: {
        breadcrumbItems: [
            {
                name: "Home",
                url: "/",
                iconKey: "home",
                description: "Return to homepage"
            },
            {
                name: "Cybersecurity Training",
                url: "/CybersecurityTraining",
                iconKey: "graduation-cap",
                description: "Elite Security Training"
            },
            {
                name: "Curriculum",
                url: "/CyberSecTraining",
                current: true,
                iconKey: "book-open",
                description: "Detailed Cybersecurity Training Curriculum"
            }
        ],
        sectionTitle: "Security Lit Presents",
        title: "Are you searching for training in Cyber Security field?",
        description: "We are among the few companies in India offering internships across different sectors of Cyber Security. Check out real-life Cyber Security projects, get awesome experience to kickstart your career in cyber security and totally change your life!",
        buttons: [
            {
                text: "Enroll Now",
                href: "/CybersecurityTraining#form",
                primary: true
            },
            {
                text: "Contact Us",
                href: "mailto:<EMAIL>,<EMAIL>",
                primary: false
            }
        ],
        heroImage: "/images/p2s1.png",
        secondaryImage: "/images/p2s2.png"
    },
    programStructure: {
        title: "Program Structure",
        description: "We provide a job-focused, hands-on curriculum designed to take participants from foundational to advanced cybersecurity skills across three comprehensive phases:",
        phases: [
            {
                phase: "Phase 1",
                title: "Web And API Penetration Testing",
                description: "Our program introduces participants to VAPT (Vulnerability Assessment and Penetration Testing) for mobile and web applications, focusing on identifying and mitigating security flaws. With the growing reliance on mobile apps and APIs, security is critical for organizations.",
                image: "/images/p2s3.png",
                benefits: [
                    "Assignments",
                    "Labs",
                    "Mentoring (Premium Only)",
                    "Hands-On Real Project (Premium Only)"
                ]
            },
            {
                phase: "Phase 2",
                title: "Network and Cloud Penetration Testing",
                description: "This phase focuses on network security, equipping participants with the skills to assess and secure organizational networks and endpoints. The emphasis is on identifying vulnerabilities in network infrastructure and developing robust defenses.",
                image: "/images/p2s4.png",
                benefits: [
                    "Assignments",
                    "Labs",
                    "Mentoring (Premium Only)",
                    "Hands-On Real Project (Premium Only)"
                ]
            },
            {
                phase: "Phase 3",
                title: "Cloud Security",
                description: "This phase introduces participants to essential cloud security practices, focusing on securing AWS environments. With more organizations moving to the cloud, understanding foundational cloud security principles is crucial for safeguarding cloud-based infrastructure.",
                image: "/images/p2s5.png",
                benefits: [
                    "Assignments",
                    "Labs",
                    "Mentoring (Premium Only)",
                    "Hands-On Real Project (Premium Only)"
                ]
            }
        ]
    },
    testimonials: {
        title: "Real Feedback from Professionals Who Completed Our Security Training",
        testimonials: [
            {
                name: "Keziah Achshah Guha",
                role: "Information Security Analyst at DataTorque Ltd",
                content: "SecurityLit transformed my perspective on IT security. Through identifying risks for organizations, I've grown in my role and feel more capable now. The supportive work environment and knowledgeable colleagues at SecurityLit have been invaluable.",
                rating: 5
            },
            {
                name: "Anindya Roy",
                role: "Associate Penetration Tester at SecurityLit",
                content: "The training boosted my expertise and confidence, helping me excel as a pentester. Hands-on projects deepened my skills in web security and advanced exploitation, which I now apply regularly, along with improved communication skills.",
                rating: 5
            },
            {
                name: "Krishna Dinkar Biradar",
                role: "Associate Penetration Tester at SecurityLit",
                content: "This training improved my approach to pentesting, enhancing my process, documentation, and teamwork. Collaborating on tests and following a structured process was far more effective than my previous ad-hoc methods in bug hunting.",
                rating: 5
            },
            {
                name: "Rini Sebastian",
                role: "Information Security Analyst at AIA NZ",
                content: "I believe I got the job thanks to the training, support, and feedback from Ankita and the SecurityLit team, which helped me excel in the interviews. Thank you for all the guidance that led me to this role.",
                rating: 5
            }
        ]
    },
    contactForm: {
        title: "Start Your Training Now!",
        subtitle: "Become part of the next generation of cybersecurity professionals with SecurityLit.",
        description: "SecurityLit offers an industry-aligned training program designed to equip you with the skills needed to succeed in today's cybersecurity landscape.",
        features: [
            "Industry-aligned curriculum",
            "Hands-on practical experience",
            "Expert mentorship and guidance",
            "Job-ready skills development"
        ]
    }
};
}),
"[project]/src/app/CyberSecTraining/page.jsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>CyberSecTrainingPage,
    "metadata": ()=>metadata
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$training$2f$components$2f$HeroBento$2e$jsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app/training/components/HeroBento.jsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$training$2f$components$2f$ProgramStructure$2e$jsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app/training/components/ProgramStructure.jsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$training$2f$components$2f$Testimonials$2e$jsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app/training/components/Testimonials.jsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$training$2f$components$2f$TrainingAccessForm$2e$jsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app/training/components/TrainingAccessForm.jsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$training$2f$content$2f$trainingContent$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app/training/content/trainingContent.js [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
const metadata = {
    title: "Cybersecurity Training Curriculum | Become Cyber Security Expert - SecurityLit",
    description: "Comprehensive cybersecurity training curriculum with 3 phases: Web & API Pentesting, Network & Cloud Security, and Cloud Security. Become job-ready in 6 months.",
    keywords: "cybersecurity curriculum, penetration testing course, web security training, network security, cloud security, cybersecurity expert training",
    robots: "index, follow",
    openGraph: {
        title: "Cybersecurity Training Curriculum | Become Cyber Security Expert - SecurityLit",
        description: "Comprehensive cybersecurity training curriculum with 3 phases covering web security, network pentesting, and cloud security.",
        url: "https://securitylit.com/CyberSecTraining",
        siteName: "SecurityLit",
        images: [
            {
                url: "/images/p2s1.png",
                width: 1200,
                height: 630,
                alt: "SecurityLit Cybersecurity Training Curriculum"
            }
        ],
        locale: "en_GB",
        type: "website"
    }
};
function CyberSecTrainingPage() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "min-h-screen bg-white",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$training$2f$components$2f$HeroBento$2e$jsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                content: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$training$2f$content$2f$trainingContent$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cyberSecTrainingContent"].hero
            }, void 0, false, {
                fileName: "[project]/src/app/CyberSecTraining/page.jsx",
                lineNumber: 35,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$training$2f$components$2f$ProgramStructure$2e$jsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                content: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$training$2f$content$2f$trainingContent$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cyberSecTrainingContent"].programStructure
            }, void 0, false, {
                fileName: "[project]/src/app/CyberSecTraining/page.jsx",
                lineNumber: 38,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$training$2f$components$2f$Testimonials$2e$jsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                content: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$training$2f$content$2f$trainingContent$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cyberSecTrainingContent"].testimonials
            }, void 0, false, {
                fileName: "[project]/src/app/CyberSecTraining/page.jsx",
                lineNumber: 41,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$training$2f$components$2f$TrainingAccessForm$2e$jsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                content: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f$training$2f$content$2f$trainingContent$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cyberSecTrainingContent"].contactForm
            }, void 0, false, {
                fileName: "[project]/src/app/CyberSecTraining/page.jsx",
                lineNumber: 44,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/CyberSecTraining/page.jsx",
        lineNumber: 33,
        columnNumber: 5
    }, this);
}
}),
"[project]/src/app/CyberSecTraining/page.jsx [app-rsc] (ecmascript, Next.js Server Component)": ((__turbopack_context__) => {

__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/CyberSecTraining/page.jsx [app-rsc] (ecmascript)"));
}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__38953f94._.js.map