import PrivacyPolicy from './components/privacyPolicy';

export const metadata = {
  title: "Privacy Policy | SecurityLit - Cybersecurity Services",
  description: "SecurityLit's privacy policy outlines how we collect, use, and protect your personal information. Learn about our data practices and your rights under the New Zealand Privacy Act 1993.",
  keywords: "privacy policy, SecurityLit, data protection, personal information, cybersecurity privacy, New Zealand Privacy Act, data security",
  robots: "index, follow",
  icons: {
    icon: '/favicon.ico',
  },
  openGraph: {
    title: "Privacy Policy | SecurityLit - Cybersecurity Services",
    type: "website",
    url: "https://securitylit.com/privacy",
    description: "SecurityLit's privacy policy outlines how we collect, use, and protect your personal information. Learn about our data practices and your rights under the New Zealand Privacy Act 1993.",
    images: "/seclit-logo-1.png",
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Privacy Policy | SecurityLit - Cybersecurity Services',
    description: 'SecurityLit\'s privacy policy outlines how we collect, use, and protect your personal information. Learn about our data practices and your rights under the New Zealand Privacy Act 1993.',
    images: "/seclit-logo-1.png",
  }
};

export default function PrivacyPage() {
  return (
    <div className="min-h-screen">
      <PrivacyPolicy />
    </div>
  );
} 