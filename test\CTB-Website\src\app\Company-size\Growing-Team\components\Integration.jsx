"use client";

import React from 'react';
import <PERSON> from "next/link";
import { Search, GitBranch, Bar<PERSON>hart3, <PERSON><PERSON>, ArrowRight } from 'lucide-react';
import { motion } from 'framer-motion';
import Button from "../../../common/buttons/Button";

const ToolsForGrowth = () => {
  const features = [
    {
      icon: Search,
      title: "Vulnerability Discovery",
      description: "Run continuous pentests that surface new risks as they appear-not once or twice a year. See critical, high, and low vulnerabilities clearly prioritized for fast remediation.",
      iconBg: "bg-[#F8F5F7]",
      iconColor: "text-ctb-green-50"
    },
    {
      icon: GitBranch,
      title: "Integrations",
      description: "CTB connects with your stack-GitHub, Jira, Slack-to keep security aligned with your sprint cycle. One-click integrations, no developer friction.",
      iconBg: "bg-[#F8F5F7]",
      iconColor: "text-ctb-green-50"
    },
    {
      icon: BarChart3,
      title: "Security Reports",
      description: "Instantly access audit-ready reports that map findings to frameworks like ISO 27001, SOC 2, and OWASP.",
      iconBg: "bg-[#F8F5F7]",
      iconColor: "text-ctb-green-50"
    },
    {
      icon: Wrench,
      title: "Developer-Centric Fixes",
      description: "We don't just flag issues-we guide your devs with replication steps, exploit paths, and fix instructions they can act on. Save time and ship secure.",
      iconBg: "bg-[#F8F5F7]",
      iconColor: "text-ctb-green-50"
    }
  ];

  return (
    <motion.div 
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
      className="bg-[#F8F5F7] p-8 sm:p-20 w-full m-0"
    >
      <div className="w-full md:px-12 lg:px-16">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <motion.div 
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.4, delay: 0.1 }}
            className="text-center mb-12 md:mb-16"
          >
            <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-secondary-blue leading-tight">
              Pentesting that keeps up with your roadmap
            </h1>
          </motion.div>

          {/* Features Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 lg:gap-8 mb-12 md:mb-16">
            {features.map((feature, index) => {
              const IconComponent = feature.icon;
              return (
                <motion.div 
                  key={index}
                  initial={{ y: 30, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ duration: 0.4, delay: 0.1 + (index * 0.05) }}
                  whileHover={{ 
                    y: -8, 
                    transition: { duration: 0.2 } 
                  }}
                  className="bg-white rounded-2xl p-6 lg:p-7 shadow-sm border border-gray-100 hover:shadow-lg transition-all duration-200 group cursor-pointer"
                >
                  <div className="flex flex-col items-center lg:items-start text-center lg:text-left h-full">
                    {/* Icon */}
                    <motion.div 
                      className={`w-14 h-14 ${feature.iconBg} rounded-xl flex items-center justify-center mb-5 group-hover:scale-110 transition-transform duration-200`}
                      whileHover={{ rotate: 5 }}
                    >
                      <IconComponent className={`w-6 h-6 ${feature.iconColor}`} />
                    </motion.div>
                    
                    {/* Title */}
                    <h3 className="text-lg font-bold text-secondary-blue mb-4 group-hover:text-primary-blue transition-colors duration-200">
                      {feature.title}
                    </h3>
                    
                    {/* Description */}
                    <p className="text-sm text-gray-600 leading-relaxed flex-grow">
                      {feature.description}
                    </p>
                     
                  </div>
                </motion.div>
              );
            })}
          </div>

          {/* CTA Button */}
          <motion.div 
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.7 }}
            className="text-center"
          >
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button
                href="/Request-Demo"
                variant="primary"
                size="lg"
                rightIcon={
                  <motion.div
                    animate={{ x: [0, 4, 0] }}
                    transition={{ duration: 1.5, repeat: Infinity }}
                  >
                    <ArrowRight className="w-4 h-4" />
                  </motion.div>
                }
              >
                Request a demo
              </Button>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </motion.div>
  );
};

export default ToolsForGrowth;