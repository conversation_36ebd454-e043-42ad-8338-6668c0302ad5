"use client";

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { ArrowRight, Star, Calendar, Clock } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';

// Blog data for homepage - same structure as CTB
const blogData = [
  {
    id: 1,
    title: "Penetration Testing for Fintech: Securing Innovation in the Digital Economy",
    excerpt: "Explore how fintech companies can implement robust penetration testing strategies to protect sensitive financial data and maintain regulatory compliance.",
    author: "SecurityLit Team",
    date: "2024-01-15",
    readTime: "8 min read",
    category: "Fintech Security",
    tags: ["Penetration Testing", "Fintech", "Compliance"],
    featured: true,
    image: "/images/blog-fintech.jpg",
    slug: "penetration-testing-fintech-securing-innovation"
  },
  {
    id: 2,
    title: "API Security Testing: Protecting the Backbone of Modern Applications",
    excerpt: "Learn about comprehensive API security testing methodologies and best practices for securing your application's critical interfaces.",
    author: "SecurityLit Team",
    date: "2024-01-10",
    readTime: "6 min read",
    category: "API Security",
    tags: ["API Security", "Testing", "OWASP"],
    featured: false,
    image: "/images/blog-api-security.jpg",
    slug: "api-security-testing-protecting-backbone"
  },
  {
    id: 3,
    title: "Web Application Security Testing: Beyond OWASP Top 10",
    excerpt: "Discover advanced web application security testing techniques that go beyond the standard OWASP Top 10 vulnerabilities.",
    author: "SecurityLit Team",
    date: "2024-01-05",
    readTime: "10 min read",
    category: "Web Security",
    tags: ["Web Security", "OWASP", "Vulnerability Assessment"],
    featured: true,
    image: "/images/blog-web-security.jpg",
    slug: "web-application-security-testing-beyond-owasp"
  },
  {
    id: 4,
    title: "Network Penetration Testing: Securing Your Company Inside and Out",
    excerpt: "Comprehensive guide to network penetration testing methodologies and strategies for enterprise security.",
    author: "SecurityLit Team",
    date: "2023-12-28",
    readTime: "7 min read",
    category: "Network Security",
    tags: ["Network Security", "Penetration Testing"],
    featured: false,
    image: "/images/blog-network-security.jpg",
    slug: "network-penetration-testing-securing-company"
  },
  {
    id: 5,
    title: "Healthcare Security Testing: Protecting Patient Data in Digital Health Systems",
    excerpt: "Essential security testing strategies for healthcare organizations to protect sensitive patient data and comply with regulations.",
    author: "SecurityLit Team",
    date: "2023-12-20",
    readTime: "9 min read",
    category: "Healthcare Security",
    tags: ["Healthcare", "Patient Data", "HIPAA"],
    featured: true,
    image: "/images/blog-healthcare-security.jpg",
    slug: "healthcare-security-testing-protecting-patient-data"
  },
  {
    id: 6,
    title: "Modern Frontend Security: Protecting Your Application Beyond XSS and CSRF",
    excerpt: "Advanced frontend security testing techniques for modern web applications and frameworks.",
    author: "SecurityLit Team",
    date: "2023-12-15",
    readTime: "8 min read",
    category: "Frontend Security",
    tags: ["Frontend Security", "XSS", "CSRF"],
    featured: false,
    image: "/images/blog-frontend-security.jpg",
    slug: "modern-frontend-security-protecting-application"
  }
];

export default function HomepageBlogs() {
  const [visibleCount, setVisibleCount] = useState(3);

  const handleLoadMore = () => {
    setVisibleCount(prev => prev + 3);
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-6">
        {/* Section Header */}
        <div className="text-center mb-12">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-3xl md:text-4xl font-bold text-[var(--color-dark-blue)] mb-4">
              Latest Security Insights
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Stay ahead of emerging threats with our expert analysis and industry-leading cybersecurity insights
            </p>
          </motion.div>
        </div>

        {/* Blog Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {blogData.slice(0, visibleCount).map((blog, index) => (
            <motion.article
              key={blog.id}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden group"
            >
              {/* Blog Image */}
              <div className="relative h-48 overflow-hidden">
                <Image
                  src={blog.image}
                  alt={blog.title}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-300"
                />
                {blog.featured && (
                  <div className="absolute top-4 left-4 bg-[var(--color-yellow)] text-[var(--color-dark-blue)] px-3 py-1 rounded-full text-sm font-semibold flex items-center gap-1">
                    <Star className="w-4 h-4" />
                    Featured
                  </div>
                )}
                <div className="absolute top-4 right-4 bg-black/50 text-white px-3 py-1 rounded-full text-sm">
                  {blog.readTime}
                </div>
              </div>

              {/* Blog Content */}
              <div className="p-6">
                {/* Category */}
                <div className="mb-3">
                  <span className="text-sm text-[var(--color-blue)] font-medium">
                    {blog.category}
                  </span>
                </div>

                {/* Title */}
                <h3 className="text-xl font-bold text-[var(--color-dark-blue)] mb-3 line-clamp-2 group-hover:text-[var(--color-blue)] transition-colors">
                  {blog.title}
                </h3>

                {/* Excerpt */}
                <p className="text-gray-600 mb-4 line-clamp-3">
                  {blog.excerpt}
                </p>

                {/* Meta Info */}
                <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                  <span>{blog.author}</span>
                  <span>{formatDate(blog.date)}</span>
                </div>

                {/* Tags */}
                <div className="flex flex-wrap gap-2 mb-4">
                  {blog.tags.slice(0, 2).map(tag => (
                    <span
                      key={tag}
                      className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full"
                    >
                      {tag}
                    </span>
                  ))}
                  {blog.tags.length > 2 && (
                    <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                      +{blog.tags.length - 2}
                    </span>
                  )}
                </div>

                {/* Read More Link */}
                <Link
                  href={`/blogs/${blog.slug}`}
                  className="inline-flex items-center gap-2 text-[var(--color-blue)] font-semibold hover:text-[var(--color-dark-blue)] transition-colors group/link"
                >
                  Read More
                  <ArrowRight className="w-4 h-4 group-hover/link:translate-x-1 transition-transform" />
                </Link>
              </div>
            </motion.article>
          ))}
        </div>

        {/* Load More Button */}
        {visibleCount < blogData.length && (
          <div className="text-center">
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={handleLoadMore}
              className="px-8 py-3 bg-[var(--color-blue)] text-white rounded-lg hover:bg-[var(--color-dark-blue)] transition-colors font-semibold"
            >
              Load More Articles
            </motion.button>
          </div>
        )}

        {/* View All Blogs CTA */}
        <div className="text-center mt-12">
          <Link
            href="/blogs"
            className="inline-flex items-center gap-2 text-[var(--color-blue)] font-semibold hover:text-[var(--color-dark-blue)] transition-colors group"
          >
            View All Articles
            <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
          </Link>
        </div>
      </div>
    </section>
  );
} 