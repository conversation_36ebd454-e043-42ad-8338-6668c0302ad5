import UsefulLinksPage from '@/app/common/pages/UsefulLinksPage'
import React from 'react'

export const metadata = {
  openGraph: {
    title: "Capture The Bug | Researcher Terms and Conditions",
    type: "website",
    url: "https://capturethebug.xyz/Useful-Links/Researcher-tc",
    description:
      "This webpage is a legal document that outlines the terms and conditions for individuals who have registered usernames (also referred to as “ID”) with Capture The Bug (“CTB”) through the CTB website.",
    images:  "https://i.postimg.cc/PJqxvBKq/Researcher-tc.png"
  },
};

export default function page() {
  return (
    <div>
      <title>Capture The Bug | Researcher Terms and Conditions</title>
      <UsefulLinksPage title="Researcher Terms and Conditions">
        {/* <div className='font-bold'>Code of Ethics</div>        
        <div className='font-bold'>CTB-COE-V2.0</div>        
        <div className='font-bold'>Last update 16-03-2023</div>       */}

<div className='font-bold'>Researcher Agreement</div>

       
       
        <p className='text-slate-600'>This webpage is a legal document that outlines the terms and conditions for individuals who have registered usernames (also referred to as “ID”) with Capture The Bug (“CTB”) through the CTB website. The Researcher Agreement includes the Code of Ethics, Disclosure Policy, and CTB Platform T&Cs, which are incorporated by reference. Once you obtain a username with CTB, you become a “Researcher” and are required to comply with the Researcher Agreement.</p>  

       
       
        <ul className="list-disc pl-7">
  <li className="font-bold mb-2">Submitting a Vulnerability</li>
</ul>

<p className='text-slate-600'>
  When you believe you have discovered a vulnerability, please submit a report for the relevant program through the CTB platform. Each program has specific guidelines known as the Program Guide, which is maintained by the Program Owner. Please note that the terms outlined in the Program Brief take precedence over these terms.
</p>

<p className='text-slate-600'>
  As part of the submission process, each report will be updated with significant events, including validation of the issue, requests for additional information, or confirmation of eligibility for a reward.
</p>

<p className='text-slate-600'>
  The Program Owner will assess each report on a first-to-find basis, although CTB may assist in the evaluation process.
</p>

<p className='text-slate-600'>
  To qualify for a reward, you must be the first to alert the Program Owner to an unknown issue and the issue must trigger a code or configuration change.
</p>

<ul className="list-disc pl-7">
          <li className="font-bold mb-2">Standard Program Rules</li>
        </ul>

<p className='text-slate-600'>
  Our top priority is to protect Security Researchers, and to do so, we have established a set of standard rules. Please note that rules may differ between programs, so make sure to read the specific program brief before submitting. These are the standard rules that apply to all programs:
</p>

<ul className="list-disc pl-7 text-slate-600">
  <li>Testing should only be conducted on systems listed as targets in the program brief. Any other systems are Out Of Scope.</li>
  <li>Unless otherwise specified in the program Guide, create accounts for testing purposes.</li>
  <li>To qualify for a reward, submissions must be made exclusively through CTB.</li>
  <li>Communication regarding submissions must be done through CTB and/or official CTB support channels.</li>
  <li>Prohibited actions that affect the integrity or availability of program targets are strictly enforced. If automated tools cause performance degradation on the target systems, immediately suspend their use.</li>
  <li>Submissions must meaningfully affect the target’s users, systems, or data security to qualify for a reward.</li>
  <li>Submissions may be closed if a Researcher is non-responsive to requests for information after 7 days.</li>
  <li>Private or invitation-only programs must not be communicated to anyone who is not a CTB or authorised employee.</li>
  <li>Researchers are encouraged to include a Proof-of-Concept in their submissions but not share them publicly. If the file is larger than 100MB, upload it to a secure online service such as Vimeo with a password.</li>
  <li>Our Disclosure policies apply to all submissions, including Duplicate, Out of Scope, and Not Applicable submissions. Customers may choose Nondisclosure, Coordinated Disclosure, or Custom Disclosure policies for their program brief.</li>
  <li>To retain disclosure rights for vulnerabilities out of scope for a bounty program, contact the Program Owner directly. CTB can assist Researchers in identifying the appropriate email address to contact.</li>
  <li>Violation of a program’s stated disclosure policy may result in enforcement action as outlined in the CTB Terms and Conditions.</li>
  
</ul>
<p className='text-slate-600'>To be eligible to receive monetary compensation as a Researcher, you must be at least 18 years old or have reached the age of majority in your jurisdiction of primary residence and citizenship. Additional eligibility requirements are stated in our Terms of Service. Exceptions for minors may be considered on a case-by-case basis between CTB and the applicable minor’s guardian(s).</p>

<ul className="list-disc pl-7">
          <li className="font-bold mb-2">User Accounts</li>
        </ul>

<p className='text-slate-600'>
  To become a Researcher, you will need to create an account and username on the CTB platform. You cannot use a third-party’s account without their permission. When creating your account, you must provide accurate and complete information, using your real name and contact information. You are responsible for everything that happens on your account, so keep your password secure and do not share it with others. If you suspect that someone is using your account without permission, you must inform CTB immediately. You may not transfer your account to someone else. CTB reserves the right to deny the use of certain usernames or require usernames to be changed, and usernames with offensive or discriminatory language are prohibited. If someone uses your account without your permission and causes damage, you may be held liable.
</p>

<ul className="list-disc pl-7">
          <li className="font-bold mb-2">Exclusions</li>
        </ul>

<p className='text-slate-600'>
  Some types of submissions are not eligible for rewards due to their potential danger or low impact on the Program Owner’s security. This section lists the types of issues that are immediately deemed invalid and not eligible for rewards. Such issues include findings resulting from physical testing, social engineering, systems and applications that are not part of the “Targets” section, functional, UI, and UX bugs, spelling errors, and network level Denial of Service (DoS/DDoS) vulnerabilities.
</p>

<ul className="list-disc pl-7">
          <li className="font-bold mb-2">Unacceptable Vulnerability Reports</li>
        </ul>

<p className='text-slate-600'>
  Certain types of submissions are not eligible for a reward as they have a low security impact on the program owner and do not result in a code change. This section lists commonly reported issues that are often not eligible, and we advise against reporting them unless you can demonstrate a chained attack with a higher impact.
</p>

<ul className="list-disc pl-7 text-slate-600">
  <li>Vulnerabilities that require physical access to the device or system.</li>
  <li>Vulnerabilities that require the attacker to have administrative access to the system.</li>
  <li>Vulnerabilities that require social engineering or phishing to exploit.</li>
  <li>Vulnerabilities in third-party software that is not owned or maintained by the organisation.</li>
  <li>Outdated or unsupported software versions.</li>
  <li>Use of weak or commonly-used passwords.</li>
  <li>Use of insecure file transfer protocols such as FTP.</li>
  <li>Vulnerabilities that are already publicly known or documented.</li>
  <li>Self-XSS or reflected XSS that cannot be exploited to steal sensitive information.</li>
  <li>User enumeration via email address or account ID.</li>
  <li>Open redirect vulnerabilities that cannot be used to conduct phishing attacks.</li>
  <li>Vulnerabilities that do not affect the confidentiality, integrity or availability of the system or data.</li>
  <li>Reports that lack technical information or do not provide enough details to reproduce the issue.</li>
  <li>Vulnerabilities that are a result of a user’s misconfiguration or misuse of the system.</li>
  <li>Vulnerabilities in beta or pre-release versions of software.</li>
  <li>Missing or weak SSL/TLS certificates.</li>
  <li>Content injection that cannot be used to execute malicious code.</li>
  <li>Reports that are a result of automated vulnerability scanners or tools.</li>
  <li>Vulnerabilities that have already been submitted by another researcher.</li>
</ul>

<ul className="list-disc pl-7">
          <li className="font-bold mb-2">Bounty</li>
        </ul>

<p className='text-slate-600'>
  The eligibility for a Bounty depends on being the first eligible person to report a previously unknown issue that triggers a code or configuration change to the Program Owner. The Bounty details may vary for each program. Each submission’s Bounty amount is determined by the issue’s business impact, severity, and creativity, with higher levels of awards given to bugs found in applications, features, and functions called out in the program guide as “Focus Area(s).” To receive a monetary award, you may need to provide additional verification and tax information, fulfil various eligibility requirements, and agree to additional terms and conditions with a third-party payment processor. You are solely responsible for paying taxes on Bounty paid to you, and any Bounty that remain unclaimed or undeliverable for twelve (12) months will be forfeited.
</p>

<ul className="list-disc pl-7">
          <li className="font-bold mb-2">Intellectual Property; Ownership of Testing Results</li>
        </ul>

<p className='text-slate-600'>
  By participating as a Researcher, you confirm that you have obtained all necessary approvals and consents from third parties, including your employer, to conduct the testing services.
</p>

<p className='text-slate-600'>
  In this section, “Testing Results” refer to any information about vulnerabilities discovered on the target systems by Researchers, while “Target Systems” refer to the applications and systems that are being tested. You agree to disclose all of your Testing Results to CTB and assign any of your Testing Results and related rights to CTB.
</p>

<p className='text-slate-600'>
  If any rights in your Testing Results cannot be assigned, you shall grant CTB an irrevocable, perpetual, royalty-free, exclusive, transferable, sublicensable (directly or indirectly through multiple tiers), and worldwide license to use the Testing Results in any manner desired by CTB, including without limitation, the right to make, have made, sell, offer for sale, use, rent, lease, import, copy, prepare derivative works, publicly display, publicly perform, and distribute all or any part of such Testing Results and modifications and combinations thereof, and to sublicense or transfer any and all such rights.
</p>

<p className='text-slate-600'>
  You also waive any moral rights or other rights or claims that are inconsistent with the intent of the complete transfer of rights to CTB in your Testing Results. You authorize CTB, Bug Bounty Programs, or Vulnerability Disclosure sponsors to publicize your Testing Results, including your account name (ID), and other information as required by the Program Brief. If any Program Brief requests personally identifiable information about you, your participation in the program indicates your consent to provide such information.
</p>

<ul className="list-disc pl-7">
          <li className="font-bold mb-2">Confidentiality Obligations</li>
        </ul>

<p className='text-slate-600'>
  The term “Confidential Information” refers to any information that is designated as confidential at the time of disclosure or would reasonably be considered confidential based on the circumstances and content of the disclosure. This includes, but is not limited to, customer information, personally identifiable information, financial information, information about Target Systems, information about crowdsourced security programs, pricing information, business information, fees paid to Researchers, and the existence and terms of private crowdsourced security programs. Confidential Information does not include information that: (i) is obtained from a source other than the disclosing party under no obligation of confidentiality; (ii) becomes publicly known or ceases to be confidential, except through a breach of this Agreement; or (iii) is independently developed by the receiving party.
</p>

<p className='text-slate-600'>
  As a Researcher, you agree to keep all Confidential Information confidential and not disclose it to any third party without written approval from the disclosing party. You also agree to protect the Confidential Information using reasonable care and only use it for the purpose permitted by the disclosing party. If you discover any unauthorised disclosure of Confidential Information, you must notify the disclosing party immediately.
</p>

<p className='text-slate-600'>
  All submissions are considered confidential information of the Program Owner unless stated otherwise in the bounty brief. This means that you cannot publicly disclose any submission without the Program Owner’s consent. Please see the Disclosure Policy for more information on disclosing vulnerabilities in connection with Bug Bounty Programs.
</p>

<p className='text-slate-600'>
  For information about the confidentiality of Researchers’ Confidential Information, please refer to the CTB privacy policy.
</p>


<ul className="list-disc pl-7">
          <li className="font-bold mb-2">Authorised Communication Methods</li>
        </ul>


<p className='text-slate-600'>
  During each program, the CTB team may communicate updates via:
  Program Updates’ section within the program.
  Email.
</p>



<p className='text-slate-600'>
  If you have questions about a program or a specific submission, you may contact the CTB team via:<EMAIL>
</p>



    
      
</UsefulLinksPage>
    </div>
  )
}

