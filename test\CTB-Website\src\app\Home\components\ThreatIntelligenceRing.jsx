"use client"
import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Image from 'next/image';

export default function ThreatIntelligenceRing() {
  const [activeSegment, setActiveSegment] = useState(null);
  const [hoveredSegment, setHoveredSegment] = useState(null);
  const [pulseEffect, setPulseEffect] = useState(false);
  const [rotation, setRotation] = useState(0);
  const hoverTimeoutRef = useRef(null);
  const lastHoveredRef = useRef(null);
  
  const segments = [
    { id: 1, label: "Critical", value: 30, color: "#1E3A8A" },
    { id: 2, label: "High", value: 25, color: "#0835A7" },
    { id: 3, label: "Medium", value: 20, color: "#0B45DB" },
    { id: 4, label: "Low", value: 25, color: "#38B7FF" }
  ];
  
  useEffect(() => {
    // Create pulse effect at intervals
    const pulseInterval = setInterval(() => {
      setPulseEffect(true);
      setTimeout(() => setPulseEffect(false), 800);
    }, 5000);
    
    return () => {
      clearInterval(pulseInterval);
      // Clear any pending hover timeouts
      if (hoverTimeoutRef.current) {
        clearTimeout(hoverTimeoutRef.current);
      }
    };
  }, []);
  
  // Calculate total value
  const total = segments.reduce((sum, segment) => sum + segment.value, 0);
  
  // Create proper pie chart segments
  let currentAngle = 0;
  const pieSegments = segments.map(segment => {
    const startAngle = currentAngle;
    const percentage = segment.value / total;
    const angle = percentage * 360;
    const endAngle = currentAngle + angle;
    currentAngle = endAngle;
    
    return {
      ...segment,
      percentage,
      startAngle,
      endAngle,
      angleRad: angle * (Math.PI / 180)
    };
  });
  
  // Handle segment click
  const handleSegmentClick = (segment) => {
    if (activeSegment?.id === segment.id) {
      setActiveSegment(null);
      // Add rotation effect when deselecting
      setRotation(prev => prev + 360);
    } else {
      setPulseEffect(true);
      setActiveSegment(segment);
      setTimeout(() => setPulseEffect(false), 500);
    }
  };
  
  // Debounced hover handler to prevent vibration
  const handleSegmentHover = (segment) => {
    // Clear any existing timeout
    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current);
    }
    
    // Don't update if the segment is the same as the last hovered
    if (lastHoveredRef.current?.id === segment?.id) {
      return;
    }
    
    // Set a small delay before updating hover state
    hoverTimeoutRef.current = setTimeout(() => {
      setHoveredSegment(segment);
      lastHoveredRef.current = segment;
      hoverTimeoutRef.current = null;
    }, 40); // Small delay to prevent jittering
  };

  // Generate SVG path for a pie segment
  const createPieSegment = (startAngle, endAngle, radius, breakOut = 0) => {
    const startRad = (startAngle - 90) * (Math.PI / 180); // -90 to start from top
    const endRad = (endAngle - 90) * (Math.PI / 180);
    const largeArcFlag = endAngle - startAngle <= 180 ? 0 : 1;
    
    const centerX = 50 + breakOut * Math.cos((startAngle + endAngle - 180) / 2 * (Math.PI / 180));
    const centerY = 50 + breakOut * Math.sin((startAngle + endAngle - 180) / 2 * (Math.PI / 180));
    
    const x1 = centerX + radius * Math.cos(startRad);
    const y1 = centerY + radius * Math.sin(startRad);
    const x2 = centerX + radius * Math.cos(endRad);
    const y2 = centerY + radius * Math.sin(endRad);
    
    return `M ${centerX} ${centerY} L ${x1} ${y1} A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2} Z`;
  };
  
  return (
    <div className="w-full h-full flex items-center justify-center relative">
      <div className="relative w-36 h-36 md:w-40 md:h-40 transform hover:scale-102 transition-all duration-500 cursor-pointer">
        {/* SVG Pie Chart */}
        <motion.svg 
          viewBox="0 0 100 100" 
          className="w-full h-full drop-shadow-lg"
          animate={{ rotate: rotation }}
          transition={{ type: "spring", damping: 15, stiffness: 60 }}
          onMouseLeave={() => {
            // Clear any existing timeout
            if (hoverTimeoutRef.current) {
              clearTimeout(hoverTimeoutRef.current);
            }
            setHoveredSegment(null);
            lastHoveredRef.current = null;
          }}
        >
          {/* Background subtle glow */}
          <defs>
            <radialGradient id="bgGlow" cx="50%" cy="50%" r="50%" fx="50%" fy="50%">
              <stop offset="0%" stopColor="rgba(255,255,255,0.8)" />
              <stop offset="70%" stopColor="rgba(240,240,255,0.6)" />
              <stop offset="100%" stopColor="rgba(230,240,255,0.2)" />
            </radialGradient>
            
            {/* Segment glows and gradients */}
            {pieSegments.map(segment => (
              <radialGradient key={`gradient-${segment.id}`} id={`gradient-${segment.id}`} cx="50%" cy="50%" r="70%" fx="40%" fy="40%">
                <stop offset="0%" stopColor={`${segment.color}cc`} />
                <stop offset="85%" stopColor={segment.color} />
                <stop offset="100%" stopColor={`${segment.color}ee`} />
              </radialGradient>
            ))}
            
            {/* Filter for the glow effect */}
            <filter id="glow" x="-20%" y="-20%" width="140%" height="140%">
              <feGaussianBlur stdDeviation="2" result="blur" />
              <feComposite in="SourceGraphic" in2="blur" operator="over" />
            </filter>
          </defs>
          
          {/* Base circle for subtle background */}
          <circle cx="50" cy="50" r="48" fill="url(#bgGlow)" opacity="0.8" />
          
          {/* Pie chart segments with expanded hit area */}
          {pieSegments.map((segment) => {
            const isHovered = hoveredSegment?.id === segment.id;
            const isActive = activeSegment?.id === segment.id;
            // Calculate middle angle for break direction
            const midAngle = (segment.startAngle + segment.endAngle) / 2;
            const breakOutDistance = isHovered ? 5 : isActive ? 3 : 0;
            
            return (
              <motion.g 
                key={segment.id} 
                onMouseEnter={() => handleSegmentHover(segment)}
                onClick={() => handleSegmentClick(segment)}
                initial={{ opacity: 0.8 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.3 }}
                style={{ cursor: 'pointer' }}
              >
                {/* Invisible wider hit area to prevent jitter */}
                <path
                  d={createPieSegment(
                    segment.startAngle - 1, // Slightly wider for better hit detection
                    segment.endAngle + 1,
                    46,
                    0
                  )}
                  fill="transparent"
                  stroke="transparent"
                  strokeWidth="0"
                  onMouseEnter={() => handleSegmentHover(segment)}
                  style={{ cursor: 'pointer' }}
                />
                
                {/* Main pie segment with break-out effect */}
                <motion.path
                  d={createPieSegment(segment.startAngle, segment.endAngle, 45, breakOutDistance)}
                  fill={`url(#gradient-${segment.id})`}
                  stroke="white"
                  strokeWidth={isHovered || isActive ? 1.2 : 0.6}
                  initial={{ scale: 1, opacity: 0.9 }}
                  animate={{ 
                    scale: isActive ? [1, 1.05, 1] : 1,
                    opacity: isHovered || isActive ? 1 : 0.9,
                  }}
                  transition={{
                    scale: {
                      duration: 1.5,
                      repeat: isActive ? Infinity : 0,
                      repeatType: "reverse"
                    },
                    default: { duration: 0.3 }
                  }}
                  whileTap={{ scale: 0.98 }}
                />
                
                {/* Segment glow effect */}
                <motion.path
                  d={createPieSegment(segment.startAngle, segment.endAngle, 45, breakOutDistance)}
                  fill="none"
                  stroke={segment.color}
                  strokeWidth="0.4"
                  opacity={isHovered || isActive ? 0.6 : 0.2}
                  filter="url(#glow)"
                  animate={{ 
                    opacity: isHovered || isActive ? [0.3, 0.6, 0.3] : 0.2
                  }}
                  transition={{
                    opacity: {
                      duration: 1.8,
                      repeat: isHovered || isActive ? Infinity : 0,
                      repeatType: "reverse"
                    }
                  }}
                />
                
                {/* Segment value indicator */}
                {(isHovered || isActive) && (
                  <motion.g>
                    {/* Highlight effect */}
                    <motion.circle
                      cx={50 + (breakOutDistance + 22) * Math.cos((midAngle - 90) * (Math.PI / 180))}
                      cy={50 + (breakOutDistance + 22) * Math.sin((midAngle - 90) * (Math.PI / 180))}
                      r={10}
                      fill="white"
                      opacity={0.9}
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      exit={{ scale: 0 }}
                      transition={{ type: "spring", damping: 12 }}
                    />
                    
                    {/* Percentage text */}
                    <motion.text
                      x={50 + (breakOutDistance + 22) * Math.cos((midAngle - 90) * (Math.PI / 180))}
                      y={50 + (breakOutDistance + 22) * Math.sin((midAngle - 90) * (Math.PI / 180))}
                      fontSize="5.5"
                      fontWeight="bold"
                      fill={segment.color}
                      textAnchor="middle"
                      dominantBaseline="middle"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      transition={{ delay: 0.1 }}
                    >
                      {Math.round(segment.percentage * 100)}%
                    </motion.text>
                  </motion.g>
                )}
                
                {/* Particles effect on hover */}
                {isHovered && (
                  <g>
                    {Array.from({ length: 4 }).map((_, i) => {
                      const particleAngle = midAngle - 90 + (i - 1.5) * 10;
                      const particleRad = particleAngle * (Math.PI / 180);
                      const distance = 35 + Math.random() * 6;
                      
                      return (
                        <motion.circle
                          key={i}
                          cx={50}
                          cy={50}
                          r={0.6 + Math.random() * 0.5}
                          fill={segment.color}
                          initial={{ opacity: 0 }}
                          animate={{ 
                            opacity: [0, 0.8, 0],
                            x: [0, distance * Math.cos(particleRad) * 0.15],
                            y: [0, distance * Math.sin(particleRad) * 0.15],
                            scale: [0, 1.5, 0]
                          }}
                          transition={{ 
                            duration: 1 + Math.random(),
                            repeat: Infinity,
                            delay: i * 0.1
                          }}
                        />
                      );
                    })}
                  </g>
                )}
              </motion.g>
            );
          })}
          
          {/* Center circle with subtle style */}
          <motion.circle 
            cx="50"
            cy="50"
            r="18"
            fill="white"
            stroke="#f0f0f0"
            strokeWidth="1.5"
            animate={pulseEffect ? { scale: [1, 1.1, 1], opacity: [0.9, 1, 0.9] } : {}}
            transition={{ duration: 0.8, ease: "easeInOut" }}
            filter="drop-shadow(0px 2px 3px rgba(0, 0, 0, 0.1))"
            onMouseEnter={() => handleSegmentHover(null)}
          />
          
          {/* Subtle inner circle for depth */}
          <motion.circle 
            cx="50"
            cy="50"
            r="14"
            fill="white"
            filter="drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.05))"
          />
          
          {/* Inner circle with brand color accent */}
          <circle
            cx="50"
            cy="50"
            r="12"
            fill="white"
            stroke="rgba(8, 53, 167, 0.1)"
            strokeWidth="1.5"
          />
          
          {/* Ripple effect for center */}
          <AnimatePresence>
            {pulseEffect && (
              <motion.circle
                cx="50"
                cy="50"
                r="22"
                fill="none"
                stroke="rgba(8, 53, 167, 0.4)"
                strokeWidth="1"
                initial={{ scale: 0.5, opacity: 0.8 }}
                animate={{ scale: 2, opacity: 0 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 1.2, ease: "easeOut" }}
              />
            )}
          </AnimatePresence>
        </motion.svg>
        
        {/* Center logo */}
        <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
          <motion.div
            className="flex items-center justify-center w-8 h-8 md:w-10 md:h-10"
            animate={activeSegment ? { scale: [1, 1.08, 1] } : {}}
            transition={{ duration: 0.5 }}
          >
            <Image
              src="/images/ctb_only_logo.png"
              alt="CTB Logo"
              width={40}
              height={40}
              className="w-full h-full object-contain"
            />
          </motion.div>
        </div>
      </div>
    </div>
  );
} 