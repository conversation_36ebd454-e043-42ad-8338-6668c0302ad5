{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/common/components/BreadcrumbNavigation.jsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState } from 'react';\r\nimport Link from 'next/link';\r\nimport { ChevronRight, Home, FileText, Users, Settings, MapPin, Shield, Code, Smartphone, Globe, DollarSign, Building, Phone, Download } from 'lucide-react';\r\nimport { motion } from 'framer-motion';\r\n\r\n/**\r\n * Truncated Text Component for Mobile Breadcrumbs\r\n * Shows truncated text on mobile with click to expand functionality\r\n */\r\nconst TruncatedText = ({ text, maxLength = 25, onExpansionChange }) => {\r\n  const [isExpanded, setIsExpanded] = useState(false);\r\n\r\n  const handleToggle = () => {\r\n    const newExpanded = !isExpanded;\r\n    setIsExpanded(newExpanded);\r\n    if (onExpansionChange) {\r\n      onExpansionChange(newExpanded);\r\n    }\r\n  };\r\n\r\n  if (text.length <= maxLength) {\r\n    return <span>{text}</span>;\r\n  }\r\n\r\n  return (\r\n    <span\r\n      className=\"cursor-pointer\"\r\n      onClick={handleToggle}\r\n      title={isExpanded ? \"Click to collapse\" : \"Click to expand\"}\r\n    >\r\n      <span className=\"sm:hidden\">\r\n        {isExpanded ? text : `${text.substring(0, maxLength)}...`}\r\n      </span>\r\n      <span className=\"hidden sm:inline\">\r\n        {text}\r\n      </span>\r\n    </span>\r\n  );\r\n};\r\n\r\n/**\r\n * Structured Data Component for SEO\r\n */\r\nconst BreadcrumbStructuredData = ({ items }) => {\r\n  const structuredData = {\r\n    \"@context\": \"https://schema.org\",\r\n    \"@type\": \"BreadcrumbList\",\r\n    \"itemListElement\": items.map((item, index) => ({\r\n      \"@type\": \"ListItem\",\r\n      \"position\": index + 1,\r\n      \"name\": item.name,\r\n      \"item\": item.current ? undefined : `https://securitylit.com${item.url}`\r\n    }))\r\n  };\r\n\r\n  return (\r\n    <script\r\n      type=\"application/ld+json\"\r\n      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}\r\n    />\r\n  );\r\n};\r\n\r\n/**\r\n * Universal Breadcrumb Navigation Component with SEO structured data\r\n * Provides clear navigation hierarchy and improves SEO across all pages\r\n */\r\nconst BreadcrumbNavigation = ({ items, className = \"\" }) => {\r\n  const [hasExpandedText, setHasExpandedText] = useState(false);\r\n\r\n  // Check if this is a blog page for conditional padding\r\n  const isBlogPage = items?.some(item =>\r\n    item.url?.includes('/Blogs') ||\r\n    item.iconKey === 'blogs' ||\r\n    className?.includes('blog')\r\n  );\r\n\r\n  return (\r\n    <>\r\n      {/* Structured Data for SEO */}\r\n      <BreadcrumbStructuredData items={items} />\r\n      \r\n      {/* Simple Breadcrumb Navigation */}\r\n      <nav\r\n        className={`bg-transparent py-2 sm:py-3 ${\r\n          isBlogPage\r\n            ? hasExpandedText\r\n              ? 'pb-6 sm:pb-4'\r\n              : 'pb-3 sm:pb-4'\r\n            : 'pb-2 sm:pb-3'\r\n        } ${className}`}\r\n        aria-label=\"Breadcrumb navigation\"\r\n      >\r\n        <div className=\"max-w-7xl mx-auto px-4\">\r\n          <motion.ol\r\n            className={`flex items-center space-x-1 text-xs sm:text-sm ${\r\n              className?.includes('text-white')\r\n                ? 'text-white'\r\n                : className?.includes('text-blue-600')\r\n                ? 'text-blue-600'\r\n                : 'text-gray-600'\r\n            }`}\r\n            initial={{ opacity: 0 }}\r\n            animate={{ opacity: 1 }}\r\n            transition={{ duration: 0.4 }}\r\n          >\r\n            {items.map((item, index) => (\r\n              <motion.li\r\n                key={index}\r\n                className=\"flex items-center\"\r\n                initial={{ opacity: 0 }}\r\n                animate={{ opacity: 1 }}\r\n                transition={{ duration: 0.3, delay: index * 0.1 }}\r\n              >\r\n                {index > 0 && (\r\n                  <ChevronRight\r\n                    className={`w-3 h-3 mx-1 ${\r\n                      className?.includes('text-white')\r\n                        ? 'text-white/70'\r\n                        : className?.includes('text-blue-600')\r\n                        ? 'text-blue-400'\r\n                        : 'text-gray-400'\r\n                    }`}\r\n                    aria-hidden=\"true\"\r\n                  />\r\n                )}\r\n\r\n                {item.current ? (\r\n                  <span\r\n                    className={`font-medium flex items-center gap-1 ${\r\n                      className?.includes('text-white')\r\n                        ? 'text-white'\r\n                        : className?.includes('text-blue-600')\r\n                        ? 'text-blue-600'\r\n                        : 'text-gray-900'\r\n                    }`}\r\n                    aria-current=\"page\"\r\n                    title={item.description}\r\n                  >\r\n                    {item.iconKey === 'home' && (\r\n                      <Home className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-blue-600'\r\n                      }`} />\r\n                    )}\r\n                    {item.iconKey === 'blogs' && (\r\n                      <FileText className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-blue-600'\r\n                      }`} />\r\n                    )}\r\n                    {item.iconKey === 'contact-us' && (\r\n                      <Phone className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-blue-600'\r\n                      }`} />\r\n                    )}\r\n                    <TruncatedText\r\n                      text={item.name}\r\n                      onExpansionChange={setHasExpandedText}\r\n                    />\r\n                  </span>\r\n                ) : (\r\n                  <Link\r\n                    href={item.url}\r\n                    className={`transition-colors duration-200 flex items-center gap-1 hover:underline ${\r\n                      className?.includes('text-white')\r\n                        ? 'text-white hover:text-white/80'\r\n                        : className?.includes('text-blue-600')\r\n                        ? 'text-blue-600 hover:text-blue-800'\r\n                        : 'text-blue-600 hover:text-blue-800'\r\n                    }`}\r\n                    title={item.description}\r\n                  >\r\n                    {item.iconKey === 'home' && (\r\n                      <Home className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-current'\r\n                      }`} />\r\n                    )}\r\n                    {item.iconKey === 'blogs' && (\r\n                      <FileText className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-current'\r\n                      }`} />\r\n                    )}\r\n                    {item.iconKey === 'contact-us' && (\r\n                      <Phone className={`w-3 h-3 ${\r\n                        className?.includes('text-white')\r\n                          ? 'text-white'\r\n                          : className?.includes('text-blue-600')\r\n                          ? 'text-blue-600'\r\n                          : 'text-current'\r\n                      }`} />\r\n                    )}\r\n                    <TruncatedText\r\n                      text={item.name}\r\n                      onExpansionChange={setHasExpandedText}\r\n                    />\r\n                  </Link>\r\n                )}\r\n              </motion.li>\r\n            ))}\r\n          </motion.ol>\r\n        </div>\r\n      </nav>\r\n    </>\r\n  );\r\n};\r\n\r\n// Client-side helper function for generating breadcrumb items\r\nexport const generateBreadcrumbs = (pageType, params = {}) => {\r\n  const baseBreadcrumb = {\r\n    name: \"Home\",\r\n    url: \"/\",\r\n    iconKey: \"home\",\r\n    description: \"Return to homepage\"\r\n  };\r\n\r\n  const breadcrumbConfigs = {\r\n    blog: (slug) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Blog\",\r\n        url: \"/Blogs\",\r\n        iconKey: \"blogs\",\r\n        description: \"View all blog posts\"\r\n      },\r\n      {\r\n        name: params.title || \"Blog Post\",\r\n        url: `/Blogs/${slug}`,\r\n        current: true,\r\n        description: `${params.title || 'Blog post'} - Cybersecurity insights and penetration testing guidance`\r\n      }\r\n    ],\r\n    \r\n    service: (service) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Services\",\r\n        url: \"/Services\",\r\n        iconKey: \"services\",\r\n        description: \"View all cybersecurity services\"\r\n      },\r\n      {\r\n        name: params.title || service,\r\n        url: `/Services/${service}`,\r\n        current: true,\r\n        iconKey: service,\r\n        description: `${params.title || service} penetration testing services`\r\n      }\r\n    ],\r\n\r\n    location: (country) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Locations\",\r\n        url: \"/Locations\",\r\n        iconKey: \"locations\",\r\n        description: \"View all locations\"\r\n      },\r\n      {\r\n        name: params.countryName || country,\r\n        url: `/Locations/${country}`,\r\n        current: true,\r\n        description: `${params.countryName || country} cybersecurity services`\r\n      }\r\n    ],\r\n\r\n    company: (page) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Company\",\r\n        url: \"/Company\",\r\n        iconKey: \"company\",\r\n        description: \"Learn about SecurityLit\"\r\n      },\r\n      {\r\n        name: params.title || page,\r\n        url: `/Company/${page}`,\r\n        current: true,\r\n        description: params.description || `${page} information`\r\n      }\r\n    ],\r\n\r\n    'company-size': (size) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Company Size\",\r\n        url: \"/Company-size\",\r\n        iconKey: \"company\",\r\n        description: \"Solutions for different company sizes\"\r\n      },\r\n      {\r\n        name: params.title || size,\r\n        url: `/Company-size/${size}`,\r\n        current: true,\r\n        description: params.description || `${size} cybersecurity solutions`\r\n      }\r\n    ],\r\n\r\n    industry: (industry) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Industries\",\r\n        url: \"/Industries\",\r\n        iconKey: \"company\",\r\n        description: \"Industry-specific cybersecurity solutions\"\r\n      },\r\n      {\r\n        name: params.title || industry,\r\n        url: `/Industries/${industry}`,\r\n        current: true,\r\n        description: params.description || `${industry} cybersecurity solutions`\r\n      }\r\n    ],\r\n\r\n    product: (product) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: \"Product\",\r\n        url: \"/Product\",\r\n        iconKey: \"product\",\r\n        description: \"Explore our cybersecurity products\"\r\n      },\r\n      {\r\n        name: params.title || product,\r\n        url: `/Product/${product}`,\r\n        current: true,\r\n        description: params.description || `${product} product information`\r\n      }\r\n    ],\r\n\r\n    simple: (pageName, url) => [\r\n      baseBreadcrumb,\r\n      {\r\n        name: pageName,\r\n        url: url,\r\n        current: true,\r\n        iconKey: params.iconKey,\r\n        description: params.description || `${pageName} page`\r\n      }\r\n    ]\r\n  };\r\n\r\n  return breadcrumbConfigs[pageType] || (() => [baseBreadcrumb]);\r\n};\r\n\r\nexport default BreadcrumbNavigation; "], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;;;AAJA;;;;;AAMA;;;CAGC,GACD,MAAM,gBAAgB;QAAC,EAAE,IAAI,EAAE,YAAY,EAAE,EAAE,iBAAiB,EAAE;;IAChE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,eAAe;QACnB,MAAM,cAAc,CAAC;QACrB,cAAc;QACd,IAAI,mBAAmB;YACrB,kBAAkB;QACpB;IACF;IAEA,IAAI,KAAK,MAAM,IAAI,WAAW;QAC5B,qBAAO,6LAAC;sBAAM;;;;;;IAChB;IAEA,qBACE,6LAAC;QACC,WAAU;QACV,SAAS;QACT,OAAO,aAAa,sBAAsB;;0BAE1C,6LAAC;gBAAK,WAAU;0BACb,aAAa,OAAO,AAAC,GAA+B,OAA7B,KAAK,SAAS,CAAC,GAAG,YAAW;;;;;;0BAEvD,6LAAC;gBAAK,WAAU;0BACb;;;;;;;;;;;;AAIT;GA7BM;KAAA;AA+BN;;CAEC,GACD,MAAM,2BAA2B;QAAC,EAAE,KAAK,EAAE;IACzC,MAAM,iBAAiB;QACrB,YAAY;QACZ,SAAS;QACT,mBAAmB,MAAM,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;gBAC7C,SAAS;gBACT,YAAY,QAAQ;gBACpB,QAAQ,KAAK,IAAI;gBACjB,QAAQ,KAAK,OAAO,GAAG,YAAY,AAAC,0BAAkC,OAAT,KAAK,GAAG;YACvE,CAAC;IACH;IAEA,qBACE,6LAAC;QACC,MAAK;QACL,yBAAyB;YAAE,QAAQ,KAAK,SAAS,CAAC;QAAgB;;;;;;AAGxE;MAlBM;AAoBN;;;CAGC,GACD,MAAM,uBAAuB;QAAC,EAAE,KAAK,EAAE,YAAY,EAAE,EAAE;;IACrD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,uDAAuD;IACvD,MAAM,aAAa,kBAAA,4BAAA,MAAO,IAAI,CAAC,CAAA;YAC7B;eAAA,EAAA,YAAA,KAAK,GAAG,cAAR,gCAAA,UAAU,QAAQ,CAAC,cACnB,KAAK,OAAO,KAAK,YACjB,sBAAA,gCAAA,UAAW,QAAQ,CAAC;;IAGtB,qBACE;;0BAEE,6LAAC;gBAAyB,OAAO;;;;;;0BAGjC,6LAAC;gBACC,WAAW,AAAC,+BAMR,OALF,aACI,kBACE,iBACA,iBACF,gBACL,KAAa,OAAV;gBACJ,cAAW;0BAEX,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;wBACR,WAAW,AAAC,kDAMX,OALC,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,iBAChB,eACA,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,oBACpB,kBACA;wBAEN,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,YAAY;4BAAE,UAAU;wBAAI;kCAE3B,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;gCAER,WAAU;gCACV,SAAS;oCAAE,SAAS;gCAAE;gCACtB,SAAS;oCAAE,SAAS;gCAAE;gCACtB,YAAY;oCAAE,UAAU;oCAAK,OAAO,QAAQ;gCAAI;;oCAE/C,QAAQ,mBACP,6LAAC,yNAAA,CAAA,eAAY;wCACX,WAAW,AAAC,gBAMX,OALC,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,iBAChB,kBACA,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,oBACpB,kBACA;wCAEN,eAAY;;;;;;oCAIf,KAAK,OAAO,iBACX,6LAAC;wCACC,WAAW,AAAC,uCAMX,OALC,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,iBAChB,eACA,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,oBACpB,kBACA;wCAEN,gBAAa;wCACb,OAAO,KAAK,WAAW;;4CAEtB,KAAK,OAAO,KAAK,wBAChB,6LAAC,sMAAA,CAAA,OAAI;gDAAC,WAAW,AAAC,WAMjB,OALC,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,iBAChB,eACA,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,oBACpB,kBACA;;;;;;4CAGP,KAAK,OAAO,KAAK,yBAChB,6LAAC,iNAAA,CAAA,WAAQ;gDAAC,WAAW,AAAC,WAMrB,OALC,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,iBAChB,eACA,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,oBACpB,kBACA;;;;;;4CAGP,KAAK,OAAO,KAAK,8BAChB,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAW,AAAC,WAMlB,OALC,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,iBAChB,eACA,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,oBACpB,kBACA;;;;;;0DAGR,6LAAC;gDACC,MAAM,KAAK,IAAI;gDACf,mBAAmB;;;;;;;;;;;iGAIvB,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAM,KAAK,GAAG;wCACd,WAAW,AAAC,0EAMX,OALC,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,iBAChB,mCACA,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,oBACpB,sCACA;wCAEN,OAAO,KAAK,WAAW;;4CAEtB,KAAK,OAAO,KAAK,wBAChB,6LAAC,sMAAA,CAAA,OAAI;gDAAC,WAAW,AAAC,WAMjB,OALC,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,iBAChB,eACA,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,oBACpB,kBACA;;;;;;4CAGP,KAAK,OAAO,KAAK,yBAChB,6LAAC,iNAAA,CAAA,WAAQ;gDAAC,WAAW,AAAC,WAMrB,OALC,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,iBAChB,eACA,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,oBACpB,kBACA;;;;;;4CAGP,KAAK,OAAO,KAAK,8BAChB,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAW,AAAC,WAMlB,OALC,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,iBAChB,eACA,CAAA,sBAAA,gCAAA,UAAW,QAAQ,CAAC,oBACpB,kBACA;;;;;;0DAGR,6LAAC;gDACC,MAAM,KAAK,IAAI;gDACf,mBAAmB;;;;;;;;;;;;;+BAxGpB;;;;;;;;;;;;;;;;;;;;;;AAmHrB;IA5JM;MAAA;AA+JC,MAAM,sBAAsB,SAAC;QAAU,0EAAS,CAAC;IACtD,MAAM,iBAAiB;QACrB,MAAM;QACN,KAAK;QACL,SAAS;QACT,aAAa;IACf;IAEA,MAAM,oBAAoB;QACxB,MAAM,CAAC,OAAS;gBACd;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,AAAC,UAAc,OAAL;oBACf,SAAS;oBACT,aAAa,AAAC,GAA8B,OAA5B,OAAO,KAAK,IAAI,aAAY;gBAC9C;aACD;QAED,SAAS,CAAC,UAAY;gBACpB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,AAAC,aAAoB,OAAR;oBAClB,SAAS;oBACT,SAAS;oBACT,aAAa,AAAC,GAA0B,OAAxB,OAAO,KAAK,IAAI,SAAQ;gBAC1C;aACD;QAED,UAAU,CAAC,UAAY;gBACrB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,WAAW,IAAI;oBAC5B,KAAK,AAAC,cAAqB,OAAR;oBACnB,SAAS;oBACT,aAAa,AAAC,GAAgC,OAA9B,OAAO,WAAW,IAAI,SAAQ;gBAChD;aACD;QAED,SAAS,CAAC,OAAS;gBACjB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,AAAC,YAAgB,OAAL;oBACjB,SAAS;oBACT,aAAa,OAAO,WAAW,IAAI,AAAC,GAAO,OAAL,MAAK;gBAC7C;aACD;QAED,gBAAgB,CAAC,OAAS;gBACxB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,AAAC,iBAAqB,OAAL;oBACtB,SAAS;oBACT,aAAa,OAAO,WAAW,IAAI,AAAC,GAAO,OAAL,MAAK;gBAC7C;aACD;QAED,UAAU,CAAC,WAAa;gBACtB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,AAAC,eAAuB,OAAT;oBACpB,SAAS;oBACT,aAAa,OAAO,WAAW,IAAI,AAAC,GAAW,OAAT,UAAS;gBACjD;aACD;QAED,SAAS,CAAC,UAAY;gBACpB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,aAAa;gBACf;gBACA;oBACE,MAAM,OAAO,KAAK,IAAI;oBACtB,KAAK,AAAC,YAAmB,OAAR;oBACjB,SAAS;oBACT,aAAa,OAAO,WAAW,IAAI,AAAC,GAAU,OAAR,SAAQ;gBAChD;aACD;QAED,QAAQ,CAAC,UAAU,MAAQ;gBACzB;gBACA;oBACE,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,SAAS,OAAO,OAAO;oBACvB,aAAa,OAAO,WAAW,IAAI,AAAC,GAAW,OAAT,UAAS;gBACjD;aACD;IACH;IAEA,OAAO,iBAAiB,CAAC,SAAS,IAAI,CAAC,IAAM;YAAC;SAAe;AAC/D;uCAEe", "debugId": null}}, {"offset": {"line": 412, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/training/components/HeroBento.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport { Users, Star, Clock, Award } from 'lucide-react';\r\nimport BreadcrumbNavigation from '../../common/components/BreadcrumbNavigation';\r\n\r\nexport default function HeroBento({ content }) {\r\n  // Default content for backward compatibility\r\n  const defaultContent = {\r\n    breadcrumbItems: [\r\n      {\r\n        name: \"Home\",\r\n        url: \"/\",\r\n        iconKey: \"home\",\r\n        description: \"Return to homepage\"\r\n      },\r\n      {\r\n        name: \"Training\",\r\n        url: \"/training\",\r\n        current: true,\r\n        iconKey: \"graduation-cap\",\r\n        description: \"Explore SecurityLit's cybersecurity training programs\"\r\n      }\r\n    ],\r\n    title: \"Elite Security Training\",\r\n    subtitle: \"Launch Your Cyber Security Career\",\r\n    tagline: \"Free and Premium Pathways\",\r\n    description: \"Dive into the world of penetration testing with our refined program. Designed for aspiring security professionals with complete pentesting skills and real-time live projects.\",\r\n    keyBenefits: [\r\n      \"Complete pentesting skills with real-time live projects\",\r\n      \"Latest tools and topics in cybersecurity\",\r\n      \"Free and premium pathways available\"\r\n    ],\r\n    buttons: [\r\n      {\r\n        text: \"Start Free Training\",\r\n        href: \"#form\",\r\n        primary: true\r\n      },\r\n      {\r\n        text: \"Upgrade to Premium\",\r\n        href: \"#premium\",\r\n        primary: false\r\n      }\r\n    ],\r\n    heroImage: \"/images/p1s1.png\"\r\n  };\r\n\r\n  const heroContent = content || defaultContent;\r\n  const { breadcrumbItems, title, subtitle, secondaryImage } = heroContent;\r\n\r\n  const trustStats = [\r\n    { value: \"500+\", label: \"Students Trained\", icon: Users },\r\n    { value: \"98%\", label: \"Success Rate\", icon: Star },\r\n    { value: \"83\", label: \"Total Lessons\", icon: Clock },\r\n    { value: \"11\", label: \"Course Sections\", icon: Award }\r\n  ];\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-white\">\r\n      <div className=\"flex flex-col lg:flex-row min-h-screen relative\">\r\n\r\n        {/* Left Section - Optimized Content with Rounded Corner */}\r\n        <div className=\"w-full lg:w-1/2 bg-gradient-to-br from-[var(--color-dark-blue)] to-[var(--color-dark-blue-hover)] relative overflow-hidden lg:rounded-br-[100px]\">\r\n          {/* Background Pattern */}\r\n          <div className=\"absolute inset-0 bg-cover bg-center bg-no-repeat opacity-10\"\r\n               style={{ backgroundImage: 'url(/images/hexagon.svg)' }}>\r\n          </div>\r\n          <div className=\"absolute inset-0 bg-[var(--color-dark-blue)]/90 lg:rounded-br-[100px]\"></div>\r\n          \r\n          <div className=\"relative z-10 flex justify-center px-4 sm:px-6 lg:px-12 py-8 sm:py-12 lg:pt-20 lg:pb-8 min-h-screen lg:h-full\">\r\n            <div className=\"max-w-lg w-full flex flex-col justify-center\">\r\n              {/* Breadcrumb */}\r\n              <div className=\"mb-4 mt-2 lg:mt-0\">\r\n                <BreadcrumbNavigation items={breadcrumbItems} className=\"text-white\" />\r\n              </div>\r\n\r\n              {/* Main Heading */}\r\n              <h1 className=\"text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-4 leading-tight font-poppins\">\r\n                {title.includes('Security') ? (\r\n                  <>\r\n                    Elite Security\r\n                    <span className=\"block bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] bg-clip-text text-transparent\">\r\n                      Training\r\n                    </span>\r\n                  </>\r\n                ) : (\r\n                  title\r\n                )}\r\n              </h1>\r\n\r\n              {/* Subtitle */}\r\n              {subtitle && (\r\n                <h2 className=\"text-lg sm:text-xl lg:text-2xl font-bold text-white mb-6 leading-tight font-poppins\">\r\n                  {subtitle}\r\n                </h2>\r\n              )}\r\n\r\n              {/* Description */}\r\n              <p className=\"text-sm sm:text-base text-white/90 mb-8 leading-relaxed font-roboto\">\r\n                Dive Into The World Of Penetration Testing With Our Refined Program\r\n              </p>\r\n\r\n              {/* CTA Buttons */}\r\n              <div className=\"flex flex-col gap-3\">\r\n                <a\r\n                  href=\"#form\"\r\n                  className=\"bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] text-white px-6 py-3 rounded-lg font-semibold text-base transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center gap-2 font-poppins\"\r\n                >\r\n                  Enroll Now\r\n                </a>\r\n\r\n                <a\r\n                  href=\"/CyberSecTraining\"\r\n                  className=\"bg-white/10 backdrop-blur-sm text-white border-2 border-white/20 hover:bg-white/20 px-6 py-3 rounded-lg font-semibold text-base transition-all duration-300 flex items-center justify-center gap-2 font-poppins\"\r\n                >\r\n                  Program Details\r\n                </a>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n\r\n\r\n        {/* Right Section - Enhanced Visual Focus */}\r\n        <div className=\"w-full lg:w-1/2 bg-white\">\r\n          <div className=\"max-w-2xl mx-auto px-4 sm:px-6 lg:px-12 py-8 sm:py-12 lg:pt-20 lg:pb-8 min-h-screen lg:h-full flex flex-col justify-center\">\r\n            \r\n            {/* Enhanced Hero Visual */}\r\n            <div className=\"relative mb-6\">\r\n              <div className=\"relative bg-gradient-to-br from-[var(--color-blue)]/10 to-[var(--color-blue-secondary)]/10 rounded-2xl p-4 sm:p-6 lg:p-8 border-2 border-[var(--color-blue)]/20\">\r\n                <div className=\"absolute inset-0 bg-gradient-to-br from-[var(--color-blue)]/5 to-[var(--color-blue-secondary)]/5 rounded-2xl\"></div>\r\n                \r\n                <div className=\"relative z-10 text-center\">\r\n                  <div className=\"w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-xl flex items-center justify-center mx-auto mb-4 shadow-lg\">\r\n                    <img\r\n                      src=\"/SecurityLit_Icon_White.png\"\r\n                      alt=\"SecurityLit Logo\"\r\n                      className=\"w-8 h-8 sm:w-12 sm:h-12 object-contain\"\r\n                    />\r\n                  </div>\r\n\r\n                  <h3 className=\"text-lg sm:text-xl font-bold text-[var(--color-dark-blue)] mb-2\">\r\n                    SecurityLit Presents\r\n                  </h3>\r\n                  <p className=\"text-[var(--foreground-secondary)] text-sm sm:text-base\">\r\n                    Professional cybersecurity training designed by industry experts\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Trust Indicators */}\r\n            <div className=\"grid grid-cols-2 gap-3 sm:gap-4 mb-6\">\r\n              {trustStats.map((stat, index) => (\r\n                <div key={index} className=\"text-center\">\r\n                  <div className=\"w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-br from-[var(--color-blue)]/10 to-[var(--color-blue-secondary)]/10 rounded-lg flex items-center justify-center mx-auto mb-2\">\r\n                    <stat.icon className=\"w-4 h-4 sm:w-5 sm:h-5 text-[var(--color-blue)]\" />\r\n                  </div>\r\n                  <div className=\"text-xl sm:text-2xl lg:text-3xl font-bold text-[var(--color-dark-blue)] mb-1 font-poppins\">\r\n                    {stat.value}\r\n                  </div>\r\n                  <div className=\"text-[var(--foreground-secondary)] text-xs font-medium font-roboto\">\r\n                    {stat.label}\r\n                  </div>\r\n                </div>\r\n              ))}\r\n            </div>\r\n\r\n\r\n\r\n            {secondaryImage && (\r\n              <div className=\"text-center mt-6\">\r\n                <img\r\n                  src={secondaryImage}\r\n                  alt=\"Cybersecurity Expert\"\r\n                  className=\"w-full max-w-md mx-auto rounded-lg\"\r\n                />\r\n              </div>\r\n            )}\r\n\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAMe,SAAS,UAAU,KAAW;QAAX,EAAE,OAAO,EAAE,GAAX;IAChC,6CAA6C;IAC7C,MAAM,iBAAiB;QACrB,iBAAiB;YACf;gBACE,MAAM;gBACN,KAAK;gBACL,SAAS;gBACT,aAAa;YACf;YACA;gBACE,MAAM;gBACN,KAAK;gBACL,SAAS;gBACT,SAAS;gBACT,aAAa;YACf;SACD;QACD,OAAO;QACP,UAAU;QACV,SAAS;QACT,aAAa;QACb,aAAa;YACX;YACA;YACA;SACD;QACD,SAAS;YACP;gBACE,MAAM;gBACN,MAAM;gBACN,SAAS;YACX;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,SAAS;YACX;SACD;QACD,WAAW;IACb;IAEA,MAAM,cAAc,WAAW;IAC/B,MAAM,EAAE,eAAe,EAAE,KAAK,EAAE,QAAQ,EAAE,cAAc,EAAE,GAAG;IAE7D,MAAM,aAAa;QACjB;YAAE,OAAO;YAAQ,OAAO;YAAoB,MAAM,uMAAA,CAAA,QAAK;QAAC;QACxD;YAAE,OAAO;YAAO,OAAO;YAAgB,MAAM,qMAAA,CAAA,OAAI;QAAC;QAClD;YAAE,OAAO;YAAM,OAAO;YAAiB,MAAM,uMAAA,CAAA,QAAK;QAAC;QACnD;YAAE,OAAO;YAAM,OAAO;YAAmB,MAAM,uMAAA,CAAA,QAAK;QAAC;KACtD;IAED,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAGb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;4BACV,OAAO;gCAAE,iBAAiB;4BAA2B;;;;;;sCAE1D,6LAAC;4BAAI,WAAU;;;;;;sCAEf,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,8JAAA,CAAA,UAAoB;4CAAC,OAAO;4CAAiB,WAAU;;;;;;;;;;;kDAI1D,6LAAC;wCAAG,WAAU;kDACX,MAAM,QAAQ,CAAC,4BACd;;gDAAE;8DAEA,6LAAC;oDAAK,WAAU;8DAAiH;;;;;;;2DAKnI;;;;;;oCAKH,0BACC,6LAAC;wCAAG,WAAU;kDACX;;;;;;kDAKL,6LAAC;wCAAE,WAAU;kDAAsE;;;;;;kDAKnF,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,WAAU;0DACX;;;;;;0DAID,6LAAC;gDACC,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAWT,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CAGb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDAEf,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDACC,KAAI;wDACJ,KAAI;wDACJ,WAAU;;;;;;;;;;;8DAId,6LAAC;oDAAG,WAAU;8DAAkE;;;;;;8DAGhF,6LAAC;oDAAE,WAAU;8DAA0D;;;;;;;;;;;;;;;;;;;;;;;0CAQ7E,6LAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,MAAM,sBACrB,6LAAC;wCAAgB,WAAU;;0DACzB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;;;;;;0DAEvB,6LAAC;gDAAI,WAAU;0DACZ,KAAK,KAAK;;;;;;0DAEb,6LAAC;gDAAI,WAAU;0DACZ,KAAK,KAAK;;;;;;;uCARL;;;;;;;;;;4BAgBb,gCACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,KAAK;oCACL,KAAI;oCACJ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU5B;KArLwB", "debugId": null}}, {"offset": {"line": 784, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/training/components/WhoCanJoin.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\n\r\nconst audienceTypes = [\r\n  {\r\n    icon: User,\r\n    title: \"Cybersecurity Enthusiasts\",\r\n    subtitle: \"All Experience Levels\",\r\n    description: \"Whether you're new to the field or have some experience, our program caters to learners of all levels. Start with the free tier to build a strong foundation, then upgrade to the premium tier for a more immersive, mentor-guided experience.\",\r\n    features: [\"Free tier available\", \"Progressive learning path\", \"Mentor-guided experience\"],\r\n    color: \"from-[var(--color-blue)] to-[var(--color-blue-secondary)]\",\r\n    badge: \"All Levels\",\r\n    delay: 0.1\r\n  },\r\n  {\r\n    icon: GraduationCap,\r\n    title: \"Information Security Professionals\",\r\n    subtitle: \"Experienced Practitioners\",\r\n    description: \"If you have been practicing cybersecurity topics, you can join the premium tier to take your skills to the next level through hands-on projects and industry-relevant training.\",\r\n    features: [\"Advanced skill development\", \"Industry-relevant training\", \"Hands-on projects\"],\r\n    color: \"from-[var(--color-dark-blue)] to-[var(--color-dark-blue-hover)]\",\r\n    badge: \"Advanced\",\r\n    delay: 0.3\r\n  },\r\n  {\r\n    icon: Briefcase,\r\n    title: \"Career Switchers\",\r\n    subtitle: \"Transitioning to Security\",\r\n    description: \"Looking to transition into the exciting world of cybersecurity? Our program provides the knowledge and practical experience you need to kickstart your career in this high-demand field.\",\r\n    features: [\"Career transition support\", \"Practical experience\", \"High-demand field\"],\r\n    color: \"from-[var(--color-yellow)] to-[var(--color-yellow-hover)]\",\r\n    badge: \"Career Change\",\r\n    delay: 0.5\r\n  }\r\n];\r\n\r\nexport default function WhoCanJoin({ content }) {\r\n  // Use content if provided, otherwise use default\r\n  const sectionContent = content || {\r\n    title: \"So Who Can Take Up This Training?\",\r\n    description: \"Our program is designed to be accessible and beneficial for a wide range of learners, from beginners to experienced professionals. Whether you're looking to start your cybersecurity journey or take it to new heights, we've got you covered.\",\r\n    targetAudience: audienceTypes.map(type => ({\r\n      title: type.title,\r\n      description: type.description\r\n    })),\r\n    bottomImage: \"/images/TrainingHacker.png\"\r\n  };\r\n  return (\r\n    <section className=\"py-12 sm:py-16 lg:py-24 bg-white relative overflow-hidden\">\r\n      {/* Background Elements */}\r\n      <div className=\"absolute inset-0\">\r\n        <div className=\"absolute top-0 left-1/4 w-96 h-96 bg-gradient-to-br from-[var(--color-blue)]/10 to-[var(--color-blue-secondary)]/10 rounded-full blur-3xl\"></div>\r\n        <div className=\"absolute bottom-0 right-1/4 w-96 h-96 bg-gradient-to-br from-[var(--color-yellow)]/10 to-[var(--color-yellow-hover)]/10 rounded-full blur-3xl\"></div>\r\n      </div>\r\n\r\n      <div className=\"relative z-10 container mx-auto px-4 max-w-7xl\">\r\n        {/* Header */}\r\n        <div className=\"text-center mb-16\">\r\n          <h2 className=\"text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-bold text-[var(--color-dark-blue)] mb-6 font-poppins\">\r\n            {sectionContent.title}\r\n          </h2>\r\n\r\n          <p className=\"text-base sm:text-lg lg:text-xl text-[var(--foreground-secondary)] max-w-4xl mx-auto leading-relaxed px-4 font-roboto\">\r\n            {sectionContent.description}\r\n          </p>\r\n        </div>\r\n\r\n        {/* Cards with Equal Heights */}\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 lg:gap-12\">\r\n          {(sectionContent.targetAudience || audienceTypes).map((audience, index) => {\r\n            const audienceType = audienceTypes[index] || audienceTypes[0]; // Fallback for styling\r\n            return (\r\n            <div key={index} className=\"h-full\">\r\n              {/* Simple Professional Card */}\r\n              <div className=\"bg-white rounded-lg p-6 shadow-md border border-gray-200 h-full flex flex-col\">\r\n\r\n                {/* Icon Container */}\r\n                <div className=\"w-12 h-12 bg-[var(--color-blue)] rounded-lg flex items-center justify-center mb-4\">\r\n                  <img\r\n                    src=\"/images/block.png\"\r\n                    alt=\"Target Audience Icon\"\r\n                    className=\"w-6 h-6\"\r\n                  />\r\n                </div>\r\n\r\n                {/* Content */}\r\n                <div className=\"flex-1\">\r\n                  <h4 className=\"text-xl font-bold text-[var(--color-dark-blue)] mb-3 font-poppins\">\r\n                    {audience.title}\r\n                  </h4>\r\n\r\n                  <p className=\"text-[var(--foreground-secondary)] leading-relaxed font-roboto\">\r\n                    {audience.description}\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            );\r\n          })}\r\n        </div>\r\n\r\n\r\n      </div>\r\n    </section>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIA,MAAM,gBAAgB;IACpB;QACE,MAAM;QACN,OAAO;QACP,UAAU;QACV,aAAa;QACb,UAAU;YAAC;YAAuB;YAA6B;SAA2B;QAC1F,OAAO;QACP,OAAO;QACP,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;QACP,UAAU;QACV,aAAa;QACb,UAAU;YAAC;YAA8B;YAA8B;SAAoB;QAC3F,OAAO;QACP,OAAO;QACP,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;QACP,UAAU;QACV,aAAa;QACb,UAAU;YAAC;YAA6B;YAAwB;SAAoB;QACpF,OAAO;QACP,OAAO;QACP,OAAO;IACT;CACD;AAEc,SAAS,WAAW,KAAW;QAAX,EAAE,OAAO,EAAE,GAAX;IACjC,iDAAiD;IACjD,MAAM,iBAAiB,WAAW;QAChC,OAAO;QACP,aAAa;QACb,gBAAgB,cAAc,GAAG,CAAC,CAAA,OAAQ,CAAC;gBACzC,OAAO,KAAK,KAAK;gBACjB,aAAa,KAAK,WAAW;YAC/B,CAAC;QACD,aAAa;IACf;IACA,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CACX,eAAe,KAAK;;;;;;0CAGvB,6LAAC;gCAAE,WAAU;0CACV,eAAe,WAAW;;;;;;;;;;;;kCAK/B,6LAAC;wBAAI,WAAU;kCACZ,CAAC,eAAe,cAAc,IAAI,aAAa,EAAE,GAAG,CAAC,CAAC,UAAU;4BAC/D,MAAM,eAAe,aAAa,CAAC,MAAM,IAAI,aAAa,CAAC,EAAE,EAAE,uBAAuB;4BACtF,qBACA,6LAAC;gCAAgB,WAAU;0CAEzB,cAAA,6LAAC;oCAAI,WAAU;;sDAGb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDACC,KAAI;gDACJ,KAAI;gDACJ,WAAU;;;;;;;;;;;sDAKd,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DACX,SAAS,KAAK;;;;;;8DAGjB,6LAAC;oDAAE,WAAU;8DACV,SAAS,WAAW;;;;;;;;;;;;;;;;;;+BApBnB;;;;;wBA0BZ;;;;;;;;;;;;;;;;;;AAOV;KArEwB", "debugId": null}}, {"offset": {"line": 994, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/training/components/LearningModules.jsx"], "sourcesContent": ["\"use client\";\n\nimport React from 'react';\n\nexport default function LearningModules({ content }) {\n  // Use content if provided, otherwise use default modules\n  const moduleContent = content || {\n    title: \"Here's A Short Teaser Of What You Will Learn\",\n    modules: [\n      {\n        title: \"Web Application Security\",\n        description: \"Fortifying the digital frontline against cyber threats.\"\n      },\n      {\n        title: \"API & Network Security\", \n        description: \"Safeguarding the backbone of modern interconnected systems.\"\n      },\n      {\n        title: \"Practical Skills Development\",\n        description: \"Honing real-world cybersecurity expertise through hands-on learning.\"\n      },\n      {\n        title: \"Soft Skill & Professional Growth\",\n        description: \"Cultivating the human element in technical cybersecurity roles.\"\n      },\n      {\n        title: \"Real World Environment Navigation\",\n        description: \"Mastering the art of securing complex, live digital ecosystems.\"\n      },\n      {\n        title: \"Active Directory & Cloud Security\",\n        description: \"Protecting the nerve centers of enterprise and cloud infrastructures.\"\n      },\n      {\n        title: \"Continuous Learning & Adaption\",\n        description: \"Staying ahead in the ever-evolving cybersecurity landscape.\"\n      },\n      {\n        title: \"Report Writing Skills\",\n        description: \"Crafting clear, concise, and impactful cybersecurity documentation for stakeholders at all levels.\"\n      }\n    ]\n  };\n\n  return (\n    <section className=\"py-16 lg:py-24 bg-gradient-to-br from-gray-50 to-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        \n        {/* Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-3xl sm:text-4xl lg:text-5xl font-bold text-[var(--color-dark-blue)] mb-6 font-poppins\">\n            {moduleContent.title}\n          </h2>\n        </div>\n\n        {/* Learning Areas Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 lg:gap-8\">\n          {moduleContent.modules.map((module, index) => (\n            <div key={index} className=\"group\">\n              <div className=\"bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 group-hover:border-[var(--color-blue)]/20 h-full flex flex-col\">\n                \n                {/* Icon */}\n                <div className=\"w-12 h-12 bg-gradient-to-br from-[var(--color-blue)]/10 to-[var(--color-blue-secondary)]/10 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform\">\n                  <img \n                    src=\"/images/web.png\" \n                    alt=\"Web Security Icon\" \n                    className=\"w-6 h-6\"\n                  />\n                </div>\n\n                {/* Content */}\n                <h5 className=\"text-lg font-bold text-[var(--color-dark-blue)] mb-3 font-poppins\">\n                  {module.title}\n                </h5>\n                \n                <p className=\"text-[var(--foreground-secondary)] text-sm leading-relaxed flex-grow font-roboto\">\n                  {module.description}\n                </p>\n              </div>\n            </div>\n          ))}\n        </div>\n\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS,gBAAgB,KAAW;QAAX,EAAE,OAAO,EAAE,GAAX;IACtC,yDAAyD;IACzD,MAAM,gBAAgB,WAAW;QAC/B,OAAO;QACP,SAAS;YACP;gBACE,OAAO;gBACP,aAAa;YACf;YACA;gBACE,OAAO;gBACP,aAAa;YACf;YACA;gBACE,OAAO;gBACP,aAAa;YACf;YACA;gBACE,OAAO;gBACP,aAAa;YACf;YACA;gBACE,OAAO;gBACP,aAAa;YACf;YACA;gBACE,OAAO;gBACP,aAAa;YACf;YACA;gBACE,OAAO;gBACP,aAAa;YACf;YACA;gBACE,OAAO;gBACP,aAAa;YACf;SACD;IACH;IAEA,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BAGb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAG,WAAU;kCACX,cAAc,KAAK;;;;;;;;;;;8BAKxB,6LAAC;oBAAI,WAAU;8BACZ,cAAc,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAClC,6LAAC;4BAAgB,WAAU;sCACzB,cAAA,6LAAC;gCAAI,WAAU;;kDAGb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,KAAI;4CACJ,KAAI;4CACJ,WAAU;;;;;;;;;;;kDAKd,6LAAC;wCAAG,WAAU;kDACX,OAAO,KAAK;;;;;;kDAGf,6LAAC;wCAAE,WAAU;kDACV,OAAO,WAAW;;;;;;;;;;;;2BAlBf;;;;;;;;;;;;;;;;;;;;;AA4BtB;KAlFwB", "debugId": null}}, {"offset": {"line": 1141, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/training/components/TrainingAccessForm.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState } from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport { Shield, User, Mail, Phone, Building, GraduationCap, ArrowRight, CheckCircle } from 'lucide-react';\r\n\r\nexport default function TrainingAccessForm() {\r\n  const [formData, setFormData] = useState({\r\n    name: '',\r\n    organization: '',\r\n    phone: '',\r\n    email: '',\r\n    disclaimer: false,\r\n    terms: false\r\n  });\r\n\r\n  const handleInputChange = (e) => {\r\n    const { name, value, type, checked } = e.target;\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      [name]: type === 'checkbox' ? checked : value\r\n    }));\r\n  };\r\n\r\n  const handleSubmit = (e) => {\r\n    e.preventDefault();\r\n    // Handle form submission\r\n    console.log('Form submitted:', formData);\r\n  };\r\n\r\n  return (\r\n    <section className=\"py-24 bg-gradient-to-br from-[var(--color-dark-blue)] via-[var(--color-dark-blue-hover)] to-[var(--color-dark-blue-bg)] relative overflow-hidden\">\r\n      {/* Background Pattern */}\r\n      <div className=\"absolute inset-0\">\r\n        <div className=\"absolute top-0 left-0 w-full h-full bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.03%22%3E%3Ccircle%20cx%3D%2230%22%20cy%3D%2230%22%20r%3D%221%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-50\"></div>\r\n      </div>\r\n\r\n      <div className=\"relative z-10 container mx-auto px-4 max-w-7xl\">\r\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center\">\r\n          {/* Content */}\r\n          <motion.div \r\n            initial={{ opacity: 0, x: -50 }}\r\n            whileInView={{ opacity: 1, x: 0 }}\r\n            transition={{ duration: 0.8 }}\r\n            viewport={{ once: true }}\r\n            className=\"space-y-8\"\r\n          >\r\n            <div>\r\n              <motion.div \r\n                initial={{ opacity: 0, scale: 0.8 }}\r\n                whileInView={{ opacity: 1, scale: 1 }}\r\n                transition={{ delay: 0.2, duration: 0.6 }}\r\n                viewport={{ once: true }}\r\n                className=\"inline-flex items-center bg-[var(--color-blue)]/20 px-4 py-2 rounded-full mb-6\"\r\n              >\r\n                <Shield className=\"w-4 h-4 text-[var(--color-blue)] mr-2\" />\r\n                <span className=\"text-sm font-medium text-[var(--color-blue)]\">Free Access</span>\r\n              </motion.div>\r\n              \r\n              <motion.h2 \r\n                initial={{ opacity: 0, y: 20 }}\r\n                whileInView={{ opacity: 1, y: 0 }}\r\n                transition={{ delay: 0.4, duration: 0.6 }}\r\n                viewport={{ once: true }}\r\n                className=\"text-4xl lg:text-5xl font-bold text-white mb-6\"\r\n              >\r\n                Start Your\r\n                <span className=\"block bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] bg-clip-text text-transparent\">\r\n                  Training Now!\r\n                </span>\r\n              </motion.h2>\r\n              \r\n              <motion.p \r\n                initial={{ opacity: 0, y: 20 }}\r\n                whileInView={{ opacity: 1, y: 0 }}\r\n                transition={{ delay: 0.6, duration: 0.6 }}\r\n                viewport={{ once: true }}\r\n                className=\"text-xl text-white/80 leading-relaxed mb-8\"\r\n              >\r\n                Become part of the next generation of cybersecurity professionals with SecurityLit. Our industry-aligned training program is designed to equip you with the skills needed to succeed in today's cybersecurity landscape.\r\n              </motion.p>\r\n            </div>\r\n\r\n            {/* Benefits */}\r\n            <div className=\"space-y-4\">\r\n              <div className=\"flex items-center gap-4\">\r\n                <div className=\"w-12 h-12 bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-xl flex items-center justify-center shadow-lg\">\r\n                  <CheckCircle className=\"w-6 h-6 text-white\" />\r\n                </div>\r\n                <div>\r\n                  <h3 className=\"text-white font-semibold text-lg\">Free Access</h3>\r\n                  <p className=\"text-white/70\">Get started with our comprehensive cybersecurity training program at no cost.</p>\r\n                </div>\r\n              </div>\r\n              \r\n              <div className=\"flex items-center gap-4\">\r\n                <div className=\"w-12 h-12 bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-xl flex items-center justify-center shadow-lg\">\r\n                  <GraduationCap className=\"w-6 h-6 text-white\" />\r\n                </div>\r\n                <div>\r\n                  <h3 className=\"text-white font-semibold text-lg\">Industry-Relevant Skills</h3>\r\n                  <p className=\"text-white/70\">Learn the latest tools and techniques used by cybersecurity professionals.</p>\r\n                </div>\r\n              </div>\r\n              \r\n              <div className=\"flex items-center gap-4\">\r\n                <div className=\"w-12 h-12 bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-xl flex items-center justify-center shadow-lg\">\r\n                  <Shield className=\"w-6 h-6 text-white\" />\r\n                </div>\r\n                <div>\r\n                  <h3 className=\"text-white font-semibold text-lg\">Career Ready</h3>\r\n                  <p className=\"text-white/70\">Develop practical skills that prepare you for real-world cybersecurity challenges.</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </motion.div>\r\n\r\n          {/* Form */}\r\n          <motion.div \r\n            initial={{ opacity: 0, x: 50 }}\r\n            whileInView={{ opacity: 1, x: 0 }}\r\n            transition={{ duration: 0.8 }}\r\n            viewport={{ once: true }}\r\n            className=\"relative\"\r\n          >\r\n            <div className=\"bg-white/10 backdrop-blur-xl rounded-3xl p-8 border border-white/20 shadow-2xl\">\r\n              <div className=\"text-center mb-8\">\r\n                <div className=\"w-16 h-16 bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-3xl flex items-center justify-center mx-auto mb-4\">\r\n                  <Shield className=\"w-8 h-8 text-white\" />\r\n                </div>\r\n                <h3 className=\"text-2xl font-bold text-white mb-2\">Sign Up for Free Access</h3>\r\n                <p className=\"text-white/70\">Join our Cybersecurity Training Program</p>\r\n              </div>\r\n\r\n              <form onSubmit={handleSubmit} className=\"space-y-6\">\r\n                <div>\r\n                  <label className=\"block text-white font-medium mb-2\">Name *</label>\r\n                  <div className=\"relative\">\r\n                    <User className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-white/50\" />\r\n                    <input\r\n                      type=\"text\"\r\n                      name=\"name\"\r\n                      value={formData.name}\r\n                      onChange={handleInputChange}\r\n                      required\r\n                      className=\"w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/50 focus:outline-none focus:border-[var(--color-blue)] transition-colors\"\r\n                      placeholder=\"Enter your full name\"\r\n                    />\r\n                  </div>\r\n                </div>\r\n\r\n                <div>\r\n                  <label className=\"block text-white font-medium mb-2\">Name of Organization *</label>\r\n                  <div className=\"relative\">\r\n                    <Building className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-white/50\" />\r\n                    <input\r\n                      type=\"text\"\r\n                      name=\"organization\"\r\n                      value={formData.organization}\r\n                      onChange={handleInputChange}\r\n                      required\r\n                      className=\"w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/50 focus:outline-none focus:border-[var(--color-blue)] transition-colors\"\r\n                      placeholder=\"Enter your organization name\"\r\n                    />\r\n                  </div>\r\n                </div>\r\n\r\n                <div>\r\n                  <label className=\"block text-white font-medium mb-2\">Contact Number</label>\r\n                  <div className=\"relative\">\r\n                    <Phone className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-white/50\" />\r\n                    <input\r\n                      type=\"tel\"\r\n                      name=\"phone\"\r\n                      value={formData.phone}\r\n                      onChange={handleInputChange}\r\n                      className=\"w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/50 focus:outline-none focus:border-[var(--color-blue)] transition-colors\"\r\n                      placeholder=\"Enter your phone number\"\r\n                    />\r\n                  </div>\r\n                </div>\r\n\r\n                <div>\r\n                  <label className=\"block text-white font-medium mb-2\">Work Email *</label>\r\n                  <div className=\"relative\">\r\n                    <Mail className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-white/50\" />\r\n                    <input\r\n                      type=\"email\"\r\n                      name=\"email\"\r\n                      value={formData.email}\r\n                      onChange={handleInputChange}\r\n                      required\r\n                      className=\"w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/50 focus:outline-none focus:border-[var(--color-blue)] transition-colors\"\r\n                      placeholder=\"Enter your work email\"\r\n                    />\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"space-y-4\">\r\n                  <label className=\"flex items-center gap-3 cursor-pointer\">\r\n                    <input\r\n                      type=\"checkbox\"\r\n                      name=\"disclaimer\"\r\n                      checked={formData.disclaimer}\r\n                      onChange={handleInputChange}\r\n                      required\r\n                      className=\"w-4 h-4 text-[var(--color-blue)] bg-white/10 border-white/20 rounded focus:ring-[var(--color-blue)]\"\r\n                    />\r\n                    <span className=\"text-white/80 text-sm\">\r\n                      I consent to receive communications about the security training program\r\n                    </span>\r\n                  </label>\r\n\r\n                  <label className=\"flex items-center gap-3 cursor-pointer\">\r\n                    <input\r\n                      type=\"checkbox\"\r\n                      name=\"terms\"\r\n                      checked={formData.terms}\r\n                      onChange={handleInputChange}\r\n                      required\r\n                      className=\"w-4 h-4 text-[var(--color-blue)] bg-white/10 border-white/20 rounded focus:ring-[var(--color-blue)]\"\r\n                    />\r\n                    <span className=\"text-white/80 text-sm\">\r\n                      I agree to the terms and conditions\r\n                    </span>\r\n                  </label>\r\n                </div>\r\n\r\n                <motion.button\r\n                  whileHover={{ scale: 1.02 }}\r\n                  whileTap={{ scale: 0.98 }}\r\n                  type=\"submit\"\r\n                  className=\"w-full bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] text-white py-4 px-8 rounded-xl font-bold text-lg shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center gap-2\"\r\n                >\r\n                  <Shield className=\"w-5 h-5\" />\r\n                  Submit Now\r\n                  <ArrowRight className=\"w-5 h-5\" />\r\n                </motion.button>\r\n              </form>\r\n            </div>\r\n          </motion.div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,cAAc;QACd,OAAO;QACP,OAAO;QACP,YAAY;QACZ,OAAO;IACT;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM;QAC/C,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE,SAAS,aAAa,UAAU;YAC1C,CAAC;IACH;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,yBAAyB;QACzB,QAAQ,GAAG,CAAC,mBAAmB;IACjC;IAEA,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;;;;;;;;;;0BAGjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,6LAAC;;sDACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,SAAS;gDAAG,OAAO;4CAAI;4CAClC,aAAa;gDAAE,SAAS;gDAAG,OAAO;4CAAE;4CACpC,YAAY;gDAAE,OAAO;gDAAK,UAAU;4CAAI;4CACxC,UAAU;gDAAE,MAAM;4CAAK;4CACvB,WAAU;;8DAEV,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC;oDAAK,WAAU;8DAA+C;;;;;;;;;;;;sDAGjE,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;4CACR,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,aAAa;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAChC,YAAY;gDAAE,OAAO;gDAAK,UAAU;4CAAI;4CACxC,UAAU;gDAAE,MAAM;4CAAK;4CACvB,WAAU;;gDACX;8DAEC,6LAAC;oDAAK,WAAU;8DAAiH;;;;;;;;;;;;sDAKnI,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;4CACP,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,aAAa;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAChC,YAAY;gDAAE,OAAO;gDAAK,UAAU;4CAAI;4CACxC,UAAU;gDAAE,MAAM;4CAAK;4CACvB,WAAU;sDACX;;;;;;;;;;;;8CAMH,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,8NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;;;;;;8DAEzB,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAAmC;;;;;;sEACjD,6LAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;sDAIjC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;;;;;;8DAE3B,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAAmC;;;;;;sEACjD,6LAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;sDAIjC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;8DAEpB,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAAmC;;;;;;sEACjD,6LAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOrC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;sCAEV,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;0DAEpB,6LAAC;gDAAG,WAAU;0DAAqC;;;;;;0DACnD,6LAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;kDAG/B,6LAAC;wCAAK,UAAU;wCAAc,WAAU;;0DACtC,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAAoC;;;;;;kEACrD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,6LAAC;gEACC,MAAK;gEACL,MAAK;gEACL,OAAO,SAAS,IAAI;gEACpB,UAAU;gEACV,QAAQ;gEACR,WAAU;gEACV,aAAY;;;;;;;;;;;;;;;;;;0DAKlB,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAAoC;;;;;;kEACrD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,6LAAC;gEACC,MAAK;gEACL,MAAK;gEACL,OAAO,SAAS,YAAY;gEAC5B,UAAU;gEACV,QAAQ;gEACR,WAAU;gEACV,aAAY;;;;;;;;;;;;;;;;;;0DAKlB,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAAoC;;;;;;kEACrD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,6LAAC;gEACC,MAAK;gEACL,MAAK;gEACL,OAAO,SAAS,KAAK;gEACrB,UAAU;gEACV,WAAU;gEACV,aAAY;;;;;;;;;;;;;;;;;;0DAKlB,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAAoC;;;;;;kEACrD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,6LAAC;gEACC,MAAK;gEACL,MAAK;gEACL,OAAO,SAAS,KAAK;gEACrB,UAAU;gEACV,QAAQ;gEACR,WAAU;gEACV,aAAY;;;;;;;;;;;;;;;;;;0DAKlB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAM,WAAU;;0EACf,6LAAC;gEACC,MAAK;gEACL,MAAK;gEACL,SAAS,SAAS,UAAU;gEAC5B,UAAU;gEACV,QAAQ;gEACR,WAAU;;;;;;0EAEZ,6LAAC;gEAAK,WAAU;0EAAwB;;;;;;;;;;;;kEAK1C,6LAAC;wDAAM,WAAU;;0EACf,6LAAC;gEACC,MAAK;gEACL,MAAK;gEACL,SAAS,SAAS,KAAK;gEACvB,UAAU;gEACV,QAAQ;gEACR,WAAU;;;;;;0EAEZ,6LAAC;gEAAK,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;0DAM5C,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gDACZ,YAAY;oDAAE,OAAO;gDAAK;gDAC1B,UAAU;oDAAE,OAAO;gDAAK;gDACxB,MAAK;gDACL,WAAU;;kEAEV,6LAAC,yMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAY;kEAE9B,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxC;GA/OwB;KAAA", "debugId": null}}]}