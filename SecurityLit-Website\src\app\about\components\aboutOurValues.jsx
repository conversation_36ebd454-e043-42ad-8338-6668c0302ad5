"use client";

import React, { useState } from "react";
import { Heart, User, Users, Zap, Link, Shield } from "lucide-react";

export default function AboutOurValues() {
  const [hoveredCard, setHoveredCard] = useState(null);

  const values = [
    {
      icon: <Heart className="w-8 h-8" />,
      title: "CARE",
      description: "We genuinely care about helping individuals and businesses operate in a secure cyberspace, and tailor customised solutions to meet our clients' needs"
    },
    {
      icon: <User className="w-8 h-8" />,
      title: "EMPATHY",
      description: "As a team, we have a high level of empathy and respect for each other and for the communities we serve, whether it is our employees, customers or stakeholders"
    },
    {
      icon: <Users className="w-8 h-8" />,
      title: "COLLABORATION",
      description: "We believe that a strong organisation is built on the pillars of individual strengths, so our aim is to collaborate and grow together in pursuit of a common goal"
    },
    {
      icon: <Zap className="w-8 h-8" />,
      title: "FAST MOVER",
      description: "We understand how critical time is when it comes to cyber threats, which is why we move fast to identify our customers' problems and even faster to come up with a solution"
    },
    {
      icon: <Link className="w-8 h-8" />,
      title: "COMMITMENT",
      description: "We are committed to continuously upskilling our team at all levels and to delivering on our mission of keeping your business safe online"
    },
    {
      icon: <Shield className="w-8 h-8" />,
      title: "INTEGRITY",
      description: "You can rely on us to communicate honestly and stay true to our word, as we value integrity, trust, and transparency within our team and towards our clients"
    }
  ];

  return (
    // Main section with a dark blue background
    <section 
      className="py-20 lg:py-28" 
      style={{ backgroundColor: 'var(--color-dark-blue)' }}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-12 lg:mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-2">
            OUR VALUES
          </h2>
          <p className="text-lg font-bold text-[var(--color-yellow)] mb-4">
            What we do
          </p>
          <p className="text-lg text-white/90 max-w-3xl mx-auto">
            We believe that our clients deserve the best level of care when it comes to their cybersecurity needs. That's why we hold ourselves to the highest standards, underpinned by our six core values...
          </p>
        </div>

        {/* Three-column grid design for values */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {values.map((value, index) => (
            <div 
              key={value.title} 
              className="bg-white/5 backdrop-blur-sm p-8 rounded-lg border border-white/20 transform hover:-translate-y-2 transition-transform duration-300 relative overflow-hidden"
              onMouseEnter={() => setHoveredCard(index)}
              onMouseLeave={() => setHoveredCard(null)}
            >
              {/* Yellow line animation on hover */}
              <div className="absolute top-0 left-0 w-full h-1 bg-[var(--color-yellow)]/20">
                <div 
                  className={`h-full bg-[var(--color-yellow)] transition-all duration-500 ease-out ${
                    hoveredCard === index ? 'w-full' : 'w-0'
                  }`}
                />
              </div>
              
              {/* Icon */}
              <div className="flex justify-center mb-6">
                <div className="text-white">
                  {value.icon}
                </div>
              </div>
              
              {/* Title */}
              <h3 className="text-xl font-bold text-[var(--color-yellow)] text-center mb-4">
                {value.title}
              </h3>
              
              {/* Description */}
              <p className="text-white/90 leading-relaxed text-center">
                {value.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
