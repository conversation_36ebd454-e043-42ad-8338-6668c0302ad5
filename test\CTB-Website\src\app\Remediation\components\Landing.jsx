'use client';

import React from 'react';
import Image from 'next/image';
import Button from '@/app/common/buttons/Button';

export default function CybersecurityLanding() {
  return (
    <div className="min-h-screen relative overflow-hidden" style={{
      background: 'linear-gradient(to right, #010D2C, #062575, #0835A7)'
    }}>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 xl:px-24 py-8 sm:py-12 lg:py-16 relative z-10">
        <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 items-center min-h-[80vh]">
          
          {/* Left Content */}
          <div className="space-y-6 sm:space-y-8">
            <div>
              <span className="text-ctb-green-50 text-xs sm:text-sm font-semibold tracking-widest uppercase">
                CYBERSECURITY REMEDIATION PLATFORM
              </span>
            </div>
            
            <div className="space-y-4 sm:space-y-6">
              <h1 className="text-white text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-bold leading-tight">
                From Bug Report to Fix - 
                <br />
                <span className="text-ctb-green-50"> Fast, Clear & Complete</span>
              </h1>
              
              <p className="text-slate-300 text-sm sm:text-base lg:text-lg leading-relaxed max-w-xl">
                Don&apos;t just find vulnerabilities - fix them. <span className="text-ctb-green-50">Capture The Bug</span> lets you chat with pentesters, track fixes, and verify resolutions, all in one secure dashboard. Learn more about <a href="/How-it-works" className="text-ctb-green-50 hover:underline font-semibold">how our platform works</a>.
              </p>
            </div>
            
            <div>
              <Button
                href="/Request-Demo"
                variant="primary"
                size="sm"
                className="bg-lime-500 w-1/3 hover:bg-lime-400 text-slate-900 font-semibold px-6 sm:px-8 py-3 sm:py-4 rounded-lg transition-all duration-300 flex items-center gap-3 group shadow-lg hover:shadow-xl transform hover:scale-105 text-sm sm:text-base"
              >
                Request a demo
              </Button>
           
            </div>
          </div>

          {/* Right Side - Reserved for Image */}
          <div className="flex justify-center lg:justify-end mt-8 lg:mt-0">
            <div className="w-full max-w-md sm:max-w-lg lg:max-w-xl xl:max-w-2xl">
              <Image
                src="/images/Remediation.svg"
                alt="Dashboard Home"
                width={600}
                height={400}
                className="w-full h-auto"
              />
            </div>
          </div>
        </div>
      </div>
      
    </div>
  );
}