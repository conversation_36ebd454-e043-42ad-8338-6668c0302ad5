'use client';

import React, { useState } from 'react';
import { Plus, Minus } from 'lucide-react';

const FAQItem = ({ question, answer, isOpen, onToggle, uniqueId }) => (
  <div className="mb-4">
    <button
      onClick={onToggle}
      aria-expanded={isOpen}
      className="flex w-full justify-between items-center text-left bg-gray-100 p-4 rounded-lg transition-all"
    >
      <span className="text-sm md:text-lg font-medium text-gray-900">
        {question}
      </span>
      <span className="ml-4 shrink-0">
        {isOpen ? (
          <Minus className="h-5 w-5 text-[#1e83fb]" />
        ) : (
          <Plus className="h-5 w-5 text-[#1e83fb]" />
        )}
      </span>
    </button>

    <div
      className={`transition-all duration-300 ease-in-out overflow-hidden ${
        isOpen ? 'max-h-[1000px] opacity-100 py-4' : 'max-h-0 opacity-0 py-0'
      } bg-gray-50`}
    >
      <div className="px-4">
        <p className="text-sm md:text-base text-gray-700 leading-relaxed">{answer}</p>
      </div>
    </div>
  </div>
);

const FAQ = () => {
  const [openItem, setOpenItem] = useState(null);

  const handleToggle = (uniqueId) => {
    setOpenItem((prev) => (prev === uniqueId ? null : uniqueId));
  };

 const faqs = [
  {
    id: 'network-pt-definition',
    question: 'What is Network Penetration Testing?',
    answer:
      'Network Penetration Testing involves assessing your internal and external network infrastructure to identify security vulnerabilities that could be exploited by attackers. This includes routers, switches, firewalls, servers, and other devices.',
  },
  {
    id: 'internal-vs-external',
    question: 'What’s the difference between internal and external network testing?',
    answer:
      'External testing focuses on identifying vulnerabilities in internet-facing assets, while internal testing simulates an attacker who has gained internal access to your network, assessing risks from within the organization.',
  },
  {
    id: 'network-ptaas-difference',
    question: "How does Capture The Bug's PTaaS approach benefit Network PT?",
    answer:
      "Our PTaaS solution allows for on-demand, continuous assessments of your network infrastructure. Unlike traditional periodic testing, our service integrates with your workflows and delivers real-time, actionable insights.",
  },
  {
    id: 'network-vulnerability-types',
    question: 'What kind of vulnerabilities can be identified in network tests?',
    answer:
      'We detect misconfigurations, open ports, weak authentication, outdated software, firewall bypasses, and vulnerabilities in services and protocols used across your network.',
  },
  {
    id: 'tool-support',
    question: 'Do you use automated tools or manual techniques?',
    answer:
      'Our assessments combine industry-leading automated tools with manual techniques performed by skilled ethical hackers, ensuring comprehensive and accurate vulnerability identification.',
  },
  {
    id: 'network-standards-followed',
    question: 'Which standards do you follow for Network Penetration Testing?',
    answer:
      'We follow globally recognized methodologies including OSSTMM, NIST SP 800-115, and PTES to ensure thorough and industry-aligned testing.',
  },
  {
    id: 'network-testing-process',
    question: 'What is your process for Network Penetration Testing?',
    answer:
      'Our process includes scoping and planning, enumeration and scanning, vulnerability analysis, exploitation, post-exploitation analysis, and detailed reporting with remediation guidance.',
  },
  {
    id: 'network-testing-readiness',
    question: 'How can we prepare for a network penetration test?',
    answer:
      'We recommend preparing network architecture diagrams, identifying in-scope assets, and ensuring relevant stakeholders are informed. Our team will guide you through every step to streamline the process.',
  },
  {
    id: 'compliance-benefits',
    question: 'Does network testing help with compliance?',
    answer:
      'Yes, our testing supports compliance with standards like ISO 27001, PCI-DSS, and HIPAA by uncovering network-related vulnerabilities and helping implement necessary controls.',
  },
  {
    id: 'aftercare-support',
    question: 'Do you offer support after the assessment?',
    answer:
      'Absolutely. We assist with interpreting the report, fixing vulnerabilities, and conducting retests to validate remediation, ensuring your network remains secure over time.',
  },
];


  return (
    <section className="w-full py-16 bg-white">
      <div className="max-w-6xl mx-auto px-4">
        <div className="flex flex-col md:flex-row md:gap-20 gap-10">
          {/* Left: Section Heading */}
          <div className="md:w-1/3 w-full flex items-center justify-center">
            <h2 className="text-4xl md:text-6xl font-bold text-[#1e83fb] text-center">
              FAQ
            </h2>
          </div>

          {/* Right: FAQ Items */}
          <div className="md:w-2/3 w-full flex flex-col justify-center">
            {faqs.map((faq) => (
              <FAQItem
                key={faq.id}
                uniqueId={faq.id}
                question={faq.question}
                answer={faq.answer}
                isOpen={openItem === faq.id}
                onToggle={() => handleToggle(faq.id)}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default FAQ;
