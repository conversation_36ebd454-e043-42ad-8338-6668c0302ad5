"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { CheckCircle, Code, Network, Cloud, Clock, Users, Award } from 'lucide-react';

const phases = [
  {
    id: 1,
    title: "Web And API Penetration Testing",
    description: "Our program introduces participants to VAPT (Vulnerability Assessment and Penetration Testing) for mobile and web applications, focusing on identifying and mitigating security flaws. With the growing reliance on mobile apps and APIs, security is critical for organizations.",
    icon: Code,
    color: "from-[var(--color-blue)] to-[var(--color-blue-secondary)]",
    features: ["Assignments", "Labs", "Mentoring (Premium Only)", "Hands-On Real Project (Premium Only)"],
    image: "https://securitylit.com/images/p2s3.png"
  },
  {
    id: 2,
    title: "Network and Cloud Penetration Testing",
    description: "This phase focuses on network security, equipping participants with the skills to assess and secure organizational networks and endpoints. The emphasis is on identifying vulnerabilities in network infrastructure and developing robust defenses.",
    icon: Network,
    color: "from-[var(--color-dark-blue)] to-[var(--color-dark-blue-hover)]",
    features: ["Assignments", "Labs", "Mentoring (Premium Only)", "Hands-On Real Project (Premium Only)"],
    image: "https://securitylit.com/images/p2s4.png"
  },
  {
    id: 3,
    title: "Cloud Security",
    description: "This phase introduces participants to essential cloud security practices, focusing on securing AWS environments. With more organizations moving to the cloud, understanding foundational cloud security principles is crucial for safeguarding cloud-based infrastructure.",
    icon: Cloud,
    color: "from-[var(--color-blue)] to-[var(--color-blue-secondary)]",
    features: ["Assignments", "Labs", "Mentoring (Premium Only)", "Hands-On Real Project (Premium Only)"],
    image: "https://securitylit.com/images/p2s5.png"
  }
];

export default function ProgramStructure() {
  return (
    <section className="py-24 bg-gradient-to-br from-gray-50 to-white relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-72 h-72 bg-[var(--color-blue)]/5 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob"></div>
        <div className="absolute bottom-20 right-10 w-72 h-72 bg-[var(--color-blue-secondary)]/5 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-2000"></div>
      </div>

      <div className="relative z-10 container mx-auto px-4 max-w-7xl">
        {/* Header */}
        <motion.div 
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-20"
        >
          <motion.div 
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.2, duration: 0.6 }}
            viewport={{ once: true }}
            className="inline-flex items-center bg-gradient-to-r from-[var(--color-blue)]/10 to-[var(--color-blue-secondary)]/10 px-6 py-3 rounded-full mb-8 border border-[var(--color-blue)]/20"
          >
            <Award className="w-5 h-5 text-[var(--color-blue)] mr-3" />
            <span className="text-sm font-semibold text-[var(--color-blue)]">Job-Focused Curriculum</span>
          </motion.div>
          
          <motion.h2 
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, duration: 0.6 }}
            viewport={{ once: true }}
            className="text-5xl lg:text-6xl font-bold text-[var(--color-dark-blue)] mb-8 leading-tight"
          >
            Program
            <span className="block bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] bg-clip-text text-transparent">
              Structure
            </span>
          </motion.h2>
          
          <motion.p 
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6, duration: 0.6 }}
            viewport={{ once: true }}
            className="text-xl text-[var(--foreground-secondary)] max-w-4xl mx-auto leading-relaxed"
          >
            We provide a job-focused, hands-on curriculum designed to take participants from foundational to advanced cybersecurity skills across three comprehensive phases.
          </motion.p>
        </motion.div>

        {/* Phases Grid */}
        <div className="space-y-16">
          {phases.map((phase, index) => (
            <motion.div
              key={phase.id}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.2, duration: 0.8 }}
              viewport={{ once: true }}
              className={`flex flex-col lg:flex-row items-center gap-12 ${
                index % 2 === 1 ? 'lg:flex-row-reverse' : ''
              }`}
            >
              {/* Content */}
              <div className="lg:w-1/2 space-y-6">
                <div className="flex items-center gap-4">
                  <div className={`w-16 h-16 bg-gradient-to-br ${phase.color} rounded-2xl flex items-center justify-center shadow-lg`}>
                    <phase.icon className="w-8 h-8 text-white" />
                  </div>
                  <div>
                    <div className="text-sm font-semibold text-[var(--color-blue)] uppercase tracking-wide">Phase {phase.id}</div>
                    <h3 className="text-3xl lg:text-4xl font-bold text-[var(--color-dark-blue)] leading-tight">{phase.title}</h3>
                  </div>
                </div>
                
                <p className="text-lg text-[var(--foreground-secondary)] leading-relaxed">
                  {phase.description}
                </p>

                {/* Features */}
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  {phase.features.map((feature, featureIndex) => (
                    <div key={featureIndex} className="flex items-center gap-3">
                      <CheckCircle className="w-5 h-5 text-[var(--color-blue)] flex-shrink-0" />
                      <span className="text-[var(--color-dark-blue)] font-medium">{feature}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Image */}
              <div className="lg:w-1/2">
                <div className="relative">
                  <div className={`absolute inset-0 bg-gradient-to-br ${phase.color} rounded-3xl opacity-10 blur-xl`}></div>
                  <img 
                    src={phase.image} 
                    alt={phase.title}
                    className="w-full h-auto rounded-2xl relative z-10"
                  />
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Bottom Stats */}
        <motion.div 
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8, duration: 0.6 }}
          viewport={{ once: true }}
          className="mt-20 grid grid-cols-1 md:grid-cols-3 gap-8"
        >
          <div className="text-center">
            <div className="text-4xl font-bold text-[var(--color-blue)] mb-2">3</div>
            <div className="text-[var(--foreground-secondary)]">Comprehensive Phases</div>
          </div>
          <div className="text-center">
            <div className="text-4xl font-bold text-[var(--color-dark-blue)] mb-2">6</div>
            <div className="text-[var(--foreground-secondary)]">Months Duration</div>
          </div>
          <div className="text-center">
            <div className="text-4xl font-bold text-[var(--color-blue-secondary)] mb-2">100%</div>
            <div className="text-[var(--foreground-secondary)]">Hands-On Learning</div>
          </div>
        </motion.div>
      </div>
    </section>
  );
} 