import DarkButton from "@/app/common/buttons/DarkButton";
import Image from "next/image";
import React from "react";

export default function Case1() {
  return (
    <div className="md:p-12 p-8 flex md:flex-row flex-col justify-between items-start bg-gray-50">
      <div className="Image md:w-[35%] w-full md:pr-10 md:mt-0 mt-10 mb-6 md:mb-0">
        <Image
          src="/images/Case 3-img.png"
          width={750}
          height={550}
          alt="Enterprise security testing case study demonstrating advanced penetration testing methodologies and comprehensive vulnerability assessment outcomes"
        />
      </div>

      <div className="Content md:w-[65%] w-full flex flex-col md:gap-8 gap-6 md:pl-10">
        <div className="Title text-lg md:text-xl lg:text-2xl text-black py-2 flex flex-col md:flex-row md:items-center md:gap-2">
          <div className="font-bold">Industry:</div>
          <span className="text-lg md:text-xl lg:text-2xl">Automotive</span>
        </div>

        <div className="Title text-lg md:text-xl lg:text-2xl text-black py-2 flex flex-col md:flex-row md:items-center md:gap-2">
          <div className="font-bold">Services Provided:</div>
          <span className="text-lg md:text-xl lg:text-2xl">
            Agile Pentesting
          </span>
        </div>

        <div className="description leading-8 md:pr-10 text-slate-800 text-base md:text-lg lg:text-xl text-justify">
          Our client is a fast-growing technology company within the automotive
          industry, offering innovative e-commerce solutions that help
          businesses streamline inventory management, sales, and logistics. With
          rapid growth and global market reach, the company has established
          itself as a leader in automotive tech.
        </div>

        <div className="button flex gap-2 mt-4">
          <a
            href="/Automotive Case Study.pdf"
            download="Automotive Case Study.pdf"
          >
            <DarkButton className="px-8 py-4 text-sm md:text-lg">
              Download Success Story
            </DarkButton>
          </a>
        </div>
      </div>
    </div>
  );
}
