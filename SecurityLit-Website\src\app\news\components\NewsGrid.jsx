"use client";

import React from "react";
import { ArrowRight } from "lucide-react";
import { newsItems } from "./newsData";

export default function NewsGrid() {
  return (
    <section className="py-20 bg-white relative">
      <div className="container mx-auto px-4 max-w-7xl">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-10">
          {newsItems.map((item, idx) => (
            <article
              key={idx}
              className="group bg-white rounded-3xl shadow-[0_8px_30px_rgb(0,0,0,0.08)] hover:shadow-[0_20px_40px_rgb(0,0,0,0.12)] border border-gray-100 overflow-hidden flex flex-col transition-all duration-300 focus-within:ring-2 focus-within:ring-[var(--color-blue)]"
              tabIndex={0}
            >
              <div className="relative h-56 bg-gray-100 overflow-hidden flex items-center justify-center">
                <img
                  src={item.image}
                  alt={item.alt}
                  className="object-contain h-full w-full transition-transform duration-300 group-hover:scale-105"
                  loading="lazy"
                />
                <span className="absolute top-4 left-4 bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] text-white text-xs font-semibold px-3 py-1 rounded-full shadow-lg">
                  {item.outlet}
                </span>
              </div>
              <div className="flex-1 flex flex-col p-6">
                <h3 className="text-lg font-bold text-[var(--color-dark-blue)] mb-2 line-clamp-2">
                  {item.headline}
                </h3>
                <p className="text-[var(--foreground-secondary)] text-sm mb-4 line-clamp-3">
                  {item.description}
                </p>
                <div className="mt-auto flex items-center justify-between">
                  <span className="text-xs text-gray-400">{item.date}</span>
                  <a
                    href={item.link}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center gap-2 text-[var(--color-blue)] font-semibold hover:underline focus:underline focus:outline-none"
                    aria-label={`Learn more about: ${item.headline}`}
                  >
                    Learn more
                    <ArrowRight className="w-4 h-4" />
                  </a>
                </div>
              </div>
            </article>
          ))}
        </div>
      </div>
    </section>
  );
} 