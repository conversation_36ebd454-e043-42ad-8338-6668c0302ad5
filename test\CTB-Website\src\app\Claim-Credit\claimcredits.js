
"use client";
import React, { useEffect } from 'react'
import { motion } from 'framer-motion';
import { useRouter } from 'next/navigation';
import Hero from './components/Hero'
import WhyFounders from './components/WhyFounders'
import EligibilityAndForm from './components/EligibilityAndForm'
import Closing from './components/Closing'


function Page() {
  const router = useRouter();

  useEffect(() => {
    if (window.location.hash) { 
      window.history.replaceState(null, null, window.location.pathname);
    }
    
    const timer = setTimeout(() => {
      window.scrollTo({ top: 0, behavior: 'instant' });
    }, 50);
    
    return () => clearTimeout(timer);
  }, []);

  return (
    <>
      <title>Capture The Bug | Claim Your Credit</title>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.1 }}
        >
          <Hero />
        </motion.div>
        <WhyFounders />
        <EligibilityAndForm />
        {/* <Closing /> */}
      </motion.div>
    </>
  );
}

export default Page;
