"use client"
import { useState, useEffect } from "react";
import { Star } from "lucide-react";
import Button from "../../common/buttons/Button";
import Image from 'next/image';

const topRowTestimonials = [
  {
    id: 1,
    name: "<PERSON>",
    title: "Chief Operating Officer at PARTLY",
    avatar: "/images/partly_logo.png",
    testimonial:  
      "Capture The Bug has efficiently and affordably helped us meet our cybersecurity goals. Their tailored solutions and proactive approach have fortified our defenses, providing peace of mind. The real-time bug reports and their dedicated assistance ensure we are vigilant against cyber threats."
  },
  {
    id: 2,
    name: "<PERSON>",
    title: "Senior Security and DevOps Engineer at Kademi.co",
    avatar: "/images/kademi_logo.png",
    testimonial:
      "Capture The Bug helped us with our company's security compliance needs. Their team of highly skilled and professional security experts provided a quality service at a reasonable price. We highly recommend their IT cyber security services!"
  },
  {
    id: 3,
    name: "<PERSON>",
    title: "Senior Director of Engineering at Rafay Systems.",
    avatar: "/images/rafay_logo.png",
    testimonial:
      "As a leading Kubernetes company, we understand the importance of securing our data and systems. We engage Capture The Bug's pentesting as a service platform for black box penetration testing. Their ethical hackers provided a thorough security assessment, with clear and concise reporting that included actionable recommendations. We highly recommend their platform for any organization looking to conduct comprehensive penetration testing."
  },
  {
    id: 4,
    name: "Nathan Cheeseman",
    title: "Chief Executive Officer at Forsite",
    avatar: "/images/forsite_logo_2.png",
    testimonial:
      "As a fast-moving SaaS provider, we've witnessed the significant advantages offered by Capture The Bug's platform. The ability to immediately address vulnerabilities as they are identified not only saves time for our developers but also reduces costs associated with lengthy security processes. Our collaboration with penetration testers through the platform has been seamless. We are enthusiastic about the ongoing partnership with Capture The Bug, looking forward to strengthening our security posture and further cost savings"
  },
  {
    id: 5,
    name: "Lorraine Guerin",
    title: "Chief Product Officer at Yabble",
    avatar: "/images/yabble_logo.png",
    testimonial:
      "The team at Capture The Bug have been amazing and super easy to work with. In reality, security testing is ongoing, and needs to be effective yet cost efficient. I love the CTB platform format over traditional pen testing, not sure I could go back!"
  }
];


const bottomRowTestimonials = [
  {
    id: 6,
    name: "Jeremy Peaks",
    title: "Director of Engineering - Security at EROAD",
    avatar: "/images/EROAD_Logo.webp", 
  testimonial: "Traditional pentesting from independent vendors just didn't scale for a business like ours. Waiting weeks for a final PDF report meant we couldn't act fast enough, and the process always felt disconnected from how our teams actually work. With Capture The Bug's PTaaS platform, that's changed for the better. Now, every time we launch a test - whether it's web, mobile, or infrastructure - we start getting actionable vulnerabilities much faster. It fits right into our existing workflows, so that we can react much more quickly. The real-time visibility, continuous updates, and integration with our reporting cycles mean I'm no longer chasing static reports before board meetings. We have live insights into what's open. It's given us a much faster, more scalable, and far more transparent way to manage our independent vendor offensive security-without compromising on depth or quality"
},
  {
    id: 7,
    name: "Jan Behrens",
    title: "Chief Technology Officer at Devoli",
    avatar: "/images/devoli_logo.jpeg", 
    testimonial: "At Devoli, we simplify telco for our customers across ANZ. InfoSec and data privacy are critically important to everything we do. We are a fast-moving tech company and have used various traditional pen-testing providers in the past. We were intrigued by Capture The Bug's more continuous testing approach. Three months into our journey with CTB, we are impressed by the responsiveness of their team and the immediate insights from CTB's App. We can now launch pen-test programs on-demand and start seeing results within a couple of days. The real-time visibility and collaborative workflow let's our team triage, comment, and request retests directly in the App. The workflow overall feels far more natural and is much better aligned with our delivery cadence – and provides learning opportunities for our team at the same time. If you are a fast-moving tech company looking for a more continuous security assessment as part of your development lifecycle, I would recommend checking them out."
  },
  {
    id: 8,
    name: "Jacques Labuschagne",
    title: "Chief Technology Officer at PaySauce",
    avatar: "/images/paysauce_logo.png", 
    testimonial: "Capture The Bug helped us level up our security game quickly. In just two weeks, we surfaced more relevant, high-impact vulnerabilities than we ever got from our previous pentesting vendor. The difference was clear: always-on testing, real-time visibility, and the ability to manage our entire vulnerability lifecycle-assign, comment, retest-all within the platform. Their pentesters felt like an extension of our team, and the quality of reports made stakeholder communication effortless. For any listed company that needs continuous assurance and speed without compromising depth, Capture The Bug is the platform to trust."
  },
  {
    id: 9,
    name: "Sarah Webb",
    title: "Chief Operating Officer at LawVu",
    avatar: "/images/lawvu.jpg", 
    testimonial: "Capture The Bug's continuous pentesting approach has been a game-changer for us at LawVu. By integrating their solution, we've significantly reduced the time our development team spends on security tasks, leading to both time and cost savings. Their platform's real-time insights and seamless integration into our workflow have enhanced our security posture without disrupting our development cycles."
  },
  {
    id: 10,
    name: "Shai Bhula",
    title: "Chief Technology Officer at Whip Around",
    avatar: "/images/whiparound_logo.png", 
    testimonial: `We recently partnered with Capture The Bug for a penetration test, and the experience went far beyond a traditional security engagement. The platform made it easy to scope, schedule, and track the test in real time-no long email chains or delays.
What really impressed us was the clarity of the findings and the usability of the platform. Our engineering team could view vulnerabilities as they were discovered, complete with context, repro steps, and clear prioritization. The final report was audit-ready and mapped directly to our compliance requirements.
CTB worked closely with us to ensure the testing aligned with our environment, and were transparent throughout about both strengths and areas of risk. It's a modern, scalable approach to pentesting-and one we'll continue to use as part of our ongoing security efforts`
  },
  {
    id: 11,
    name: "Tomas Brown",
    title: "Software Engineer at ImmerseMe",
    avatar: "/images/immerseme_logo.jpeg", 
    testimonial: `Working with Capture The Bug made our first pentest feel effortless. After reviewing multiple vendors, it was clear their approach stood out-fast onboarding, clear communication, and a platform that just made sense for our dev team.
Throughout the test, we had full visibility into progress and could easily collaborate on findings using built-in feedback tools. The bug reports were clear, actionable, and easy to discuss with our internal team.
The experience felt less like working with a vendor and more like adding a true extension to our engineering workflow. We'll definitely be partnering with them again.`
  }
];


export default function MovingTestimonials() {
  const [expandedCards, setExpandedCards] = useState(new Set());

  
  const countWords = (text) => {
    return text.trim().split(/\s+/).length;
  };

  const truncateText = (text, wordLimit = 50) => {
    const words = text.trim().split(/\s+/);
    if (words.length <= wordLimit) return text;
    return words.slice(0, wordLimit).join(' ') + '...';
  };

  const toggleExpand = (cardId, index) => {
    const uniqueId = `${cardId}-${index}`;
    setExpandedCards(prev => {
      const newSet = new Set(prev);
      if (newSet.has(uniqueId)) {
        newSet.delete(uniqueId);
      } else {
        newSet.add(uniqueId);
      }
      return newSet;
    });
  };

  const TestimonialCard = ({ testimonial, index }) => {
    const wordCount = countWords(testimonial.testimonial);
    const needsTruncation = wordCount > 50;
    const uniqueId = `${testimonial.id}-${index}`;
    const isExpanded = expandedCards.has(uniqueId);
    
    const displayText = needsTruncation && !isExpanded 
      ? truncateText(testimonial.testimonial, 50)
      : testimonial.testimonial;

    return (
      <div className="relative flex-shrink-0 w-80 sm:w-96 bg-white/80 backdrop-blur-sm rounded-xl p-6 sm:p-8 shadow-sm border border-gray-100/50 mx-3 sm:mx-6 hover:shadow-md transition-all duration-300 card-hover group">
      
        <div className="absolute top-3 sm:top-4 left-3 sm:left-4 text-blue-200 opacity-50">
          <svg width="20" height="20" className="sm:w-6 sm:h-6" viewBox="0 0 24 24" fill="currentColor">
            <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.509l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.509l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h4v10h-10z"/>
          </svg>
        </div>

        {/* Testimonial Text */}
        <div className="mt-6 sm:mt-8 mb-16 sm:mb-20">
          <p className="text-gray-900 text-sm sm:text-base leading-relaxed font-light italic text-center">
            &ldquo;{displayText}&rdquo;
          </p>
          
          {/* Show More/Less Button */}
          {needsTruncation && (
            <div className="text-center mt-3">
              <Button
                onClick={() => toggleExpand(testimonial.id, index)}
                variant="ghost"
                size="sm"
                className="text-blue-500 hover:text-blue-800 font-medium underline underline-offset-2"
              >
                {isExpanded ? 'Show less' : 'Show more'}
              </Button>
            </div>
          )}
        </div>


        <div className="absolute bottom-4 sm:bottom-6 left-4 sm:left-6 right-4 sm:right-auto">
          <div className="flex items-center">
            <div className="relative w-12 h-12 sm:w-16 sm:h-16 rounded-xl overflow-hidden mr-4 flex-shrink-0 bg-white/50 backdrop-blur-sm border border-gray-100/30 p-1 sm:p-2">
              <Image
                src={testimonial.avatar}
                alt={testimonial.name}
                className="w-full h-full object-contain scale-[110%] rounded-lg"
                width={64}
                height={64}
                onError={(e) => { 
                  e.target.style.display = 'none';
                  e.target.nextSibling.style.display = 'flex';
                }}
              />
              <div className="w-full h-full rounded-lg bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center absolute top-1 left-1 sm:top-2 sm:left-2" style={{display: 'none'}}>
                <span className="text-white font-medium text-sm sm:text-base">
                  {testimonial.name.split(' ').map(n => n[0]).join('')}
                </span>
              </div>
            </div>
            <div className="text-left min-w-0 flex-1">
              <h3 className="text-gray-800 font-medium text-sm sm:text-base truncate">
                {testimonial.name}
              </h3>
              <p className="text-gray-500 text-xs sm:text-sm font-light leading-tight">
                {testimonial.title}
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <section className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 py-16 sm:py-20 overflow-hidden relative">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='50' height='50' viewBox='0 0 50 50' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%236366f1' fill-opacity='1'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }} />
      </div>

      {/* Header */}
      <div className="text-center mb-16 sm:mb-20 px-4 relative z-10">
        <p className="text-blue-500 text-sm font-medium mb-4 tracking-wide uppercase">
          Customer Success Stories
        </p>
        <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-gray-900 mb-6">
          What Our Customers{' '}
          <span className="text-blue-500">Are Saying</span>
        </h2>
        <p className="text-gray-500 text-base sm:text-lg max-w-2xl mx-auto leading-relaxed font-light">
          See how fast-moving teams are transforming their security with Capture The Bug.
        </p>
      </div>

      {/* Moving Testimonials */}
      <div className="space-y-16 sm:space-y-24">
        {/* Top Row - Moving Left */}
        <div className="w-full overflow-hidden">
          <div className="flex animate-scroll-left-seamless pause-on-hover">
            {/* Create enough copies for seamless loop */}
            {Array(6).fill([...topRowTestimonials]).flat().map((testimonial, index) => (
              <TestimonialCard 
                key={`top-${testimonial.id}-${index}`} 
                testimonial={testimonial} 
                index={index}
              />
            ))}
          </div>
        </div>

        {/* Bottom Row - Moving Right */}
        <div className="w-full overflow-hidden">
          <div className="flex animate-scroll-right-seamless pause-on-hover">
            {/* Create enough copies for seamless loop */}
            {Array(6).fill([...bottomRowTestimonials]).flat().map((testimonial, index) => (
              <TestimonialCard 
                key={`bottom-${testimonial.id}-${index}`} 
                testimonial={testimonial} 
                index={index}
              />
            ))}
          </div>
        </div>
      </div>

      {/* Call to Action */}
      <div className="text-center mt-16 sm:mt-20 px-4 relative z-10">
        <Button
           href="/Request-Demo"
          variant="primary"
          size="lg"
          className="shadow-lg hover:shadow-xl hover:scale-105 transform text-sm sm:text-lg px-4 sm:px-6 py-2.5 sm:py-3.5"
        >
          <span className="hidden sm:inline">Start Your Success Story</span>
          <span className="sm:hidden">Start Success Story</span>
        </Button>
      </div>

      <style jsx>{`
        @keyframes scroll-left-seamless {
          0% {
            transform: translateX(0);
          }
          100% {
            transform: translateX(calc(-100% / 6));
          }
        }

        @keyframes scroll-right-seamless {
          0% {
            transform: translateX(calc(-100% / 6));
          }
          100% {
            transform: translateX(0);
          }
        }

        .animate-scroll-left-seamless {
          animation: scroll-left-seamless 120s linear infinite;
          width: calc(6 * 100%);
        }

        .animate-scroll-right-seamless {
          animation: scroll-right-seamless 140s linear infinite;
          width: calc(6 * 100%);
        }

        .card-hover:hover {
          transform: translateY(-4px);
          transition: transform 0.3s ease;
        }

        .pause-on-hover:hover {
          animation-play-state: paused;
        }

        @media (max-width: 640px) {
          .animate-scroll-left-seamless {
            animation-duration: 10s;
          }
          
          .animate-scroll-right-seamless {
            animation-duration: 70s;
          }
        }
      `}</style>
    </section>
  );
}