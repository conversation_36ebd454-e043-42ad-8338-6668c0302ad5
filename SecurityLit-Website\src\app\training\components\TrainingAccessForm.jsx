"use client";

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Shield, User, Mail, Phone, Building, GraduationCap, ArrowRight, CheckCircle, ChevronLeft, ChevronRight, Linkedin, Briefcase, BookOpen, MessageSquare, ExternalLink } from 'lucide-react';

export default function TrainingAccessForm() {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({
    disclaimer: false,
    fullName: '',
    email: '',
    phone: '',
    linkedin: '',
    occupation: '',
    experience: '',
    education: '',
    interest: '',
    source: '',
    securitySkills: 'no',
    certification: false,
    terms: false
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [apiError, setApiError] = useState("");
  const [success, setSuccess] = useState("");

  const totalSteps = 3;

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const nextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (isSubmitting) return;
    setIsSubmitting(true);
    setApiError("");
    setSuccess("");

    try {
      // Make the API request to store submission and send email
      const response = await fetch("/api/submissions", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          fullName: formData.fullName,
          email: formData.email,
          phone: formData.phone,
          linkedin: formData.linkedin,
          occupation: formData.occupation,
          experience: formData.experience,
          education: formData.education,
          interest: formData.interest,
          source: formData.source,
          securitySkills: formData.securitySkills,
          certification: formData.certification,
          disclaimer: formData.disclaimer,
          terms: formData.terms,
          formType: 'training-enrollment',
          subject: 'New Training Enrollment Application'
        }),
      });

      const result = await response.json();
      if (response.ok && result.success) {
        // Clear form fields on success
        setFormData({
          disclaimer: false,
          fullName: '',
          email: '',
          phone: '',
          linkedin: '',
          occupation: '',
          experience: '',
          education: '',
          interest: '',
          source: '',
          securitySkills: 'no',
          certification: false,
          terms: false
        });
        setCurrentStep(1); // Reset to first step
        setSuccess("Thank you! Your training application has been submitted successfully. We'll review your application and get back to you within 24 hours.");
      } else {
        setApiError(result.error || "Something went wrong. Please try again.");
      }
    } catch (error) {
      console.error("Error submitting form:", error);
      setApiError("Failed to submit application. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <div className="w-16 h-16 bg-gradient-to-br from-[var(--color-blue)] to-[var(--color-blue-secondary)] rounded-3xl flex items-center justify-center mx-auto mb-4">
                <Shield className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-[var(--color-dark-blue)] mb-2">Step 1 of {totalSteps}</h3>
              <p className="text-[var(--foreground-secondary)]">Personal Information & Disclaimer</p>
            </div>
            
            {/* Disclaimer */}
            <div className="bg-gradient-to-br from-[var(--color-blue)]/10 to-[var(--color-blue-secondary)]/10 rounded-2xl p-6 border border-[var(--color-blue)]/20">
              <p className="text-[var(--color-dark-blue)] leading-relaxed mb-4">
                By submitting this form, you consent to our use of the provided information to contact you regarding the security training program. We respect your privacy and will only use this information for program-related communications.
              </p>
              <label className="flex items-center gap-3 cursor-pointer">
                <input
                  type="checkbox"
                  name="disclaimer"
                  checked={formData.disclaimer}
                  onChange={handleInputChange}
                  required
                  className="w-4 h-4 text-[var(--color-blue)] border-gray-300 rounded focus:ring-[var(--color-blue)]"
                />
                <span className="text-[var(--color-dark-blue)] font-medium">Yes, I agree to the disclaimer</span>
              </label>
            </div>

            {/* Personal Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-[var(--color-dark-blue)] font-medium mb-2">Full Name *</label>
                <div className="relative">
                  <User className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-[var(--color-blue)]" />
                  <input
                    type="text"
                    name="fullName"
                    value={formData.fullName}
                    onChange={handleInputChange}
                    required
                    className="w-full pl-10 pr-4 py-3 bg-white border border-gray-200 rounded-xl text-[var(--color-dark-blue)] placeholder-gray-400 focus:outline-none focus:border-[var(--color-blue)] focus:ring-2 focus:ring-[var(--color-blue)]/20 transition-all"
                    placeholder="Enter your full name"
                  />
                </div>
              </div>

              <div>
                <label className="block text-[var(--color-dark-blue)] font-medium mb-2">Email Address *</label>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-[var(--color-blue)]" />
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                    className="w-full pl-10 pr-4 py-3 bg-white border border-gray-200 rounded-xl text-[var(--color-dark-blue)] placeholder-gray-400 focus:outline-none focus:border-[var(--color-blue)] focus:ring-2 focus:ring-[var(--color-blue)]/20 transition-all"
                    placeholder="Enter your email address"
                  />
                </div>
              </div>

              <div>
                <label className="block text-[var(--color-dark-blue)] font-medium mb-2">Phone Number</label>
                <div className="relative">
                  <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-[var(--color-blue)]" />
                  <input
                    type="tel"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    className="w-full pl-10 pr-4 py-3 bg-white border border-gray-200 rounded-xl text-[var(--color-dark-blue)] placeholder-gray-400 focus:outline-none focus:border-[var(--color-blue)] focus:ring-2 focus:ring-[var(--color-blue)]/20 transition-all"
                    placeholder="Enter your phone number"
                  />
                </div>
              </div>

              <div>
                <label className="block text-[var(--color-dark-blue)] font-medium mb-2">LinkedIn Profile</label>
                <div className="relative">
                  <Linkedin className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-[var(--color-blue)]" />
                  <input
                    type="url"
                    name="linkedin"
                    value={formData.linkedin}
                    onChange={handleInputChange}
                    className="w-full pl-10 pr-4 py-3 bg-white border border-gray-200 rounded-xl text-[var(--color-dark-blue)] placeholder-gray-400 focus:outline-none focus:border-[var(--color-blue)] focus:ring-2 focus:ring-[var(--color-blue)]/20 transition-all"
                    placeholder="Enter your LinkedIn profile URL"
                  />
                </div>
              </div>
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-[var(--color-dark-blue)] mb-2">Step 2 of {totalSteps}</h3>
              <p className="text-[var(--foreground-secondary)]">Professional Background & Interest</p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-[var(--color-dark-blue)] font-medium mb-2">Current Occupation</label>
                <div className="relative">
                  <Briefcase className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-[var(--color-blue)]" />
                  <input
                    type="text"
                    name="occupation"
                    value={formData.occupation}
                    onChange={handleInputChange}
                    className="w-full pl-10 pr-4 py-3 bg-white border border-gray-200 rounded-xl text-[var(--color-dark-blue)] placeholder-gray-400 focus:outline-none focus:border-[var(--color-blue)] focus:ring-2 focus:ring-[var(--color-blue)]/20 transition-all"
                    placeholder="Enter your current occupation"
                  />
                </div>
              </div>

              <div>
                <label className="block text-[var(--color-dark-blue)] font-medium mb-2">Years of Experience</label>
                <div className="relative">
                  <BookOpen className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-[var(--color-blue)]" />
                  <input
                    type="text"
                    name="experience"
                    value={formData.experience}
                    onChange={handleInputChange}
                    className="w-full pl-10 pr-4 py-3 bg-white border border-gray-200 rounded-xl text-[var(--color-dark-blue)] placeholder-gray-400 focus:outline-none focus:border-[var(--color-blue)] focus:ring-2 focus:ring-[var(--color-blue)]/20 transition-all"
                    placeholder="e.g., 2 years in IT, 1 year in cybersecurity"
                  />
                </div>
              </div>

              <div className="md:col-span-2">
                <label className="block text-[var(--color-dark-blue)] font-medium mb-2">Highest Educational Qualification</label>
                <div className="relative">
                  <GraduationCap className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-[var(--color-blue)]" />
                  <input
                    type="text"
                    name="education"
                    value={formData.education}
                    onChange={handleInputChange}
                    className="w-full pl-10 pr-4 py-3 bg-white border border-gray-200 rounded-xl text-[var(--color-dark-blue)] placeholder-gray-400 focus:outline-none focus:border-[var(--color-blue)] focus:ring-2 focus:ring-[var(--color-blue)]/20 transition-all"
                    placeholder="e.g., Bachelor's in Computer Science"
                  />
                </div>
              </div>

              <div className="md:col-span-2">
                <label className="block text-[var(--color-dark-blue)] font-medium mb-2">Why are you interested in this security training program?</label>
                <textarea
                  name="interest"
                  value={formData.interest}
                  onChange={handleInputChange}
                  rows={4}
                  className="w-full px-4 py-3 bg-white border border-gray-200 rounded-xl text-[var(--color-dark-blue)] placeholder-gray-400 focus:outline-none focus:border-[var(--color-blue)] focus:ring-2 focus:ring-[var(--color-blue)]/20 transition-all resize-none"
                  placeholder="Tell us about your interest in cybersecurity training..."
                />
              </div>

              <div>
                <label className="block text-[var(--color-dark-blue)] font-medium mb-2">How did you hear about us?</label>
                <div className="relative">
                  <MessageSquare className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-[var(--color-blue)]" />
                  <input
                    type="text"
                    name="source"
                    value={formData.source}
                    onChange={handleInputChange}
                    className="w-full pl-10 pr-4 py-3 bg-white border border-gray-200 rounded-xl text-[var(--color-dark-blue)] placeholder-gray-400 focus:outline-none focus:border-[var(--color-blue)] focus:ring-2 focus:ring-[var(--color-blue)]/20 transition-all"
                    placeholder="e.g., Social media, referral, search engine"
                  />
                </div>
              </div>

              <div>
                <label className="block text-[var(--color-dark-blue)] font-medium mb-2">Do you have security skills?</label>
                <div className="space-y-3">
                  <label className="flex items-center gap-3 cursor-pointer">
                    <input
                      type="radio"
                      name="securitySkills"
                      value="yes"
                      checked={formData.securitySkills === 'yes'}
                      onChange={handleInputChange}
                      className="w-4 h-4 text-[var(--color-blue)] border-gray-300 focus:ring-[var(--color-blue)]"
                    />
                    <span className="text-[var(--color-dark-blue)]">Yes</span>
                  </label>
                  <label className="flex items-center gap-3 cursor-pointer">
                    <input
                      type="radio"
                      name="securitySkills"
                      value="no"
                      checked={formData.securitySkills === 'no'}
                      onChange={handleInputChange}
                      className="w-4 h-4 text-[var(--color-blue)] border-gray-300 focus:ring-[var(--color-blue)]"
                    />
                    <span className="text-[var(--color-dark-blue)]">No</span>
                  </label>
                </div>
              </div>
            </div>
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-[var(--color-dark-blue)] mb-2">Step 3 of {totalSteps}</h3>
              <p className="text-[var(--foreground-secondary)]">Certification & Terms</p>
            </div>
            
            {/* Certification */}
            <div className="bg-gradient-to-br from-[var(--color-blue)]/10 to-[var(--color-blue-secondary)]/10 rounded-2xl p-6 border border-[var(--color-blue)]/20">
              <p className="text-[var(--color-dark-blue)] leading-relaxed mb-4">
                I hereby certify that all the information provided in this form is true and correct to the best of my knowledge. I understand that any false or misleading information may result in the rejection of my application.
              </p>
              
              <label className="flex items-center gap-3 cursor-pointer">
                <input
                  type="checkbox"
                  name="certification"
                  checked={formData.certification}
                  onChange={handleInputChange}
                  required
                  className="w-4 h-4 text-[var(--color-blue)] border-gray-300 rounded focus:ring-[var(--color-blue)]"
                />
                <span className="text-[var(--color-dark-blue)] font-medium">Yes, I certify</span>
              </label>
            </div>

            {/* Terms and Conditions */}
            <div className="bg-gradient-to-br from-[var(--color-blue)]/10 to-[var(--color-blue-secondary)]/10 rounded-2xl p-6 border border-[var(--color-blue)]/20">
              <p className="text-[var(--color-dark-blue)] leading-relaxed mb-4">
                By checking this box, you agree to our terms and conditions and privacy policy regarding course access and communications.
              </p>
              <a 
                href="https://securitylit.com/TrainingTermsAndCondition.pdf" 
                target="_blank" 
                rel="noopener noreferrer"
                className="inline-flex items-center gap-2 text-[var(--color-blue)] hover:text-[var(--color-blue-secondary)] transition-colors mb-4"
              >
                <span>View Terms and Conditions</span>
                <ExternalLink className="w-4 h-4" />
              </a>
              
              <label className="flex items-center gap-3 cursor-pointer">
                <input
                  type="checkbox"
                  name="terms"
                  checked={formData.terms}
                  onChange={handleInputChange}
                  required
                  className="w-4 h-4 text-[var(--color-blue)] border-gray-300 rounded focus:ring-[var(--color-blue)]"
                />
                <span className="text-[var(--color-dark-blue)] font-medium">Yes, I agree to the terms and conditions</span>
              </label>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <section className="py-24 bg-gradient-to-br from-[var(--color-blue)]/10 to-[var(--color-blue-secondary)]/10 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-0 w-full h-full bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23000000%22%20fill-opacity%3D%220.03%22%3E%3Ccircle%20cx%3D%2230%22%20cy%3D%2230%22%20r%3D%221%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-50"></div>
      </div>

      <div className="relative z-10 container mx-auto px-4 max-w-4xl">
        {/* Header */}
        <motion.div 
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <motion.div 
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.2, duration: 0.6 }}
            viewport={{ once: true }}
            className="inline-flex items-center bg-gradient-to-r from-[var(--color-blue)]/10 to-[var(--color-blue-secondary)]/10 px-6 py-3 rounded-full mb-8 border border-[var(--color-blue)]/20"
          >
            <Shield className="w-5 h-5 text-[var(--color-blue)] mr-3" />
            <span className="text-sm font-semibold text-[var(--color-blue)]">Training Application</span>
          </motion.div>
          
          <motion.h2 
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, duration: 0.6 }}
            viewport={{ once: true }}
            className="text-4xl lg:text-5xl font-bold text-[var(--color-dark-blue)] mb-8"
          >
            Complete Your
            <span className="block bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] bg-clip-text text-transparent">
              Training Application
            </span>
          </motion.h2>
          
          <motion.p 
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6, duration: 0.6 }}
            viewport={{ once: true }}
            className="text-lg text-[var(--foreground-secondary)] max-w-3xl mx-auto leading-relaxed"
          >
            Join our comprehensive cybersecurity training program. Complete this 3-step application to get started on your cybersecurity journey.
          </motion.p>
        </motion.div>

        {/* Progress Bar */}
        <div className="mb-12">
          <div className="flex justify-between items-center mb-4">
            <span className="text-sm font-medium text-[var(--color-dark-blue)]">Progress</span>
            <span className="text-sm font-medium text-[var(--color-blue)]">{currentStep} of {totalSteps}</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <motion.div 
              className="bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] h-2 rounded-full"
              initial={{ width: 0 }}
              animate={{ width: `${(currentStep / totalSteps) * 100}%` }}
              transition={{ duration: 0.5 }}
            />
          </div>
        </div>

        {/* Form Container */}
        <motion.div 
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="bg-white rounded-3xl p-8 lg:p-12 shadow-2xl border border-gray-100"
        >
          <form onSubmit={handleSubmit}>
            {/* Error Message */}
            {apiError && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6">
                {apiError}
              </div>
            )}

            {/* Success Message */}
            {success && (
              <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg mb-6">
                {success}
              </div>
            )}

            <AnimatePresence mode="wait">
              <motion.div
                key={currentStep}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
              >
                {renderStep()}
              </motion.div>
            </AnimatePresence>

            {/* Navigation Buttons */}
            <div className="flex justify-between items-center mt-12 pt-8 border-t border-gray-200">
              <motion.button
                type="button"
                onClick={prevStep}
                disabled={currentStep === 1}
                className={`flex items-center gap-2 px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${
                  currentStep === 1
                    ? 'text-gray-400 cursor-not-allowed'
                    : 'text-[var(--color-dark-blue)] hover:bg-gray-100'
                }`}
                whileHover={currentStep !== 1 ? { scale: 1.02 } : {}}
                whileTap={currentStep !== 1 ? { scale: 0.98 } : {}}
              >
                <ChevronLeft className="w-5 h-5" />
                Previous
              </motion.button>

              {currentStep < totalSteps ? (
                <motion.button
                  type="button"
                  onClick={nextStep}
                  className="flex items-center gap-2 bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] text-white px-8 py-3 rounded-xl font-semibold hover:shadow-lg transition-all duration-300"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  Next
                  <ChevronRight className="w-5 h-5" />
                </motion.button>
              ) : (
                <motion.button
                  type="submit"
                  disabled={isSubmitting}
                  className="flex items-center gap-2 bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] disabled:from-gray-400 disabled:to-gray-500 text-white px-8 py-3 rounded-xl font-semibold hover:shadow-lg transition-all duration-300"
                  whileHover={{ scale: isSubmitting ? 1 : 1.02 }}
                  whileTap={{ scale: isSubmitting ? 1 : 0.98 }}
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                      Submitting Application...
                    </>
                  ) : (
                    <>
                      <Shield className="w-5 h-5" />
                      Submit Application
                      <ArrowRight className="w-5 h-5" />
                    </>
                  )}
                </motion.button>
              )}
            </div>
          </form>
        </motion.div>
      </div>
    </section>
  );
} 