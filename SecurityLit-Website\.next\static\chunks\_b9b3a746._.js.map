{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/training/page.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect } from 'react';\r\nimport { useRouter } from 'next/navigation';\r\n\r\nexport default function TrainingPage() {\r\n  const router = useRouter();\r\n\r\n  useEffect(() => {\r\n    // Redirect to the new training page\r\n    router.replace('/CybersecurityTraining');\r\n  }, [router]);\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-white flex items-center justify-center\">\r\n      <div className=\"text-center\">\r\n        <h1 className=\"text-2xl font-bold text-[var(--color-dark-blue)] mb-4\">\r\n          Redirecting to Cybersecurity Training...\r\n        </h1>\r\n        <p className=\"text-[var(--foreground-secondary)]\">\r\n          If you are not redirected automatically, <a href=\"/CybersecurityTraining\" className=\"text-[var(--color-blue)] hover:underline\">click here</a>.\r\n        </p>\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,oCAAoC;YACpC,OAAO,OAAO,CAAC;QACjB;iCAAG;QAAC;KAAO;IAEX,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAAwD;;;;;;8BAGtE,6LAAC;oBAAE,WAAU;;wBAAqC;sCACP,6LAAC;4BAAE,MAAK;4BAAyB,WAAU;sCAA2C;;;;;;wBAAc;;;;;;;;;;;;;;;;;;AAKvJ;GApBwB;;QACP,qIAAA,CAAA,YAAS;;;KADF", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}