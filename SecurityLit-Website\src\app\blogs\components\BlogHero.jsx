"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { BookO<PERSON>, ArrowRight, Shield, Users, Star, Clock } from 'lucide-react';
import BreadcrumbNavigation from '../../common/components/BreadcrumbNavigation';

export default function BlogHero() {
  const breadcrumbItems = [
    {
      name: "Home",
      url: "/",
      iconKey: "home",
      description: "Return to homepage"
    },
    {
      name: "Blogs",
      url: "/blogs",
      current: true,
      iconKey: "book-open",
      description: "Explore SecurityLit's cybersecurity insights and industry knowledge"
    }
  ];

  const blogStats = [
    { value: "50+", label: "Articles Published", icon: BookOpen },
    { value: "10K+", label: "Monthly Readers", icon: Users },
    { value: "4.9", label: "Average Rating", icon: Star },
    { value: "24/7", label: "Expert Insights", icon: Clock }
  ];

  return (
    <div className="bg-gradient-to-br from-[var(--color-dark-blue)] to-[var(--color-dark-blue-hover)] relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-10"
           style={{ backgroundImage: 'url(/images/hexagon.svg)' }}>
      </div>
      <div className="absolute inset-0 bg-[var(--color-dark-blue)]/90"></div>
      
      <div className="relative z-10 max-w-7xl mx-auto px-6 py-20 lg:px-12 lg:py-24">
        <div className="text-center">
          {/* Breadcrumb */}
          <div className="mb-6">
            <BreadcrumbNavigation items={breadcrumbItems} className="text-white" />
          </div>
          
          {/* Main Heading */}
          <motion.h1 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-4xl lg:text-6xl font-bold text-white mb-6 leading-tight"
          >
            Cybersecurity
            <span className="block bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] bg-clip-text text-transparent">
              Insights & Knowledge
            </span>
          </motion.h1>
          
          {/* Subtitle */}
          <motion.p 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-xl lg:text-2xl text-white/90 mb-8 max-w-3xl mx-auto leading-relaxed"
          >
            Stay ahead of cyber threats with expert insights, industry trends, and practical security guidance from SecurityLit's cybersecurity professionals.
          </motion.p>

          {/* CTA Button */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12"
          >
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="group bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center gap-3"
            >
              <BookOpen className="w-5 h-5" />
              Explore Articles
              <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
            </motion.button>
            
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="group bg-white/10 backdrop-blur-sm text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300 border-2 border-white/20 hover:bg-white/20 flex items-center justify-center gap-3"
            >
              <Shield className="w-5 h-5" />
              Subscribe to Updates
            </motion.button>
          </motion.div>

          {/* Blog Stats */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="grid grid-cols-2 lg:grid-cols-4 gap-6 max-w-4xl mx-auto"
          >
            {blogStats.map((stat, index) => (
              <motion.div 
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.8 + index * 0.1, duration: 0.6 }}
                className="text-center group"
              >
                <div className="w-12 h-12 bg-gradient-to-br from-[var(--color-blue)]/20 to-[var(--color-blue-secondary)]/20 rounded-xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform">
                  <stat.icon className="w-6 h-6 text-[var(--color-blue)]" />
                </div>
                <div className="text-2xl lg:text-3xl font-bold text-white mb-1">
                  {stat.value}
                </div>
                <div className="text-white/80 text-sm font-medium">
                  {stat.label}
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </div>
    </div>
  );
} 