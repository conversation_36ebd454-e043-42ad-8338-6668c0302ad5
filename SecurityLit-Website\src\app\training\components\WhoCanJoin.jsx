"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { User, GraduationCap, Briefcase, Star, ArrowRight, Shield, Zap } from 'lucide-react';

const audienceTypes = [
  {
    icon: User,
    title: "Cybersecurity Enthusiasts",
    description: "Whether you're new to the field or have some experience, our program caters to learners of all levels. Start with the free tier to build a strong foundation, then upgrade to the premium tier for a more immersive, mentor-guided experience.",
    color: "from-[var(--color-blue)] to-[var(--color-blue-secondary)]",
    badge: "All Levels",
    delay: 0.1
  },
  {
    icon: GraduationCap,
    title: "Information Security Professionals",
    description: "If you have been practicing cybersecurity topics, you can join the premium tier to take your skills to the next level through hands-on projects and industry-relevant training.",
    color: "from-[var(--color-dark-blue)] to-[var(--color-dark-blue-hover)]",
    badge: "Advanced",
    delay: 0.3
  },
  {
    icon: Briefcase,
    title: "Career Switchers",
    description: "Looking to transition into the exciting world of cybersecurity? Our program provides the knowledge and practical experience you need to kickstart your career in this high-demand field.",
    color: "from-[var(--color-yellow)] to-[var(--color-yellow-hover)]",
    badge: "Career Change",
    delay: 0.5
  }
];

export default function WhoCanJoin() {
  return (
    <section className="py-12 sm:py-16 lg:py-24 bg-white relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-gradient-to-br from-[var(--color-blue)]/10 to-[var(--color-blue-secondary)]/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-gradient-to-br from-[var(--color-yellow)]/10 to-[var(--color-yellow-hover)]/10 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10 container mx-auto px-4 max-w-7xl">
        {/* Header */}
        <motion.div 
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <motion.div 
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.2, duration: 0.6 }}
            viewport={{ once: true }}
            className="inline-flex items-center bg-gradient-to-r from-[var(--color-blue)]/10 to-[var(--color-blue-secondary)]/10 px-4 py-2 rounded-full mb-6"
          >
            <Star className="w-4 h-4 text-[var(--color-blue)] mr-2" />
            <span className="text-sm font-medium text-[var(--color-blue)]">Who Can Join</span>
          </motion.div>
          
          <motion.h2 
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, duration: 0.6 }}
            viewport={{ once: true }}
            className="text-4xl lg:text-5xl font-bold text-[var(--color-dark-blue)] mb-6"
          >
            So Who Can Take Up
            <span className="block bg-gradient-to-r from-[var(--color-blue)] to-[var(--color-blue-secondary)] bg-clip-text text-transparent">
              This Training?
            </span>
          </motion.h2>
          
          <motion.p 
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6, duration: 0.6 }}
            viewport={{ once: true }}
            className="text-lg text-[var(--foreground-secondary)] max-w-4xl mx-auto leading-relaxed px-4"
          >
            Our program is designed to be accessible and beneficial for a wide range of learners, from beginners to experienced professionals. Whether you're looking to start your cybersecurity journey or take it to new heights, we've got you covered.
          </motion.p>
        </motion.div>

        {/* Cards with Equal Heights */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 lg:gap-12">
          {audienceTypes.map((audience, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: audience.delay, duration: 0.8 }}
              viewport={{ once: true }}
              whileHover={{ 
                y: -10,
                transition: { duration: 0.3 }
              }}
              className="group relative h-full"
            >
              {/* Card with Equal Height */}
              <div className="relative bg-white rounded-3xl p-8 shadow-[0_20px_40px_rgb(0,0,0,0.08)] hover:shadow-[0_30px_60px_rgb(0,0,0,0.12)] transition-all duration-500 border border-gray-100 h-full flex flex-col">
                
                {/* Fixed Badge - No Transparency Issues */}
                <motion.div 
                  initial={{ opacity: 0, scale: 0 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  transition={{ delay: audience.delay + 0.2, duration: 0.5 }}
                  viewport={{ once: true }}
                  className={`absolute -top-4 left-8 bg-gradient-to-r ${audience.color} text-white px-4 py-2 rounded-full text-sm font-semibold shadow-lg z-10 border-2 border-white`}
                >
                  {audience.badge}
                </motion.div>

                {/* Icon Container */}
                <motion.div 
                  whileHover={{ 
                    rotate: 360,
                    scale: 1.1
                  }}
                  transition={{ duration: 0.6 }}
                  className={`w-20 h-20 bg-gradient-to-br ${audience.color} rounded-3xl flex items-center justify-center mb-6 shadow-xl group-hover:shadow-2xl transition-all duration-300`}
                >
                  <audience.icon className="w-10 h-10 text-white" />
                </motion.div>

                {/* Content - Flex Grow to Fill Space */}
                <div className="space-y-4 flex-1 flex flex-col">
                  <div>
                    <h3 className="text-xl lg:text-2xl font-bold text-[var(--color-dark-blue)] mb-4">
                      {audience.title}
                    </h3>
                  </div>
                  
                  <p className="text-lg text-[var(--foreground-secondary)] leading-relaxed flex-1">
                    {audience.description}
                  </p>

                  {/* CTA Button - Fixed at Bottom */}
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className={`w-full bg-gradient-to-r ${audience.color} text-white py-3 px-6 rounded-2xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center gap-2 group/btn mt-auto`}
                  >
                    Get Started
                    <ArrowRight className="w-4 h-4 group-hover/btn:translate-x-1 transition-transform" />
                  </motion.button>
                </div>

                {/* Enhanced Claymorphic Effects */}
                <div className="absolute inset-0 bg-gradient-to-br from-white/30 to-transparent rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
                <div className={`absolute inset-0 bg-gradient-to-br ${audience.color} rounded-3xl opacity-0 group-hover:opacity-5 transition-opacity duration-300 blur-xl pointer-events-none`} />
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
} 