import DarkButton from "@/app/common/buttons/DarkButton";
import Image from "next/image";
import React from "react";

export default function Case1() {
  return (
    <div className="md:px-4 md:py-12 p-8 flex md:flex-row flex-col justify-between items-start">
      <div className="Content md:w-[62%] w-full flex flex-col md:gap-8 gap-6 md:pl-10">
        <div className="Title text-lg md:text-xl lg:text-2xl text-black py-2 flex flex-col md:flex-row md:items-center md:gap-2">
          <div className="font-bold">Industry:</div>
          <span className="text-lg md:text-xl lg:text-2xl">
            AI-Driven Consumer Insights
          </span>
        </div>

        <div className="Title text-lg md:text-xl lg:text-2xl text-black py-2 flex flex-col md:flex-row md:items-center md:gap-2">
          <div className="font-bold">Services Provided:</div>
          <span className="text-lg md:text-xl lg:text-2xl">
            Vulnerability Assessment & Penetration Testing
          </span>
        </div>

        <div className="description leading-8 md:pr-10 text-slate-800 text-base md:text-lg lg:text-xl">
          Our client is a leader in transforming consumer insights through
          AI-powered technology, offering real-time data and analytics solutions
          to businesses. As part of their growth and global expansion, they
          required robust security measures to safeguard their platform and
          maintain compliance with international security standards.
        </div>

        <div className="button flex gap-2 mt-4">
          <a
            href="/AI-Driven Consumer Insights Case Study.pdf"
            download="AI-Driven Consumer Insights Case Study.pdf"
          >
            <DarkButton className="px-8 py-4 text-sm md:text-lg">
              Download Success Story
            </DarkButton>
          </a>
        </div>
      </div>
      <div className="Image md:w-[35%] w-full md:pr-10 md:mt-0 mt-10 mb-6 md:mb-0">
        <Image
          src="/images/Case 2-img.png"
          width={750}
          height={550}
          alt="AI-driven consumer insights platform security assessment case study showing comprehensive penetration testing and vulnerability analysis results"
        />
      </div>
    </div>
  );
}
