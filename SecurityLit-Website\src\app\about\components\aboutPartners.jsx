"use client";

import React from "react";
import { Linkedin } from "lucide-react";
import Image from "next/image";

export default function AboutCertificationsAndPartnerships() {
  const items = [
    {
      name: "Indira Gandhi Delhi Technical University for Women",
      title: "University Partner",
      imageUrl: "/images/igdtuw-logo.png",
      description: "Proud partners with Indira Gandhi Delhi Technical University for Women, collaborating to advance cybersecurity education and research.",
      social: {
        linkedin: "https://www.linkedin.com/company/university" // Replace with actual link
      }
    },
    {
      name: "ISO 27001:2013",
      title: "ISO 27001 Certified",
      imageUrl: "/images/iso27001-logo.png", // Replace with actual ISO logo
      description: "SecurityLit is ISO 27001:2013 certified, demonstrating our commitment to the highest standards of information security management.",
      social: {
        linkedin: "https://www.linkedin.com/company/securitylit" // Replace with your company link
      }
    },
    {
      name: "Pae Hokohoko",
      title: "Marketplace Supplier",
      imageUrl: "/images/pae-hokohoko-logo.jpeg",
      description: "Recognized supplier of VAPT services on the Pae Hokohoko marketplace, delivering trusted cybersecurity solutions.",
      social: {
        linkedin: "https://www.linkedin.com/company/pae-hokohoko" // Replace with actual link
      }
    }
  ];

  return (
    <section className="bg-gray-50 py-20 lg:py-28">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-12 lg:mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-[var(--color-dark-blue)]">
            Certifications & Strategic Partnerships
          </h2>
          <p className="mt-4 text-lg text-gray-600 max-w-2xl mx-auto">
            SecurityLit is recognized for its industry-leading certifications and valued partnerships, ensuring the highest standards in cybersecurity services and education.
          </p>
        </div>

        {/* Cards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-12">
          {items.map((item) => (
            <div 
              key={item.name}
              className="bg-white rounded-xl shadow-md overflow-hidden transform hover:-translate-y-2 transition-transform duration-300"
            >
              <div className="relative h-64">
                <Image 
                  className="object-cover object-center" 
                  src={item.imageUrl} 
                  alt={`Logo of ${item.name} - ${item.title}`}
                  fill
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                  style={{ objectPosition: 'center 30%' }}
                  onError={(e) => { 
                    e.target.src = 'https://via.placeholder.com/400x400.png?text=Logo'; 
                  }}
                />
                {/* LinkedIn Icon */}
                <a
                  href={item.social.linkedin}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="absolute top-4 right-4 bg-white/80 backdrop-blur-sm p-2 rounded-full text-[var(--color-dark-blue)] hover:bg-white hover:text-[var(--color-blue)] transition-colors"
                  aria-label={`${item.name}'s LinkedIn Profile`}
                >
                  <Linkedin className="w-5 h-5" />
                </a>
              </div>

              <div className="p-6">
                <h3 className="text-xl font-bold text-[var(--color-dark-blue)]">{item.title}</h3>
                <p className="text-[var(--color-blue)] font-semibold mt-1">{item.name}</p>
                <p className="text-gray-600 mt-4 text-sm leading-relaxed">
                  {item.description}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
