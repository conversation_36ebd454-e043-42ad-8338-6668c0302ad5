"use client";
import React, { useState, useEffect, useRef } from "react";
import { Users, Clock, Shield, Star, Zap, CheckCircle } from 'lucide-react';


const AnimatedCounter = ({ end, duration = 2000, suffix = "", decimals = 0 }) => {
  const [count, setCount] = useState(0);
  const [isVisible, setIsVisible] = useState(false);
  const ref = useRef();

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.1 }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => observer.disconnect();
  }, []);

  useEffect(() => {
    if (!isVisible) return;

    let startTime;
    let animationFrame;

    const animate = (timestamp) => {
      if (!startTime) startTime = timestamp;
      const progress = Math.min((timestamp - startTime) / duration, 1);
      
      // Easing function for smooth animation
      const easeOutQuart = 1 - Math.pow(1 - progress, 4);
      const currentCount = end * easeOutQuart;
      
      setCount(currentCount);

      if (progress < 1) {
        animationFrame = requestAnimationFrame(animate);
      }
    };

    animationFrame = requestAnimationFrame(animate);

    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
      }
    };
  }, [isVisible, end, duration]);

  const formatNumber = (num) => {
    return num.toFixed(decimals);
  };

  return (
    <span ref={ref} className="tabular-nums">
      {formatNumber(count)}{suffix}
    </span>
  );
};

const Stats = () => {
  return (
    <div className="bg-[#f9fafb] relative overflow-hidden">
      <div className="container px-4 sm:px-6 md:px-8 lg:px-12 xl:pl-24 xl:pr-4 py-8 md:py-12 lg:py-12">
        
        {/* Background decorative elements */}
        
        
        
        <div className="max-w-[1800px] mx-auto lg:ml-auto relative z-10">
          
          {/* Main Content Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 md:gap-12 xl:gap-32 items-start lg:items-center">
            
            {/* Left Side - Text Content */}
            <div className="space-y-4 sm:space-y-6 lg:space-y-8 lg:px-8 lg:sticky lg:top-24 max-w-full sm:max-w-[600px] mx-auto lg:mx-0">
              <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-4xl font-[600] text-[#062575] leading-tight text-center lg:text-left">
                We&apos;re built to support your security, fast.
              </h2>
              <p className="text-base sm:text-lg text-slate-600 leading-relaxed text-center lg:text-left">
                From critical vulnerabilities to compliance queries, our team responds with the urgency and depth you expect from a trusted partner.
              </p>
            </div>

            {/* Right Side - Stats Cards */}
            <div className="space-y-4 w-full max-w-full sm:max-w-[500px] md:max-w-[600px] lg:max-w-[800px] xl:max-w-[1000px] mx-auto lg:mx-0">
              {/* Expert-Led Support Card */}
              <div className="bg-white rounded-xl p-4 sm:p-6 shadow-lg border border-slate-100 hover:shadow-2xl hover:border-[#027bfc]/30 hover:scale-[1.01] transition-all duration-500 relative overflow-hidden group cursor-pointer">
                {/* Animated Background Element */}
                <div className="absolute top-0 right-0 w-32 h-32 sm:w-40 sm:h-40 bg-gradient-to-br from-[#027bfc]/8 via-[#027bfc]/4 to-transparent rounded-full transform translate-x-16 sm:translate-x-20 -translate-y-16 sm:-translate-y-20 group-hover:translate-x-12 sm:group-hover:translate-x-16 group-hover:-translate-y-12 sm:group-hover:-translate-y-16 group-hover:scale-110 transition-all duration-500"></div>
                
                {/* Content */}
                <div className="relative">
                  <div className="flex flex-col sm:flex-row items-start sm:items-start justify-between mb-4 space-y-3 sm:space-y-0">
                    <div className="flex items-center space-x-3 w-full sm:w-auto">
                      <div className="w-10 h-10 sm:w-12 sm:h-12 bg-ctb-green-50 rounded-lg flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow duration-300 flex-shrink-0">
                        <Users className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                      </div>
                      <div className="flex-1 sm:flex-initial">
                        <h3 className="text-lg sm:text-xl font-semibold text-[#062575] group-hover:text-[#027bfc] transition-colors duration-300">
                          Expert-Led Support
                        </h3>
                        <p className="text-xs sm:text-sm text-slate-500">Available 24/7</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-1 px-2 sm:px-3 py-1 sm:py-1.5 rounded-full border self-start sm:self-auto">
                      <Star className="w-3 h-3 sm:w-4 sm:h-4 text-ctb-green-50 fill-current" />
                      <span className="text-xs sm:text-sm font-medium text-ctb-green-50">4.9/5 Rating</span>
                    </div>
                  </div>
                  
                  <p className="text-sm sm:text-base text-slate-600 leading-relaxed mb-4">
                    Get help from certified security experts who understand your compliance needs and can guide you through complex requirements.
                  </p>
                  
                  <div className="flex flex-wrap items-center gap-2 sm:gap-4 text-xs sm:text-sm text-slate-500">
                    <div className="flex items-center space-x-1">
                      <CheckCircle className="w-3 h-3 sm:w-4 sm:h-4 text-ctb-green-50" />
                      <span>Certified Experts</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <CheckCircle className="w-3 h-3 sm:w-4 sm:h-4 text-ctb-green-50" />
                      <span>Personalized Help</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Fast Response Times Card */}
              <div className="bg-white rounded-xl p-4 sm:p-6 shadow-lg border border-slate-100 hover:shadow-2xl hover:border-[#027bfc]/30 hover:scale-[1.01] transition-all duration-500 relative overflow-hidden group cursor-pointer">
                {/* Animated Background Element */}
                <div className="absolute top-0 right-0 w-32 h-32 sm:w-40 sm:h-40 bg-gradient-to-br from-orange-500/8 via-orange-500/4 to-transparent rounded-full transform translate-x-16 sm:translate-x-20 -translate-y-16 sm:-translate-y-20 group-hover:translate-x-12 sm:group-hover:translate-x-16 group-hover:-translate-y-12 sm:group-hover:-translate-y-16 group-hover:scale-110 transition-all duration-500"></div>
                
                {/* Content */}
                <div className="relative">
                  <div className="flex flex-col sm:flex-row items-start sm:items-start justify-between mb-4 space-y-3 sm:space-y-0">
                    <div className="flex items-center space-x-3 w-full sm:w-auto">
                      <div className="w-10 h-10 sm:w-12 sm:h-12 bg-ctb-green-50 rounded-lg flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow duration-300 flex-shrink-0">
                        <Zap className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                      </div>
                      <div className="flex-1 sm:flex-initial">
                        <h3 className="text-lg sm:text-xl font-semibold text-[#062575] group-hover:text-blue-600 transition-colors duration-300">
                          Lightning Fast Response
                        </h3>
                        <p className="text-xs sm:text-sm text-slate-500">Industry Leading</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-1 px-2 sm:px-3 py-1 sm:py-1.5 rounded-full border self-start sm:self-auto">
                      <Clock className="w-3 h-3 sm:w-4 sm:h-4 text-ctb-green-50" />
                      <span className="text-xs sm:text-sm font-medium text-ctb-green-50">&lt; 2hrs</span>
                    </div>
                  </div>
                  
                  <p className="text-sm sm:text-base text-slate-600 leading-relaxed mb-4">
                    Get rapid responses to your questions with our streamlined support system designed for fast-moving teams.
                  </p>
                  
                  <div className="flex flex-wrap items-center gap-2 sm:gap-4 text-xs sm:text-sm text-slate-500">
                    <div className="flex items-center space-x-1">
                      <CheckCircle className="w-3 h-3 sm:w-4 sm:h-4 text-ctb-green-50" />
                      <span>Priority Queue</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <CheckCircle className="w-3 h-3 sm:w-4 sm:h-4 text-ctb-green-50" />
                      <span>Smart Routing</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Enterprise-Grade Reliability Card */}
              <div className="bg-white rounded-xl p-4 sm:p-6 shadow-lg border border-slate-100 hover:shadow-2xl hover:border-[#027bfc]/30 hover:scale-[1.01] transition-all duration-500 relative overflow-hidden group cursor-pointer">
                {/* Animated Background Element */}
                <div className="absolute top-0 right-0 w-32 h-32 sm:w-40 sm:h-40 bg-gradient-to-br from-purple-500/8 via-purple-500/4 to-transparent rounded-full transform translate-x-16 sm:translate-x-20 -translate-y-16 sm:-translate-y-20 group-hover:translate-x-12 sm:group-hover:translate-x-16 group-hover:-translate-y-12 sm:group-hover:-translate-y-16 group-hover:scale-110 transition-all duration-500"></div>
                
                {/* Content */}
                <div className="relative">
                  <div className="flex flex-col sm:flex-row items-start sm:items-start justify-between mb-4 space-y-3 sm:space-y-0">
                    <div className="flex items-center space-x-3 w-full sm:w-auto">
                      <div className="w-10 h-10 sm:w-12 sm:h-12 bg-ctb-green-50 rounded-lg flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow duration-300 flex-shrink-0">
                        <Shield className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                      </div>
                      <div className="flex-1 sm:flex-initial">
                        <h3 className="text-lg sm:text-xl font-semibold text-[#062575] group-hover:text-blue-600 transition-colors duration-300">
                          Enterprise-Grade Reliability
                        </h3>
                        <p className="text-xs sm:text-sm text-slate-500">99.9% Uptime</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-1 px-2 sm:px-3 py-1 sm:py-1.5 rounded-full border self-start sm:self-auto">
                      <Shield className="w-3 h-3 sm:w-4 sm:h-4 text-ctb-green-50" />
                      <span className="text-xs sm:text-sm font-medium text-ctb-green-50">SOC 2</span>
                    </div>
                  </div>
                  
                  <p className="text-sm sm:text-base text-slate-600 leading-relaxed mb-4">
                    Built on enterprise infrastructure with redundant systems, automated monitoring, and comprehensive security protocols.
                  </p>
                  
                  <div className="flex flex-wrap items-center gap-2 sm:gap-4 text-xs sm:text-sm text-slate-500">
                    <div className="flex items-center space-x-1">
                      <CheckCircle className="w-3 h-3 sm:w-4 sm:h-4 text-ctb-green-50" />
                      <span>Auto-Scaling</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <CheckCircle className="w-3 h-3 sm:w-4 sm:h-4 text-ctb-green-50" />
                      <span>24/7 Monitoring</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

          </div>
        </div>
      </div>
    </div>
  );
};

export default Stats;