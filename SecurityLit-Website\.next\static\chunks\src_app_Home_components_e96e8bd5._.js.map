{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/Home/components/heroSection.jsx"], "sourcesContent": ["// src/components/Hero.jsx (or your existing file path)\r\n\"use client\";\r\n\r\nimport React, { useState } from 'react';\r\nimport { PrimaryButton, SecondaryButton, OutlinedButton } from \"@/app/common/buttons/BrandButtons\";\r\nimport { FaArrowRight, FaShieldAlt, FaCheckCircle, FaLock } from 'react-icons/fa';\r\nimport Image from 'next/image';\r\n\r\n// Reusable component for the benefit list items\r\nconst BenefitItem = ({ text }) => (\r\n  <li className=\"flex items-center gap-3\">\r\n    <FaCheckCircle className=\"text-green-500 h-5 w-5 flex-shrink-0\" />\r\n    <span className=\"text-white/80\">{text}</span>\r\n  </li>\r\n);\r\n\r\nexport default function Hero() {\r\n  const [formData, setFormData] = useState({ name: '', email: '' });\r\n  const locations = ['New Zealand', 'India', 'Australia', 'USA'];\r\n\r\n  const handleInputChange = (e) => {\r\n    const { name, value } = e.target;\r\n    setFormData(prev => ({ ...prev, [name]: value }));\r\n  };\r\n\r\n  const handleSubmit = (e) => {\r\n    e.preventDefault();\r\n    console.log(\"Form Submitted:\", formData);\r\n    // Add your form submission logic here\r\n  };\r\n\r\n  return (\r\n    <section className=\"relative w-full min-h-screen flex items-center pt-16 md:pt-20 bg-white\">\r\n      {/* Background Grid Pattern */}\r\n      <div className=\"absolute inset-0 cyber-grid opacity-20 z-0\"></div>\r\n      \r\n      {/* Subtle overlay */}\r\n      <div className=\"absolute inset-0 bg-gradient-to-br from-white/80 via-gray-50/80 to-gray-100/80 z-0\"></div>\r\n      \r\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\r\n        <div className=\"grid lg:grid-cols-2 gap-8 lg:gap-16 items-center max-w-full\">\r\n          \r\n          {/* Left Column: Content */}\r\n          <div className=\"space-y-6 md:space-y-8 w-full fade-in-up\">\r\n            <div className=\"inline-flex items-center gap-2 px-3 py-1.5 md:px-4 md:py-2 bg-[var(--color-blue)]/10 border border-[var(--color-blue)]/20 rounded-full text-xs md:text-sm font-semibold text-[var(--color-blue)]\">\r\n              <FaShieldAlt className=\"text-[var(--color-blue)] text-xs md:text-sm\" />\r\n              <span>Trusted by Fortune 500</span>\r\n            </div>\r\n\r\n            <h1 className=\"text-3xl sm:text-4xl md:text-5xl lg:text56xl font-bold text-[var(--color-dark-blue)] leading-tight\">\r\n            Enterprise-Grade <br />\r\n              <span className=\"text-[var(--color-blue)]\">Cybersecurity Consulting</span>\r\n            </h1>\r\n\r\n            <p className=\"text-base md:text-lg text-gray-600 max-w-xl\">\r\n            Strategic cybersecurity leadership and hands-on security expertise. From vCISO services to advanced threat simulation, we strengthen your security posture across all business dimensions.\r\n            </p>\r\n\r\n            <div className=\"flex flex-wrap items-center gap-2 md:gap-3\">\r\n              <span className=\"text-xs md:text-sm font-medium text-gray-500\">Serving:</span>\r\n              {locations.map((loc, index) => (\r\n                <span \r\n                  key={loc} \r\n                  className=\"px-2 py-1 md:px-3 md:py-1 bg-gray-100 border border-gray-200 text-gray-700 rounded-full text-xs md:text-sm cursor-pointer\"\r\n                  style={{ animationDelay: `${index * 0.1}s` }}\r\n                >\r\n                  {loc}\r\n                </span>\r\n              ))}\r\n              <span className=\"px-2 py-1 md:px-3 md:py-1 bg-[var(--color-blue)] text-white rounded-full text-xs md:text-sm font-semibold\">\r\n                + more\r\n              </span>\r\n            </div>\r\n\r\n            <div className=\"flex flex-col sm:flex-row gap-3 md:gap-4 pt-2 md:pt-4\">\r\n              <PrimaryButton className=\"px-4 py-2.5 md:px-5 md:py-2.5 text-sm md:text-base\">\r\n                Talk to Our Expert <FaArrowRight />\r\n              </PrimaryButton>\r\n              <button className=\"px-4 py-2.5 md:px-5 md:py-2.5 text-sm md:text-base bg-[var(--color-yellow)] text-[var(--color-gray)] border-2 border-[var(--color-yellow)] rounded-lg font-bold transition-all duration-300 hover:bg-white hover:text-[var(--color-dark-blue)] hover:border-white shadow-md flex items-center justify-center gap-2 cursor-pointer\">\r\n                Our Services\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Right Column: Hero Image */}\r\n          <div className=\"hidden lg:block relative\">\r\n            <div className=\"relative w-full h-full flex items-center justify-center\">\r\n              <Image \r\n                src=\"/images/hero-image-9.svg\" \r\n                alt=\"SecurityLit Cybersecurity Services\" \r\n                width={500} \r\n                height={500}\r\n                className=\"object-contain max-w-full max-h-full\"\r\n                priority\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,uDAAuD;;;;;AAGvD;AACA;AACA;AACA;;;AALA;;;;;AAOA,gDAAgD;AAChD,MAAM,cAAc;QAAC,EAAE,IAAI,EAAE;yBAC3B,6LAAC;QAAG,WAAU;;0BACZ,6LAAC,iJAAA,CAAA,gBAAa;gBAAC,WAAU;;;;;;0BACzB,6LAAC;gBAAK,WAAU;0BAAiB;;;;;;;;;;;;;KAH/B;AAOS,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,MAAM;QAAI,OAAO;IAAG;IAC/D,MAAM,YAAY;QAAC;QAAe;QAAS;QAAa;KAAM;IAE9D,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,KAAK,EAAE;YAAM,CAAC;IACjD;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,QAAQ,GAAG,CAAC,mBAAmB;IAC/B,sCAAsC;IACxC;IAEA,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;;;;;;0BAGf,6LAAC;gBAAI,WAAU;;;;;;0BAEf,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAGb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,iJAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,6LAAC;sDAAK;;;;;;;;;;;;8CAGR,6LAAC;oCAAG,WAAU;;wCAAqG;sDAClG,6LAAC;;;;;sDAChB,6LAAC;4CAAK,WAAU;sDAA2B;;;;;;;;;;;;8CAG7C,6LAAC;oCAAE,WAAU;8CAA8C;;;;;;8CAI3D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAA+C;;;;;;wCAC9D,UAAU,GAAG,CAAC,CAAC,KAAK,sBACnB,6LAAC;gDAEC,WAAU;gDACV,OAAO;oDAAE,gBAAgB,AAAC,GAAc,OAAZ,QAAQ,KAAI;gDAAG;0DAE1C;+CAJI;;;;;sDAOT,6LAAC;4CAAK,WAAU;sDAA4G;;;;;;;;;;;;8CAK9H,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,mJAAA,CAAA,gBAAa;4CAAC,WAAU;;gDAAqD;8DACzD,6LAAC,iJAAA,CAAA,eAAY;;;;;;;;;;;sDAElC,6LAAC;4CAAO,WAAU;sDAAoU;;;;;;;;;;;;;;;;;;sCAO1V,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,QAAQ;oCACR,WAAU;oCACV,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQxB;GArFwB;MAAA", "debugId": null}}, {"offset": {"line": 287, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/Home/components/OurServices.jsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState } from \"react\";\r\nimport Image from \"next/image\";\r\nimport Link from \"next/link\";\r\n\r\n\r\nexport default function SecurityChallengesSection() {\r\n  const [currentSlide, setCurrentSlide] = useState(0);\r\n\r\nconst allChallenges = [\r\n    {\r\n    title: \"Prepare for compliance success\",\r\n    description: \"Streamline your audit readiness with tailored assessments. Identify gaps and align with ISO, SOC 2, and other standards confidently and efficiently.\",\r\n    buttonText: \"Cloud Security\",\r\n    linkText: \"Discover our Cloud Security Services\",\r\n    linkHref: \"/compliance-services\",\r\n    image: \"/images/homepagecards/1.svg\",\r\n    alt: \"Cloud security infrastructure and monitoring\"\r\n  },\r\n   {\r\n    title: \"Advanced threat simulation and security validation\",\r\n    description: \"Conduct red team simulations that mimic sophisticated attackers. Evaluate your detection, response, and resilience under real adversarial conditions.\",\r\n    buttonText: \"Security Architecture\",\r\n    linkText: \"Discover our Red Team Services\",\r\n    linkHref: \"/red-team-simulation\",\r\n    image: \"/images/homepagecards/4.svg\",\r\n    alt: \"Team of professionals collaborating in modern office environment\"\r\n  },\r\n   {\r\n    title: \"Secure your blockchain and DeFi applications\",\r\n    description: \"Perform in-depth audits of smart contracts, decentralized apps, and blockchain networks to prevent exploits and protect digital assets.\",\r\n    buttonText: \"SOC Services\",\r\n    linkText: \"Discover our SOC Services\",\r\n    linkHref: \"/blockchain-security\",\r\n    image: \"/images/homepagecards/2.svg\",\r\n    alt: \"Security operations center with multiple monitors\"\r\n  },\r\n  {\r\n    title: \"Find weaknesses before attackers do\",\r\n    description: \"Simulate real-world attacks to identify hidden vulnerabilities in your systems. Strengthen your defenses before cybercriminals can take advantage of them.\",\r\n    buttonText: \"Penetration Testing\",\r\n    image: \"/images/homepagecards/6.svg\",\r\n    linkText: \"Discover our pentesting services\",\r\n    linkHref: \"/penetration-testing\",\r\n    alt: \"Professional man working on computer in modern office\"\r\n  },\r\n  {\r\n    title: \"Strategic cybersecurity leadership on demand\",\r\n    description: \"Access experienced cybersecurity leaders who provide tailored strategy, compliance guidance, and executive-level insight without full-time cost.\",\r\n    buttonText: \"Cyber Threat Intelligence\",\r\n    linkText: \"Meet Our Security Leaders\",\r\n    linkHref: \"/cybersecurity-leadership\",\r\n    image: \"/images/homepagecards/3.svg\",\r\n    alt: \"Cybersecurity analyst working on threat detection systems\"\r\n  }, \r\n  {\r\n    title: \"Rapid response when security incidents strike\",\r\n    description: \"Get immediate support during a breach with expert-led investigation, containment, and recovery. We help you resume operations quickly and securely.\",\r\n    buttonText: \"Compliance Services\",\r\n    linkText: \"Discover our Compliance Services\",\r\n    linkHref: \"/incident-response\",\r\n    image: \"/images/homepagecards/5.svg\",\r\n    alt: \"Compliance team reviewing security frameworks\"\r\n  },\r\n\r\n];\r\n\r\n\r\n  const slides = [\r\n    allChallenges.slice(0, 3), // First slide - first 3 items\r\n    allChallenges.slice(3, 6)  // Second slide - next 3 items\r\n  ];\r\n\r\n  const nextSlide = () => {\r\n    setCurrentSlide((prev) => (prev + 1) % slides.length);\r\n  };\r\n\r\n  const goToSlide = (index) => {\r\n    setCurrentSlide(index);\r\n  };\r\n\r\n  return (\r\n    <section className=\"py-12 sm:py-16 lg:py-24 bg-gray-50 relative\">\r\n      {/* Background Image */}\r\n      <div className=\"absolute inset-0 z-0\">\r\n        <Image\r\n          src=\"/images/<EMAIL>\"\r\n          alt=\"Background pattern\"\r\n          fill\r\n          className=\"object-cover opacity-60\"\r\n          priority\r\n        />\r\n      </div>\r\n      \r\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\r\n        {/* Header Badge */}\r\n        <div className=\"text-center mb-8 sm:mb-12\">\r\n          <div className=\"inline-flex items-center px-3 sm:px-4 py-2 rounded-full border border-gray-300 bg-white mb-6 sm:mb-8\">\r\n            <span className=\"text-gray-600 text-xs sm:text-sm\">Backed by experience and results</span>\r\n          </div>\r\n          \r\n          {/* Main Heading */}\r\n          <h2 className=\"text-3xl font-bold text-slate-800 mb-4 sm:mb-6 leading-tight px-4 sm:px-0\">\r\n            Solving the security challenges that matter most\r\n          </h2>\r\n          \r\n          {/* Subheading */}\r\n          <p className=\"text-base sm:text-lg  text-gray-600 max-w-5xl mx-auto leading-relaxed px-4 sm:px-0\">\r\n            We're more than <span className=\"text-slate-800 font-medium\">just another cyber security provider</span> - we're your trusted partner in identifying what matters, cutting through complexity, and delivering the protection your business needs to stay secure.\r\n          </p>\r\n        </div>\r\n\r\n        {/* Mobile: Single card scroll, Desktop: Carousel */}\r\n        <div className=\"relative\">\r\n          {/* Mobile View - Single card at a time */}\r\n          <div className=\"block lg:hidden\">\r\n            <div className=\"px-4 py-6\">\r\n              <div className=\"flex overflow-x-auto snap-x snap-mandatory scrollbar-hide space-x-4 pb-4\">\r\n                {allChallenges.map((challenge, index) => (\r\n                  <div \r\n                    key={index} \r\n                    className=\"flex-none w-80 sm:w-96 bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 relative z-10 snap-center\"\r\n                  >\r\n                    {/* Image */}\r\n                    <div className=\"relative overflow-hidden h-40 sm:h-48 rounded-t-2xl\">\r\n                      <Image\r\n                        src={challenge.image}\r\n                        alt={challenge.alt}\r\n                        fill\r\n                        className=\"object-cover hover:scale-105 transition-transform duration-300\"\r\n                        placeholder=\"blur\"\r\n                        blurDataURL=\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q==\"\r\n                      />\r\n                    </div>\r\n                    \r\n                    {/* Content */}\r\n                    <div className=\"p-4 sm:p-6\">\r\n                      <h3 className=\"text-lg sm:text-xl font-bold text-slate-800 mb-3 leading-tight\">\r\n                        {challenge.title}\r\n                      </h3>\r\n                      <p className=\"text-gray-600 mb-4 sm:mb-6 leading-relaxed text-sm sm:text-base\">\r\n                        {challenge.description}\r\n                      </p>\r\n                      \r\n                      {/* Button */}\r\n                       <Link\r\n              href={challenge.linkHref}\r\n              className=\"inline-flex items-center text-sm text-blue-600 hover:text-blue-800 hover:underline font-medium transition-colors duration-200\"\r\n            >\r\n              <span className=\"text-lg font-bold italic\">{challenge.linkText}</span>\r\n              <svg \r\n                className=\"ml-1 w-4 h-4 font-bold\" \r\n                fill=\"none\" \r\n                stroke=\"currentColor\" \r\n                viewBox=\"0 0 24 24\"\r\n              >\r\n                <path \r\n                  strokeLinecap=\"round\" \r\n                  strokeLinejoin=\"round\" \r\n                  strokeWidth={2} \r\n                  d=\"M9 5l7 7-7 7\" \r\n                />\r\n                <path \r\n                  strokeLinecap=\"round\" \r\n                  strokeLinejoin=\"round\" \r\n                  strokeWidth={2} \r\n                  d=\"M13 5l7 7-7 7\" \r\n                />\r\n              </svg>\r\n            </Link>\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Desktop View - Carousel with 3 cards */}\r\n          <div className=\"hidden lg:block\">\r\n            <div className=\"px-4 py-8\">\r\n              <div \r\n                className=\"flex transition-transform duration-500 ease-in-out\"\r\n                style={{ transform: `translateX(-${currentSlide * 100}%)` }}\r\n              >\r\n                {slides.map((slideCards, slideIndex) => (\r\n                  <div key={slideIndex} className=\"w-full flex-shrink-0 px-4\">\r\n                    <div className=\"grid grid-cols-1 xl:grid-cols-3 gap-6 xl:gap-8\">\r\n                      {slideCards.map((challenge, cardIndex) => (\r\n                        <div \r\n                          key={cardIndex} \r\n                          className=\"bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 relative z-10\"\r\n                        >\r\n                          {/* Image */}\r\n                          <div className=\"relative overflow-hidden h-48 xl:h-48 rounded-t-2xl\">\r\n                            <Image\r\n                              src={challenge.image}\r\n                              alt={challenge.alt}\r\n                              fill\r\n                              className=\"object-cover hover:scale-105 transition-transform duration-300\"\r\n                              placeholder=\"blur\"\r\n                              blurDataURL=\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q==\"\r\n                            />\r\n                          </div>\r\n                          \r\n                          {/* Content */}\r\n                          <div className=\"p-6\">\r\n                            <h3 className=\"text-lg xl:text-xl font-bold text-slate-800 mb-3 leading-tight\">\r\n                              {challenge.title}\r\n                            </h3>\r\n                            <p className=\"text-gray-600 mb-6 leading-relaxed text-sm xl:text-base\">\r\n                              {challenge.description}\r\n                            </p>\r\n                            \r\n                              <Link\r\n              href={challenge.linkHref}\r\n              className=\"inline-flex items-center text-sm text-blue-600 hover:text-blue-800 hover:underline font-medium transition-colors duration-200\"\r\n            >\r\n              <span className=\"text-lg font-bold italic\">{challenge.linkText}</span>\r\n              <svg \r\n                className=\"ml-1 w-4 h-4 font-bold\" \r\n                fill=\"none\" \r\n                stroke=\"currentColor\" \r\n                viewBox=\"0 0 24 24\"\r\n              >\r\n                <path \r\n                  strokeLinecap=\"round\" \r\n                  strokeLinejoin=\"round\" \r\n                  strokeWidth={2} \r\n                  d=\"M9 5l7 7-7 7\" \r\n                />\r\n                <path \r\n                  strokeLinecap=\"round\" \r\n                  strokeLinejoin=\"round\" \r\n                  strokeWidth={2} \r\n                  d=\"M13 5l7 7-7 7\" \r\n                />\r\n              </svg>\r\n            </Link>\r\n                          </div>\r\n                        </div>\r\n                      ))}\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Navigation Dots - Only show on desktop */}\r\n        <div className=\"hidden lg:flex justify-center items-center mt-6 lg:mt-8 space-x-3\">\r\n          {slides.map((_, index) => (\r\n            <button\r\n              key={index}\r\n              onClick={() => goToSlide(index)}\r\n              className={`transition-all duration-300 rounded-full ${\r\n                index === currentSlide \r\n                  ? 'w-6 lg:w-8 h-2 lg:h-3 bg-gray-600' \r\n                  : 'w-2 lg:w-3 h-2 lg:h-3 bg-gray-400 hover:bg-gray-500'\r\n              }`}\r\n            />\r\n          ))}\r\n        </div>\r\n\r\n        {/* Scroll indicator for mobile */}\r\n        <div className=\"block lg:hidden text-center mt-4\">\r\n          <p className=\"text-xs text-gray-500\">Swipe to see more services</p>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;AAHA;;;;AAMe,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,gBAAgB;QAClB;YACA,OAAO;YACP,aAAa;YACb,YAAY;YACZ,UAAU;YACV,UAAU;YACV,OAAO;YACP,KAAK;QACP;QACC;YACC,OAAO;YACP,aAAa;YACb,YAAY;YACZ,UAAU;YACV,UAAU;YACV,OAAO;YACP,KAAK;QACP;QACC;YACC,OAAO;YACP,aAAa;YACb,YAAY;YACZ,UAAU;YACV,UAAU;YACV,OAAO;YACP,KAAK;QACP;QACA;YACE,OAAO;YACP,aAAa;YACb,YAAY;YACZ,OAAO;YAC<PERSON>,UAAU;YACV,UAAU;YACV,KAAK;QACP;QACA;YACE,OAAO;YACP,aAAa;YACb,YAAY;YACZ,UAAU;YACV,UAAU;YACV,OAAO;YACP,KAAK;QACP;QACA;YACE,OAAO;YACP,aAAa;YACb,YAAY;YACZ,UAAU;YACV,UAAU;YACV,OAAO;YACP,KAAK;QACP;KAED;IAGC,MAAM,SAAS;QACb,cAAc,KAAK,CAAC,GAAG;QACvB,cAAc,KAAK,CAAC,GAAG,GAAI,8BAA8B;KAC1D;IAED,MAAM,YAAY;QAChB,gBAAgB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,OAAO,MAAM;IACtD;IAEA,MAAM,YAAY,CAAC;QACjB,gBAAgB;IAClB;IAEA,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oBACJ,KAAI;oBACJ,KAAI;oBACJ,IAAI;oBACJ,WAAU;oBACV,QAAQ;;;;;;;;;;;0BAIZ,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAAmC;;;;;;;;;;;0CAIrD,6LAAC;gCAAG,WAAU;0CAA4E;;;;;;0CAK1F,6LAAC;gCAAE,WAAU;;oCAAqF;kDAChF,6LAAC;wCAAK,WAAU;kDAA6B;;;;;;oCAA2C;;;;;;;;;;;;;kCAK5G,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACZ,cAAc,GAAG,CAAC,CAAC,WAAW,sBAC7B,6LAAC;gDAEC,WAAU;;kEAGV,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;4DACJ,KAAK,UAAU,KAAK;4DACpB,KAAK,UAAU,GAAG;4DAClB,IAAI;4DACJ,WAAU;4DACV,aAAY;4DACZ,aAAY;;;;;;;;;;;kEAKhB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EACX,UAAU,KAAK;;;;;;0EAElB,6LAAC;gEAAE,WAAU;0EACV,UAAU,WAAW;;;;;;0EAIvB,6LAAC,+JAAA,CAAA,UAAI;gEACd,MAAM,UAAU,QAAQ;gEACxB,WAAU;;kFAEV,6LAAC;wEAAK,WAAU;kFAA4B,UAAU,QAAQ;;;;;;kFAC9D,6LAAC;wEACC,WAAU;wEACV,MAAK;wEACL,QAAO;wEACP,SAAQ;;0FAER,6LAAC;gFACC,eAAc;gFACd,gBAAe;gFACf,aAAa;gFACb,GAAE;;;;;;0FAEJ,6LAAC;gFACC,eAAc;gFACd,gBAAe;gFACf,aAAa;gFACb,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;+CA9CK;;;;;;;;;;;;;;;;;;;;0CA0Df,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,WAAW,AAAC,eAAiC,OAAnB,eAAe,KAAI;wCAAI;kDAEzD,OAAO,GAAG,CAAC,CAAC,YAAY,2BACvB,6LAAC;gDAAqB,WAAU;0DAC9B,cAAA,6LAAC;oDAAI,WAAU;8DACZ,WAAW,GAAG,CAAC,CAAC,WAAW,0BAC1B,6LAAC;4DAEC,WAAU;;8EAGV,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;wEACJ,KAAK,UAAU,KAAK;wEACpB,KAAK,UAAU,GAAG;wEAClB,IAAI;wEACJ,WAAU;wEACV,aAAY;wEACZ,aAAY;;;;;;;;;;;8EAKhB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAG,WAAU;sFACX,UAAU,KAAK;;;;;;sFAElB,6LAAC;4EAAE,WAAU;sFACV,UAAU,WAAW;;;;;;sFAGtB,6LAAC,+JAAA,CAAA,UAAI;4EACrB,MAAM,UAAU,QAAQ;4EACxB,WAAU;;8FAEV,6LAAC;oFAAK,WAAU;8FAA4B,UAAU,QAAQ;;;;;;8FAC9D,6LAAC;oFACC,WAAU;oFACV,MAAK;oFACL,QAAO;oFACP,SAAQ;;sGAER,6LAAC;4FACC,eAAc;4FACd,gBAAe;4FACf,aAAa;4FACb,GAAE;;;;;;sGAEJ,6LAAC;4FACC,eAAc;4FACd,gBAAe;4FACf,aAAa;4FACb,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;2DA7CW;;;;;;;;;;+CAJH;;;;;;;;;;;;;;;;;;;;;;;;;;kCAiEpB,6LAAC;wBAAI,WAAU;kCACZ,OAAO,GAAG,CAAC,CAAC,GAAG,sBACd,6LAAC;gCAEC,SAAS,IAAM,UAAU;gCACzB,WAAW,AAAC,4CAIX,OAHC,UAAU,eACN,sCACA;+BALD;;;;;;;;;;kCAYX,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;;;;;;;AAK/C;GAzQwB;KAAA", "debugId": null}}, {"offset": {"line": 774, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/Home/components/ComplianceSection.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport { FaArrowRight } from 'react-icons/fa';\r\nimport { PrimaryButton } from '@/app/common/buttons/BrandButtons';\r\nimport { useRouter } from 'next/navigation';\r\nimport Image from 'next/image';\r\n\r\nexport default function ComplianceSection() {\r\n  const router = useRouter();\r\n\r\n  const handleContactRedirect = () => {\r\n    router.push('/contact');\r\n  };\r\n\r\n  return (\r\n    <section className=\"w-full py-12 md:py-16 lg:py-26  bg-[#F8F8F8]\">\r\n      <div className=\"container mx-auto \">\r\n        <div className=\"grid lg:grid-cols-2 gap-8 md:gap-12 items-center max-w-10xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n          \r\n          {/* Left Section: Compliance Logos */}\r\n          <div className=\"space-y-8 md:space-y-12\">\r\n            {/* Top Row */}\r\n            <div className=\"grid grid-cols-3 gap-4 md:gap-6 lg:gap-8\">\r\n              {/* HIPAA Compliance */}\r\n              <div className=\"flex flex-col items-center space-y-2\">\r\n                <div className=\"w-20 h-20 md:w-24 md:h-24 lg:w-28 lg:h-28 rounded-lg flex items-center justify-center overflow-hidden\">\r\n                  <Image \r\n                    src=\"/images/hipaa-logo.png\" \r\n                    alt=\"HIPAA Compliance Logo\" \r\n                    width={112} \r\n                    height={112}\r\n                    className=\"object-contain w-full h-full\"\r\n                  />\r\n                </div>\r\n              </div>\r\n              \r\n              {/* PCI DSS Compliant */}\r\n              <div className=\"flex flex-col items-center space-y-2\">\r\n                <div className=\"w-20 h-20 md:w-24 md:h-24 lg:w-28 lg:h-28 rounded-lg flex items-center justify-center overflow-hidden\">\r\n                  <Image \r\n                    src=\"/images/pci-dss-logo.png\" \r\n                    alt=\"PCI DSS Compliant Logo\" \r\n                    width={112} \r\n                    height={112}\r\n                    className=\"object-contain w-full h-full\"\r\n                  />\r\n                </div>\r\n              </div>\r\n              \r\n              {/* CREST */}\r\n              <div className=\"flex flex-col items-center space-y-2\">\r\n                <div className=\"w-20 h-20 md:w-24 md:h-24 lg:w-28 lg:h-28 rounded-lg flex items-center justify-center overflow-hidden\">\r\n                  <Image \r\n                    src=\"/images/crest-logo.png\" \r\n                    alt=\"CREST Logo\" \r\n                    width={112} \r\n                    height={112}\r\n                    className=\"object-contain w-full h-full\"\r\n                  />\r\n                </div>\r\n              </div>\r\n            </div>\r\n            \r\n            {/* Bottom Row */}\r\n            <div className=\"grid grid-cols-3 gap-4 md:gap-6 lg:gap-8\">\r\n              {/* ISO 27001 Certified */}\r\n              <div className=\"flex flex-col items-center space-y-2\">\r\n                <div className=\"w-20 h-20 md:w-24 md:h-24 lg:w-28 lg:h-28 rounded-full flex items-center justify-center overflow-hidden\">\r\n                  <Image \r\n                    src=\"/images/iso27001-logo-2.png\" \r\n                    alt=\"ISO 27001 Certified Logo\" \r\n                    width={112} \r\n                    height={112}\r\n                    className=\"object-contain w-full h-full\"\r\n                  />\r\n                </div>\r\n              </div>\r\n              \r\n              {/* AICPA SOC 2 */}\r\n              <div className=\"flex flex-col items-center space-y-2\">\r\n                <div className=\"w-20 h-20 md:w-24 md:h-24 lg:w-28 lg:h-28 rounded-full flex items-center justify-center overflow-hidden\">\r\n                  <Image \r\n                    src=\"/images/aicpa-soc2-logo.jpg\" \r\n                    alt=\"AICPA SOC 2 Logo\" \r\n                    width={112} \r\n                    height={112}\r\n                    className=\"object-contain w-full h-full\"\r\n                  />\r\n                </div>\r\n              </div>\r\n              \r\n              {/* GDPR */}\r\n              <div className=\"flex flex-col items-center space-y-2\">\r\n                <div className=\"w-20 h-20 md:w-24 md:h-24 lg:w-28 lg:h-28 rounded-full flex items-center justify-center overflow-hidden\">\r\n                  <Image \r\n                    src=\"/images/gdpr-logo.jpg\" \r\n                    alt=\"GDPR Logo\" \r\n                    width={112} \r\n                    height={112}\r\n                    className=\"object-contain w-full h-full\"\r\n                  />\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          \r\n          {/* Right Section: Text and Button */}\r\n          <div className=\"space-y-4 md:space-y-8\">\r\n            <h2 className=\"text-2xl sm:text-3xl md:text-4xl font-bold text-[var(--color-dark-blue)] leading-tight\">\r\n              Penetration Testing Requirements<br />\r\n              Covered by <span className=\"text-[var(--color-blue)]\">SecurityLit</span>\r\n            </h2>\r\n            \r\n            <p className=\"text-base md:text-lg text-gray-700 max-w-lg\">\r\n              At SecurityLit, our pentesting guarantees extensive coverage of key compliance frameworks, offering robust security solutions tailored to your unique requirements.\r\n            </p>\r\n            \r\n            <PrimaryButton onClick={handleContactRedirect}>\r\n              Request a Pentest\r\n              <FaArrowRight className=\"text-sm\" />\r\n            </PrimaryButton>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAQe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,wBAAwB;QAC5B,OAAO,IAAI,CAAC;IACd;IAEA,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAGb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,OAAO;gDACP,QAAQ;gDACR,WAAU;;;;;;;;;;;;;;;;kDAMhB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,OAAO;gDACP,QAAQ;gDACR,WAAU;;;;;;;;;;;;;;;;kDAMhB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,OAAO;gDACP,QAAQ;gDACR,WAAU;;;;;;;;;;;;;;;;;;;;;;0CAOlB,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,OAAO;gDACP,QAAQ;gDACR,WAAU;;;;;;;;;;;;;;;;kDAMhB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,OAAO;gDACP,QAAQ;gDACR,WAAU;;;;;;;;;;;;;;;;kDAMhB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,OAAO;gDACP,QAAQ;gDACR,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQpB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;oCAAyF;kDACrE,6LAAC;;;;;oCAAK;kDAC3B,6LAAC;wCAAK,WAAU;kDAA2B;;;;;;;;;;;;0CAGxD,6LAAC;gCAAE,WAAU;0CAA8C;;;;;;0CAI3D,6LAAC,mJAAA,CAAA,gBAAa;gCAAC,SAAS;;oCAAuB;kDAE7C,6LAAC,iJAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtC;GAvHwB;;QACP,qIAAA,CAAA,YAAS;;;KADF", "debugId": null}}, {"offset": {"line": 1073, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/Home/components/WhyChooseUsSection.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect } from \"react\";\r\nimport { Shield, Clock, AlertCircle, Award, Users, Lock, Headphones } from \"lucide-react\";\r\n\r\n// Counter component for animated numbers\r\nconst Counter = ({ end, duration = 2000, suffix = \"\" }) => {\r\n  const [count, setCount] = useState(0);\r\n\r\n  useEffect(() => {\r\n    let startTime = null;\r\n    const startValue = 0;\r\n    const endValue = end;\r\n\r\n    const animate = (currentTime) => {\r\n      if (!startTime) startTime = currentTime;\r\n      const progress = Math.min((currentTime - startTime) / duration, 1);\r\n      \r\n      const currentCount = Math.floor(startValue + (endValue - startValue) * progress);\r\n      setCount(currentCount);\r\n\r\n      if (progress < 1) {\r\n        requestAnimationFrame(animate);\r\n      }\r\n    };\r\n\r\n    requestAnimationFrame(animate);\r\n  }, [end, duration]);\r\n\r\n  return <span>{count}{suffix}</span>;\r\n};\r\n\r\nexport default function TrustedByLeadersSection() {\r\n  const trustMetrics = [\r\n    { \r\n      metric: 472, \r\n      label: \"Companies Protected\", \r\n      icon: Shield, \r\n      suffix: \"+\",\r\n      color: \"bg-blue-500\"\r\n    },\r\n    { \r\n      metric: 94.3, \r\n      label: \"Uptime Guarantee\", \r\n      icon: Clock, \r\n      suffix: \"\",\r\n      color: \"bg-blue-500\"\r\n    },\r\n    { \r\n      metric: 0, \r\n      label: \"Security Breaches\", \r\n      icon: AlertCircle, \r\n      suffix: \"+\",\r\n      color: \"bg-blue-500\"\r\n    },\r\n    { \r\n      metric: 14.2, \r\n      label: \"Years Experience\", \r\n      icon: Award, \r\n      suffix: \"\",\r\n      color: \"bg-blue-500\"\r\n    }\r\n  ];\r\n\r\n  const serviceCards = [\r\n    {\r\n      icon: Shield,\r\n      title: \"Industry Compliance\",\r\n      description: \"ISO 27001, SOC 2, GDPR, and industry-specific compliance frameworks covered\",\r\n      iconBg: \"bg-yellow-500\"\r\n    },\r\n    {\r\n      icon: Lock,\r\n      title: \"Advanced Technology\", \r\n      description: \"AI-powered threat detection and automated response systems\",\r\n      iconBg: \"bg-yellow-500\"\r\n    },\r\n    {\r\n      icon: Headphones,\r\n      title: \"Dedicated Support\",\r\n      description: \"Personal security consultant assigned to your account\",\r\n      iconBg: \"bg-yellow-500\"\r\n    }\r\n  ];\r\n\r\n  return (\r\n    <section className=\"py-24 bg-slate-800 text-white\">\r\n      <div className=\"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 \">\r\n        {/* Header */}\r\n        <div className=\"text-center mb-12\">\r\n          <h2 className=\"text-3xl font-bold mb-4\">Trusted by Industry Leaders</h2>\r\n          <p className=\"text-slate-300 max-w-2xl mx-auto\">\r\n            Our numbers speak for themselves - delivering exceptional cybersecurity results across industries\r\n          </p>\r\n        </div>\r\n\r\n        {/* Trust Metrics Grid */}\r\n        <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-6 mb-12\">\r\n          {trustMetrics.map((item, index) => {\r\n            const IconComponent = item.icon;\r\n            return (\r\n              <div key={index} className=\"bg-white rounded-2xl p-6 text-center\">\r\n                <div className=\"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\">\r\n                  <IconComponent className=\"w-6 h-6 text-blue-600\" />\r\n                </div>\r\n                <div className=\"text-3xl font-bold text-slate-800 mb-2\">\r\n                  {typeof item.metric === 'number' && item.metric !== 0 ? (\r\n                    <Counter end={item.metric} suffix={item.suffix} />\r\n                  ) : item.metric === 0 ? (\r\n                    <span>0{item.suffix}</span>\r\n                  ) : (\r\n                    <span>{item.metric}{item.suffix}</span>\r\n                  )}\r\n                </div>\r\n                <div className=\"text-slate-600 text-sm font-medium\">\r\n                  {item.label}\r\n                </div>\r\n              </div>\r\n            );\r\n          })}\r\n        </div>\r\n\r\n        {/* Service Cards */}\r\n        <div className=\"grid lg:grid-cols-3 gap-6\">\r\n          {serviceCards.map((card, index) => {\r\n            const IconComponent = card.icon;\r\n            return (\r\n              <div key={index} className=\"bg-[#1E40AF]/20 rounded-2xl p-6\">\r\n                <div className=\"w-12 h-12 bg-yellow-500 rounded-lg flex items-center justify-center mb-4\">\r\n                  <IconComponent className=\"w-6 h-6 text-white\" />\r\n                </div>\r\n                <h3 className=\"text-xl font-bold text-white mb-3\">\r\n                  {card.title}\r\n                </h3>\r\n                <p className=\"text-slate-300 leading-relaxed\">\r\n                  {card.description}\r\n                </p>\r\n              </div>\r\n            );\r\n          })}\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AAKA,yCAAyC;AACzC,MAAM,UAAU;QAAC,EAAE,GAAG,EAAE,WAAW,IAAI,EAAE,SAAS,EAAE,EAAE;;IACpD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,IAAI,YAAY;YAChB,MAAM,aAAa;YACnB,MAAM,WAAW;YAEjB,MAAM;6CAAU,CAAC;oBACf,IAAI,CAAC,WAAW,YAAY;oBAC5B,MAAM,WAAW,KAAK,GAAG,CAAC,CAAC,cAAc,SAAS,IAAI,UAAU;oBAEhE,MAAM,eAAe,KAAK,KAAK,CAAC,aAAa,CAAC,WAAW,UAAU,IAAI;oBACvE,SAAS;oBAET,IAAI,WAAW,GAAG;wBAChB,sBAAsB;oBACxB;gBACF;;YAEA,sBAAsB;QACxB;4BAAG;QAAC;QAAK;KAAS;IAElB,qBAAO,6LAAC;;YAAM;YAAO;;;;;;;AACvB;GAxBM;KAAA;AA0BS,SAAS;IACtB,MAAM,eAAe;QACnB;YACE,QAAQ;YACR,OAAO;YACP,MAAM,yMAAA,CAAA,SAAM;YACZ,QAAQ;YACR,OAAO;QACT;QACA;YACE,QAAQ;YACR,OAAO;YACP,MAAM,uMAAA,CAAA,QAAK;YACX,QAAQ;YACR,OAAO;QACT;QACA;YACE,QAAQ;YACR,OAAO;YACP,MAAM,uNAAA,CAAA,cAAW;YACjB,QAAQ;YACR,OAAO;QACT;QACA;YACE,QAAQ;YACR,OAAO;YACP,MAAM,uMAAA,CAAA,QAAK;YACX,QAAQ;YACR,OAAO;QACT;KACD;IAED,MAAM,eAAe;QACnB;YACE,MAAM,yMAAA,CAAA,SAAM;YACZ,OAAO;YACP,aAAa;YACb,QAAQ;QACV;QACA;YACE,MAAM,qMAAA,CAAA,OAAI;YACV,OAAO;YACP,aAAa;YACb,QAAQ;QACV;QACA;YACE,MAAM,iNAAA,CAAA,aAAU;YAChB,OAAO;YACP,aAAa;YACb,QAAQ;QACV;KACD;IAED,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA0B;;;;;;sCACxC,6LAAC;4BAAE,WAAU;sCAAmC;;;;;;;;;;;;8BAMlD,6LAAC;oBAAI,WAAU;8BACZ,aAAa,GAAG,CAAC,CAAC,MAAM;wBACvB,MAAM,gBAAgB,KAAK,IAAI;wBAC/B,qBACE,6LAAC;4BAAgB,WAAU;;8CACzB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAc,WAAU;;;;;;;;;;;8CAE3B,6LAAC;oCAAI,WAAU;8CACZ,OAAO,KAAK,MAAM,KAAK,YAAY,KAAK,MAAM,KAAK,kBAClD,6LAAC;wCAAQ,KAAK,KAAK,MAAM;wCAAE,QAAQ,KAAK,MAAM;;;;;+CAC5C,KAAK,MAAM,KAAK,kBAClB,6LAAC;;4CAAK;4CAAE,KAAK,MAAM;;;;;;6DAEnB,6LAAC;;4CAAM,KAAK,MAAM;4CAAE,KAAK,MAAM;;;;;;;;;;;;8CAGnC,6LAAC;oCAAI,WAAU;8CACZ,KAAK,KAAK;;;;;;;2BAdL;;;;;oBAkBd;;;;;;8BAIF,6LAAC;oBAAI,WAAU;8BACZ,aAAa,GAAG,CAAC,CAAC,MAAM;wBACvB,MAAM,gBAAgB,KAAK,IAAI;wBAC/B,qBACE,6LAAC;4BAAgB,WAAU;;8CACzB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAc,WAAU;;;;;;;;;;;8CAE3B,6LAAC;oCAAG,WAAU;8CACX,KAAK,KAAK;;;;;;8CAEb,6LAAC;oCAAE,WAAU;8CACV,KAAK,WAAW;;;;;;;2BARX;;;;;oBAYd;;;;;;;;;;;;;;;;;AAKV;MAhHwB", "debugId": null}}, {"offset": {"line": 1358, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/Home/components/TrustedTestimonials.jsx"], "sourcesContent": ["\"use client\";\r\nimport React from \"react\";\r\nimport Image from \"next/image\";\r\n\r\nconst TrustedTestimonials = () => {\r\n  const trustedCompanies = [\r\n    {\r\n      name: \"Microsoft\",\r\n      logo: \"/images/iso27001-logo.png\", // Using existing logo as placeholder\r\n      alt: \"Microsoft Logo\"\r\n    },\r\n    {\r\n      name: \"Amazon\", \r\n      logo: \"/images/pci-dss-logo.png\",\r\n      alt: \"Amazon Logo\"\r\n    },\r\n    {\r\n      name: \"Google\",\r\n      logo: \"/images/crest-logo.png\", \r\n      alt: \"Google Logo\"\r\n    },\r\n    {\r\n      name: \"Apple\",\r\n      logo: \"/images/hipaa-logo.png\",\r\n      alt: \"Apple Logo\"\r\n    }\r\n  ];\r\n\r\n  const testimonials = [\r\n    {\r\n      name: \"<PERSON>\",\r\n      title: \"CTO at TechCorp\",\r\n      photo: \"/images/ankita-dhakar.jpeg\", // Using existing team photo\r\n      quote: \"SecurityLit's comprehensive security solutions have transformed our approach to cybersecurity. Their real-time monitoring and incident response capabilities are unmatched.\"\r\n    },\r\n    {\r\n      name: \"<PERSON>\", \r\n      title: \"CISO at DataFlow\",\r\n      photo: \"/images/akash-verma.jpeg\", // Using existing team photo\r\n      quote: \"The level of expertise and dedication from the SecurityLit team is exceptional. They've helped us maintain the highest security standards while scaling our operations.\"\r\n    },\r\n    {\r\n      name: \"<PERSON>\",\r\n      title: \"VP Security at CloudNet\", \r\n      photo: \"/images/archana-verma.jpeg\", // Using existing team photo\r\n      quote: \"Implementing SecurityLit's PTaaS solution was a game-changer for our organization. The continuous testing and real-time alerts have significantly improved our security posture.\"\r\n    }\r\n  ];\r\n\r\n  return (\r\n    <section className=\"w-full\">\r\n             {/* Dark Section - Trusted by Industry Leaders */}\r\n       <div className=\"bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 py-20\">\r\n         <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n           <div className=\"text-center mb-10\">\r\n                         <h2 className=\"text-3xl md:text-4xl font-bold text-white mb-3\">\r\n               Trusted by Industry Leaders\r\n             </h2>\r\n             <p className=\"text-lg text-gray-300 max-w-3xl mx-auto\">\r\n               Join hundreds of leading organizations that trust SecurityLit for their cybersecurity needs\r\n             </p>\r\n          </div>\r\n          \r\n                     {/* Company Logos */}\r\n           <div className=\"flex flex-wrap justify-center items-center gap-6 md:gap-8\">\r\n             {trustedCompanies.map((company, index) => (\r\n               <div \r\n                 key={index}\r\n                 className=\"bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 rounded-xl p-4 md:p-6 hover:bg-gray-800/70 transition-all duration-300 group\"\r\n               >\r\n                                   <div className=\"w-20 h-20 md:w-24 md:h-24 mx-auto mb-3 bg-white rounded-lg flex items-center justify-center p-3 group-hover:scale-110 transition-transform duration-300\">\r\n                    <Image\r\n                      src={company.logo}\r\n                      alt={company.alt}\r\n                      width={64}\r\n                      height={64}\r\n                      className=\"w-12 h-12 md:w-16 md:h-16 object-contain\"\r\n                    />\r\n                  </div>\r\n                                   <p className=\"text-white font-medium text-center text-sm md:text-base\">\r\n                    {company.name}\r\n                  </p>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n             {/* White Section - Testimonials */}\r\n       <div className=\"bg-white py-20 border-t-4 border-purple-500\">\r\n         <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n           <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\r\n            {testimonials.map((testimonial, index) => (\r\n                                            <div \r\n                 key={index}\r\n                 className=\"bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100\"\r\n               >\r\n                 {/* Testimonial Photo */}\r\n                 <div className=\"flex items-center mb-6\">\r\n                   <div className=\"w-14 h-14 rounded-full overflow-hidden mr-3\">\r\n                     <Image\r\n                       src={testimonial.photo}\r\n                       alt={testimonial.name}\r\n                       width={56}\r\n                       height={56}\r\n                       className=\"w-full h-full object-cover\"\r\n                     />\r\n                   </div>\r\n                   <div>\r\n                     <h3 className=\"font-bold text-gray-900 text-base\">\r\n                       {testimonial.name}\r\n                     </h3>\r\n                     <p className=\"text-orange-500 font-medium text-sm\">\r\n                       {testimonial.title}\r\n                     </p>\r\n                   </div>\r\n                 </div>\r\n                 \r\n                                   {/* Testimonial Quote */}\r\n                  <blockquote className=\"text-gray-600 leading-relaxed text-sm pt-4\">\r\n                    \"{testimonial.quote}\"\r\n                  </blockquote>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default TrustedTestimonials; "], "names": [], "mappings": ";;;;AACA;AACA;AAFA;;;;AAIA,MAAM,sBAAsB;IAC1B,MAAM,mBAAmB;QACvB;YACE,MAAM;YACN,MAAM;YACN,KAAK;QACP;QACA;YACE,MAAM;YACN,MAAM;YACN,KAAK;QACP;QACA;YACE,MAAM;YACN,MAAM;YACN,KAAK;QACP;QACA;YACE,MAAM;YACN,MAAM;YACN,KAAK;QACP;KACD;IAED,MAAM,eAAe;QACnB;YACE,MAAM;YACN,OAAO;YACP,OAAO;YACP,OAAO;QACT;QACA;YACE,MAAM;YACN,OAAO;YACP,OAAO;YACP,OAAO;QACT;QACA;YACE,MAAM;YACN,OAAO;YACP,OAAO;YACP,OAAO;QACT;KACD;IAED,qBACE,6LAAC;QAAQ,WAAU;;0BAEhB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACD,6LAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAG3E,6LAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAMzD,6LAAC;4BAAI,WAAU;sCACZ,iBAAiB,GAAG,CAAC,CAAC,SAAS,sBAC9B,6LAAC;oCAEC,WAAU;;sDAEQ,6LAAC;4CAAI,WAAU;sDAC9B,cAAA,6LAAC,gIAAA,CAAA,UAAK;gDACJ,KAAK,QAAQ,IAAI;gDACjB,KAAK,QAAQ,GAAG;gDAChB,OAAO;gDACP,QAAQ;gDACR,WAAU;;;;;;;;;;;sDAGG,6LAAC;4CAAE,WAAU;sDAC3B,QAAQ,IAAI;;;;;;;mCAbX;;;;;;;;;;;;;;;;;;;;;0BAsBf,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,aAAa,GAAG,CAAC,CAAC,aAAa,sBACA,6LAAC;gCAE5B,WAAU;;kDAGV,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oDACJ,KAAK,YAAY,KAAK;oDACtB,KAAK,YAAY,IAAI;oDACrB,OAAO;oDACP,QAAQ;oDACR,WAAU;;;;;;;;;;;0DAGd,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEACX,YAAY,IAAI;;;;;;kEAEnB,6LAAC;wDAAE,WAAU;kEACV,YAAY,KAAK;;;;;;;;;;;;;;;;;;kDAMvB,6LAAC;wCAAW,WAAU;;4CAA6C;4CAC/D,YAAY,KAAK;4CAAC;;;;;;;;+BA1BlB;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCtB;KA7HM;uCA+HS", "debugId": null}}, {"offset": {"line": 1610, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/Home/components/ContactUsSection.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState } from \"react\";\r\nimport { ArrowRight, Mail, Phone, MapPin } from \"lucide-react\";\r\n\r\nexport function ContactUsSection() {\r\n  const [formData, setFormData] = useState({\r\n    name: '',\r\n    email: '',\r\n    company: '',\r\n    message: ''\r\n  });\r\n\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [apiError, setApiError] = useState(\"\");\r\n  const [success, setSuccess] = useState(\"\");\r\n\r\n  const handleInputChange = (e) => {\r\n    const { name, value } = e.target;\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      [name]: value\r\n    }));\r\n  };\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    if (isSubmitting) return;\r\n    setIsSubmitting(true);\r\n    setApiError(\"\");\r\n    setSuccess(\"\");\r\n\r\n    try {\r\n      // Make the API request to store submission and send email\r\n      const response = await fetch(\"/api/submissions\", {\r\n        method: \"POST\",\r\n        headers: { \"Content-Type\": \"application/json\" },\r\n        body: JSON.stringify({\r\n          name: formData.name,\r\n          email: formData.email,\r\n          company: formData.company,\r\n          message: formData.message,\r\n          formType: 'contact-form',\r\n          subject: 'New Contact Form Submission from SecurityLit Website'\r\n        }),\r\n      });\r\n\r\n      const result = await response.json();\r\n      if (response.ok && result.success) {\r\n        // Clear form fields on success\r\n        setFormData({ name: '', email: '', company: '', message: '' });\r\n        setSuccess(\"Thank you! We'll get back to you within 24 hours.\");\r\n      } else {\r\n        setApiError(result.error || \"Something went wrong. Please try again.\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error submitting form:\", error);\r\n      setApiError(\"Failed to send message. Please try again.\");\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <section className=\"py-16 bg-gray-50 relative overflow-hidden\">\r\n      {/* Background Pattern */}\r\n      <div className=\"absolute inset-0\">\r\n        <div className=\"absolute top-0 right-1/4 w-72 h-72 bg-[var(--color-blue)]/5 rounded-full blur-3xl\"></div>\r\n        <div className=\"absolute bottom-0 left-1/4 w-96 h-96 bg-[var(--color-yellow)]/5 rounded-full blur-3xl\"></div>\r\n      </div>\r\n\r\n      <div className=\"w-full max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 relative\">\r\n        {/* Header Section */}\r\n        <div className=\"text-center mb-16 fade-in-up\">\r\n          <div className=\"inline-flex items-center bg-[var(--color-blue)]/10 px-4 py-2 rounded-full mb-6\">\r\n            <div className=\"w-2 h-2 bg-[var(--color-blue)] rounded-full mr-2\"></div>\r\n            <span className=\"text-sm font-medium text-[var(--color-blue)]\">Get In Touch</span>\r\n          </div>\r\n          \r\n          <h2 className=\"text-3xl md:text-4xl font-bold font-bold text-[var(--color-dark-blue)] mb-6 leading-tight\">\r\n            Ready to\r\n            <span className=\"block text-[var(--color-blue)]\">\r\n              Secure Your Business?\r\n            </span>\r\n          </h2>\r\n          \r\n          <p className=\"text-xl text-[var(--color-dark-blue)]/70 max-w-3xl mx-auto leading-relaxed\">\r\n            Connect with our cybersecurity experts for a free consultation and discover how we can protect your organization.\r\n          </p>\r\n        </div>\r\n\r\n        <div className=\"grid lg:grid-cols-2 gap-12 items-start\">\r\n          {/* Left: Content */}\r\n          <div className=\"space-y-8\">\r\n            <div className=\"bg-white rounded-2xl p-8 shadow-md border border-gray-100 fade-in-up\">\r\n              <div className=\"flex items-center gap-4 mb-6\">\r\n                <div className=\"w-14 h-14 bg-[var(--color-blue)] rounded-xl flex items-center justify-center shadow-lg\">\r\n                  <Mail className=\"w-7 h-7 text-white\" />\r\n                </div>\r\n                <div>\r\n                  <h3 className=\"text-2xl font-bold text-[var(--color-dark-blue)]\">Contact Information</h3>\r\n                  <p className=\"text-[var(--color-dark-blue)]/70\">Get in touch with our team</p>\r\n                </div>\r\n              </div>\r\n              \r\n              <div className=\"space-y-6\">\r\n                <div className=\"flex items-center gap-4\">\r\n                  <div className=\"w-10 h-10 bg-[var(--color-blue)]/10 rounded-lg flex items-center justify-center\">\r\n                    <Mail className=\"w-5 h-5 text-[var(--color-blue)]\" />\r\n                  </div>\r\n                  <div>\r\n                    <p className=\"font-semibold text-[var(--color-dark-blue)]\">Email</p>\r\n                    <p className=\"text-[var(--color-blue)]\"><EMAIL></p>\r\n                  </div>\r\n                </div>\r\n                \r\n                <div className=\"flex items-center gap-4\">\r\n                  <div className=\"w-10 h-10 bg-[var(--color-blue)]/10 rounded-lg flex items-center justify-center\">\r\n                    <Phone className=\"w-5 h-5 text-[var(--color-blue)]\" />\r\n                  </div>\r\n                  <div>\r\n                    <p className=\"font-semibold text-[var(--color-dark-blue)]\">Phone</p>\r\n                    <p className=\"text-[var(--color-blue)]\">+91 8527 800769</p>\r\n                  </div>\r\n                </div>\r\n                \r\n                <div className=\"flex items-center gap-4\">\r\n                  <div className=\"w-10 h-10 bg-[var(--color-blue)]/10 rounded-lg flex items-center justify-center\">\r\n                    <MapPin className=\"w-5 h-5 text-[var(--color-blue)]\" />\r\n                  </div>\r\n                  <div>\r\n                    <p className=\"font-semibold text-[var(--color-dark-blue)]\">Office</p>\r\n                    <p className=\"text-[var(--color-dark-blue)]/70\">Gurugram, Haryana, India</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"bg-[var(--color-dark-blue)] rounded-2xl p-8 text-white fade-in-up\">\r\n              <h4 className=\"text-xl font-bold mb-4\">Why Choose SecurityLit?</h4>\r\n              <ul className=\"space-y-3 text-white\">\r\n                <li className=\"flex items-center gap-3\">\r\n                  <div className=\"w-2 h-2 bg-white rounded-full\"></div>\r\n                  <span>Free initial consultation</span>\r\n                </li>\r\n                <li className=\"flex items-center gap-3\">\r\n                  <div className=\"w-2 h-2  bg-white rounded-full\"></div>\r\n                  <span>24/7 expert support</span>\r\n                </li>\r\n                <li className=\"flex items-center gap-3\">\r\n                  <div className=\"w-2 h-2 bg-white rounded-full\"></div>\r\n                  <span>Customized security solutions</span>\r\n                </li>\r\n                <li className=\"flex items-center gap-3\">\r\n                  <div className=\"w-2 h-2 bg-white rounded-full\"></div>\r\n                  <span>Proven track record</span>\r\n                </li>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Right: Form */}\r\n          <div className=\"bg-white rounded-2xl shadow-xl p-8 border border-gray-100 fade-in-up\">\r\n            <div className=\"mb-8\">\r\n              <h3 className=\"text-2xl font-bold text-[var(--color-dark-blue)] mb-2\">Send us a message</h3>\r\n              <p className=\"text-[var(--color-dark-blue)]/70\">Fill out the form below and we'll get back to you within 24 hours.</p>\r\n            </div>\r\n\r\n            <form onSubmit={handleSubmit} className=\"space-y-6\">\r\n              {/* Error Message */}\r\n              {apiError && (\r\n                <div className=\"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg\">\r\n                  {apiError}\r\n                </div>\r\n              )}\r\n\r\n              {/* Success Message */}\r\n              {success && (\r\n                <div className=\"bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg\">\r\n                  {success}\r\n                </div>\r\n              )}\r\n\r\n              <div className=\"grid md:grid-cols-2 gap-4\">\r\n                <div>\r\n                  <label className=\"block text-sm font-semibold text-[var(--color-dark-blue)] mb-2\">Name *</label>\r\n                  <input\r\n                    type=\"text\"\r\n                    name=\"name\"\r\n                    value={formData.name}\r\n                    onChange={handleInputChange}\r\n                    required\r\n                    className=\"w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg text-[var(--color-dark-blue)] placeholder-gray-400 focus:outline-none focus:border-[var(--color-blue)] focus:bg-white transition-all\"\r\n                    placeholder=\"Your name\"\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <label className=\"block text-sm font-semibold text-[var(--color-dark-blue)] mb-2\">Email *</label>\r\n                  <input\r\n                    type=\"email\"\r\n                    name=\"email\"\r\n                    value={formData.email}\r\n                    onChange={handleInputChange}\r\n                    required\r\n                    className=\"w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg text-[var(--color-dark-blue)] placeholder-gray-400 focus:outline-none focus:border-[var(--color-blue)] focus:bg-white transition-all\"\r\n                    placeholder=\"<EMAIL>\"\r\n                  />\r\n                </div>\r\n              </div>\r\n              \r\n              <div>\r\n                <label className=\"block text-sm font-semibold text-[var(--color-dark-blue)] mb-2\">Company</label>\r\n                <input\r\n                  type=\"text\"\r\n                  name=\"company\"\r\n                  value={formData.company}\r\n                  onChange={handleInputChange}\r\n                  className=\"w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg text-[var(--color-dark-blue)] placeholder-gray-400 focus:outline-none focus:border-[var(--color-blue)] focus:bg-white transition-all\"\r\n                  placeholder=\"Your company\"\r\n                />\r\n              </div>\r\n              \r\n              <div>\r\n                <label className=\"block text-sm font-semibold text-[var(--color-dark-blue)] mb-2\">Message</label>\r\n                <textarea\r\n                  name=\"message\"\r\n                  value={formData.message}\r\n                  onChange={handleInputChange}\r\n                  rows=\"4\"\r\n                  className=\"w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg text-[var(--color-dark-blue)] placeholder-gray-400 focus:outline-none focus:border-[var(--color-blue)] focus:bg-white transition-all resize-none\"\r\n                  placeholder=\"Tell us about your security needs...\"\r\n                ></textarea>\r\n              </div>\r\n              \r\n              <button\r\n                type=\"submit\"\r\n                disabled={isSubmitting}\r\n                className=\"w-full bg-[var(--color-blue)] hover:bg-[var(--color-blue-secondary)] disabled:bg-gray-400 text-white px-8 py-4 rounded-lg font-bold text-lg transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center gap-2\"\r\n              >\r\n                {isSubmitting ? (\r\n                  <>\r\n                    <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-white\"></div>\r\n                    Sending Message...\r\n                  </>\r\n                ) : (\r\n                  <>\r\n                    Send Message\r\n                    <ArrowRight className=\"w-5 h-5\" />\r\n                  </>\r\n                )}\r\n              </button>\r\n            </form>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n\r\nexport default ContactUsSection;\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;;;AAHA;;;AAKO,SAAS;;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,OAAO;QACP,SAAS;QACT,SAAS;IACX;IAEA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,IAAI,cAAc;QAClB,gBAAgB;QAChB,YAAY;QACZ,WAAW;QAEX,IAAI;YACF,0DAA0D;YAC1D,MAAM,WAAW,MAAM,MAAM,oBAAoB;gBAC/C,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,MAAM,SAAS,IAAI;oBACnB,OAAO,SAAS,KAAK;oBACrB,SAAS,SAAS,OAAO;oBACzB,SAAS,SAAS,OAAO;oBACzB,UAAU;oBACV,SAAS;gBACX;YACF;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,IAAI,SAAS,EAAE,IAAI,OAAO,OAAO,EAAE;gBACjC,+BAA+B;gBAC/B,YAAY;oBAAE,MAAM;oBAAI,OAAO;oBAAI,SAAS;oBAAI,SAAS;gBAAG;gBAC5D,WAAW;YACb,OAAO;gBACL,YAAY,OAAO,KAAK,IAAI;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,YAAY;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAK,WAAU;kDAA+C;;;;;;;;;;;;0CAGjE,6LAAC;gCAAG,WAAU;;oCAA4F;kDAExG,6LAAC;wCAAK,WAAU;kDAAiC;;;;;;;;;;;;0CAKnD,6LAAC;gCAAE,WAAU;0CAA6E;;;;;;;;;;;;kCAK5F,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;kEAElB,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAmD;;;;;;0EACjE,6LAAC;gEAAE,WAAU;0EAAmC;;;;;;;;;;;;;;;;;;0DAIpD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;;;;;;0EAElB,6LAAC;;kFACC,6LAAC;wEAAE,WAAU;kFAA8C;;;;;;kFAC3D,6LAAC;wEAAE,WAAU;kFAA2B;;;;;;;;;;;;;;;;;;kEAI5C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;;;;;;0EAEnB,6LAAC;;kFACC,6LAAC;wEAAE,WAAU;kFAA8C;;;;;;kFAC3D,6LAAC;wEAAE,WAAU;kFAA2B;;;;;;;;;;;;;;;;;;kEAI5C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC,6MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;;;;;;0EAEpB,6LAAC;;kFACC,6LAAC;wEAAE,WAAU;kFAA8C;;;;;;kFAC3D,6LAAC;wEAAE,WAAU;kFAAmC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAMxD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAyB;;;;;;0DACvC,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;0EAAK;;;;;;;;;;;;kEAER,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;0EAAK;;;;;;;;;;;;kEAER,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;0EAAK;;;;;;;;;;;;kEAER,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;0EAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOd,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAwD;;;;;;0DACtE,6LAAC;gDAAE,WAAU;0DAAmC;;;;;;;;;;;;kDAGlD,6LAAC;wCAAK,UAAU;wCAAc,WAAU;;4CAErC,0BACC,6LAAC;gDAAI,WAAU;0DACZ;;;;;;4CAKJ,yBACC,6LAAC;gDAAI,WAAU;0DACZ;;;;;;0DAIL,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAAiE;;;;;;0EAClF,6LAAC;gEACC,MAAK;gEACL,MAAK;gEACL,OAAO,SAAS,IAAI;gEACpB,UAAU;gEACV,QAAQ;gEACR,WAAU;gEACV,aAAY;;;;;;;;;;;;kEAGhB,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAAiE;;;;;;0EAClF,6LAAC;gEACC,MAAK;gEACL,MAAK;gEACL,OAAO,SAAS,KAAK;gEACrB,UAAU;gEACV,QAAQ;gEACR,WAAU;gEACV,aAAY;;;;;;;;;;;;;;;;;;0DAKlB,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAAiE;;;;;;kEAClF,6LAAC;wDACC,MAAK;wDACL,MAAK;wDACL,OAAO,SAAS,OAAO;wDACvB,UAAU;wDACV,WAAU;wDACV,aAAY;;;;;;;;;;;;0DAIhB,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAAiE;;;;;;kEAClF,6LAAC;wDACC,MAAK;wDACL,OAAO,SAAS,OAAO;wDACvB,UAAU;wDACV,MAAK;wDACL,WAAU;wDACV,aAAY;;;;;;;;;;;;0DAIhB,6LAAC;gDACC,MAAK;gDACL,UAAU;gDACV,WAAU;0DAET,6BACC;;sEACE,6LAAC;4DAAI,WAAU;;;;;;wDAAkE;;iFAInF;;wDAAE;sEAEA,6LAAC,qNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU1C;GA5PgB;KAAA;uCA8PD", "debugId": null}}, {"offset": {"line": 2357, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/securitylit/SecurityLit-Website/src/app/Home/components/LandingSection.jsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { Download, ArrowRight, Shield, CheckCircle, Users, Cloud } from 'lucide-react';\r\nimport Image from 'next/image';\r\n\r\nexport default function Landing() {\r\n  const locations = ['New Zealand', 'India', 'Australia', 'USA'];\r\n  const [currentContentIndex, setCurrentContentIndex] = useState(0);\r\n  const [isTransitioning, setIsTransitioning] = useState(false);\r\n\r\n const contentVariations = [\r\n  {\r\n    statText: \"6 out of 10 companies flunk their first security audit.\",\r\n    questionText: \"Would your business pass or end up on the fail list?\"\r\n  },\r\n  {\r\n    statText: \"Data breaches cost companies an average of $4.45 million in 2023.\",\r\n    questionText: \"Can your business take that kind of hit and keep going?\"\r\n  },\r\n  {\r\n    statText: \"Human error is behind 95% of successful cyber attacks.\",\r\n    questionText: \"Is your team trained to spot threats or trigger them?\"\r\n  },\r\n  {\r\n    statText: \"Nearly half of all cyberattacks target small and mid-sized businesses.\",\r\n    questionText: \"Still think you're too small to be a target?\"\r\n  },\r\n  {\r\n    statText: \"Ransomware attacks jumped 41% last year and they're not slowing down.\",\r\n    questionText: \"If an attack lands today, do you fight back—or freeze?\"\r\n  }\r\n];\r\n\r\n\r\n  useEffect(() => {\r\n    const interval = setInterval(() => {\r\n      setIsTransitioning(true);\r\n      \r\n      setTimeout(() => {\r\n        setCurrentContentIndex((prev) => (prev + 1) % contentVariations.length);\r\n        setIsTransitioning(false);\r\n      }, 300);\r\n    }, 9000); \r\n\r\n    return () => clearInterval(interval);\r\n  }, []);\r\n\r\n  const currentContent = contentVariations[currentContentIndex];\r\n\r\n  return (\r\n    <section className=\"relative w-full min-h-screen bg-[var(--bg-light-blue)] py-16 lg:py:28 \">\r\n      <div className=\"container mx-auto mt-16 sm:mt-26 max-w-7xl px-4 lg:px-8 relative z-10\">\r\n        <div className=\"grid lg:grid-cols-2 gap-12 lg:gap-16 items-center\">\r\n          \r\n          {/* Left Column: Content */}\r\n          <div className=\"space-y-8\">\r\n            <div className=\"space-y-6\">\r\n              <h1 className=\"text-3xl sm:text-4xl md:text-5xl lg:text-5xl font-bold text-[var(--color-dark-blue)] leading-tight\">\r\n                Enterprise-Grade <br />\r\n                <span className=\"text-[var(--color-blue)]\">Cybersecurity Consulting</span>\r\n              </h1>\r\n              \r\n              <h2 className=\"text-xl md:text-2xl font-semibold text-yellow-500\">\r\n                Expert AWS & Azure Configuration Solutions\r\n              </h2>\r\n              \r\n              <p className=\"text-base md:text-lg text-gray-600 max-w-xl\">\r\n                Strategic cybersecurity leadership and hands-on security expertise. From vCISO services to advanced threat simulation, we strengthen your security posture across all business dimensions.\r\n              </p>\r\n            </div>\r\n\r\n            {/* Location Tags */}\r\n            <div className=\"flex flex-wrap items-center gap-2 md:gap-3\">\r\n              <span className=\"text-xs md:text-sm font-medium text-gray-500\">Serving:</span>\r\n              {locations.map((loc, index) => (\r\n                <span \r\n                  key={loc} \r\n                  className=\"px-3 py-1 bg-white border border-gray-200 text-gray-700 rounded-full text-xs md:text-sm hover:bg-gray-50 transition-colors cursor-pointer shadow-sm\"\r\n                  style={{ animationDelay: `${index * 0.1}s` }}\r\n                >\r\n                  {loc}\r\n                </span>\r\n              ))}\r\n              <span className=\"px-3 py-1 bg-blue-600 text-white rounded-full text-xs md:text-sm font-semibold shadow-sm\">\r\n                + more\r\n              </span>\r\n            </div>\r\n\r\n            {/* Action Buttons */}\r\n            <div className=\"flex flex-col sm:flex-row gap-4\">\r\n              <button className=\"group bg-blue-600 text-white px-8 py-4 rounded-lg font-semibold text-base hover:bg-blue-700 transition-all duration-300 flex items-center justify-center gap-2 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\">\r\n                Start Configuration \r\n                <ArrowRight className=\"w-4 h-4 group-hover:translate-x-1 transition-transform\" />\r\n              </button>\r\n              \r\n              <button className=\"group bg-white text-blue-600 border-2 border-yellow-500 px-8 py-4 rounded-lg font-semibold text-base hover:border-blue-300 hover:bg-blue-50 transition-all duration-300 flex items-center justify-center gap-2 shadow-sm hover:shadow-md\">\r\n                <Download className=\"w-4 h-4\" />\r\n                Download Service Overview\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Right Column: Cards */}\r\n          <div className=\"space-y-6 lg:pl-8\">\r\n            \r\n            {/* Security Alert Card */}\r\n            <div className=\"relative\">\r\n              <div className=\"bg-white rounded-2xl shadow-xl border border-gray-100 p-8 relative overflow-hidden\"> \r\n                {/* Background Overlay Image\r\n                <Image\r\n                  src=\"/images/geometric-pattern.png\" // change this path to your overlay image\r\n                  alt=\"Background Overlay\"\r\n                  layout=\"fill\"\r\n                  objectFit=\"contain\"\r\n                  className=\"absolute inset-0 opacity-100 pointer-events-none\"\r\n                  priority\r\n                /> */}\r\n\r\n                {/* Logo */}\r\n                <div className=\"flex justify-center mb-6 relative z-10\">\r\n                  <Image \r\n                    src=\"/images/securitylit_logo.png\" \r\n                    alt=\"Cybersecurity Protection\" \r\n                    width={50} \r\n                    height={50}\r\n                    className=\"rounded-lg\"\r\n                    priority\r\n                  />\r\n                </div>\r\n\r\n                {/* Content */}\r\n                <div className={`text-center space-y-4 relative z-10 transition-all duration-500 ease-in-out transform ${\r\n                  isTransitioning ? 'opacity-0 translate-y-4 scale-95' : 'opacity-100 translate-y-0 scale-100'\r\n                }`}>\r\n                  <p className=\"text-gray-900 max-w-2xl font-bold text-xl leading-tight\">\r\n                    {currentContent.statText}\r\n                  </p>\r\n                  <p className=\"text-blue-600 font-semibold text-lg\">\r\n                    {currentContent.questionText}\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Statistics Card */}\r\n            <div className=\"bg-white rounded-2xl shadow-xl border border-gray-100 p-8\">\r\n              <div className=\"grid grid-cols-3 gap-6\">\r\n                <div className=\"text-center\">\r\n                  <div className=\"w-12 h-12 bg-blue-600 rounded-xl flex items-center justify-center mx-auto mb-3\">\r\n                    <Cloud className=\"w-6 h-6 text-white\" />\r\n                  </div>\r\n                  <div className=\"text-2xl font-bold text-yellow-500 mb-1\">500+</div>\r\n                  <div className=\"text-sm text-gray-600 font-medium\">Projects Delivered</div>\r\n                </div>\r\n                \r\n                <div className=\"text-center\">\r\n                  <div className=\"w-12 h-12 bg-blue-600 rounded-xl flex items-center justify-center mx-auto mb-3\">\r\n                    <CheckCircle className=\"w-6 h-6 text-white\" />\r\n                  </div>\r\n                  <div className=\"text-2xl font-bold text-yellow-500 mb-1\">99.9%</div>\r\n                  <div className=\"text-sm text-gray-600 font-medium\">Success Rate</div>\r\n                </div>\r\n                \r\n                <div className=\"text-center\">\r\n                  <div className=\"w-12 h-12 bg-blue-600 rounded-xl flex items-center justify-center mx-auto mb-3\">\r\n                    <Shield className=\"w-6 h-6 text-white\" />\r\n                  </div>\r\n                  <div className=\"text-2xl font-bold text-yellow-500 mb-1\">24/7</div>\r\n                  <div className=\"text-sm text-gray-600 font-medium\">Expert Support</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,YAAY;QAAC;QAAe;QAAS;QAAa;KAAM;IAC9D,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAExD,MAAM,oBAAoB;QACzB;YACE,UAAU;YACV,cAAc;QAChB;QACA;YACE,UAAU;YACV,cAAc;QAChB;QACA;YACE,UAAU;YACV,cAAc;QAChB;QACA;YACE,UAAU;YACV,cAAc;QAChB;QACA;YACE,UAAU;YACV,cAAc;QAChB;KACD;IAGC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,MAAM,WAAW;8CAAY;oBAC3B,mBAAmB;oBAEnB;sDAAW;4BACT;8DAAuB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,kBAAkB,MAAM;;4BACtE,mBAAmB;wBACrB;qDAAG;gBACL;6CAAG;YAEH;qCAAO,IAAM,cAAc;;QAC7B;4BAAG,EAAE;IAEL,MAAM,iBAAiB,iBAAiB,CAAC,oBAAoB;IAE7D,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAGb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;;4CAAqG;0DAChG,6LAAC;;;;;0DAClB,6LAAC;gDAAK,WAAU;0DAA2B;;;;;;;;;;;;kDAG7C,6LAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAIlE,6LAAC;wCAAE,WAAU;kDAA8C;;;;;;;;;;;;0CAM7D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAA+C;;;;;;oCAC9D,UAAU,GAAG,CAAC,CAAC,KAAK,sBACnB,6LAAC;4CAEC,WAAU;4CACV,OAAO;gDAAE,gBAAgB,AAAC,GAAc,OAAZ,QAAQ,KAAI;4CAAG;sDAE1C;2CAJI;;;;;kDAOT,6LAAC;wCAAK,WAAU;kDAA2F;;;;;;;;;;;;0CAM7G,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAO,WAAU;;4CAA4N;0DAE5O,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;kDAGxB,6LAAC;wCAAO,WAAU;;0DAChB,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;;;;;;;;kCAOtC,6LAAC;wBAAI,WAAU;;0CAGb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDAYb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,OAAO;gDACP,QAAQ;gDACR,WAAU;gDACV,QAAQ;;;;;;;;;;;sDAKZ,6LAAC;4CAAI,WAAW,AAAC,yFAEhB,OADC,kBAAkB,qCAAqC;;8DAEvD,6LAAC;oDAAE,WAAU;8DACV,eAAe,QAAQ;;;;;;8DAE1B,6LAAC;oDAAE,WAAU;8DACV,eAAe,YAAY;;;;;;;;;;;;;;;;;;;;;;;0CAOpC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;8DAEnB,6LAAC;oDAAI,WAAU;8DAA0C;;;;;;8DACzD,6LAAC;oDAAI,WAAU;8DAAoC;;;;;;;;;;;;sDAGrD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,8NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;;;;;;8DAEzB,6LAAC;oDAAI,WAAU;8DAA0C;;;;;;8DACzD,6LAAC;oDAAI,WAAU;8DAAoC;;;;;;;;;;;;sDAGrD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;8DAEpB,6LAAC;oDAAI,WAAU;8DAA0C;;;;;;8DACzD,6LAAC;oDAAI,WAAU;8DAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrE;GA5KwB;KAAA", "debugId": null}}]}