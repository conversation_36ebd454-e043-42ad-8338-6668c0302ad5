"use client";

import React from 'react';

const paymentOptions = [
  {
    id: "annual",
    title: "Annual Payment",
    description: "Pay the full annual subscription fee upfront and save approximately 16% compared to monthly payments.",
    perfectFor: "Organizations that prefer annual budget cycles and want maximum savings.",
    hasDiscount: true,
    discountText: "16% Discount",
    icon: (
      // New Icon: CalendarDays - Represents a full year and planning.
      <svg className="w-10 h-10" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M8 2V5" stroke="currentColor" strokeWidth="2" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M16 2V5" stroke="currentColor" strokeWidth="2" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M3.5 9.09H20.5" stroke="currentColor" strokeWidth="2" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M21 8.5V17C21 20 19.5 22 16 22H8C4.5 22 3 20 3 17V8.5C3 5.5 4.5 3.5 8 3.5H16C19.5 3.5 21 5.5 21 8.5Z" stroke="currentColor" strokeWidth="2" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M15.6947 13.7H15.7037" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M15.6947 16.7H15.7037" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M11.9955 13.7H12.0045" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M11.9955 16.7H12.0045" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M8.29431 13.7H8.30331" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M8.29431 16.7H8.30331" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
      </svg>
    )
  },
  {
    id: "monthly",
    title: "Monthly Payments",
    description: "Pay a fixed monthly fee throughout the year for maximum flexibility in cash flow management.",
    perfectFor: "Organizations that prefer smaller, predictable monthly payments.",
    hasDiscount: false,
    discountText: "",
    icon: (
      // New Icon: CreditCard - Represents payments and flexibility.
      <svg className="w-10 h-10" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect x="3" y="5" width="18" height="14" rx="2" stroke="currentColor" strokeWidth="2"/>
        <line x1="3" y1="10" x2="21" y2="10" stroke="currentColor" strokeWidth="2"/>
        <line x1="7" y1="15" x2="9" y2="15" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
      </svg>
    )
  }
];

export default function PaymentOption() {
  return (
    <section className="w-full py-20 md:py-24 lg:py-32 px-4 sm:px-6 md:px-8 lg:px-12 xl:px-28 bg-tertiary-blue">
      <div className="max-w-8xl mx-auto">
        {/* Header */}
        <div className="text-center mb-16 lg:mb-20">
          <h2 className="text-3xl sm:text-4xl md:text-5xl font-[600] leading-tight mb-6">
            <span className="text-white">Subscription Payment Options</span>
          </h2>
          <p className="text-white text-lg md:text-xl leading-relaxed max-w-3xl mx-auto">
            Choose the payment plan that best fits your organization&apos;s needs and budget cycle.
          </p>
        </div>

        {/* Payment Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 lg:gap-8">
          {paymentOptions.map((option) => (
            <div
              key={option.id}
              className="bg-white rounded-xl p-8 h-full flex flex-col transition-all duration-300 ease-in-out border-2 border-transparent hover:-translate-y-2 hover:border-secondary-blue hover:shadow-xl cursor-pointer group relative"
            >
              {/* Discount Capsule Badge */}
              {option.hasDiscount && (
                <div className="absolute -top-4 left-8 z-20">
                  <span className="bg-[#062575] text-white text-sm font-bold px-5 py-2 rounded-full shadow-md">
                    {option.discountText}
                  </span>
                </div>
              )}

              {/* Icon */}
              <div className="mb-6 relative z-10">
                <div className="p-3 rounded-full w-fit bg-gradient-to-br from-primary-blue/10 to-secondary-blue/5 text-secondary-blue group-hover:text-primary-blue transition-colors duration-300">
                  {option.icon}
                </div>
              </div>

              <div className="flex-grow relative z-10">
                <h3 className="text-xl lg:text-2xl font-semibold text-tertiary-blue mb-4 group-hover:text-primary-blue transition-colors duration-300">
                  {option.title}
                </h3>
                <p className="text-tertiary-blue/80 text-base leading-relaxed mb-6">
                  {option.description}
                </p>
              </div>

              {/* "Perfect for" Section */}
              <div className="mt-auto pt-6 border-t-2 border-tertiary-blue/20 relative z-10">
                <p className="text-tertiary-blue/80">
                  <span className="font-semibold text-tertiary-blue">Perfect for:</span> {option.perfectFor}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
