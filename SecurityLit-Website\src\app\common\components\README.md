# Breadcrumb Navigation Component

This component provides responsive breadcrumb navigation with SEO structured data for better search engine optimization.

## Usage

### Method 1: In Hero Components (Recommended)

Add breadcrumbs directly to your hero components for better visual integration:

```jsx
// In your hero component (e.g., contactHero.jsx)
import BreadcrumbNavigation from "../../common/components/BreadcrumbNavigation";

export default function ContactHero() {
  const breadcrumbItems = [
    {
      name: "Home",
      url: "/",
      iconKey: "home",
      description: "Return to homepage"
    },
    {
      name: "Contact Us",
      url: "/contact",
      current: true,
      iconKey: "contact-us",
      description: "Get in touch with SecurityLit for cybersecurity consultation"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-[var(--color-dark-blue)] via-[var(--color-dark-blue)] to-[var(--color-dark-blue-hover)]">
      <section className="pt-32 pb-20 px-4 relative overflow-hidden">
        <div className="container mx-auto max-w-7xl relative z-10">
          {/* Breadcrumb Navigation - Starts after navbar */}
          <div className="pt-24 mb-8">
            <BreadcrumbNavigation items={breadcrumbItems} className="text-white" />
          </div>
          
          {/* Your hero content */}
          <div className="text-center mb-16">
            {/* ... rest of your hero content */}
          </div>
        </div>
      </section>
    </div>
  );
}
```

### Method 2: Direct Usage in Page Components

For server components, define the breadcrumb items directly:

```jsx
import BreadcrumbNavigation from '../common/components/BreadcrumbNavigation';

export default function YourPage() {
  const breadcrumbItems = [
    {
      name: "Home",
      url: "/",
      iconKey: "home",
      description: "Return to homepage"
    },
    {
      name: "Your Page Name",
      url: "/your-page-url",
      current: true,
      iconKey: "your-icon-key",
      description: "Description for SEO"
    }
  ];

  return (
    <div>
      <BreadcrumbNavigation items={breadcrumbItems} />
      {/* Your page content */}
    </div>
  );
}
```

### Method 3: Using Helper Functions (Client Components Only)

For client components, you can use the helper functions:

```jsx
"use client";
import BreadcrumbWrapper from '../common/components/BreadcrumbWrapper';

export default function YourClientPage() {
  return (
    <div>
      <BreadcrumbWrapper 
        pageType="simple" 
        params={{
          pageName: 'Your Page Name',
          url: '/your-page-url',
          iconKey: 'your-icon-key',
          description: 'Description for SEO'
        }}
      />
      {/* Your page content */}
    </div>
  );
}
```

## Available Page Types

The `generateBreadcrumbs` function supports different page types:

1. **Simple Page** (`simple`): For basic pages like Contact, About, etc.
2. **Blog** (`blog`): For blog posts
3. **Service** (`service`): For service pages
4. **Location** (`location`): For location-specific pages
5. **Company** (`company`): For company-related pages
6. **Product** (`product`): For product pages

## Examples

### Contact Page Hero Component
```jsx
const breadcrumbItems = [
  {
    name: "Home",
    url: "/",
    iconKey: "home",
    description: "Return to homepage"
  },
  {
    name: "Contact Us",
    url: "/contact",
    current: true,
    iconKey: "contact-us",
    description: "Get in touch with SecurityLit for cybersecurity consultation"
  }
];
```

### Service Page Hero Component
```jsx
const breadcrumbItems = [
  {
    name: "Home",
    url: "/",
    iconKey: "home",
    description: "Return to homepage"
  },
  {
    name: "Services",
    url: "/services",
    iconKey: "services",
    description: "View all cybersecurity services"
  },
  {
    name: "VAPT Services",
    url: "/services/vapt",
    current: true,
    iconKey: "vapt",
    description: "Vulnerability Assessment and Penetration Testing services"
  }
];
```

### Blog Post Hero Component
```jsx
const breadcrumbItems = [
  {
    name: "Home",
    url: "/",
    iconKey: "home",
    description: "Return to homepage"
  },
  {
    name: "Blog",
    url: "/blog",
    iconKey: "blogs",
    description: "View all blog posts"
  },
  {
    name: "Cybersecurity Best Practices",
    url: "/blog/cybersecurity-best-practices",
    current: true,
    description: "Learn about cybersecurity best practices"
  }
];
```

## Features

- ✅ **Responsive Design**: Works on all screen sizes
- ✅ **SEO Optimized**: Includes structured data for search engines
- ✅ **Accessible**: Proper ARIA labels and semantic HTML
- ✅ **Animated**: Smooth animations using Framer Motion
- ✅ **Mobile Friendly**: Truncated text with expand/collapse functionality
- ✅ **Icon Support**: Icons for different page types
- ✅ **Customizable**: Custom styling and colors
- ✅ **Server/Client Compatible**: Works with both server and client components
- ✅ **Hero Integration**: Perfect for integration into hero sections

## Styling

The component supports custom styling through the `className` prop:

```jsx
<BreadcrumbNavigation 
  items={breadcrumbItems} 
  className="text-white" // For dark backgrounds
/>
```

## Icons

Available icon keys:
- `home`: Home icon
- `blogs`: File/Text icon
- `services`: Shield icon
- `contact-us`: Phone icon
- `product`: Shield icon
- `company`: Building icon
- And more...

## SEO Benefits

The component automatically generates structured data for breadcrumbs, which helps search engines understand your site's navigation hierarchy.

## Best Practices

### For Hero Components:
1. **Position**: Place breadcrumbs at the top of the hero section
2. **Navbar Spacing**: Use `pt-24` (96px) to account for fixed navbar
3. **Styling**: Use `className="text-white"` for dark backgrounds
4. **Spacing**: Add `mb-8` or similar margin for proper spacing
5. **Integration**: Include within the hero's container for proper alignment

### For Page Components:
1. **Server Components**: Use direct item definition for better performance
2. **Client Components**: Use `BreadcrumbWrapper` with helper functions
3. **SEO**: Both approaches include structured data

## Important Notes

- **Hero Integration**: Recommended approach for better visual integration
- **Navbar Consideration**: Always use `pt-24` to start breadcrumbs after fixed navbar
- **Server Components**: Use `BreadcrumbNavigation` directly with manually defined items
- **Client Components**: Use `BreadcrumbWrapper` with helper functions
- **Performance**: Server components are preferred for better performance
- **SEO**: Both approaches include structured data for search engines
- **Home Page**: No breadcrumbs needed on the home page 